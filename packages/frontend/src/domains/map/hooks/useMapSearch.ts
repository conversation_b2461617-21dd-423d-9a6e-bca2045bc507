/**
 * 地图搜索Hook
 * 企业级状态管理和错误处理
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { mapSearchService, type SearchCircleParams, type MapMarker } from '../services/mapSearchService';

interface UseMapSearchOptions {
  onSearchStart?: () => void;
  onSearchComplete?: (results: MapMarker[]) => void;
  onSearchError?: (error: Error) => void;
  debounceMs?: number;
}

interface UseMapSearchReturn {
  // 状态
  properties: MapMarker[];
  loading: boolean;
  error: string | null;
  searchHistory: SearchCircleParams[];
  
  // 操作
  searchProperties: (params: SearchCircleParams) => Promise<void>;
  clearSearch: () => void;
  clearError: () => void;
  retryLastSearch: () => Promise<void>;
  
  // 统计
  lastSearchParams: SearchCircleParams | null;
  searchCount: number;
}

export const useMapSearch = (options: UseMapSearchOptions = {}): UseMapSearchReturn => {
  const {
    onSearchStart,
    onSearchComplete,
    onSearchError,
    debounceMs = 300,
  } = options;

  // 状态管理
  const [properties, setProperties] = useState<MapMarker[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchHistory, setSearchHistory] = useState<SearchCircleParams[]>([]);
  const [lastSearchParams, setLastSearchParams] = useState<SearchCircleParams | null>(null);
  const [searchCount, setSearchCount] = useState(0);

  // 防抖控制
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 使用ref存储回调函数，避免依赖问题
  const callbacksRef = useRef({
    onSearchStart,
    onSearchComplete,
    onSearchError,
  });

  // 更新回调函数引用
  callbacksRef.current = {
    onSearchStart,
    onSearchComplete,
    onSearchError,
  };

  /**
   * 执行搜索
   */
  const searchProperties = useCallback(async (params: SearchCircleParams) => {
    // 清除之前的防抖定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // 防抖处理
    return new Promise<void>((resolve, reject) => {
      debounceTimerRef.current = setTimeout(async () => {
        try {
          setLoading(true);
          setError(null);
          callbacksRef.current.onSearchStart?.();

          // 创建新的AbortController
          abortControllerRef.current = new AbortController();

          console.log('🔍 开始地图搜索:', params);

          // 执行搜索
          const results = await mapSearchService.searchInCircle(params);

          // 检查请求是否被取消
          if (abortControllerRef.current?.signal.aborted) {
            console.log('🚫 搜索请求被取消');
            return;
          }

          // 更新状态
          setProperties(results);
          setLastSearchParams(params);
          setSearchCount(prev => prev + 1);

          // 更新搜索历史
          setSearchHistory(prev => {
            const newHistory = [params, ...prev.slice(0, 9)]; // 保留最近10次搜索
            return newHistory;
          });

          // 记录搜索行为（异步，不影响主流程）
          mapSearchService.recordSearchAction({
            center: params.center,
            radius: params.radius,
            query: params.query,
            results_count: results.length,
          }).catch(console.warn);

          console.log('✅ 地图搜索完成:', {
            resultsCount: results.length,
            searchParams: params,
          });

          callbacksRef.current.onSearchComplete?.(results);
          resolve();
        } catch (err) {
          if (abortControllerRef.current?.signal.aborted) {
            console.log('🚫 搜索请求被取消');
            return;
          }

          const error = err instanceof Error ? err : new Error('搜索失败');
          console.error('❌ 地图搜索失败:', error);
          
          setError(error.message);
          callbacksRef.current.onSearchError?.(error);
          reject(error);
        } finally {
          setLoading(false);
        }
      }, debounceMs);
    });
  }, [debounceMs]);

  /**
   * 清除搜索结果
   */
  const clearSearch = useCallback(() => {
    // 取消正在进行的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // 清除防抖定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    setProperties([]);
    setError(null);
    setLoading(false);
    
    console.log('🧹 清除地图搜索结果');
  }, []);

  /**
   * 清除错误状态
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * 重试上次搜索
   */
  const retryLastSearch = useCallback(async () => {
    if (!lastSearchParams) {
      console.warn('⚠️ 没有上次搜索参数，无法重试');
      return;
    }

    console.log('🔄 重试上次搜索:', lastSearchParams);
    await searchProperties(lastSearchParams);
  }, [lastSearchParams, searchProperties]);

  // 清理函数
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    // 状态
    properties,
    loading,
    error,
    searchHistory,
    
    // 操作
    searchProperties,
    clearSearch,
    clearError,
    retryLastSearch,
    
    // 统计
    lastSearchParams,
    searchCount,
  };
};

/**
 * 简化版Hook，用于快速搜索
 */
export const useQuickMapSearch = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const quickSearch = useCallback(async (
    lat: number,
    lng: number,
    radius: number = 1000
  ): Promise<MapMarker[]> => {
    try {
      setLoading(true);
      setError(null);
      
      const results = await mapSearchService.searchInCircleSimple(lat, lng, radius);
      return results;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('快速搜索失败');
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    quickSearch,
    loading,
    error,
    clearError: () => setError(null),
  };
};
