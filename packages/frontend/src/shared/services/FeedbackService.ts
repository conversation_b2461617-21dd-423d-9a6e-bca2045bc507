/**
 * 简化版用户反馈服务
 * 统一管理Alert.alert调用，避免代码重复
 * 基于业界标准的简单抽象层
 */

import { Alert } from 'react-native';

/**
 * 简化版用户反馈服务
 * 只封装最常用的Alert.alert场景，避免过度设计
 */
class FeedbackService {
  
  /**
   * 显示成功提示
   * @param message 成功消息
   * @param onConfirm 确认回调
   */
  static showSuccess(message: string, onConfirm?: () => void) {
    Alert.alert('成功', message, [
      {
        text: '确定',
        onPress: onConfirm
      }
    ]);
  }

  /**
   * 显示错误提示
   * @param message 错误消息
   * @param onConfirm 确认回调
   */
  static showError(message: string, onConfirm?: () => void) {
    Alert.alert('错误', message, [
      {
        text: '确定',
        onPress: onConfirm
      }
    ]);
  }

  /**
   * 显示警告提示
   * @param message 警告消息
   * @param onConfirm 确认回调
   */
  static showWarning(message: string, onConfirm?: () => void) {
    Alert.alert('警告', message, [
      {
        text: '确定',
        onPress: onConfirm
      }
    ]);
  }

  /**
   * 显示信息提示
   * @param message 信息内容
   * @param onConfirm 确认回调
   */
  static showInfo(message: string, onConfirm?: () => void) {
    Alert.alert('提示', message, [
      {
        text: '确定',
        onPress: onConfirm
      }
    ]);
  }

  /**
   * 显示确认对话框
   * @param title 标题
   * @param message 消息内容
   * @param onConfirm 确认回调
   * @param onCancel 取消回调
   * @param confirmText 确认按钮文字
   * @param cancelText 取消按钮文字
   */
  static showConfirm(
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void,
    confirmText: string = '确定',
    cancelText: string = '取消'
  ) {
    Alert.alert(title, message, [
      {
        text: cancelText,
        style: 'cancel',
        onPress: onCancel
      },
      {
        text: confirmText,
        onPress: onConfirm
      }
    ]);
  }

  // === 业务场景的快捷方法 ===

  /**
   * 验证码发送成功提示
   */
  static showCodeSent() {
    // 业界标准：验证码发送成功不需要弹窗，用户能看到倒计时就知道成功了
    console.log('[FeedbackService] 验证码发送成功');
  }

  /**
   * 验证码发送失败提示
   */
  static showCodeSendFailed(error: string) {
    this.showError(`验证码发送失败：${error}`);
  }

  /**
   * 登录成功提示
   */
  static showLoginSuccess() {
    // 业界标准：登录成功不需要弹窗，直接跳转页面用户就知道成功了
    console.log('[FeedbackService] 登录成功');
  }

  /**
   * 登录失败提示
   */
  static showLoginFailed(error: string) {
    this.showError(error || '登录失败，请重试');
  }

  /**
   * 注册成功提示
   */
  static showRegisterSuccess(onConfirm?: () => void) {
    this.showSuccess('注册成功，欢迎加入！', onConfirm);
  }

  /**
   * 注册失败提示
   */
  static showRegisterFailed(error: string) {
    this.showError(error || '注册失败，请重试');
  }

  /**
   * 验证码验证失败提示
   */
  static showCodeVerifyFailed(error: string) {
    this.showError(`验证码验证失败：${error}`);
  }

  /**
   * 输入验证提示（手机号、验证码格式等）
   */
  static showInputValidation(message: string) {
    this.showInfo(message);
  }

  /**
   * 网络错误提示
   */
  static showNetworkError() {
    this.showError('网络连接失败，请检查网络后重试');
  }

  /**
   * 权限不足提示
   */
  static showPermissionDenied(permission: string) {
    this.showWarning(`需要${permission}权限才能继续操作`);
  }

  /**
   * 上传成功提示
   */
  static showUploadSuccess() {
    this.showSuccess('上传成功');
  }

  /**
   * 上传失败提示
   */
  static showUploadFailed() {
    this.showError('上传失败，请检查网络连接后重试');
  }
}

export default FeedbackService;
