/**
 * 最小高德地图定位测试组件
 * 用于排查SDK定位回调问题
 */

import React, { useEffect } from 'react';
import { View, StyleSheet, Text, Alert } from 'react-native';
import { MapView } from 'react-native-amap3d';

const MinimalMapTest = () => {
  useEffect(() => {
    console.log('🔧 [MinimalMapTest] 组件挂载，开始测试高德地图定位');
  }, []);

  const handleLocation = (event: any) => {
    console.log('🎯 [MinimalMapTest] 高德SDK定位回调触发！', event);
    console.log('🎯 [MinimalMapTest] 定位数据详情:', JSON.stringify(event, null, 2));
    
    // 弹窗显示定位结果，便于测试时确认
    Alert.alert(
      '定位成功！', 
      `经度: ${event.nativeEvent?.longitude}\n纬度: ${event.nativeEvent?.latitude}\n精度: ${event.nativeEvent?.accuracy}m`,
      [{ text: '确定' }]
    );
  };

  const handleMapReady = () => {
    console.log('🗺️ [MinimalMapTest] 地图加载完成');
  };

  // 移除 handleError - onError 属性在 MapViewProps 中不存在

  return (
    <View style={styles.container}>
      <Text style={styles.header}>最小高德地图定位测试</Text>
      <Text style={styles.instruction}>
        如果配置正确，应该会：{'\n'}
        1. 自动弹出位置权限对话框{'\n'}
        2. 用户允许后显示蓝色定位点{'\n'}
        3. 触发定位回调并弹窗显示坐标
      </Text>
      
      <MapView
        style={styles.map}
        myLocationEnabled={true}
        myLocationButtonEnabled={true}
        onLocation={handleLocation}
        onLoad={handleMapReady}
        initialCameraPosition={{
          target: {
            latitude: 39.9042,
            longitude: 116.4074,
          },
          zoom: 15,
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    padding: 10,
    color: '#333333',
  },
  instruction: {
    fontSize: 12,
    textAlign: 'center',
    padding: 10,
    color: '#666666',
    backgroundColor: '#f0f0f0',
  },
  map: {
    flex: 1,
  },
});

export default MinimalMapTest;