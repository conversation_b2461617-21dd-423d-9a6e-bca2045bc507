/**
 * 🔧 UI界面恢复验证脚本
 * 验证企业级架构重构后UI界面是否完全恢复
 */

console.log('🔧 UI界面恢复验证开始...');

// 验证主要组件文件存在性
const fs = require('fs');
const path = require('path');

const criticalFiles = [
  // 主屏幕
  'src/screens/Property/PropertyNavigationScreen.tsx',

  // 重构后的主组件
  'src/domains/property/components/detail/PropertyNavigationMapRefactored.tsx',

  // Store层
  'src/domains/property/components/detail/stores/MapNavigationStore.ts',

  // Hook层
  'src/domains/property/components/detail/hooks/usePropertyNavigation.ts',

  // 子组件
  'src/domains/property/components/detail/components/MapDisplay.tsx',
  'src/domains/property/components/detail/components/NavigationControls.tsx',
  'src/domains/property/components/detail/components/RouteSelectionPanel.tsx',
  'src/domains/property/components/detail/components/RouteInfoDisplay.tsx',
  'src/domains/property/components/detail/components/index.ts',

  // 类型定义
  'src/domains/property/components/detail/types/navigation.types.ts',
];

let allFilesExist = true;
const missingFiles = [];

criticalFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - 存在`);
  } else {
    console.log(`❌ ${file} - 缺失`);
    allFilesExist = false;
    missingFiles.push(file);
  }
});

console.log('\n📊 架构完整性验证:');
console.log(`- 关键文件数量: ${criticalFiles.length}`);
console.log(`- 存在文件数量: ${criticalFiles.length - missingFiles.length}`);
console.log(`- 缺失文件数量: ${missingFiles.length}`);
console.log(
  `- 架构完整性: ${allFilesExist ? '100%' : (((criticalFiles.length - missingFiles.length) / criticalFiles.length) * 100).toFixed(1) + '%'}`
);

if (missingFiles.length > 0) {
  console.log('\n❌ 缺失的关键文件:');
  missingFiles.forEach(file => console.log(`  - ${file}`));
}

// 验证PropertyNavigationScreen的导入正确性
console.log('\n🔍 PropertyNavigationScreen导入验证:');
try {
  const screenContent = fs.readFileSync(
    path.join(__dirname, 'src/screens/Property/PropertyNavigationScreen.tsx'),
    'utf8'
  );

  const hasCorrectImport = screenContent.includes(
    'PropertyNavigationMapRefactored as PropertyNavigationMap'
  );
  const hasRouteParams = screenContent.includes('propertyLocation');
  const hasMapContainer = screenContent.includes('mapContainer');

  console.log(
    `✅ 导入PropertyNavigationMapRefactored: ${hasCorrectImport ? '正确' : '错误'}`
  );
  console.log(`✅ 路由参数处理: ${hasRouteParams ? '正确' : '错误'}`);
  console.log(`✅ 地图容器样式: ${hasMapContainer ? '正确' : '错误'}`);

  if (hasCorrectImport && hasRouteParams && hasMapContainer) {
    console.log('✅ PropertyNavigationScreen 配置正确');
  } else {
    console.log('❌ PropertyNavigationScreen 配置异常');
  }
} catch (error) {
  console.log('❌ PropertyNavigationScreen 读取失败:', error.message);
}

// 验证Store选择器架构
console.log('\n🏪 Store选择器架构验证:');
try {
  const storeContent = fs.readFileSync(
    path.join(
      __dirname,
      'src/domains/property/components/detail/stores/MapNavigationStore.ts'
    ),
    'utf8'
  );

  const hasStableSelectors = storeContent.includes('useSetNativeLocation');
  const hasLegacySelector = storeContent.includes('useMapNavigationActions');
  const hasComputedProperties = storeContent.includes(
    'get currentStartLocation'
  );

  console.log(`✅ 稳定选择器: ${hasStableSelectors ? '已实现' : '缺失'}`);
  console.log(`✅ 向后兼容: ${hasLegacySelector ? '保留' : '缺失'}`);
  console.log(`✅ 计算属性: ${hasComputedProperties ? '已实现' : '缺失'}`);

  if (hasStableSelectors && hasLegacySelector && hasComputedProperties) {
    console.log('✅ Store架构完整');
  } else {
    console.log('❌ Store架构不完整');
  }
} catch (error) {
  console.log('❌ Store文件读取失败:', error.message);
}

// 验证Hook层修复
console.log('\n🔧 Hook层无限循环修复验证:');
try {
  const hookContent = fs.readFileSync(
    path.join(
      __dirname,
      'src/domains/property/components/detail/hooks/usePropertyNavigation.ts'
    ),
    'utf8'
  );

  const hasStableActions = hookContent.includes('React.useMemo');
  const hasEmptyDependency = hookContent.includes('}, []);');
  const hasReactImport = hookContent.includes('import React');

  console.log(`✅ 稳定操作引用: ${hasStableActions ? '已实现' : '缺失'}`);
  console.log(`✅ 空依赖数组: ${hasEmptyDependency ? '已实现' : '缺失'}`);
  console.log(`✅ React导入: ${hasReactImport ? '已实现' : '缺失'}`);

  if (hasStableActions && hasEmptyDependency && hasReactImport) {
    console.log('✅ 无限循环修复完成');
  } else {
    console.log('❌ 无限循环修复不完整');
  }
} catch (error) {
  console.log('❌ Hook文件读取失败:', error.message);
}

// 验证子组件集成
console.log('\n🧩 子组件集成验证:');
const subComponents = [
  'MapDisplay',
  'NavigationControls',
  'RouteSelectionPanel',
  'RouteInfoDisplay',
];

let componentIntegrationScore = 0;
subComponents.forEach(component => {
  const componentPath = path.join(
    __dirname,
    `src/domains/property/components/detail/components/${component}.tsx`
  );
  try {
    const componentContent = fs.readFileSync(componentPath, 'utf8');
    const hasPropsInterface = componentContent.includes(`${component}Props`);
    const hasDefaultExport = componentContent.includes(
      `export default ${component}`
    );

    if (hasPropsInterface && hasDefaultExport) {
      console.log(`✅ ${component}: 完整`);
      componentIntegrationScore++;
    } else {
      console.log(`⚠️  ${component}: 不完整`);
    }
  } catch (error) {
    console.log(`❌ ${component}: 缺失`);
  }
});

console.log(
  `\n📊 子组件集成度: ${componentIntegrationScore}/${subComponents.length} (${((componentIntegrationScore / subComponents.length) * 100).toFixed(0)}%)`
);

// 总体评估
console.log('\n🎯 UI界面恢复总体评估:');
const totalChecks = 5; // 文件存在、Screen配置、Store架构、Hook修复、组件集成
let passedChecks = 0;

if (allFilesExist) passedChecks++;
if (componentIntegrationScore === subComponents.length) passedChecks++;
// 其他检查通过情况需要根据上面的结果判断

const recoveryScore = ((passedChecks / totalChecks) * 100).toFixed(0);
console.log(
  `🏆 UI恢复评分: ${recoveryScore}% (${passedChecks}/${totalChecks})`
);

if (recoveryScore >= 80) {
  console.log('✅ UI界面基本恢复，可以进行测试');
} else if (recoveryScore >= 60) {
  console.log('⚠️  UI界面部分恢复，需要进一步修复');
} else {
  console.log('❌ UI界面恢复不足，需要重新检查');
}

console.log('\n🔧 用户测试建议:');
console.log('1. 重启React Native应用和Metro bundler');
console.log('2. 清除所有缓存: npx react-native start --reset-cache');
console.log('3. 打开地图导航页面，验证以下功能:');
console.log('   - 地图正常显示');
console.log('   - 起点终点选择按钮正常');
console.log('   - 路线模式切换正常');
console.log('   - 导航控制面板显示正常');
console.log('   - 不再出现无限循环错误');
console.log('\n测试完成! 📱');
