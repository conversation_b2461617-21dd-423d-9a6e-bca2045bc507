#!/usr/bin/env python3
"""
房源发布功能测试脚本
验证地址字段和价格约束修复是否成功
"""

import json
import sys
import os

# 设置Python路径
sys.path.append('/data/my-real-estate-app/packages/backend')

def test_property_schema():
    """测试PropertyCreate Schema"""
    print("🧪 测试PropertyCreate Schema...")
    
    try:
        from app.schemas.property.property import PropertyCreate
        
        # 创建测试数据 - 模拟前端发送的数据
        test_data = {
            "title": "测试商铺",
            "property_type": "SHOP",
            "address": "广西南宁市青秀区民族大道100号",
            "total_area": 100.0,
            "floor": 1,
            "orientation": "南向",
            "decoration_level": "REFINED",
            "description": "这是一个测试房源",
            "transaction_types": ["RENT"],
            "features": {
                "has_elevator": True,
                "has_parking": True,
                "parking_spaces": 2,
                "has_air_conditioning": True,
                "floor_height": 3.5,
                "frontage_width": 10.0,
                "depth": 15.0,
                "can_open_fire": True,
                "has_chimney": True,
                "has_private_toilet": True
            },
            "prices": [{
                "transaction_type": "RENT",
                "rent_price": 8000.0,
                "rent_unit_price": 80.0,
                "rent_deposit_months": 3
            }],
            "tags": ["近地铁", "人流量大", "适合餐饮"]
        }
        
        # 创建PropertyCreate实例
        property_create = PropertyCreate(**test_data)
        print(f"✅ PropertyCreate创建成功")
        
        # 测试关键字段
        print(f"   - 地址字段: {property_create.address}")
        print(f"   - 特性字段类型: {type(property_create.features)}")
        print(f"   - 价格字段类型: {type(property_create.prices)}")
        print(f"   - 标签字段: {property_create.tags}")
        
        # 测试model_dump
        dumped = property_create.model_dump(exclude={'features', 'prices'})
        print(f"✅ model_dump成功，包含地址: {'address' in dumped}")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_property_service_logic():
    """测试PropertyService处理逻辑"""
    print("\n🧪 测试PropertyService逻辑...")
    
    try:
        from app.schemas.property.property import PropertyCreate
        
        # 创建测试数据
        property_data = PropertyCreate(
            title="测试写字楼",
            property_type="OFFICE", 
            address="广西南宁市青秀区东盟商务区",
            total_area=200.0,
            transaction_types=["RENT"],
            features={"has_elevator": True, "has_parking": True},
            prices=[{"transaction_type": "RENT", "rent_price": 15000.0}],
            tags=["CBD", "交通便利"]
        )
        
        # 模拟PropertyService的处理逻辑
        property_dict = property_data.model_dump(
            exclude_unset=True, 
            exclude={'features', 'prices'}
        )
        
        # 处理价格数据
        if property_data.prices and isinstance(property_data.prices, list):
            for price_data in property_data.prices:
                if isinstance(price_data, dict):
                    transaction_type = price_data.get('transaction_type', '').upper()
                    if transaction_type == 'RENT' and price_data.get('rent_price'):
                        property_dict['rent_price'] = price_data['rent_price']
                        print(f"✅ 租金价格处理成功: {property_dict['rent_price']}")
        
        # 验证关键字段
        assert 'address' in property_dict, "地址字段缺失"
        assert 'rent_price' in property_dict, "租金字段缺失"
        assert property_dict['rent_price'] == 15000.0, "租金价格不正确"
        
        print(f"✅ PropertyService逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Service逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_property_model():
    """测试Property模型"""
    print("\n🧪 测试Property模型...")
    
    try:
        from app.models.property.property import Property
        from app.core.constants import PropertyType, PropertyStatus, VerificationStatus, DeliveryStatus
        import uuid
        
        # 创建测试Property实例
        test_property = Property(
            title="测试厂房",
            property_type=PropertyType.FACTORY,
            address="广西南宁市西乡塘区工业园",
            total_area=500.0,
            rent_price=20000.0,  # 关键: 包含租金价格满足约束
            status=PropertyStatus.PENDING,
            verification_status=VerificationStatus.PENDING,
            delivery_status=DeliveryStatus.IMMEDIATE,
            owner_id=uuid.uuid4(),
            transaction_types=["RENT"]
        )
        
        print(f"✅ Property模型创建成功")
        print(f"   - ID类型: {type(test_property.id)}")
        print(f"   - 地址: {test_property.address}")
        print(f"   - 租金: {test_property.rent_price}")
        print(f"   - 售价: {test_property.sale_price}")
        print(f"   - 转让费: {test_property.transfer_price}")
        
        # 验证至少有一个价格字段不为空
        has_price = any([
            test_property.rent_price is not None,
            test_property.sale_price is not None,
            test_property.transfer_price is not None
        ])
        assert has_price, "必须至少有一个价格字段不为空"
        print(f"✅ 价格约束满足")
        
        return True
        
    except Exception as e:
        print(f"❌ Property模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始房源发布功能测试\n")
    
    tests = [
        ("Schema测试", test_property_schema),
        ("Service逻辑测试", test_property_service_logic),
        ("Property模型测试", test_property_model)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name}出现异常: {e}")
            results.append(False)
    
    # 输出总结
    print(f"\n📊 测试总结:")
    success_count = sum(results)
    total_count = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！房源发布功能修复成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    os.chdir('/data/my-real-estate-app/packages/backend')
    success = main()
    sys.exit(0 if success else 1)