/**
 * 买房筛选弹窗组件
 * 职责：专门用于买房房源的详细筛选面板
 *
 * 设计理念：
 * 1. 极简风格，按照用户截图样式设计
 * 2. 包含完整的买房筛选选项
 * 3. 底部弹窗样式，支持手势关闭
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { wp, hp, fp } from '@shared/utils/responsive';

interface SaleFilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filters: SaleFilters) => void;
}

interface SaleFilters {
  priceRange: [number, number];
  areaRange: [number, number];
  propertyTypes: string[];
  features: string[];
  location: string[];
}

export const SaleFilterModal: React.FC<SaleFilterModalProps> = ({
  visible,
  onClose,
  onApply,
}) => {
  // 筛选状态
  const [filters, setFilters] = useState<SaleFilters>({
    priceRange: [0, 99999999], // 默认"不限"价格区间
    areaRange: [0, 999999], // 默认"不限"面积区间
    propertyTypes: ['不限'], // 默认选中"不限"
    features: ['不限'], // 默认选中"不限"
    location: ['不限'], // 默认选中"不限"
  });

  // 价格区间选项（买房）
  const priceRanges = [
    { label: '不限', value: [0, 99999999] },
    { label: '50万以下', value: [0, 500000] },
    { label: '50-100万', value: [500000, 1000000] },
    { label: '100-200万', value: [1000000, 2000000] },
    { label: '200-500万', value: [2000000, 5000000] },
    { label: '500-1000万', value: [5000000, 10000000] },
    { label: '1000万以上', value: [10000000, 99999999] },
  ];

  // 面积区间选项
  const areaRanges = [
    { label: '不限', value: [0, 999999] },
    { label: '50㎡以下', value: [0, 50] },
    { label: '50-100㎡', value: [50, 100] },
    { label: '100-200㎡', value: [100, 200] },
    { label: '200-500㎡', value: [200, 500] },
    { label: '500㎡以上', value: [500, 999999] },
  ];

  // 房源类型选项 - 添加"不限"选项
  const propertyTypes = [
    '不限',
    '写字楼',
    '商铺',
    '厂房',
    '仓库',
    '土地',
    '车位',
    '其他',
  ];

  // 特色选项 - 添加"不限"选项
  const features = [
    '不限',
    '精装修',
    '可注册',
    '地铁沿线',
    '停车位',
    '电梯',
    '空调',
    '产权清晰',
  ];

  // 位置选项 - 添加"不限"选项
  const locations = [
    '不限',
    '青秀区',
    '兴宁区',
    '江南区',
    '良庆区',
    '邕宁区',
    '西乡塘区',
    '武鸣区',
  ];

  const handlePriceRangeSelect = (range: [number, number]) => {
    setFilters(prev => ({ ...prev, priceRange: range }));
  };

  const handleAreaRangeSelect = (range: [number, number]) => {
    setFilters(prev => ({ ...prev, areaRange: range }));
  };

  const handlePropertyTypeToggle = (type: string) => {
    setFilters(prev => ({
      ...prev,
      propertyTypes: prev.propertyTypes.includes(type)
        ? prev.propertyTypes.filter(t => t !== type)
        : [...prev.propertyTypes, type],
    }));
  };

  const handleFeatureToggle = (feature: string) => {
    setFilters(prev => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter(f => f !== feature)
        : [...prev.features, feature],
    }));
  };

  const handleLocationToggle = (location: string) => {
    setFilters(prev => ({
      ...prev,
      location: prev.location.includes(location)
        ? prev.location.filter(l => l !== location)
        : [...prev.location, location],
    }));
  };

  const handleReset = () => {
    setFilters({
      priceRange: [0, 10000000],
      areaRange: [0, 1000],
      propertyTypes: [],
      features: [],
      location: [],
    });
  };

  const handleApply = () => {
    onApply(filters);
  };

  const renderFilterSection = (title: string, children: React.ReactNode) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const renderTagOptions = (
    options: string[] | { label: string; value: any }[],
    selectedValues: string[] | [number, number],
    onToggle: (value: any) => void,
    isRange = false
  ) => (
    <View style={styles.tagContainer}>
      {options.map((option, index) => {
        let isSelected = false;
        let displayText = '';
        let toggleValue: any;

        if (typeof option === 'string') {
          // 字符串选项
          displayText = option;
          toggleValue = option;
          isSelected =
            Array.isArray(selectedValues) && selectedValues.includes(option);
        } else {
          // 对象选项（价格/面积区间）
          displayText = option.label;
          toggleValue = option.value;
          if (
            isRange &&
            Array.isArray(selectedValues) &&
            selectedValues.length === 2
          ) {
            isSelected =
              JSON.stringify(selectedValues) === JSON.stringify(option.value);
          }
        }

        return (
          <TouchableOpacity
            key={index}
            style={[styles.tag, isSelected && styles.tagSelected]}
            onPress={() => onToggle(toggleValue)}
          >
            <Text
              style={[styles.tagText, isSelected && styles.tagTextSelected]}
            >
              {displayText}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity
          style={styles.overlayTouchable}
          activeOpacity={1}
          onPress={onClose}
        />
        <View style={styles.container}>
          {/* 头部 */}
          <View style={styles.header}>
            <TouchableOpacity onPress={handleReset}>
              <Text style={styles.resetText}>重置</Text>
            </TouchableOpacity>
            <Text style={styles.title}>买房筛选</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {/* 筛选内容 - ScrollView直接处理所有触摸事件 */}
          <ScrollView
            style={styles.content}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            bounces={false}
            overScrollMode="never"
            keyboardShouldPersistTaps="handled"
          >
            {/* 售价 */}
            {renderFilterSection(
              '售价',
              renderTagOptions(
                priceRanges,
                filters.priceRange,
                handlePriceRangeSelect,
                true
              )
            )}

            {/* 面积 */}
            {renderFilterSection(
              '面积',
              renderTagOptions(
                areaRanges,
                filters.areaRange,
                handleAreaRangeSelect,
                true
              )
            )}

            {/* 房源类型 */}
            {renderFilterSection(
              '房源类型',
              renderTagOptions(
                propertyTypes,
                filters.propertyTypes,
                handlePropertyTypeToggle
              )
            )}

            {/* 特色 */}
            {renderFilterSection(
              '特色',
              renderTagOptions(features, filters.features, handleFeatureToggle)
            )}

            {/* 位置 */}
            {renderFilterSection(
              '位置',
              renderTagOptions(
                locations,
                filters.location,
                handleLocationToggle
              )
            )}
          </ScrollView>

          {/* 底部按钮 */}
          <View style={styles.footer}>
            <TouchableOpacity style={styles.applyButton} onPress={handleApply}>
              <Text style={styles.applyButtonText}>确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  overlayTouchable: {
    flex: 1, // 🔧 占据上方空间，点击关闭弹窗
  },
  container: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: '80%', // 🔧 关键修复：固定高度，确保ScrollView有足够空间
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: wp(16),
    paddingVertical: hp(16),
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  resetText: {
    fontSize: fp(16),
    color: '#007AFF',
  },
  title: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#333',
  },
  content: {
    flex: 1, // 🔧 优化：改回flex: 1，配合ScrollView更好
    paddingHorizontal: wp(16),
    paddingVertical: hp(8), // 🔧 修复：添加垂直内边距
  },
  scrollContent: {
    flexGrow: 1, // 🔧 关键：确保内容至少填满ScrollView高度
    minHeight: hp(400), // 🔧 强制最小高度，确保空白区域也能滑动
    paddingBottom: hp(20), // 底部留白，避免内容贴边
  },
  section: {
    marginVertical: hp(16),
  },
  sectionTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#333',
    marginBottom: hp(12),
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    // gap: wp(8), // 🔧 修复：React Native可能不支持gap属性
  },
  tag: {
    paddingHorizontal: wp(16),
    paddingVertical: hp(8),
    backgroundColor: '#f8f8f8',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginRight: wp(8), // 🔧 修复：使用margin替代gap
    marginBottom: hp(8),
  },
  tagSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  tagText: {
    fontSize: fp(14),
    color: '#666',
  },
  tagTextSelected: {
    color: '#fff',
  },
  footer: {
    padding: wp(16),
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  applyButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: hp(14),
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#fff',
  },
});
