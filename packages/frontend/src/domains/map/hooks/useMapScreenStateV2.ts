/**
 * 地图屏幕状态管理Hook V2 - 企业级重构版本
 * 
 * 职责：
 * 1. 集成企业级MapStore状态管理
 * 2. 使用统一转换层处理数据转换
 * 3. 提供简化的组件接口
 * 4. 处理地理位置和搜索逻辑
 * 
 * 遵循项目企业级架构规范
 */

import { useEffect, useCallback } from 'react';
import { LayoutAnimation, UIManager, Platform } from 'react-native';
import { useMapStore } from '../stores/mapStore';
import { Transformers } from '../../../shared/services/dataTransform';
import FeedbackService from '../../../shared/services/FeedbackService';
import LocationService from '../../../shared/services/LocationService';

// ===== 常量定义 =====

// 南宁市中心默认位置
const DEFAULT_CENTER = {
  latitude: 22.8167,
  longitude: 108.3669,
};

// 房源类型选项（按照设计图）
const PROPERTY_TYPES = ['商铺', '写字楼', '摊位', '土地厂房', '共享办公', '活动会场'];

// 底部功能图标网格数据
const FUNCTION_ICONS = [
  { name: 'business-outline', text: '租商铺', color: '#FF9500' },
  { name: 'wallet-outline', text: '买商铺', color: '#34C759' },
  { name: 'newspaper-outline', text: '租写字楼', color: '#5856D6' },
  { name: 'cash-outline', text: '买写字楼', color: '#007AFF' },
  { name: 'map-outline', text: '地图找铺', color: '#5AC8FA' },
  { name: 'add-circle-outline', text: '上新房源', color: '#AF52DE' },
  { name: 'easel-outline', text: '摊位档口', color: '#FF2D55' },
  { name: 'layers-outline', text: '商业新盘', color: '#FF9500' },
  { name: 'bulb-outline', text: 'AI匹配', color: '#34C759' },
  { name: 'rocket-outline', text: '求租速配', color: '#5856D6' },
  { name: 'swap-horizontal-outline', text: '极速转铺', color: '#007AFF' },
  { name: 'pricetag-outline', text: '我要估价', color: '#5AC8FA' },
];

// Android动画配置
if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

// ===== Hook实现 =====

/**
 * 企业级地图屏幕状态管理Hook
 */
export const useMapScreenStateV2 = () => {
  // 从MapStore获取状态和操作
  const {
    // 状态
    center,
    zoomLevel,
    userLocation,
    markers,
    totalCount,
    search,
    ui,
    isLoading,
    error,
    
    // 操作
    setCenter,
    setUserLocation,
    updateSearchQuery,
    setSelectedPropertyType,
    performSearch,
    toggleGrid,
    setLoading,
    setError,
    clearError,
    setMarkers,
  } = useMapStore();

  // ===== 地理位置处理 =====
  
  /**
   * 获取用户当前位置
   */
  const getCurrentLocation = useCallback(async () => {
    try {
      setLoading(true);
      clearError();

      // 使用高德原生定位服务
      const locationResult = await LocationService.getCurrentLocation();
      
      if (locationResult.success && locationResult.coordinates) {
        const userCoords = {
          latitude: locationResult.coordinates.latitude,
          longitude: locationResult.coordinates.longitude,
        };

        console.log('[useMapScreenStateV2] 获取到用户位置:', userCoords);
        setUserLocation(userCoords);
        setCenter(userCoords);
      } else {
        console.log('[useMapScreenStateV2] 定位失败，使用默认位置:', locationResult.error);
        setUserLocation(DEFAULT_CENTER);
        setCenter(DEFAULT_CENTER);
        if (locationResult.error && locationResult.error !== 'using_default_location') {
          setError('获取位置失败，使用默认位置');
        }
      }

    } catch (error) {
      console.error('[useMapScreenStateV2] 获取位置失败:', error);
      setError('获取位置失败，使用默认位置');
      setUserLocation(DEFAULT_CENTER);
      setCenter(DEFAULT_CENTER);
    } finally {
      setLoading(false);
    }
  }, [setLoading, clearError, setUserLocation, setCenter, setError]);

  // ===== 搜索处理 =====
  
  /**
   * 执行房源搜索
   */
  const handleSearch = useCallback(async () => {
    try {
      setLoading(true);
      clearError();

      console.log('[useMapScreenStateV2] 🔍 开始搜索房源:', {
        query: search.query,
        propertyType: search.selectedPropertyType,
        center,
        radius: search.searchRadius,
      });

      if (!userLocation) {
        FeedbackService.showInputValidation('请先获取当前位置');
        return;
      }

      // 使用统一转换层转换搜索参数
      const searchStateData = {
        center,
        searchRadius: search.searchRadius,
        selectedPropertyType: search.selectedPropertyType,
        priceRange: search.priceRange,
        maxResults: 20,
      };

      const transformResult = Transformers.map.searchStateToAPI(searchStateData);
      
      if (!transformResult.success) {
        throw new Error(transformResult.error);
      }

      const apiParams = transformResult.data;

      // 调用地图搜索API
      const response = await fetch('http://*************:8082/api/v1/map/search-circle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiParams),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('[useMapScreenStateV2] ✅ 搜索成功:', data);

        // 使用统一转换层转换API响应
        const markersResult = Transformers.map.postgisToMarkers(data.properties || []);
        
        if (markersResult.success) {
          setMarkers(markersResult.data || []);
        } else {
          throw new Error(markersResult.error);
        }

      } else {
        console.error('[useMapScreenStateV2] ❌ 搜索失败:', response.status);
        throw new Error(`搜索请求失败: ${response.status}`);
      }

    } catch (error) {
      console.error('[useMapScreenStateV2] ❌ 搜索错误:', error);
      const errorMessage = error instanceof Error ? error.message : '搜索房源时出现错误';
      setError(errorMessage);
      FeedbackService.showError(errorMessage);
      
      // 使用模拟数据作为后备
      loadMockData();
    } finally {
      setLoading(false);
    }
  }, [
    search.query,
    search.selectedPropertyType,
    search.searchRadius,
    search.priceRange,
    center,
    userLocation,
    setLoading,
    clearError,
    setError,
    setMarkers,
  ]);

  /**
   * 加载模拟数据（后备方案）
   */
  const loadMockData = useCallback(() => {
    const mockMarkers = [
      {
        id: '1',
        title: '万象城商铺',
        coordinate: {
          latitude: 22.8167 + 0.001,
          longitude: 108.3669 + 0.001,
        },
        price: '5000元/月',
        type: '商铺',
        status: 'available' as const,
      },
      {
        id: '2',
        title: '青秀万达商铺',
        coordinate: {
          latitude: 22.8167 - 0.002,
          longitude: 108.3669 + 0.002,
        },
        price: '8000元/月',
        type: '商铺',
        status: 'available' as const,
      },
      {
        id: '3',
        title: '南宁CBD写字楼',
        coordinate: {
          latitude: 22.8167 + 0.003,
          longitude: 108.3669 - 0.001,
        },
        price: '12000元/月',
        type: '写字楼',
        status: 'available' as const,
      },
    ];

    setMarkers(mockMarkers);
  }, [setMarkers]);

  // ===== UI操作处理 =====
  
  /**
   * 切换功能网格展开状态
   */
  const handleToggleGrid = useCallback(() => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    toggleGrid();
  }, [toggleGrid]);

  /**
   * 处理搜索查询变更
   */
  const handleSearchQueryChange = useCallback((query: string) => {
    updateSearchQuery(query);
  }, [updateSearchQuery]);

  /**
   * 处理房源类型选择
   */
  const handlePropertyTypeSelect = useCallback((type: string) => {
    setSelectedPropertyType(type);
  }, [setSelectedPropertyType]);

  // ===== 初始化 =====
  
  useEffect(() => {
    // 组件挂载时获取用户位置和加载数据
    getCurrentLocation();
    loadMockData();
  }, [getCurrentLocation, loadMockData]);

  // ===== 返回接口 =====
  
  return {
    // 状态
    center,
    zoomLevel,
    userLocation,
    markers,
    totalCount,
    searchQuery: search.query,
    selectedPropertyType: search.selectedPropertyType,
    isGridExpanded: ui.isGridExpanded,
    isLoading,
    error,
    
    // 常量
    propertyTypes: PROPERTY_TYPES,
    functionIcons: FUNCTION_ICONS,
    
    // 操作
    getCurrentLocation,
    handleSearch,
    handleToggleGrid,
    handleSearchQueryChange,
    handlePropertyTypeSelect,
    clearError,
  };
};
