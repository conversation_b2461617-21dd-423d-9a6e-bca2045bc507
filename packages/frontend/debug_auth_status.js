/**
 * 认证状态调试工具
 * 用于检查当前的登录状态和token
 */

// 模拟检查认证状态
function checkAuthStatus() {
  console.log('=== 认证状态检查 ===');
  
  // 检查不同存储位置的token
  const storageKeys = [
    'userToken',
    'access_token', 
    'accessToken',
    'global-store',
    'auth-store'
  ];
  
  console.log('检查的存储键:', storageKeys);
  
  // 模拟全局状态
  const mockGlobalState = {
    session: {
      isAuthenticated: true,
      accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
      user: {
        id: '123',
        name: '测试用户',
        phone: '13800138000'
      }
    }
  };
  
  console.log('模拟全局状态:', mockGlobalState);
  
  // 检查token格式
  const token = mockGlobalState.session.accessToken;
  if (token) {
    console.log('Token长度:', token.length);
    console.log('Token前缀:', token.substring(0, 20) + '...');
    console.log('是否为JWT格式:', token.includes('.'));
  }
  
  return mockGlobalState.session.isAuthenticated;
}

// 模拟API请求
function simulateAPIRequest() {
  console.log('\n=== 模拟API请求 ===');
  
  const baseURL = 'http://*************:8081';
  const endpoint = '/api/v1/media/upload-url';
  const fullURL = baseURL + endpoint;
  
  console.log('请求URL:', fullURL);
  
  const requestData = {
    property_id: 1,
    media_type: 'VIDEO',
    file_name: 'test.mp4',
    file_size: 5242880,
  };
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  };
  
  console.log('请求头:', headers);
  console.log('请求体:', JSON.stringify(requestData, null, 2));
  
  // 模拟可能的错误响应
  const possibleErrors = [
    { status: 401, message: '认证失败' },
    { status: 403, message: '权限不足' },
    { status: 422, message: '参数验证失败' },
    { status: 500, message: '服务器内部错误' }
  ];
  
  console.log('可能的错误响应:', possibleErrors);
}

// 检查网络连接
function checkNetworkConfig() {
  console.log('\n=== 网络配置检查 ===');
  
  const configs = {
    frontend_port: 8082,
    backend_port: 8081,
    server_ip: '*************',
    api_base_url: 'http://*************:8081',
    frontend_url: 'http://*************:8082'
  };
  
  console.log('网络配置:', configs);
  
  // 检查端口是否正确
  console.log('前端应该连接到:', `${configs.server_ip}:${configs.frontend_port}`);
  console.log('API请求应该发送到:', configs.api_base_url);
}

// 运行所有检查
function runAllChecks() {
  console.log('🔍 开始全面检查...\n');
  
  const isAuthenticated = checkAuthStatus();
  console.log('认证状态:', isAuthenticated ? '✅ 已登录' : '❌ 未登录');
  
  simulateAPIRequest();
  checkNetworkConfig();
  
  console.log('\n🎯 调试建议:');
  console.log('1. 检查前端是否正确获取到token');
  console.log('2. 检查token是否有效且未过期');
  console.log('3. 检查API请求的URL和端口是否正确');
  console.log('4. 检查后端是否正常运行');
  console.log('5. 检查网络连接是否正常');
}

// 导出或直接运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    checkAuthStatus, 
    simulateAPIRequest, 
    checkNetworkConfig, 
    runAllChecks 
  };
} else {
  runAllChecks();
}
