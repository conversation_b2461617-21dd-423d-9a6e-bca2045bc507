/**
 * 通用数据转换器
 * 
 * 遵循AI编码指导规范：
 * - 继承BaseTransformer基础类
 * - 处理通用的数据转换逻辑
 * - 企业级错误处理
 * 
 * @fileoverview 处理通用的数据转换功能
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */

import { z } from 'zod';
import { BaseTransformer } from '../core/BaseTransformer';
import { 
  TransformResult, 
  TransformOptions,
  PaginationParams,
  FilterParams,
  SortParams
} from '../types/TransformTypes';

/**
 * 通用转换器
 * 负责通用数据格式的转换
 */
export class CommonTransformer extends BaseTransformer<any, any> {
  
  constructor() {
    super('CommonTransformer', '1.0.0');
  }

  /**
   * 初始化验证规则
   */
  protected initializeValidationRules(): void {
    // 分页参数验证
    this.addValidationRule('pagination', {
      name: 'pagination',
      description: '分页参数验证',
      schema: z.object({
        page: z.number().min(1),
        size: z.number().min(1).max(100),
        sort_by: z.string().optional(),
        sort_order: z.enum(['asc', 'desc']).optional()
      }),
      required: true
    });

    // 筛选参数验证
    this.addValidationRule('filters', {
      name: 'filters',
      description: '筛选参数验证',
      schema: z.record(z.any()),
      required: false
    });

    // 排序参数验证
    this.addValidationRule('sort', {
      name: 'sort',
      description: '排序参数验证',
      schema: z.object({
        field: z.string(),
        order: z.enum(['asc', 'desc'])
      }),
      required: false
    });
  }

  /**
   * 转换为API格式
   */
  toAPI(input: any, options?: TransformOptions): TransformResult<any> {
    return this.safeTransform(() => {
      if (this.isPaginationParams(input)) {
        return this.transformPaginationToAPI(input);
      }

      if (this.isFilterParams(input)) {
        return this.transformFiltersToAPI(input);
      }

      if (this.isSortParams(input)) {
        return this.transformSortToAPI(input);
      }

      // 通用对象转换
      return this.transformObjectToAPI(input);
    }, 'CommonTransformer.toAPI');
  }

  /**
   * 从API格式转换
   */
  fromAPI(input: any, options?: TransformOptions): TransformResult<any> {
    return this.safeTransform(() => {
      // 通用API响应转换
      return this.transformAPIResponseToUI(input);
    }, 'CommonTransformer.fromAPI');
  }

  // ==================== 具体转换方法 ====================

  /**
   * 分页参数转换
   */
  private transformPaginationToAPI(params: PaginationParams): PaginationParams {
    return {
      page: params.page,
      size: Math.min(params.size, 100), // 限制最大页面大小
      sort_by: params.sort_by || 'created_at',
      sort_order: params.sort_order || 'desc'
    };
  }

  /**
   * 筛选参数转换
   */
  private transformFiltersToAPI(filters: FilterParams): FilterParams {
    const apiFilters: FilterParams = {};
    
    Object.entries(filters).forEach(([key, value]) => {
      // 过滤空值和"全部"选项
      if (value !== null && value !== undefined && value !== '' && value !== '全部') {
        // 转换特殊字段名
        const apiKey = this.transformFilterKey(key);
        apiFilters[apiKey] = value;
      }
    });

    return apiFilters;
  }

  /**
   * 排序参数转换
   */
  private transformSortToAPI(sort: SortParams): { sort_by: string; sort_order: string } {
    const fieldMap: Record<string, string> = {
      '创建时间': 'created_at',
      '更新时间': 'updated_at',
      '价格': 'price',
      '面积': 'total_area',
      '热度': 'view_count'
    };

    return {
      sort_by: fieldMap[sort.field] || sort.field,
      sort_order: sort.order
    };
  }

  /**
   * 通用对象转换为API格式
   */
  private transformObjectToAPI(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.transformObjectToAPI(item));
    }

    if (typeof obj === 'object') {
      const result: any = {};
      
      Object.entries(obj).forEach(([key, value]) => {
        // 转换键名（驼峰转下划线）
        const apiKey = this.camelToSnake(key);
        result[apiKey] = this.transformObjectToAPI(value);
      });

      return result;
    }

    return obj;
  }

  /**
   * API响应转换为UI格式
   */
  private transformAPIResponseToUI(response: any): any {
    if (response === null || response === undefined) {
      return response;
    }

    if (Array.isArray(response)) {
      return response.map(item => this.transformAPIResponseToUI(item));
    }

    if (typeof response === 'object') {
      const result: any = {};
      
      Object.entries(response).forEach(([key, value]) => {
        // 转换键名（下划线转驼峰）
        const uiKey = this.snakeToCamel(key);
        result[uiKey] = this.transformAPIResponseToUI(value);
      });

      return result;
    }

    return response;
  }

  // ==================== 工具方法 ====================

  /**
   * 转换筛选字段名
   */
  private transformFilterKey(key: string): string {
    const keyMap: Record<string, string> = {
      '房源类型': 'property_type',
      '价格范围': 'price_range',
      '面积范围': 'area_range',
      '位置': 'location',
      '标签': 'tags'
    };

    return keyMap[key] || this.camelToSnake(key);
  }

  /**
   * 驼峰转下划线
   */
  private camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * 下划线转驼峰
   */
  private snakeToCamel(str: string): string {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  // ==================== 类型检查方法 ====================

  private isPaginationParams(input: any): input is PaginationParams {
    return input && typeof input === 'object' && 'page' in input && 'size' in input;
  }

  private isFilterParams(input: any): input is FilterParams {
    return input && typeof input === 'object' && !('page' in input) && !('field' in input);
  }

  private isSortParams(input: any): input is SortParams {
    return input && typeof input === 'object' && 'field' in input && 'order' in input;
  }
}

export default CommonTransformer;
