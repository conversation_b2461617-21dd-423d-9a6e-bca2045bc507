/**
 * 消息转换器工厂
 * 提供统一的转换器实例管理，符合企业级架构规范
 */
import {
  MessageQueryTransformer,
  MessageDataTransformer,
  UserRoleInfoTransformer,
  PaymentUnlockInfoTransformer,
  InteractedUserTransformer,
  SMSCreditAccountTransformer,
} from './MessageTransformer';

/**
 * 转换器工厂类
 * 单例模式，确保转换器实例的唯一性和性能优化
 */
export class MessageTransformerFactory {
  private static instance: MessageTransformerFactory;
  
  // 转换器实例缓存
  private messageQueryTransformer?: MessageQueryTransformer;
  private messageDataTransformer?: MessageDataTransformer;
  private userRoleInfoTransformer?: UserRoleInfoTransformer;
  private paymentUnlockInfoTransformer?: PaymentUnlockInfoTransformer;
  private interactedUserTransformer?: InteractedUserTransformer;
  private smsCreditAccountTransformer?: SMSCreditAccountTransformer;

  private constructor() {
    // 私有构造函数，确保单例
  }

  /**
   * 获取工厂实例
   */
  public static getInstance(): MessageTransformerFactory {
    if (!MessageTransformerFactory.instance) {
      MessageTransformerFactory.instance = new MessageTransformerFactory();
    }
    return MessageTransformerFactory.instance;
  }

  /**
   * 获取消息查询转换器
   */
  public getMessageQueryTransformer(): MessageQueryTransformer {
    if (!this.messageQueryTransformer) {
      this.messageQueryTransformer = new MessageQueryTransformer();
      console.log('[TransformerFactory] 创建MessageQueryTransformer实例');
    }
    return this.messageQueryTransformer;
  }

  /**
   * 获取消息数据转换器
   */
  public getMessageDataTransformer(): MessageDataTransformer {
    if (!this.messageDataTransformer) {
      this.messageDataTransformer = new MessageDataTransformer();
      console.log('[TransformerFactory] 创建MessageDataTransformer实例');
    }
    return this.messageDataTransformer;
  }

  /**
   * 获取用户角色信息转换器
   */
  public getUserRoleInfoTransformer(): UserRoleInfoTransformer {
    if (!this.userRoleInfoTransformer) {
      this.userRoleInfoTransformer = new UserRoleInfoTransformer();
      console.log('[TransformerFactory] 创建UserRoleInfoTransformer实例');
    }
    return this.userRoleInfoTransformer;
  }

  /**
   * 获取付费解锁信息转换器
   */
  public getPaymentUnlockInfoTransformer(): PaymentUnlockInfoTransformer {
    if (!this.paymentUnlockInfoTransformer) {
      this.paymentUnlockInfoTransformer = new PaymentUnlockInfoTransformer();
      console.log('[TransformerFactory] 创建PaymentUnlockInfoTransformer实例');
    }
    return this.paymentUnlockInfoTransformer;
  }

  /**
   * 获取互动用户信息转换器
   */
  public getInteractedUserTransformer(): InteractedUserTransformer {
    if (!this.interactedUserTransformer) {
      this.interactedUserTransformer = new InteractedUserTransformer();
      console.log('[TransformerFactory] 创建InteractedUserTransformer实例');
    }
    return this.interactedUserTransformer;
  }

  /**
   * 获取SMS积分账户转换器
   */
  public getSMSCreditAccountTransformer(): SMSCreditAccountTransformer {
    if (!this.smsCreditAccountTransformer) {
      this.smsCreditAccountTransformer = new SMSCreditAccountTransformer();
      console.log('[TransformerFactory] 创建SMSCreditAccountTransformer实例');
    }
    return this.smsCreditAccountTransformer;
  }

  /**
   * 重置所有转换器实例（用于测试或重新初始化）
   */
  public resetTransformers(): void {
    console.log('[TransformerFactory] 重置所有转换器实例');
    this.messageQueryTransformer = undefined;
    this.messageDataTransformer = undefined;
    this.userRoleInfoTransformer = undefined;
    this.paymentUnlockInfoTransformer = undefined;
    this.interactedUserTransformer = undefined;
    this.smsCreditAccountTransformer = undefined;
  }

  /**
   * 获取所有转换器的状态信息
   */
  public getTransformerStatus(): Record<string, boolean> {
    return {
      messageQueryTransformer: !!this.messageQueryTransformer,
      messageDataTransformer: !!this.messageDataTransformer,
      userRoleInfoTransformer: !!this.userRoleInfoTransformer,
      paymentUnlockInfoTransformer: !!this.paymentUnlockInfoTransformer,
      interactedUserTransformer: !!this.interactedUserTransformer,
      smsCreditAccountTransformer: !!this.smsCreditAccountTransformer,
    };
  }
}

/**
 * 便捷的转换器获取函数
 */
export const getMessageTransformers = () => {
  const factory = MessageTransformerFactory.getInstance();
  
  return {
    messageQuery: factory.getMessageQueryTransformer(),
    messageData: factory.getMessageDataTransformer(),
    userRoleInfo: factory.getUserRoleInfoTransformer(),
    paymentUnlockInfo: factory.getPaymentUnlockInfoTransformer(),
    interactedUser: factory.getInteractedUserTransformer(),
    smsCreditAccount: factory.getSMSCreditAccountTransformer(),
  };
};

/**
 * 转换器类型映射
 */
export type MessageTransformers = ReturnType<typeof getMessageTransformers>;

/**
 * 导出单例实例
 */
export const messageTransformerFactory = MessageTransformerFactory.getInstance();
