/**
 * 🔍 运行时诊断脚本
 * 帮助诊断用户报告的"页面打不开"问题
 */

const fs = require('fs');
const { exec } = require('child_process');

console.log('🔍 [Diagnosis] 开始运行时问题诊断...');

// 1. 检查Metro进程
console.log('\n📋 [Check 1] Metro进程检查...');

exec(
  'ps aux | grep -E "(metro|expo|react-native)" | grep -v grep',
  (error, stdout, stderr) => {
    if (stdout) {
      console.log('🔍 发现的相关进程:');
      stdout.split('\n').forEach(line => {
        if (line.trim()) {
          const parts = line.split(/\s+/);
          const pid = parts[1];
          const command = parts.slice(10).join(' ');
          console.log(`   PID ${pid}: ${command}`);
        }
      });
    } else {
      console.log('❌ 未发现Metro相关进程');
    }
  }
);

// 2. 检查端口占用
console.log('\n📋 [Check 2] 端口占用检查...');

const ports = [8081, 8083, 19000, 19001, 19002];
ports.forEach(port => {
  exec(`lsof -i :${port}`, (error, stdout, stderr) => {
    if (stdout) {
      console.log(`🔍 端口 ${port} 被占用:`);
      console.log(stdout);
    }
  });
});

// 3. 检查关键文件完整性
console.log('\n📋 [Check 3] 关键文件完整性...');

const criticalFiles = [
  '/data/my-real-estate-app/packages/frontend/package.json',
  '/data/my-real-estate-app/packages/frontend/metro.config.js',
  '/data/my-real-estate-app/packages/frontend/index.js',
  '/data/my-real-estate-app/packages/frontend/App.tsx',
];

criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}: 存在`);
  } else {
    console.log(`❌ ${file}: 缺失`);
  }
});

// 4. 检查PropertyNavigationScreen导入链
console.log('\n📋 [Check 4] PropertyNavigationScreen导入链...');

const navigationFiles = [
  '/data/my-real-estate-app/packages/frontend/src/screens/Property/PropertyNavigationScreen.tsx',
  '/data/my-real-estate-app/packages/frontend/src/domains/property/components/detail/PropertyNavigationMapRefactored.tsx',
  '/data/my-real-estate-app/packages/frontend/src/domains/property/components/detail/hooks/usePropertyNavigation.ts',
  '/data/my-real-estate-app/packages/frontend/src/domains/property/components/detail/stores/MapNavigationStore.ts',
];

navigationFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}: 存在`);

    // 检查语法错误
    const content = fs.readFileSync(file, 'utf8');
    const hasUnclosedBraces =
      (content.match(/\{/g) || []).length !==
      (content.match(/\}/g) || []).length;
    const hasUnclosedParens =
      (content.match(/\(/g) || []).length !==
      (content.match(/\)/g) || []).length;

    if (hasUnclosedBraces) {
      console.log(`   ⚠️ ${file}: 括号不匹配`);
    }
    if (hasUnclosedParens) {
      console.log(`   ⚠️ ${file}: 圆括号不匹配`);
    }
  } else {
    console.log(`❌ ${file}: 缺失`);
  }
});

// 5. 生成诊断报告
setTimeout(() => {
  console.log('\n📊 [DiagnosisReport] 诊断建议:');
  console.log('\n🔧 [用户操作建议]:');
  console.log('1. 🛑 停止所有Metro进程: killall -9 node');
  console.log('2. 🧹 清除缓存: npx expo start --clear');
  console.log('3. 📱 重新启动应用');
  console.log('4. 📋 提供具体错误日志');

  console.log('\n📋 [开发者检查清单]:');
  console.log('✅ LocationData重复声明: 已修复');
  console.log('✅ Android bundling: 可以成功');
  console.log('✅ PropertyNavigationScreen: 编译通过');
  console.log('⚠️ 运行时状态: 需要用户确认');

  console.log('\n🔚 [Diagnosis] 运行时问题诊断完成');
}, 3000);
