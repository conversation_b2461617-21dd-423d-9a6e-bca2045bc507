/**
 * 地图页面运行时测试脚本
 * 在React Native环境中直接运行
 * 
 * 使用方法：
 * 1. 将此脚本复制到MapSearchScreen组件中
 * 2. 在开发环境中运行
 * 3. 观察控制台输出
 */

// 运行时测试 - 直接集成到MapSearchScreen组件中

import React, { useEffect, useRef } from 'react';
import { View, Text, Button } from 'react-native';

// 测试组件 - 可以临时替换MapSearchScreen进行测试
export const MapScreenTest = () => {
  const renderCount = useRef(0);
  const effectCount = useRef(0);
  const stateUpdateCount = useRef(0);
  const lastRenderTime = useRef(Date.now());
  
  // 记录渲染次数
  renderCount.current++;
  console.log(`🔄 MapScreenTest 第${renderCount.current}次渲染`);
  
  // 记录渲染间隔
  const now = Date.now();
  const interval = now - lastRenderTime.current;
  console.log(`⏱️ 渲染间隔: ${interval}ms`);
  lastRenderTime.current = now;
  
  // 测试useEffect执行
  useEffect(() => {
    effectCount.current++;
    console.log(`⚡ useEffect 第${effectCount.current}次执行`);
    
    // 模拟定位
    const timer = setTimeout(() => {
      console.log('📍 模拟定位完成');
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []); // 空依赖数组测试
  
  // 测试状态更新
  const [testState, setTestState] = React.useState(0);
  
  const handleStateUpdate = () => {
    stateUpdateCount.current++;
    console.log(`🔄 状态更新第${stateUpdateCount.current}次`);
    setTestState(prev => prev + 1);
  };
  
  // 检查循环警告
  const checkForWarnings = () => {
    const originalWarn = console.warn;
    let warnings = [];
    
    console.warn = (...args) => {
      if (args[0] && args[0].includes('Maximum update depth')) {
        console.error('❌ 发现无限循环警告:', args);
        warnings.push(args);
      }
      originalWarn(...args);
    };
    
    // 5秒后恢复
    setTimeout(() => {
      console.warn = originalWarn;
      if (warnings.length === 0) {
        console.log('✅ 未发现无限循环警告');
      }
    }, 5000);
  };
  
  // 启动警告检查
  useEffect(() => {
    checkForWarnings();
  }, []);
  
  // 性能监控
  const performanceMonitor = () => {
    if (renderCount.current > 10) {
      console.warn('⚠️ 渲染次数过多，可能存在循环');
    }
    
    if (effectCount.current > 5) {
      console.warn('⚠️ useEffect执行次数过多');
    }
    
    console.log('📊 性能监控:', {
      渲染次数: renderCount.current,
      useEffect执行: effectCount.current,
      状态更新: stateUpdateCount.current
    });
  };
  
  useEffect(() => {
    performanceMonitor();
  }, [testState]);
  
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text>地图页面循环测试</Text>
      <Text>渲染次数: {renderCount.current}</Text>
      <Text>useEffect执行: {effectCount.current}</Text>
      <Text>状态更新: {stateUpdateCount.current}</Text>
      
      <Button 
        title="测试状态更新" 
        onPress={handleStateUpdate}
      />
      
      <Button 
        title="强制重渲染" 
        onPress={() => setTestState(prev => prev + 1)}
      />
      
      <Button 
        title="检查循环警告" 
        onPress={checkForWarnings}
      />
    </View>
  );
};

// 集成测试 - 添加到现有MapSearchScreen中
export const MapScreenIntegrationTest = () => {
  const debugInfo = useRef({
    renderCount: 0,
    lastRenderTime: Date.now(),
    useEffectCalls: 0,
    stateUpdates: 0
  });
  
  // 在现有MapSearchScreen中添加这些调试信息
  const addDebugLogging = () => {
    debugInfo.current.renderCount++;
    const now = Date.now();
    const interval = now - debugInfo.current.lastRenderTime;
    
    console.log(`🗺️ MapSearchScreen 渲染 #${debugInfo.current.renderCount}`);
    console.log(`⏱️ 渲染间隔: ${interval}ms`);
    console.log(`📊 状态:`, debugInfo.current);
    
    debugInfo.current.lastRenderTime = now;
  };
  
  // 在useEffect中添加调试
  const addEffectLogging = (effectName) => {
    debugInfo.current.useEffectCalls++;
    console.log(`⚡ ${effectName} useEffect 执行 #${debugInfo.current.useEffectCalls}`);
  };
  
  // 在状态更新中添加调试
  const addStateLogging = (stateName) => {
    debugInfo.current.stateUpdates++;
    console.log(`🔄 ${stateName} 状态更新 #${debugInfo.current.stateUpdates}`);
  };
  
  return {
    debugInfo: debugInfo.current,
    addDebugLogging,
    addEffectLogging,
    addStateLogging
  };
};

// 快速验证函数
export const quickVerification = () => {
  console.log('🔍 开始快速验证...');
  
  // 检查关键修复点
  const checks = [
    {
      name: 'useEffect依赖数组',
      check: () => {
        // 检查useMapScreenState中的useEffect
        return true; // 假设已修复
      }
    },
    {
      name: 'Zustand选择器',
      check: () => {
        // 检查useCurrentCityData和useSetCurrentCity
        return true; // 假设已修复
      }
    },
    {
      name: '防抖机制',
      check: () => {
        // 检查LOCATION_DEBOUNCE_MS
        return true; // 假设已修复
      }
    },
    {
      name: '异步更新',
      check: () => {
        // 检查setTimeout使用
        return true; // 假设已修复
      }
    }
  ];
  
  checks.forEach(check => {
    const result = check.check();
    console.log(`${result ? '✅' : '❌'} ${check.name}: ${result ? '通过' : '失败'}`);
  });
  
  console.log('🎯 快速验证完成');
};

// 实际使用步骤
console.log('📋 使用步骤：');
console.log('1. 将MapScreenTest组件临时替换MapSearchScreen');
console.log('2. 运行应用并打开地图页面');
console.log('3. 观察控制台输出');
console.log('4. 点击测试按钮');
console.log('5. 检查是否有循环警告');
console.log('6. 验证修复效果');

// 测试命令
console.log('\n🚀 测试命令：');
console.log('// 在React Native调试器中运行：');
console.log('quickVerification();');
console.log('// 或直接在组件中使用：');
console.log('const { debugInfo } = MapScreenIntegrationTest();');

export default {
  MapScreenTest,
  MapScreenIntegrationTest,
  quickVerification
};
