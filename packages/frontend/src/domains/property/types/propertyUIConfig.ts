/**
 * 房源UI配置类型定义
 * 用于动态配置房源详情页面的显示内容和布局
 */

export interface PropertyUIConfig {
  // 关键指标配置
  关键指标配置: {
    显示租金: boolean;
    显示转让费: boolean;
    显示面积: boolean;
    显示停车位: boolean;
    显示承重: boolean;
  };

  // 布局配置
  布局方式: '水平排列' | '垂直排列' | '网格布局';
  重点突出: '租金' | '面积' | '转让费' | '停车位' | '承重' | null;

  // 数据内容
  租金数据?: {
    价格: string | number;
    单位: string;
  };

  转让费数据?: {
    价格: string | number;
  };

  面积数据?: {
    大小: string | number;
    单位: string;
  };

  停车位数据?: {
    数量: number;
  };

  承重数据?: {
    承重: string | number;
    单位: string;
  };

  // 其他配置
  显示规则?: {
    显示租金: boolean;
    显示转让费: boolean;
    显示面积: boolean;
    显示停车位: boolean;
    显示承重: boolean;
  };

  数据内容?: {
    租金?: { 价格: string; 单位: string };
    转让费?: { 价格: string };
    面积?: { 大小: string; 单位: string };
    停车位?: { 数量: number };
    承重?: { 承重: string; 单位: string };
  };
}

// 默认配置
export const defaultPropertyUIConfig: PropertyUIConfig = {
  关键指标配置: {
    显示租金: true,
    显示转让费: false,
    显示面积: true,
    显示停车位: false,
    显示承重: false,
  },
  布局方式: '水平排列',
  重点突出: '租金',
};

// 类型守卫函数
export const isPropertyUIConfig = (config: any): config is PropertyUIConfig => {
  return (
    config &&
    typeof config === 'object' &&
    config.关键指标配置 &&
    typeof config.关键指标配置 === 'object'
  );
};
