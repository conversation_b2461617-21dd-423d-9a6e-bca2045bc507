/**
 * 媒体服务共享类型定义
 * 
 * 用于避免循环依赖，提供统一的类型定义
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月17日
 */

// STS凭证接口
export interface STSCredentials {
  access_key_id: string;
  access_key_secret: string;
  security_token: string;
  expiration: string;
  expires_in: number;
  bucket: string;
  region: string;
  endpoint: string;
  upload_path?: string;
  object_key?: string;
}

// 上传进度接口
export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  speed?: number;
  timeRemaining?: number;
  remainingTime?: number;
}

// 上传结果接口
export interface UploadResult {
  success: boolean;
  objectKey?: string;
  url?: string;
  key?: string;
  size?: number;
  etag?: string;
  error?: string;
  uploadTime?: number;
}

// 删除结果接口
export interface DeleteResult {
  success: boolean;
  objectKey?: string;
  key?: string;
  error?: string;
}

// OSS上传选项
export interface OSSUploadOptions {
  onProgress?: (progress: UploadProgress) => void;
  timeout?: number;
  retries?: number;
  chunkSize?: number;
  partSize?: number;
  parallel?: number;
  checkpoints?: any;
  headers?: Record<string, string>;
  meta?: Record<string, string>;
  folder?: string;
  propertyId?: string;
}

// 媒体类型枚举
export enum MediaType {
  IMAGE = 'image',
  VIDEO = 'video',
  DOCUMENT = 'document'
}

// 安全级别枚举
export enum SecurityLevel {
  NORMAL = 'normal',
  SENSITIVE = 'sensitive'
}

// 访问级别枚举
export enum AccessLevel {
  PUBLIC = 'public',
  PRIVATE = 'private'
}

// 媒体分类接口
export interface MediaCategory {
  securityLevel: SecurityLevel;
  accessLevel: AccessLevel;
  mediaType: MediaType;
  description?: string;
}

// 文件信息接口
export interface FileInfo {
  name: string;
  size: number;
  type: string;
  uri?: string;
  data?: any;
  lastModified?: number;
}

// 上传任务状态
export enum UploadTaskStatus {
  PENDING = 'pending',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 上传任务接口
export interface UploadTask {
  id: string;
  file: FileInfo;
  category: MediaCategory;
  status: UploadTaskStatus;
  progress: UploadProgress;
  result?: UploadResult;
  error?: string;
  retryCount: number;
  createdAt: Date;
  updatedAt: Date;
}
