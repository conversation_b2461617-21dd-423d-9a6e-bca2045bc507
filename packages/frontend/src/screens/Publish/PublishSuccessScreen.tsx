/**
 * 房源发布成功页面
 * 
 * 设计理念：
 * 1. 成功状态确认 - 清晰的成功反馈
 * 2. 用户引导 - 明确的下一步行动
 * 3. 功能推荐 - 增加用户粘性
 * 4. 视觉层次 - 突出重要信息
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Animated,
  Dimensions,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { wp, hp, fp } from '../../shared/utils/responsiveUtils';
import FeedbackService from '../../shared/services/FeedbackService';

const { width: screenWidth } = Dimensions.get('window');

interface RouteParams {
  publishedData: {
    title: string;
    propertyType: string;
    tags: string[];
    area: string;
    [key: string]: any;
  };
}

const PublishSuccessScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const insets = useSafeAreaInsets();
  const { publishedData } = route.params as RouteParams;

  // 状态管理
  const [isNavigatingToDetail, setIsNavigatingToDetail] = useState(false);

  // 动画值
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.8);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    // 触觉反馈
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

    // 动画序列
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 100,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // 导航处理函数 - 增强错误处理和用户体验
  const handleViewProperty = async () => {
    // 数据验证：确保发布数据完整性
    if (!publishedData) {
      FeedbackService.showError('房源数据获取失败，请重试');
      console.error('[PublishSuccess] 发布数据为空');
      return;
    }

    // 获取房源ID，优先使用API返回的真实ID
    const propertyId = publishedData.id || publishedData.propertyId;
    
    if (!propertyId) {
      FeedbackService.showError('房源ID获取失败，请稍后重试或联系客服');
      console.error('[PublishSuccess] 无法获取有效的房源ID:', publishedData);
      return;
    }

    try {
      setIsNavigatingToDetail(true);
      
      // 触觉反馈
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      console.log('[PublishSuccess] 跳转到房源详情，房源ID:', propertyId);
      console.log('[PublishSuccess] 发布数据:', publishedData);
      
      // 添加source参数，标识来源于发布成功页面
      navigation.navigate('PropertyDetail', {
        propertyId: propertyId,
        publishedData: publishedData,
        source: 'published',
      });
      
    } catch (error) {
      console.error('[PublishSuccess] 跳转失败:', error);
      FeedbackService.showError('跳转失败，请重试');
    } finally {
      // 延迟重置loading状态，避免快速闪烁
      setTimeout(() => {
        setIsNavigatingToDetail(false);
      }, 1000);
    }
  };

  const handleBackToHome = () => {
    (navigation as any).navigate('MainTabs');
  };

  const handleGoToMyProperties = () => {
    (navigation as any).navigate('Profile');
  };

  const handlePublishAnother = () => {
    navigation.navigate('PublishOptions');
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      <ScrollView 
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        {/* 成功状态区域 */}
        <Animated.View 
          style={[
            styles.successSection,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          {/* 成功图标 */}
          <View style={styles.successIconContainer}>
            <View style={styles.successIconBackground}>
              <Ionicons name="checkmark" size={wp(40)} color="#FFFFFF" />
            </View>
          </View>

          {/* 成功标题 */}
          <Text style={styles.successTitle}>🎉 发布成功！</Text>
          
          {/* 成功描述 */}
          <Text style={styles.successDescription}>
            您的房源已成功发布到平台，我们将帮助您推送给合适的客户
          </Text>

          {/* 房源简要信息 */}
          <View style={styles.propertyInfoCard}>
            <Text style={styles.propertyTitle} numberOfLines={1}>
              {publishedData.title || '房源信息'}
            </Text>
            <View style={styles.propertyMeta}>
              <Text style={styles.propertyType}>{publishedData.propertyType || '商业房源'}</Text>
              <Text style={styles.propertySeparator}>•</Text>
              <Text style={styles.propertyArea}>{publishedData.area || '--'}㎡</Text>
            </View>
            {publishedData.tags && publishedData.tags.length > 0 && (
              <View style={styles.tagsContainer}>
                {publishedData.tags.slice(0, 3).map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
                {publishedData.tags.length > 3 && (
                  <Text style={styles.moreTagsText}>+{publishedData.tags.length - 3}</Text>
                )}
              </View>
            )}
          </View>
        </Animated.View>

        {/* 主要操作区域 */}
        <Animated.View 
          style={[
            styles.actionsSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* 主要操作按钮 */}
          <TouchableOpacity 
            style={[styles.primaryButton, isNavigatingToDetail && styles.primaryButtonDisabled]}
            onPress={handleViewProperty}
            activeOpacity={0.8}
            disabled={isNavigatingToDetail}
          >
            {isNavigatingToDetail ? (
              <>
                <ActivityIndicator size="small" color="#FFFFFF" />
                <Text style={styles.primaryButtonText}>跳转中...</Text>
              </>
            ) : (
              <>
                <Ionicons name="eye-outline" size={wp(20)} color="#FFFFFF" />
                <Text style={styles.primaryButtonText}>查看房源详情</Text>
              </>
            )}
          </TouchableOpacity>

          {/* 次要操作按钮 */}
          <View style={styles.secondaryButtonsContainer}>
            <TouchableOpacity 
              style={styles.secondaryButton}
              onPress={handleGoToMyProperties}
              activeOpacity={0.7}
            >
              <Ionicons name="list-outline" size={wp(18)} color="#FF6B35" />
              <Text style={styles.secondaryButtonText}>我的房源</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.secondaryButton}
              onPress={handlePublishAnother}
              activeOpacity={0.7}
            >
              <Ionicons name="add-outline" size={wp(18)} color="#FF6B35" />
              <Text style={styles.secondaryButtonText}>再发一套</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* 功能推荐区域 */}
        <Animated.View 
          style={[
            styles.recommendationsSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Text style={styles.recommendationsTitle}>推荐功能</Text>
          
          <View style={styles.recommendationCards}>
            {/* 智能推广 */}
            <TouchableOpacity style={styles.recommendationCard} activeOpacity={0.7}>
              <View style={styles.recommendationIcon}>
                <Ionicons name="megaphone-outline" size={wp(24)} color="#007AFF" />
              </View>
              <Text style={styles.recommendationTitle}>智能推广</Text>
              <Text style={styles.recommendationDesc}>提升房源曝光度</Text>
            </TouchableOpacity>

            {/* 房源美化 */}
            <TouchableOpacity style={styles.recommendationCard} activeOpacity={0.7}>
              <View style={styles.recommendationIcon}>
                <Ionicons name="camera-outline" size={wp(24)} color="#34C759" />
              </View>
              <Text style={styles.recommendationTitle}>专业拍摄</Text>
              <Text style={styles.recommendationDesc}>提升房源品质</Text>
            </TouchableOpacity>

            {/* 市场分析 */}
            <TouchableOpacity style={styles.recommendationCard} activeOpacity={0.7}>
              <View style={styles.recommendationIcon}>
                <Ionicons name="analytics-outline" size={wp(24)} color="#FF9500" />
              </View>
              <Text style={styles.recommendationTitle}>市场分析</Text>
              <Text style={styles.recommendationDesc}>了解房源价值</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* 底部提示 */}
        <Animated.View 
          style={[
            styles.tipsSection,
            {
              opacity: fadeAnim,
            },
          ]}
        >
          <View style={styles.tipContainer}>
            <Ionicons name="bulb-outline" size={wp(16)} color="#8E8E93" />
            <Text style={styles.tipText}>
              房源将在24小时内完成审核，审核通过后开始推广
            </Text>
          </View>
        </Animated.View>
      </ScrollView>

      {/* 底部返回首页按钮 */}
      <Animated.View 
        style={[
          styles.bottomBar,
          {
            opacity: fadeAnim,
            paddingBottom: insets.bottom || 20,
          },
        ]}
      >
        <TouchableOpacity 
          style={styles.backToHomeButton}
          onPress={handleBackToHome}
          activeOpacity={0.8}
        >
          <Ionicons name="home-outline" size={wp(20)} color="#8E8E93" />
          <Text style={styles.backToHomeText}>返回首页</Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: wp(20),
    paddingBottom: hp(100),
  },
  
  // 成功状态区域
  successSection: {
    alignItems: 'center',
    paddingVertical: hp(40),
  },
  successIconContainer: {
    marginBottom: hp(24),
  },
  successIconBackground: {
    width: wp(80),
    height: wp(80),
    borderRadius: wp(40),
    backgroundColor: '#34C759',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#34C759',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  successTitle: {
    fontSize: fp(28),
    fontWeight: 'bold',
    color: '#1D1D1F',
    marginBottom: hp(8),
    textAlign: 'center',
  },
  successDescription: {
    fontSize: fp(16),
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: fp(22),
    marginBottom: hp(32),
    paddingHorizontal: wp(20),
  },

  // 房源信息卡片
  propertyInfoCard: {
    backgroundColor: '#F8F9FA',
    borderRadius: wp(16),
    padding: wp(20),
    width: '100%',
    borderWidth: 1,
    borderColor: '#E5E5E7',
  },
  propertyTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: hp(8),
  },
  propertyMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(12),
  },
  propertyType: {
    fontSize: fp(14),
    color: '#FF6B35',
    fontWeight: '500',
  },
  propertySeparator: {
    fontSize: fp(14),
    color: '#C7C7CC',
    marginHorizontal: wp(8),
  },
  propertyArea: {
    fontSize: fp(14),
    color: '#8E8E93',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  tag: {
    backgroundColor: '#E8F4FD',
    borderRadius: wp(12),
    paddingHorizontal: wp(12),
    paddingVertical: wp(4),
    marginRight: wp(8),
    marginBottom: wp(4),
  },
  tagText: {
    fontSize: fp(12),
    color: '#007AFF',
    fontWeight: '500',
  },
  moreTagsText: {
    fontSize: fp(12),
    color: '#8E8E93',
    fontWeight: '500',
  },

  // 操作区域
  actionsSection: {
    marginBottom: hp(40),
  },
  primaryButton: {
    backgroundColor: '#FF6B35',
    borderRadius: wp(16),
    paddingVertical: hp(16),
    paddingHorizontal: wp(24),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: hp(16),
    shadowColor: '#FF6B35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  primaryButtonDisabled: {
    backgroundColor: '#FF6B35AA',
    shadowOpacity: 0.1,
    elevation: 2,
  },
  primaryButtonText: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: wp(8),
  },
  secondaryButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  secondaryButton: {
    backgroundColor: '#F8F9FA',
    borderRadius: wp(12),
    paddingVertical: hp(14),
    paddingHorizontal: wp(20),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 0.48,
    borderWidth: 1,
    borderColor: '#E5E5E7',
  },
  secondaryButtonText: {
    fontSize: fp(14),
    fontWeight: '500',
    color: '#FF6B35',
    marginLeft: wp(6),
  },

  // 推荐功能区域
  recommendationsSection: {
    marginBottom: hp(32),
  },
  recommendationsTitle: {
    fontSize: fp(20),
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: hp(16),
  },
  recommendationCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  recommendationCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: wp(12),
    padding: wp(16),
    alignItems: 'center',
    flex: 0.31,
    borderWidth: 1,
    borderColor: '#E5E5E7',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  recommendationIcon: {
    width: wp(48),
    height: wp(48),
    borderRadius: wp(24),
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(8),
  },
  recommendationTitle: {
    fontSize: fp(14),
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: hp(4),
    textAlign: 'center',
  },
  recommendationDesc: {
    fontSize: fp(12),
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: fp(16),
  },

  // 提示区域
  tipsSection: {
    marginBottom: hp(20),
  },
  tipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: wp(8),
    padding: wp(12),
  },
  tipText: {
    fontSize: fp(13),
    color: '#8E8E93',
    marginLeft: wp(8),
    flex: 1,
    lineHeight: fp(18),
  },

  // 底部操作栏
  bottomBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E5E7',
    paddingTop: hp(16),
    paddingHorizontal: wp(20),
  },
  backToHomeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(12),
  },
  backToHomeText: {
    fontSize: fp(14),
    color: '#8E8E93',
    marginLeft: wp(6),
  },
});

export default PublishSuccessScreen;