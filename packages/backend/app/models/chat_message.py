"""
WebSocket聊天消息模型

基于企业级设计的实时聊天系统：
1. 支持多种消息类型（文本、语音、图片、联系方式）
2. 完整的消息状态管理
3. 防绕过机制集成
4. 付费验证机制
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from sqlmodel import SQLModel, Field, Relationship
from sqlalchemy import Column, DateTime, text, JSON
import uuid
from uuid import UUID
from app.core.timezone_utils import now_utc


class MessageType(str, Enum):
    """消息类型枚举"""
    TEXT = "text"                    # 文本消息
    VOICE = "voice"                  # 语音消息
    IMAGE = "image"                  # 图片消息
    CONTACT_REQUEST = "contact_request"  # 联系方式请求
    CONTACT_SHARE = "contact_share"  # 联系方式分享
    SYSTEM = "system"                # 系统消息
    PAYMENT_PROMPT = "payment_prompt"  # 付费提示


class MessageStatus(str, Enum):
    """消息状态枚举"""
    SENDING = "sending"              # 发送中
    SENT = "sent"                    # 已发送
    DELIVERED = "delivered"          # 已送达
    READ = "read"                    # 已读
    FAILED = "failed"                # 发送失败
    BLOCKED = "blocked"              # 被拦截（包含联系方式）


class ChatMessage(SQLModel, table=True):
    """
    聊天消息表
    
    核心功能：
    - 实时聊天消息存储
    - 消息状态追踪
    - 防绕过机制支持
    - 付费验证集成
    """
    __tablename__ = "chat_messages"
    __table_args__ = {"comment": "聊天消息表"}
    
    # 主键
    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        primary_key=True,
        sa_column_kwargs={"comment": "消息唯一标识"}
    )
    
    # 关联咨询记录
    inquiry_id: str = Field(
        foreign_key="property_inquiries.id",
        index=True,
        sa_column_kwargs={"comment": "关联的咨询记录ID"}
    )
    
    # 发送者和接收者
    sender_id: UUID = Field(
        foreign_key="users.id",
        index=True,
        sa_column_kwargs={"comment": "发送者用户ID"}
    )
    
    receiver_id: UUID = Field(
        foreign_key="users.id",
        index=True,
        sa_column_kwargs={"comment": "接收者用户ID"}
    )
    
    # 消息内容
    message_type: MessageType = Field(
        sa_column_kwargs={"comment": "消息类型"}
    )
    
    content: str = Field(
        max_length=5000,
        sa_column_kwargs={"comment": "消息内容"}
    )
    
    # 媒体文件信息
    media_url: Optional[str] = Field(
        default=None,
        max_length=500,
        sa_column_kwargs={"comment": "媒体文件URL"}
    )
    
    media_duration: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "媒体时长（秒）"}
    )
    
    media_size: Optional[int] = Field(
        default=None,
        sa_column_kwargs={"comment": "媒体文件大小（字节）"}
    )
    
    # 消息状态
    status: MessageStatus = Field(
        default=MessageStatus.SENDING,
        sa_column_kwargs={"comment": "消息状态"}
    )
    
    # 联系方式相关
    contains_contact_info: bool = Field(
        default=False,
        sa_column_kwargs={"comment": "是否包含联系方式"}
    )
    
    contact_info_detected: Optional[Dict[str, Any]] = Field(
        default=None,
        sa_column=Column(JSON, comment="检测到的联系方式信息")
    )
    
    # 付费相关
    requires_payment_to_view: bool = Field(
        default=False,
        sa_column_kwargs={"comment": "是否需要付费才能查看"}
    )
    
    payment_unlocked_by: Optional[UUID] = Field(
        default=None,
        foreign_key="users.id",
        sa_column_kwargs={"comment": "付费解锁的用户ID"}
    )
    
    payment_unlocked_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), comment="付费解锁时间")
    )
    
    # 时间戳
    created_at: datetime = Field(
        default_factory=now_utc,
        sa_column=Column(DateTime(timezone=True), server_default=text("now()"), comment="消息记录创建时间")
    )
    
    sent_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), comment="发送时间")
    )
    
    delivered_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), comment="送达时间")
    )
    
    read_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), comment="已读时间")
    )
    
    # 扩展信息
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        sa_column=Column(JSON, comment="扩展元数据")
    )
    
    # 消息回复
    reply_to_message_id: Optional[str] = Field(
        default=None,
        sa_column_kwargs={"comment": "回复的消息ID"}
    )
    
    # 消息编辑
    edited_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), comment="编辑时间")
    )
    
    original_content: Optional[str] = Field(
        default=None,
        max_length=5000,
        sa_column_kwargs={"comment": "原始内容（编辑前）"}
    )


class ChatRoom(SQLModel, table=True):
    """
    聊天房间表
    
    基于咨询记录的聊天房间管理
    """
    __tablename__ = "chat_rooms"
    __table_args__ = {"comment": "聊天房间表"}
    
    # 主键
    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        primary_key=True,
        sa_column_kwargs={"comment": "房间唯一标识"}
    )
    
    # 关联咨询记录（一对一关系）
    inquiry_id: str = Field(
        foreign_key="property_inquiries.id",
        unique=True,
        index=True,
        sa_column_kwargs={"comment": "关联的咨询记录ID"}
    )
    
    # 参与者
    landlord_id: UUID = Field(
        foreign_key="users.id",
        index=True,
        sa_column_kwargs={"comment": "业主用户ID"}
    )
    
    tenant_id: UUID = Field(
        foreign_key="users.id",
        index=True,
        sa_column_kwargs={"comment": "租客用户ID"}
    )
    
    # 房间状态
    is_active: bool = Field(
        default=True,
        sa_column_kwargs={"comment": "房间是否活跃"}
    )
    
    # 最后活动
    last_message_id: Optional[str] = Field(
        default=None,
        sa_column_kwargs={"comment": "最后一条消息ID"}
    )
    
    last_activity_at: datetime = Field(
        default_factory=now_utc,
        sa_column=Column(DateTime(timezone=True), comment="最后活动时间")
    )
    
    # 未读消息统计
    landlord_unread_count: int = Field(
        default=0,
        sa_column_kwargs={"comment": "业主未读消息数"}
    )
    
    tenant_unread_count: int = Field(
        default=0,
        sa_column_kwargs={"comment": "租客未读消息数"}
    )
    
    # 时间戳
    created_at: datetime = Field(
        default_factory=now_utc,
        sa_column=Column(DateTime(timezone=True), server_default=text("now()"), comment="聊天室创建时间")
    )
    
    updated_at: datetime = Field(
        default_factory=now_utc,
        sa_column=Column(DateTime(timezone=True), server_default=text("now()"), onupdate=text("now()"), comment="聊天室最后更新时间")
    )


class ChatConnection(SQLModel, table=True):
    """
    WebSocket连接管理表
    
    用于管理活跃的WebSocket连接
    """
    __tablename__ = "chat_connections"
    __table_args__ = {"comment": "WebSocket连接管理表"}
    
    # 主键
    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        primary_key=True,
        sa_column_kwargs={"comment": "连接记录唯一ID"}
    )
    
    # 用户和房间
    user_id: UUID = Field(
        foreign_key="users.id",
        index=True,
        sa_column_kwargs={"comment": "用户ID"}
    )
    
    room_id: str = Field(
        foreign_key="chat_rooms.id",
        index=True,
        sa_column_kwargs={"comment": "聊天房间ID"}
    )
    
    # 连接信息
    connection_id: str = Field(
        unique=True,
        index=True,
        sa_column_kwargs={"comment": "WebSocket连接ID"}
    )
    
    is_online: bool = Field(
        default=True,
        sa_column_kwargs={"comment": "是否在线"}
    )
    
    # 时间戳
    connected_at: datetime = Field(
        default_factory=now_utc,
        sa_column=Column(DateTime(timezone=True), comment="连接建立时间")
    )
    
    last_ping_at: datetime = Field(
        default_factory=now_utc,
        sa_column=Column(DateTime(timezone=True), comment="最后心跳时间")
    )
