#!/usr/bin/env node

/**
 * 恢复功能验证脚本
 * 验证所有之前开发的功能是否正确恢复
 */

const fs = require('fs');

console.log('🔄 恢复功能验证脚本');
console.log('==================');

// 1. 验证筛选弹窗滑动修复
const checkFilterModalFix = () => {
  console.log('\n📋 验证筛选弹窗滑动修复...');
  
  const rentModalFile = 'packages/frontend/src/domains/map/components/FilterModal/RentFilterModal.tsx';
  const saleModalFile = 'packages/frontend/src/domains/map/components/FilterModal/SaleFilterModal.tsx';
  
  let allFixed = true;
  
  [rentModalFile, saleModalFile].forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否使用了正确的结构
      const hasViewOverlay = content.includes('View style={styles.overlay}');
      const hasOverlayTouchable = content.includes('overlayTouchable') && content.includes('onPress={onClose}');
      const hasScrollView = content.includes('<ScrollView');
      const noTouchableWrapper = !content.includes('TouchableOpacity') || 
                                !content.includes('TouchableOpacity onPress={(e) => e.stopPropagation()}');
      
      console.log(`  ${filePath.split('/').pop()}:`);
      console.log(`    View + overlayTouchable结构: ${hasViewOverlay && hasOverlayTouchable ? '✅' : '❌'}`);
      console.log(`    ScrollView直接处理触摸: ${hasScrollView ? '✅' : '❌'}`);
      console.log(`    避免TouchableOpacity包装: ${noTouchableWrapper ? '✅' : '❌'}`);
      
      if (!hasViewOverlay || !hasOverlayTouchable || !hasScrollView || !noTouchableWrapper) {
        allFixed = false;
      }
    } else {
      console.log(`    ❌ 文件不存在: ${filePath}`);
      allFixed = false;
    }
  });
  
  return allFixed;
};

// 2. 验证房源类型选择器
const checkPropertyTypeSelector = () => {
  console.log('\n🏷️ 验证房源类型选择器...');
  
  const selectorFile = 'packages/frontend/src/domains/map/components/PropertyTypeSelector/PropertyTypeSelector.tsx';
  const mapScreenFile = 'packages/frontend/src/domains/map/screens/MapSearchScreen.tsx';
  
  let selectorExists = false;
  let integratedInMapScreen = false;
  
  // 检查组件是否存在
  if (fs.existsSync(selectorFile)) {
    const content = fs.readFileSync(selectorFile, 'utf8');
    selectorExists = content.includes('PropertyTypeSelector') && 
                    content.includes('selectedType') && 
                    content.includes('onTypeChange');
    console.log(`  PropertyTypeSelector组件: ${selectorExists ? '✅' : '❌'}`);
  } else {
    console.log(`  PropertyTypeSelector组件: ❌ 文件不存在`);
  }
  
  // 检查是否集成到MapScreen
  if (fs.existsSync(mapScreenFile)) {
    const content = fs.readFileSync(mapScreenFile, 'utf8');
    integratedInMapScreen = content.includes('PropertyTypeSelector') && 
                           content.includes('selectedPropertyType') && 
                           content.includes('handlePropertyTypeChange');
    console.log(`  集成到MapSearchScreen: ${integratedInMapScreen ? '✅' : '❌'}`);
  } else {
    console.log(`  集成到MapSearchScreen: ❌ MapSearchScreen文件不存在`);
  }
  
  return selectorExists && integratedInMapScreen;
};

// 3. 验证筛选弹窗集成
const checkFilterModalIntegration = () => {
  console.log('\n🔍 验证筛选弹窗集成...');
  
  const mapScreenFile = 'packages/frontend/src/domains/map/screens/MapSearchScreen.tsx';
  const hookFile = 'packages/frontend/src/domains/map/hooks/useMapScreenState.ts';
  
  let mapScreenIntegration = false;
  let hookStateManagement = false;
  
  // 检查MapScreen集成
  if (fs.existsSync(mapScreenFile)) {
    const content = fs.readFileSync(mapScreenFile, 'utf8');
    mapScreenIntegration = content.includes('RentFilterModal') && 
                          content.includes('SaleFilterModal') && 
                          content.includes('filterModalProps');
    console.log(`  MapSearchScreen集成: ${mapScreenIntegration ? '✅' : '❌'}`);
  }
  
  // 检查Hook状态管理
  if (fs.existsSync(hookFile)) {
    const content = fs.readFileSync(hookFile, 'utf8');
    hookStateManagement = content.includes('filterModalProps') && 
                         content.includes('rentModalVisible') && 
                         content.includes('saleModalVisible');
    console.log(`  useMapScreenState状态管理: ${hookStateManagement ? '✅' : '❌'}`);
  }
  
  return mapScreenIntegration && hookStateManagement;
};

// 4. 验证定位防重复机制
const checkLocationDeduplication = () => {
  console.log('\n📍 验证定位防重复机制...');
  
  const locationServiceFile = 'packages/frontend/src/shared/services/LocationService.ts';
  
  if (fs.existsSync(locationServiceFile)) {
    const content = fs.readFileSync(locationServiceFile, 'utf8');
    
    const hasDeduplicationFields = content.includes('lastProcessedCoordinates') && 
                                  content.includes('COORDINATE_THRESHOLD') && 
                                  content.includes('ACCURACY_THRESHOLD');
    
    const hasDeduplicationLogic = content.includes('坐标变化微小，跳过重复处理') && 
                                 content.includes('latDiff < this.COORDINATE_THRESHOLD');
    
    console.log(`  防重复字段定义: ${hasDeduplicationFields ? '✅' : '❌'}`);
    console.log(`  防重复逻辑实现: ${hasDeduplicationLogic ? '✅' : '❌'}`);
    
    return hasDeduplicationFields && hasDeduplicationLogic;
  } else {
    console.log(`  ❌ LocationService文件不存在`);
    return false;
  }
};

// 5. 验证组件生命周期监控
const checkLifecycleMonitoring = () => {
  console.log('\n🔄 验证组件生命周期监控...');
  
  const mapScreenFile = 'packages/frontend/src/domains/map/screens/MapSearchScreen.tsx';
  const mapContainerFile = 'packages/frontend/src/shared/components/MapContainer.tsx';
  
  let mapScreenMonitoring = false;
  let mapContainerMonitoring = false;
  
  // 检查MapSearchScreen监控
  if (fs.existsSync(mapScreenFile)) {
    const content = fs.readFileSync(mapScreenFile, 'utf8');
    mapScreenMonitoring = content.includes('组件挂载 - 开始初始化') && 
                         content.includes('组件卸载 - 开始清理');
    console.log(`  MapSearchScreen生命周期监控: ${mapScreenMonitoring ? '✅' : '❌'}`);
  }
  
  // 检查MapContainer监控
  if (fs.existsSync(mapContainerFile)) {
    const content = fs.readFileSync(mapContainerFile, 'utf8');
    mapContainerMonitoring = content.includes('组件挂载，开始初始化') && 
                            content.includes('组件卸载，清理所有异步操作');
    console.log(`  MapContainer生命周期监控: ${mapContainerMonitoring ? '✅' : '❌'}`);
  }
  
  return mapScreenMonitoring && mapContainerMonitoring;
};

// 6. 生成完整验证报告
const generateRestorationReport = () => {
  console.log('\n📊 功能恢复验证报告:');
  console.log('======================');
  
  const filterModalFixed = checkFilterModalFix();
  const propertyTypeSelectorOk = checkPropertyTypeSelector();
  const filterModalIntegrated = checkFilterModalIntegration();
  const locationDeduplicationOk = checkLocationDeduplication();
  const lifecycleMonitoringOk = checkLifecycleMonitoring();
  
  console.log('\n🎯 恢复结果汇总:');
  console.log(`✅ 筛选弹窗滑动修复: ${filterModalFixed ? '通过' : '失败'}`);
  console.log(`✅ 房源类型选择器: ${propertyTypeSelectorOk ? '通过' : '失败'}`);
  console.log(`✅ 筛选弹窗集成: ${filterModalIntegrated ? '通过' : '失败'}`);
  console.log(`✅ 定位防重复机制: ${locationDeduplicationOk ? '通过' : '失败'}`);
  console.log(`✅ 生命周期监控: ${lifecycleMonitoringOk ? '通过' : '失败'}`);
  
  const allRestored = filterModalFixed && propertyTypeSelectorOk && 
                     filterModalIntegrated && locationDeduplicationOk && 
                     lifecycleMonitoringOk;
  
  if (allRestored) {
    console.log('\n🎉 所有功能恢复成功！');
    console.log('💡 现在可以测试完整的功能体验');
    
    console.log('\n📋 测试建议:');
    console.log('1. 测试筛选弹窗滑动：在空白区域应该能流畅滑动');
    console.log('2. 测试房源类型选择：顶部应该显示房源类型选择器');
    console.log('3. 测试导航切换：观察生命周期日志，确认组件正确卸载');
    console.log('4. 测试定位功能：相同坐标不应该重复处理');
    console.log('5. 测试筛选功能：点击筛选按钮应该显示弹窗');
  } else {
    console.log('\n⚠️  部分功能恢复失败');
    console.log('需要手动检查和修复失败的功能');
  }
  
  console.log('\n✅ 验证完成！');
};

// 执行验证
generateRestorationReport();
