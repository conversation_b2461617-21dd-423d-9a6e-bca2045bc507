/**
 * 房源API服务
 * 
 * 负责与后端房源管理API的通信
 * 包含房源创建、查询、更新、删除等功能
 */

import apiClient from '../../../shared/services/client';
import { PropertyCreateRequest } from '../../../shared/services/dataTransform/types/TransformTypes';

// API响应类型
export interface PropertyResponse {
  id: number;
  title: string;
  property_type: string;
  sub_type?: string;
  address?: string;
  total_area: number;
  floor?: number;
  total_floors?: number;
  orientation?: string;
  decoration_level?: string;
  description?: string;
  transaction_types: string[];
  rent_price?: number;
  sale_price?: number;
  transfer_price?: number;
  tags: string[];
  ai_tags: string[];
  status: string;
  verification_status: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
}

export interface PropertyListResponse {
  items: PropertyResponse[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

/**
 * 房源API服务类
 */
export class PropertyAPI {
  
  /**
   * 创建房源
   */
  static async createProperty(propertyData: PropertyCreateRequest): Promise<ApiResponse<PropertyResponse>> {
    try {
      console.log('[PropertyAPI] 创建房源请求:', propertyData);
      
      const response = await apiClient.post('/properties', propertyData);
      
      console.log('[PropertyAPI] 创建房源响应:', response.data);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error('[PropertyAPI] 创建房源失败:', error);
      
      return {
        success: false,
        message: error.response?.data?.detail || '创建房源失败',
        errors: error.response?.data?.errors || []
      };
    }
  }
  
  /**
   * 获取房源详情
   */
  static async getPropertyById(propertyId: string): Promise<ApiResponse<PropertyResponse>> {
    try {
      console.log('[PropertyAPI] 获取房源详情:', propertyId);
      
      const response = await apiClient.get(`/properties/${propertyId}`);
      
      console.log('[PropertyAPI] 房源详情响应:', response.data);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error('[PropertyAPI] 获取房源详情失败:', error);
      
      return {
        success: false,
        message: error.response?.data?.detail || '获取房源详情失败'
      };
    }
  }
  
  /**
   * 搜索房源列表
   */
  static async searchProperties(params: {
    property_type?: string;
    sub_type?: string;
    min_area?: number;
    max_area?: number;
    keyword?: string;
    page?: number;
    size?: number;
    sort_by?: string;
    sort_order?: string;
  } = {}): Promise<ApiResponse<PropertyListResponse>> {
    try {
      console.log('[PropertyAPI] 搜索房源:', params);
      
      const response = await apiClient.get('/properties', { params });
      
      console.log('[PropertyAPI] 搜索房源响应:', response.data);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error('[PropertyAPI] 搜索房源失败:', error);
      
      return {
        success: false,
        message: error.response?.data?.detail || '搜索房源失败'
      };
    }
  }
  
  /**
   * 更新房源信息
   */
  static async updateProperty(
    propertyId: string, 
    propertyData: Partial<PropertyCreateRequest>
  ): Promise<ApiResponse<PropertyResponse>> {
    try {
      console.log('[PropertyAPI] 更新房源:', propertyId, propertyData);
      
      const response = await apiClient.put(`/properties/${propertyId}`, propertyData);
      
      console.log('[PropertyAPI] 更新房源响应:', response.data);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error('[PropertyAPI] 更新房源失败:', error);
      
      return {
        success: false,
        message: error.response?.data?.detail || '更新房源失败',
        errors: error.response?.data?.errors || []
      };
    }
  }
  
  /**
   * 删除房源
   */
  static async deleteProperty(propertyId: string): Promise<ApiResponse<boolean>> {
    try {
      console.log('[PropertyAPI] 删除房源:', propertyId);
      
      await apiClient.delete(`/properties/${propertyId}`);
      
      console.log('[PropertyAPI] 删除房源成功');
      
      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      console.error('[PropertyAPI] 删除房源失败:', error);
      
      return {
        success: false,
        message: error.response?.data?.detail || '删除房源失败'
      };
    }
  }
  
  /**
   * 获取用户的房源列表
   */
  static async getUserProperties(params: {
    page?: number;
    size?: number;
    status?: string;
  } = {}): Promise<ApiResponse<PropertyListResponse>> {
    try {
      console.log('[PropertyAPI] 获取用户房源列表:', params);
      
      const response = await apiClient.get('/properties/my', { params });
      
      console.log('[PropertyAPI] 用户房源列表响应:', response.data);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error('[PropertyAPI] 获取用户房源列表失败:', error);
      
      return {
        success: false,
        message: error.response?.data?.detail || '获取房源列表失败'
      };
    }
  }
  
  /**
   * 更新房源状态
   */
  static async updatePropertyStatus(
    propertyId: string, 
    status: string
  ): Promise<ApiResponse<PropertyResponse>> {
    try {
      console.log('[PropertyAPI] 更新房源状态:', propertyId, status);
      
      const response = await apiClient.patch(`/properties/${propertyId}/status`, { status });
      
      console.log('[PropertyAPI] 更新状态响应:', response.data);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error('[PropertyAPI] 更新房源状态失败:', error);
      
      return {
        success: false,
        message: error.response?.data?.detail || '更新房源状态失败'
      };
    }
  }
  
  /**
   * 房源审核
   */
  static async verifyProperty(
    propertyId: string, 
    verification_status: string,
    remarks?: string
  ): Promise<ApiResponse<PropertyResponse>> {
    try {
      console.log('[PropertyAPI] 房源审核:', propertyId, verification_status);
      
      const response = await apiClient.patch(`/properties/${propertyId}/verify`, {
        verification_status,
        remarks
      });
      
      console.log('[PropertyAPI] 审核响应:', response.data);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error('[PropertyAPI] 房源审核失败:', error);
      
      return {
        success: false,
        message: error.response?.data?.detail || '房源审核失败'
      };
    }
  }



  /**
   * 获取我的房源列表
   */
  static async getMyProperties(params: {
    status?: string;
    page?: number;
    size?: number;
  } = {}): Promise<ApiResponse<PropertyListResponse>> {
    try {
      console.log('[PropertyAPI] 获取我的房源:', params);

      // 🔧 修复：使用正确的API端点 /properties/user/my
      const response = await apiClient.get('/properties/user/my', {
        params: {
          skip: ((params.page || 1) - 1) * (params.size || 20),
          limit: params.size || 20,
          status: params.status
        }
      });

      console.log('[PropertyAPI] 获取我的房源响应:', response.data);

      // 🔧 修复：适配后端返回的PaginatedPropertyResponse格式
      const adaptedData = {
        items: response.data.items || [],
        total: response.data.total || 0,
        page: params.page || 1,
        size: params.size || 20,
        pages: Math.ceil((response.data.total || 0) / (params.size || 20))
      };

      return {
        success: true,
        data: adaptedData
      };
    } catch (error: any) {
      console.error('[PropertyAPI] 获取我的房源失败:', error);

      return {
        success: false,
        message: error.response?.data?.detail || '获取我的房源失败'
      };
    }
  }
}

export default PropertyAPI;
