/**
 * 🚀 导航控制组件
 * 企业级组件拆分 - 从PropertyNavigationMap中提取
 * 职责：起点终点选择、位置切换、开始导航
 */

import React, { useCallback } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';

// ==================== 类型定义 ====================

export interface LocationData {
  latitude: number;
  longitude: number;
  address: string;
}

export interface NavigationControlsProps {
  // 位置状态
  startLocationText: string;
  endLocationText: string;
  currentStartLocation: LocationData;
  currentEndLocation: LocationData;
  isLocationSwapped: boolean;

  // 路线状态
  hasValidRoute: boolean;
  canNavigate: boolean;

  // 事件处理
  onAddressSearch: (type: 'start' | 'end') => void;
  onLocationSwap: () => void;
  onStartNavigation: () => void;
}

// ==================== 组件实现 ====================

export const NavigationControls: React.FC<NavigationControlsProps> = ({
  startLocationText,
  endLocationText,
  currentStartLocation,
  currentEndLocation,
  isLocationSwapped,
  hasValidRoute,
  canNavigate,
  onAddressSearch,
  onLocationSwap,
  onStartNavigation,
}) => {
  // ==================== 事件处理 ====================

  const handleStartAddressPress = useCallback(() => {
    console.log('🔍 [NavigationControls] 打开起点地址搜索');
    onAddressSearch('start');
  }, [onAddressSearch]);

  const handleEndAddressPress = useCallback(() => {
    console.log('🔍 [NavigationControls] 打开终点地址搜索');
    onAddressSearch('end');
  }, [onAddressSearch]);

  const handleLocationSwapPress = useCallback(() => {
    console.log('🔄 [NavigationControls] 切换起点终点');
    onLocationSwap();
  }, [onLocationSwap]);

  const handleNavigationPress = useCallback(() => {
    if (!canNavigate) {
      Alert.alert('提示', '请先计算有效路线');
      return;
    }

    console.log('🧭 [NavigationControls] 开始导航');

    Alert.alert('开始导航', `从"${startLocationText}"到"${endLocationText}"`, [
      { text: '取消', style: 'cancel' },
      {
        text: '确定',
        onPress: () => {
          onStartNavigation();
        },
      },
    ]);
  }, [canNavigate, startLocationText, endLocationText, onStartNavigation]);

  // ==================== 渲染方法 ====================

  const renderLocationButton = useCallback(
    (type: 'start' | 'end', text: string, onPress: () => void) => {
      const isStart = type === 'start';
      const icon = isStart ? '📍' : '🎯';
      const placeholder = isStart ? '选择起点' : '选择终点';
      const displayText = text || placeholder;

      return (
        <TouchableOpacity
          style={[
            styles.locationButton,
            isStart ? styles.startLocationButton : styles.endLocationButton,
          ]}
          onPress={onPress}
          activeOpacity={0.7}
        >
          <View style={styles.locationButtonContent}>
            <Text style={styles.locationIcon}>{icon}</Text>
            <View style={styles.locationTextContainer}>
              <Text style={styles.locationLabel}>
                {isStart ? '起点' : '终点'}
              </Text>
              <Text
                style={[styles.locationText, !text && styles.placeholderText]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {displayText}
              </Text>
            </View>
            <Text style={styles.locationArrow}>{'>'}</Text>
          </View>
        </TouchableOpacity>
      );
    },
    []
  );

  const renderSwapButton = useCallback(() => {
    return (
      <TouchableOpacity
        style={styles.swapButton}
        onPress={handleLocationSwapPress}
        activeOpacity={0.7}
      >
        <Text style={styles.swapIcon}>⇅</Text>
      </TouchableOpacity>
    );
  }, [handleLocationSwapPress]);

  const renderNavigationButton = useCallback(() => {
    const isDisabled = !canNavigate;

    return (
      <TouchableOpacity
        style={[
          styles.navigationButton,
          isDisabled && styles.disabledNavigationButton,
        ]}
        onPress={handleNavigationPress}
        disabled={isDisabled}
        activeOpacity={0.8}
      >
        <Text
          style={[
            styles.navigationButtonIcon,
            isDisabled && styles.disabledNavigationButtonIcon,
          ]}
        >
          🧭
        </Text>
        <Text
          style={[
            styles.navigationButtonText,
            isDisabled && styles.disabledNavigationButtonText,
          ]}
        >
          {hasValidRoute ? '开始导航' : '计算路线中...'}
        </Text>
      </TouchableOpacity>
    );
  }, [canNavigate, hasValidRoute, handleNavigationPress]);

  const renderLocationInfo = useCallback(() => {
    if (!hasValidRoute) return null;

    return (
      <View style={styles.locationInfoContainer}>
        <View style={styles.locationInfoRow}>
          <Text style={styles.locationInfoLabel}>起点坐标:</Text>
          <Text style={styles.locationInfoValue}>
            {currentStartLocation.latitude.toFixed(6)},{' '}
            {currentStartLocation.longitude.toFixed(6)}
          </Text>
        </View>
        <View style={styles.locationInfoRow}>
          <Text style={styles.locationInfoLabel}>终点坐标:</Text>
          <Text style={styles.locationInfoValue}>
            {currentEndLocation.latitude.toFixed(6)},{' '}
            {currentEndLocation.longitude.toFixed(6)}
          </Text>
        </View>
        {isLocationSwapped && (
          <View style={styles.swapIndicator}>
            <Text style={styles.swapIndicatorText}>🔄 已切换起点终点</Text>
          </View>
        )}
      </View>
    );
  }, [
    hasValidRoute,
    currentStartLocation,
    currentEndLocation,
    isLocationSwapped,
  ]);

  // ==================== 主渲染 ====================

  return (
    <View style={styles.container}>
      {/* 起点终点选择区域 */}
      <View style={styles.locationSelectionContainer}>
        {/* 起点按钮 */}
        {renderLocationButton(
          'start',
          startLocationText,
          handleStartAddressPress
        )}

        {/* 切换按钮 */}
        {renderSwapButton()}

        {/* 终点按钮 */}
        {renderLocationButton('end', endLocationText, handleEndAddressPress)}
      </View>

      {/* 位置信息显示 */}
      {renderLocationInfo()}

      {/* 开始导航按钮 */}
      <View style={styles.navigationButtonContainer}>
        {renderNavigationButton()}
      </View>
    </View>
  );
};

// ==================== 样式定义 ====================

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },

  // 位置选择区域
  locationSelectionContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  locationButton: {
    borderRadius: 8,
    marginVertical: 4,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: '#f8f8f8',
  },
  startLocationButton: {
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  endLocationButton: {
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  locationButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  locationIcon: {
    fontSize: 18,
    marginRight: 12,
  },
  locationTextContainer: {
    flex: 1,
  },
  locationLabel: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 2,
  },
  locationText: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
  },
  placeholderText: {
    color: '#999999',
    fontStyle: 'italic',
  },
  locationArrow: {
    fontSize: 16,
    color: '#cccccc',
    marginLeft: 8,
  },

  // 切换按钮
  swapButton: {
    position: 'absolute',
    right: 24,
    top: '50%',
    transform: [{ translateY: -16 }],
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  swapIcon: {
    fontSize: 16,
    color: '#ffffff',
    fontWeight: 'bold',
  },

  // 位置信息
  locationInfoContainer: {
    backgroundColor: '#f5f5f5',
    marginHorizontal: 16,
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
  },
  locationInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 2,
  },
  locationInfoLabel: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  locationInfoValue: {
    fontSize: 12,
    color: '#333333',
    fontFamily: 'monospace',
  },
  swapIndicator: {
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  swapIndicatorText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
  },

  // 导航按钮
  navigationButtonContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  navigationButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  disabledNavigationButton: {
    backgroundColor: '#cccccc',
  },
  navigationButtonIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  disabledNavigationButtonIcon: {
    opacity: 0.5,
  },
  navigationButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledNavigationButtonText: {
    color: '#999999',
  },
});

export default NavigationControls;
