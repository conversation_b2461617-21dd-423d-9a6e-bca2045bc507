/**
 * 转换器注册中心
 * 
 * 遵循AI编码指导规范：
 * - 企业级单例模式
 * - 完整的错误处理
 * - 类型安全设计
 * 
 * @fileoverview 管理所有数据转换器的注册和获取
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */

import { ITransformer, TransformResult, TransformOptions } from '../types/TransformTypes';

/**
 * 转换器注册信息
 */
interface TransformerRegistration {
  transformer: ITransformer;
  registeredAt: number;
  version: string;
  description?: string;
}

/**
 * 转换器注册中心
 * 负责管理所有转换器的注册、获取和生命周期
 */
export class TransformRegistry {
  private transformers: Map<string, TransformerRegistration> = new Map();
  private readonly version = '1.0.0';

  /**
   * 注册转换器
   */
  register(name: string, transformer: ITransformer, description?: string): void {
    try {
      // 验证转换器
      const healthCheck = transformer.healthCheck();
      if (!healthCheck.healthy) {
        throw new Error(`转换器健康检查失败: ${healthCheck.issues.join(', ')}`);
      }

      // 注册转换器
      this.transformers.set(name, {
        transformer,
        registeredAt: Date.now(),
        version: transformer.getInfo().version,
        description
      });

      console.log(`[TransformRegistry] 转换器注册成功: ${name} v${transformer.getInfo().version}`);
    } catch (error) {
      console.error(`[TransformRegistry] 转换器注册失败: ${name}`, error);
      throw error;
    }
  }

  /**
   * 获取转换器
   */
  get<T extends ITransformer>(name: string): T {
    const registration = this.transformers.get(name);
    if (!registration) {
      throw new Error(`转换器未找到: ${name}`);
    }

    return registration.transformer as T;
  }

  /**
   * 检查转换器是否存在
   */
  has(name: string): boolean {
    return this.transformers.has(name);
  }

  /**
   * 获取所有已注册的转换器名称
   */
  getRegisteredNames(): string[] {
    return Array.from(this.transformers.keys());
  }

  /**
   * 获取转换器信息
   */
  getTransformerInfo(name: string): TransformerRegistration | undefined {
    return this.transformers.get(name);
  }

  /**
   * 获取所有转换器信息
   */
  getAllTransformersInfo(): Record<string, TransformerRegistration> {
    const result: Record<string, TransformerRegistration> = {};
    this.transformers.forEach((registration, name) => {
      result[name] = registration;
    });
    return result;
  }

  /**
   * 注销转换器
   */
  unregister(name: string): boolean {
    const existed = this.transformers.has(name);
    this.transformers.delete(name);
    
    if (existed) {
      console.log(`[TransformRegistry] 转换器注销成功: ${name}`);
    }
    
    return existed;
  }

  /**
   * 清空所有转换器
   */
  clear(): void {
    const count = this.transformers.size;
    this.transformers.clear();
    console.log(`[TransformRegistry] 清空所有转换器，共 ${count} 个`);
  }

  /**
   * 执行所有转换器的健康检查
   */
  healthCheckAll(): Record<string, { healthy: boolean; issues: string[] }> {
    const results: Record<string, { healthy: boolean; issues: string[] }> = {};
    
    this.transformers.forEach((registration, name) => {
      try {
        results[name] = registration.transformer.healthCheck();
      } catch (error) {
        results[name] = {
          healthy: false,
          issues: [`健康检查执行失败: ${error}`]
        };
      }
    });

    return results;
  }

  /**
   * 获取注册中心统计信息
   */
  getStats(): {
    totalTransformers: number;
    healthyTransformers: number;
    registryVersion: string;
    oldestRegistration: number | null;
    newestRegistration: number | null;
  } {
    const registrations = Array.from(this.transformers.values());
    const healthResults = this.healthCheckAll();
    
    const healthyCount = Object.values(healthResults).filter(result => result.healthy).length;
    
    const registrationTimes = registrations.map(reg => reg.registeredAt);
    
    return {
      totalTransformers: this.transformers.size,
      healthyTransformers: healthyCount,
      registryVersion: this.version,
      oldestRegistration: registrationTimes.length > 0 ? Math.min(...registrationTimes) : null,
      newestRegistration: registrationTimes.length > 0 ? Math.max(...registrationTimes) : null
    };
  }
}

/**
 * 转换器工厂函数
 * 快速创建简单的转换器
 */
export function createTransformer<TInput, TOutput>(
  name: string,
  config: {
    version?: string;
    toAPI: (input: TInput, options?: TransformOptions) => TransformResult<TOutput>;
    fromAPI: (input: TOutput, options?: TransformOptions) => TransformResult<TInput>;
    validationRules?: string[];
    description?: string;
  }
): ITransformer<TInput, TOutput> {
  return {
    toAPI: config.toAPI,
    fromAPI: config.fromAPI,
    getInfo: () => ({
      name,
      version: config.version || '1.0.0',
      validationRules: config.validationRules || []
    }),
    healthCheck: () => ({
      healthy: true,
      issues: []
    })
  };
}

/**
 * 注册转换器的便捷函数
 */
export function registerTransformer(
  registry: TransformRegistry,
  name: string,
  transformer: ITransformer,
  description?: string
): void {
  registry.register(name, transformer, description);
}

export default TransformRegistry;
