/**
 * 🏗️ 企业级架构：通用搜索Hook
 * 遵循前端企业级架构规范 - Hook层
 * 参考主流APP搜索模式：微信、支付宝、淘宝
 */

import { useState, useCallback, useRef, useMemo } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UseUniversalSearchProps, TabStatus } from '../types';

const RECENT_SEARCHES_KEY = 'universal_search_recent';
const MAX_RECENT_SEARCHES = 10;

export const useUniversalSearch = ({
  type,
  searchConfig,
  dataFetcher,
  onSearchResults,
}: UseUniversalSearchProps) => {
  // 🏗️ 企业级架构：搜索状态管理
  const [searchText, setSearchText] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [hotSearches] = useState<string[]>([
    // 根据type提供不同的热门搜索
    ...(type === 'property' ? ['三室一厅', '地铁房', '学区房', '精装修'] : ['求租', '求购', '整租', '合租'])
  ]);

  // 搜索防抖引用
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // 🔧 企业级方法：加载历史搜索记录
  const loadRecentSearches = useCallback(async () => {
    try {
      const key = `${RECENT_SEARCHES_KEY}_${type}`;
      const stored = await AsyncStorage.getItem(key);
      if (stored) {
        const parsed = JSON.parse(stored);
        setRecentSearches(Array.isArray(parsed) ? parsed : []);
      }
    } catch (error) {
      console.warn('[useUniversalSearch] 加载历史搜索失败:', error);
    }
  }, [type]);

  // 🔧 企业级方法：保存搜索记录
  const saveSearchRecord = useCallback(async (text: string) => {
    if (!text.trim()) return;

    try {
      const key = `${RECENT_SEARCHES_KEY}_${type}`;
      const newRecentSearches = [
        text,
        ...recentSearches.filter(item => item !== text)
      ].slice(0, MAX_RECENT_SEARCHES);

      await AsyncStorage.setItem(key, JSON.stringify(newRecentSearches));
      setRecentSearches(newRecentSearches);
    } catch (error) {
      console.warn('[useUniversalSearch] 保存搜索记录失败:', error);
    }
  }, [type, recentSearches]);

  // 🔧 企业级方法：清空搜索记录
  const clearRecentSearches = useCallback(async () => {
    try {
      const key = `${RECENT_SEARCHES_KEY}_${type}`;
      await AsyncStorage.removeItem(key);
      setRecentSearches([]);
    } catch (error) {
      console.warn('[useUniversalSearch] 清空搜索记录失败:', error);
    }
  }, [type]);

  // 🔧 企业级方法：执行搜索（支持防抖）
  const performSearch = useCallback(async (
    text: string,
    activeTab: TabStatus,
    immediate = false
  ) => {
    console.log(`[useUniversalSearch] 📝 performSearch调用:`, {
      text: `"${text}"`,
      activeTab,
      immediate,
      currentSearchText: `"${searchText}"`
    });

    // 🔧 企业级修复：立即更新搜索文本状态
    if (text !== searchText) {
      setSearchText(text);
    }

    // 清除之前的防抖定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    const executeSearch = async () => {
      console.log(`[useUniversalSearch] 🚀 执行搜索:`, {
        text: `"${text}"`,
        activeTab,
        immediate,
        currentSearchText: `"${searchText}"`
      });

      if (!text.trim()) {
        console.log(`[useUniversalSearch] ⚠️ 搜索文本为空，清空结果`);
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      try {
        setIsSearching(true);
        console.log(`[useUniversalSearch] ${type} 搜索: "${text}" in ${activeTab}`);

        // 🔧 企业级架构：使用前端过滤确保稳定性
        const allData = await dataFetcher(activeTab);

        const filtered = allData.filter(item => {
          // 使用自定义过滤函数
          if (searchConfig.filterFn) {
            return searchConfig.filterFn(item, text);
          }

          // 默认过滤逻辑：在指定字段中搜索
          return searchConfig.searchFields.some(field => {
            const fieldValue = item[field];
            if (typeof fieldValue === 'string') {
              return fieldValue.toLowerCase().includes(text.toLowerCase());
            }
            if (typeof fieldValue === 'number') {
              return fieldValue.toString().includes(text);
            }
            return false;
          });
        });

        setSearchResults(filtered);
        onSearchResults?.(filtered, text);

        // 🔧 企业级搜索：无论有无结果都保存搜索记录
        await saveSearchRecord(text);

        console.log(`[useUniversalSearch] ${type} 搜索完成: 找到 ${filtered.length} 条记录`);

      } catch (error) {
        console.warn(`[useUniversalSearch] ${type} 搜索执行异常，返回空结果:`, error);
        // 🔧 企业级错误处理：搜索异常不报错，返回空结果
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    if (immediate) {
      executeSearch();
    } else {
      // 防抖搜索（300ms）
      searchTimeoutRef.current = setTimeout(executeSearch, 300);
    }
  }, [type, searchConfig, dataFetcher, onSearchResults, saveSearchRecord]);

  // 🔧 企业级方法：处理搜索文本变化
  const handleSearchTextChange = useCallback((text: string, activeTab: TabStatus) => {
    setSearchText(text);
    performSearch(text, activeTab);
  }, [performSearch]);

  // 🔧 企业级方法：处理搜索提交
  const handleSearchSubmit = useCallback((text: string, activeTab: TabStatus) => {
    console.log(`[useUniversalSearch] 🔍 搜索提交:`, {
      text: `"${text}"`,
      activeTab,
      currentSearchText: `"${searchText}"`
    });

    // 🔧 企业级修复：立即更新搜索文本状态
    setSearchText(text);
    performSearch(text, activeTab, true);
  }, [performSearch, searchText]);

  // 🔧 企业级方法：选择历史搜索
  const selectRecentSearch = useCallback((text: string, activeTab: TabStatus) => {
    setSearchText(text);
    performSearch(text, activeTab, true);
  }, [performSearch]);

  // 🔧 企业级方法：清空搜索
  const clearSearch = useCallback(() => {
    setSearchText('');
    setSearchResults([]);
    setIsSearching(false);
    
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
  }, []);

  // 🎯 计算属性：搜索状态
  const searchState = useMemo(() => {
    const hasSearchText = searchText.trim().length > 0;
    const hasResults = searchResults.length > 0;
    const isEmpty = hasSearchText && searchResults.length === 0 && !isSearching;
    const isActive = hasSearchText || isSearching;

    console.log(`[useUniversalSearch] 搜索状态计算:`, {
      searchText: `"${searchText}"`,
      hasSearchText,
      hasResults,
      isEmpty,
      isActive,
      isSearching,
      resultsLength: searchResults.length
    });

    return {
      hasSearchText,
      hasResults,
      isEmpty,
      isActive,
    };
  }, [searchText, searchResults, isSearching]);

  return {
    // 状态
    searchText,
    isSearching,
    searchResults,
    recentSearches,
    hotSearches,
    searchState,

    // 方法
    handleSearchTextChange,
    handleSearchSubmit,
    selectRecentSearch,
    clearSearch,
    loadRecentSearches,
    clearRecentSearches,

    // 工具方法
    performSearch,
    saveSearchRecord,
  };
};
