#!/usr/bin/env python3
"""
API调试脚本 - 修复版本
"""

import sys
import traceback
import asyncio
from fastapi.testclient import TestClient

sys.path.append('/app')

def test_app_creation():
    """测试应用创建"""
    try:
        print("🔍 测试应用创建...")
        from app.main import app
        print("✅ 应用创建成功")
        return app
    except Exception as e:
        print(f"❌ 应用创建失败: {e}")
        traceback.print_exc()
        return None

def test_basic_endpoints(app):
    """测试基础端点"""
    if not app:
        return False
    
    try:
        print("\n🔍 测试基础端点...")
        client = TestClient(app)
        
        # 测试健康检查
        print("测试健康检查...")
        response = client.get("/health")
        print(f"健康检查状态: {response.status_code}")
        if response.status_code == 200:
            print(f"健康检查响应: {response.json()}")
        else:
            print(f"健康检查失败: {response.text}")
        
        # 测试房源列表API
        print("\n测试房源列表API...")
        response = client.get("/api/v1/properties/")
        print(f"房源列表状态: {response.status_code}")
        if response.status_code != 200:
            print(f"房源列表错误: {response.text}")
            # 打印详细错误信息
            print("详细错误信息:")
            try:
                error_detail = response.json()
                print(f"错误详情: {error_detail}")
            except:
                print(f"原始错误文本: {response.text}")
            return False
        else:
            print("✅ 房源列表API正常")
            data = response.json()
            print(f"返回数据: {data}")
            return True
            
    except Exception as e:
        print(f"❌ 端点测试失败: {e}")
        traceback.print_exc()
        return False

async def test_database_connection():
    """测试数据库连接"""
    try:
        print("\n🔍 测试数据库连接...")
        from app.core.db import get_async_session
        from app.models.property.property import Property
        from sqlmodel import select
        
        # 正确使用异步生成器
        async_session_gen = get_async_session()
        session = await async_session_gen.__anext__()
        
        try:
            print("✅ 数据库连接成功")
            
            # 测试简单查询
            result = await session.exec(select(Property).limit(1))
            properties = result.all()
            print(f"✅ 查询到 {len(properties)} 个房源")
            return True
        finally:
            await session.close()
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主调试函数"""
    print("🚀 开始API调试...")
    
    # 1. 测试应用创建
    app = test_app_creation()
    
    # 2. 测试数据库连接
    db_ok = asyncio.run(test_database_connection())
    
    # 3. 测试基础端点
    if app:
        api_ok = test_basic_endpoints(app)
        if api_ok and db_ok:
            print("\n🎉 所有测试通过！")
        else:
            print(f"\n❌ 测试失败 - API: {api_ok}, DB: {db_ok}")
    else:
        print("\n❌ 应用创建失败")

if __name__ == "__main__":
    main()
