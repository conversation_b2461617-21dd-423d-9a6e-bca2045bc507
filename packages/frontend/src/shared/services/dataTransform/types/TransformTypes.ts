/**
 * 数据转换相关类型定义
 * 
 * 定义企业级数据转换层的核心类型
 * 确保类型安全和接口一致性
 */

import { z } from 'zod';

// ==================== 基础转换类型 ====================

/**
 * 转换结果类型
 */
export interface TransformResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  originalValue?: any;
  metadata?: TransformMetadata;
}

/**
 * 转换元数据
 */
export interface TransformMetadata {
  transformerName?: string;
  transformerVersion?: string;
  timestamp?: number;
  duration?: number;
  warnings?: string[];
  source?: string;              // 数据来源
  businessType?: string;        // 业务类型
  withWatermark?: boolean;      // 是否包含水印
  [key: string]: any;          // 允许其他自定义字段
}

/**
 * 转换选项
 */
export interface TransformOptions {
  strict?: boolean;           // 严格模式
  skipValidation?: boolean;   // 跳过验证
  preserveOriginal?: boolean; // 保留原始数据
  context?: string;          // 转换上下文
  metadata?: Record<string, any>; // 额外元数据
  // 房源发布相关选项
  propertyType?: string;      // 房源类型
  selectedTags?: string[];    // 选中的标签
  // 媒体上传相关选项
  propertyId?: string;        // 房源ID
  userId?: string;           // 用户ID
}

/**
 * 验证规则
 */
export interface ValidationRule {
  name: string;
  description?: string;
  schema?: z.ZodSchema<any>;
  required?: boolean;
  message?: string;             // 验证失败消息
  validator?: (value: any) => { isValid: boolean; message?: string }; // 自定义验证函数
}

// ==================== 业务数据类型 ====================

/**
 * UI层房源类型（中文显示）
 */
export type UIPropertyType = '商铺' | '写字楼' | '厂房' | '仓库' | '土地' | '会所' | '活动会议室';

/**
 * API层房源类型（英文枚举）
 */
export type APIPropertyType = 'SHOP' | 'OFFICE' | 'FACTORY' | 'WAREHOUSE' | 'LAND' | 'CLUBHOUSE' | 'MEETING_ROOM';

/**
 * 需求类型
 */
export type DemandType = 'RENTAL' | 'PURCHASE';

/**
 * 交易类型
 */
export type TransactionType = 'RENT' | 'SALE' | 'TRANSFER';

/**
 * 装修等级
 */
export type DecorationLevel = 'BARE' | 'SIMPLE' | 'REFINED' | 'LUXURY';

// ==================== 转换器接口 ====================

/**
 * 转换器接口
 */
export interface ITransformer<TInput = any, TOutput = any> {
  /**
   * 转换为API格式
   */
  toAPI(input: TInput, options?: TransformOptions): TransformResult<TOutput>;

  /**
   * 从API格式转换
   */
  fromAPI(input: TOutput, options?: TransformOptions): TransformResult<TInput>;

  /**
   * 获取转换器信息
   */
  getInfo(): {
    name: string;
    version: string;
    validationRules: string[];
  };

  /**
   * 健康检查
   */
  healthCheck(): {
    healthy: boolean;
    issues: string[];
  };
}

// ==================== 通用数据结构 ====================

/**
 * 分页查询参数
 */
export interface PaginationParams {
  page: number;
  size: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

/**
 * 分页响应结构
 */
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

/**
 * API响应结构
 */
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  code?: string;
  timestamp?: number;
}

/**
 * 筛选参数
 */
export interface FilterParams {
  [key: string]: any;
}

/**
 * 排序参数
 */
export interface SortParams {
  field: string;
  order: 'asc' | 'desc';
}

// ==================== 房源相关类型 ====================

/**
 * Store层房源查询参数
 */
export interface StorePropertyParams {
  propertyType: UIPropertyType;
  sortField: string;
  keyword?: string;
  page: number;
  size: number;
  filters?: FilterParams;
}

/**
 * API层房源查询参数
 */
export interface APIPropertyParams extends PaginationParams {
  property_type: APIPropertyType;
  keyword?: string;
  [key: string]: any;
}

/**
 * 房源列表项（Store层）
 */
export interface PropertyListItem {
  id: string;
  title: string;
  price: string;
  area: string;
  location: string;
  imageUrl: string;
  tags: string[];
  aiTags?: string[];
  isVip: boolean;
  isFeatured: boolean;
  vipLabel?: string;
  _rawData?: any; // 保留原始API数据
}

/**
 * 房源API响应
 */
export interface PropertyAPIResponse {
  id: string;
  title: string;
  property_type: APIPropertyType;
  total_area: number;
  address?: string;
  building_name?: string;
  description?: string;
  tags?: string[];
  ai_tags?: string[];
  transaction_types?: string[];
  rent_price?: number;
  sale_price?: number;
  transfer_price?: number;
  status?: string;
  created_at: string;
  updated_at: string;
}

// ==================== 需求相关类型 ====================

/**
 * 需求表单数据（前端）- 匹配DemandFormScreen的实际数据格式
 */
export interface DemandFormData {
  demandType: DemandType;
  propertyType: string[];
  location: {
    city: string;
    districts: string[];
    landmarks: string[];
    transportRequirements: string[];
  };
  areaRange: {
    min: number;
    max: number;
    unit: string;
  };
  budgetRange: {
    min: number;
    max: number;
    unit: string;
  };
  industryType: string[];
  layoutType?: string[];
  specialRequirements?: string;
  contactInfo: {
    phone: string;
    surname: string;
    gender: 'male' | 'female';
    wechat?: string;
    preferredContactTime: string;
    contactMethod: string;
    additionalContacts?: {
      phone: string;
      verificationCode: string;
      surname: string;
      gender: 'male' | 'female';
      isVerified: boolean;
    }[];
  };
  agreeToRules: boolean;
  // 求租特有字段
  floorPreference?: string[];
  orientation?: string[];
  decoration?: string;
  leaseTerm?: string;
  // 求购特有字段
  paymentMethod?: string;
  transferFee?: string;
}

/**
 * 需求API请求格式
 */
export interface DemandAPIRequest {
  demand_type: DemandType;
  property_type: string;
  property_subtypes?: string[];
  target_regions?: string[];
  area_min?: number;
  area_max?: number;
  area_unit: string;
  price_min?: number;
  price_max?: number;
  price_unit?: string;
  contact_surname?: string;
  contact_gender?: string;
  contact_phone?: string;
  contact_wechat?: string;
  additional_contacts?: any;
  custom_tags?: string[];
  description?: string;
  special_requirements?: string;
}

// ==================== MyPropertiesScreen相关类型 ====================

/**
 * MyPropertiesScreen中的Property类型（UI层）
 */
export interface MyProperty {
  id: string;
  type: 'rental' | 'sale';
  title: string;
  price: string;
  area: string;
  location: string;
  description: string;
  imageUrl: string;
  status: 'active' | 'draft' | 'inactive';
  exposureCount: number;
  favoriteCount: number;
  inquiryCount: number;
  viewCount: number;
  tenantMatches: number;
  isPremiumOwner: boolean;
  priceRange: { min: number; max: number };
  areaRange: { min: number; max: number };
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 用户房源查询参数（Store层）
 */
export interface UserPropertyParams {
  page: number;
  size: number;
  status: 'active' | 'draft' | 'inactive';
}

/**
 * 用户房源API查询参数
 */
export interface UserPropertyAPIParams {
  page: number;
  size: number;
  status: string;
}

// ==================== 房源发布相关类型 ====================

/**
 * 房源创建请求格式（API层）
 * 从propertyDataAdapter迁移而来，保持向后兼容
 */
export interface PropertyCreateRequest {
  // Property模型数据
  property: {
    title: string;
    property_type: string;
    sub_type?: string;
    address?: string;
    total_area: number;
    floor?: number;
    total_floors?: number;
    orientation?: string;
    decoration_level?: string;
    description?: string;
    tags: string[];
    ai_tags: string[];
  };

  // TransactionType模型数据
  transaction_types: Array<{
    type: string;
    price: number;
    price_unit: string;
    deposit?: number;
    management_fee?: number;
    transfer_fee?: number;
    negotiable: boolean;
  }>;

  // ContactInfo模型数据
  contact_info: {
    contact_name: string;
    contact_phone: string;
    contact_wechat?: string;
    preferred_contact_time: string;
    contact_method: string;
  };

  // Media模型数据（可选）
  media?: {
    images: string[];
    videos: string[];
  };
}

/**
 * 房源发布表单数据（UI层）
 */
export interface PropertyPublishFormData {
  // 基本信息
  title: string;
  property_certificate_address: string;
  sub_type: string;
  area: string | number;
  floor: string;
  total_floors?: string;
  orientation: string;
  decoration_level: string;
  transaction_types: string[];
  description: string;

  // 价格信息
  rent_price: string;
  sale_price: string;
  transfer_price: string;
  rent_deposit_months: string;
  rent_payment_method?: string;  // 租金支付方式：MONTHLY/QUARTERLY/HALF_YEARLY/YEARLY
  property_fee: string;

  // 通用特性
  has_elevator: boolean;
  has_parking: boolean;
  parking_spaces: string;
  has_air_conditioning: boolean;
  has_heating: boolean;
  has_fire_protection: boolean;
  has_security: boolean;
  has_water_supply: boolean;
  has_natural_gas: boolean;
  has_internet: boolean;
  floor_height: string;

  // 商铺专用字段
  frontage_width: string;
  depth: string;
  can_open_fire: boolean;
  has_chimney: boolean;
  has_outdoor_area: boolean;
  suitable_business_types: string;
  has_private_toilet: boolean;
  has_water_drainage: boolean;

  // 写字楼专用字段
  space_efficiency: string;
  has_independent_ac: boolean;
  has_reception_service: boolean;
  elevator_count: string;
  has_meeting_room: boolean;
}

/**
 * 房源发布API请求格式
 */
export interface PropertyPublishAPIRequest {
  // 基本信息
  title: string;
  property_type: string;
  sub_type?: string;
  address?: string;
  total_area: number;
  floor?: number;
  total_floors?: number;
  orientation?: string;
  decoration_level?: string;
  description?: string;

  // 交易类型
  transaction_types: string[];

  // 特性
  features?: {
    has_elevator?: boolean;
    has_parking?: boolean;
    parking_spaces?: number;
    has_air_conditioning?: boolean;
    has_heating?: boolean;
    has_fire_protection?: boolean;
    has_security?: boolean;
    has_water_supply?: boolean;
    has_natural_gas?: boolean;
    has_internet?: boolean;
    floor_height?: number;

    // 商铺特性
    frontage_width?: number;
    depth?: number;
    can_open_fire?: boolean;
    has_chimney?: boolean;
    has_outdoor_area?: boolean;
    suitable_business_types?: string;
    has_private_toilet?: boolean;
    has_water_drainage?: boolean;

    // 写字楼特性
    space_efficiency?: number;
    has_independent_ac?: boolean;
    has_reception_service?: boolean;
    elevator_count?: number;
    has_meeting_room?: boolean;
  };

  // 价格信息
  prices?: {
    transaction_type: string;
    rent_price?: number;
    sale_price?: number;
    transfer_price?: number;
    rent_deposit_months?: number;
    property_fee?: number;
  }[];

  // 标签
  tags?: string[];
}

// ==================== 导出所有类型 ====================
// 注意：这些类型已经在上面定义，这里不需要重复导出
