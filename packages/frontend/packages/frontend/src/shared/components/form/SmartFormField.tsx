/**
 * 智能表单字段包装组件
 * 
 * 功能特性：
 * 1. 自动注册字段引用到滚动系统
 * 2. 支持错误状态显示
 * 3. 支持必填标记
 * 4. 企业级样式和交互
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-07-30
 */

import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { wp, hp, fp } from '../../utils/responsive';

// ===== 类型定义 =====

export interface SmartFormFieldProps {
  /** 字段名称，用于错误定位 */
  fieldName: string;
  
  /** 字段标签 */
  label?: string;
  
  /** 是否必填 */
  required?: boolean;
  
  /** 错误信息 */
  error?: string;
  
  /** 子组件 */
  children: React.ReactNode;
  
  /** 注册字段引用的回调 */
  onRegisterField?: (fieldName: string, ref: any) => void;
  
  /** 自定义样式 */
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  
  /** 测试ID */
  testID?: string;
}

// ===== 主组件 =====

/**
 * 智能表单字段包装组件
 * 
 * 使用方式：
 * ```tsx
 * <SmartFormField
 *   fieldName="email"
 *   label="邮箱地址"
 *   required
 *   error={errors.email}
 *   onRegisterField={registerField}
 * >
 *   <TextInput ... />
 * </SmartFormField>
 * ```
 */
export const SmartFormField: React.FC<SmartFormFieldProps> = ({
  fieldName,
  label,
  required = false,
  error,
  children,
  onRegisterField,
  containerStyle,
  labelStyle,
  errorStyle,
  testID,
}) => {
  // ===== Refs =====
  
  const containerRef = useRef<View>(null);

  // ===== 副作用处理 =====
  
  /**
   * 注册字段引用到滚动系统
   */
  useEffect(() => {
    if (onRegisterField && containerRef.current) {
      // 🔧 延迟注册，确保组件完全渲染
      const timer = setTimeout(() => {
        if (containerRef.current) {
          onRegisterField(fieldName, containerRef.current);
          console.log(`[SmartFormField] 📝 字段已注册: ${fieldName}`);
        }
      }, 100); // 延迟100ms确保渲染完成

      return () => clearTimeout(timer);
    }
  }, [fieldName, onRegisterField]);

  // ===== 渲染逻辑 =====
  
  return (
    <View
      ref={containerRef}
      style={[styles.container, containerStyle]}
      testID={testID || `smart-form-field-${fieldName}`}
    >
      {/* 字段标签 */}
      {label && (
        <View style={styles.labelContainer}>
          <Text style={[styles.label, labelStyle]}>
            {label}
            {required && <Text style={styles.required}> *</Text>}
          </Text>
        </View>
      )}

      {/* 字段内容 */}
      <View style={styles.fieldContainer}>
        {React.isValidElement(children) &&
         (children.type?.displayName === 'DemandDropdown' ||
          children.type?.name === 'DemandDropdown' ||
          children.props?.placeholder?.includes?.('请选择'))
          ? React.cloneElement(children, {
              ...children.props,
              hideErrorText: true,
              error: error // 🔧 确保error属性正确传递给DemandDropdown
            })
          : children}
      </View>

      {/* 错误信息 */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, errorStyle]}>
            {error}
          </Text>
        </View>
      )}
    </View>
  );
};

// ===== 样式定义 =====

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(16),
  },
  
  labelContainer: {
    marginBottom: hp(8),
  },
  
  label: {
    fontSize: fp(14),
    fontWeight: '500',
    color: '#000000',
    lineHeight: fp(20),
  },
  
  required: {
    color: '#FF3B30',
    fontSize: fp(14),
    fontWeight: '500',
  },
  
  fieldContainer: {
    // 字段内容容器，由子组件决定样式
  },
  
  errorContainer: {
    marginTop: hp(6),
    paddingHorizontal: wp(2),
  },
  
  errorText: {
    fontSize: fp(12),
    color: '#FF3B30',
    lineHeight: fp(16),
    fontWeight: '400',
  },
});

// ===== 导出 =====

export default SmartFormField;

// ===== 便捷Hook =====

/**
 * 便捷Hook：用于快速集成SmartFormField
 */
export const useSmartFormField = (registerField: (fieldName: string, ref: any) => void) => {
  const createField = (
    fieldName: string,
    label?: string,
    required?: boolean
  ) => ({
    fieldName,
    label,
    required,
    onRegisterField: registerField,
  });

  return { createField };
};
