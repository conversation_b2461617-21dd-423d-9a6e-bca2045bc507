/**
 * 房源详情页头部导航组件
 * 
 * 功能：
 * - 返回按钮
 * - 分享功能
 * - 消息查看
 * - 半透明背景设计
 */

import React from 'react';
import { View, TouchableOpacity, StatusBar, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { PropertyDetailDesignSystem } from '@property/styles/propertyDetailDesignSystem';
import { wp, fp } from '@shared/utils/responsive';

const { colors, spacing } = PropertyDetailDesignSystem;

interface PropertyHeaderProps {
  onGoBack: () => void;
  onShare: () => void;
  onMessageView: () => void;
}

export const PropertyHeader: React.FC<PropertyHeaderProps> = ({
  onGoBack,
  onShare,
  onMessageView,
}) => {
  const insets = useSafeAreaInsets();

  return (
    <>
      {/* 状态栏 */}
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent
      />

      {/* 顶部半透明导航栏 */}
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={onGoBack}
          activeOpacity={0.8}
        >
          <Ionicons
            name="arrow-back"
            size={fp(24)}
            color={colors.text.inverse}
          />
        </TouchableOpacity>

        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.iconButton} onPress={onShare}>
            <Ionicons
              name="share-outline"
              size={fp(24)}
              color={colors.text.inverse}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={onMessageView}
          >
            <Ionicons
              name="chatbubble-outline"
              size={fp(24)}
              color={colors.text.inverse}
            />
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  // 顶部导航栏
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
    backgroundColor: 'transparent',
    zIndex: 5,
  },

  // 返回按钮
  backButton: {
    width: wp(40),
    height: wp(40),
    borderRadius: wp(20),
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // 头部操作按钮组
  headerActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },

  // 图标按钮
  iconButton: {
    width: wp(40),
    height: wp(40),
    borderRadius: wp(20),
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
});