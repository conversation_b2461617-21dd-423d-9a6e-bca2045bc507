#!/usr/bin/env node

/**
 * 清理版本导航测试
 * 验证回滚到稳定版本后导航是否正常
 */

const fs = require('fs');

console.log('🧪 清理版本导航测试');
console.log('==================');

// 1. 检查MapSearchScreen是否回到简洁版本
const checkMapSearchScreen = () => {
  console.log('\n📋 检查MapSearchScreen版本...');
  
  const mapScreenFile = 'packages/frontend/src/domains/map/screens/MapSearchScreen.tsx';
  
  if (fs.existsSync(mapScreenFile)) {
    const content = fs.readFileSync(mapScreenFile, 'utf8');
    
    // 检查是否有复杂的组件
    const hasFilterModal = content.includes('RentFilterModal') || content.includes('SaleFilterModal');
    const hasPropertyTypeSelector = content.includes('PropertyTypeSelector');
    const hasComplexState = content.includes('filterModalProps') || content.includes('selectedPropertyType');
    
    console.log(`  包含FilterModal: ${hasFilterModal ? '❌ 是' : '✅ 否'}`);
    console.log(`  包含PropertyTypeSelector: ${hasPropertyTypeSelector ? '❌ 是' : '✅ 否'}`);
    console.log(`  包含复杂状态: ${hasComplexState ? '❌ 是' : '✅ 否'}`);
    
    // 检查是否有生命周期监控
    const hasLifecycleMonitoring = content.includes('组件挂载') && content.includes('组件卸载');
    console.log(`  包含生命周期监控: ${hasLifecycleMonitoring ? '✅ 是' : '❌ 否'}`);
    
    return !hasFilterModal && !hasPropertyTypeSelector && !hasComplexState;
  }
  
  return false;
};

// 2. 检查导航配置
const checkNavigationConfig = () => {
  console.log('\n🧭 检查导航配置...');
  
  const navFile = 'packages/frontend/src/navigation/MainTabNavigator.tsx';
  
  if (fs.existsSync(navFile)) {
    const content = fs.readFileSync(navFile, 'utf8');
    
    // 检查Map Tab配置
    const hasMapComponent = content.includes('name="Map"') && 
                           content.includes('component={MapSearchScreen}');
    const hasRenderFunction = content.includes('name="Map"') && 
                             (content.includes('{() => (') || content.includes('children'));
    
    console.log(`  Map Tab使用component: ${hasMapComponent ? '✅' : '❌'}`);
    console.log(`  Map Tab避免render函数: ${!hasRenderFunction ? '✅' : '❌'}`);
    
    return hasMapComponent && !hasRenderFunction;
  }
  
  return false;
};

// 3. 生成测试指南
const generateCleanTestGuide = () => {
  console.log('\n📋 清理版本测试指南:');
  console.log('====================');
  
  console.log('\n🎯 现在应该测试:');
  console.log('1. 启动应用');
  console.log('2. 点击"地图找房"Tab');
  console.log('3. 观察日志，应该看到:');
  console.log('   - [MapSearchScreen] 🚀 组件挂载 - 开始初始化');
  console.log('   - [MapContainer] 🚀 组件挂载，开始初始化');
  console.log('4. 等待地图加载完成');
  console.log('5. 点击"首页"Tab');
  console.log('6. 观察日志，应该看到:');
  console.log('   - [MapSearchScreen] 🧹 组件卸载 - 开始清理');
  console.log('   - [MapContainer] 🧹 组件卸载，清理所有异步操作');
  console.log('7. 确认没有更多MapContainer相关日志');
  
  console.log('\n🔍 关键观察点:');
  console.log('✅ 正常情况: 组件正确挂载和卸载，日志清晰');
  console.log('❌ 异常情况: 组件卸载后日志继续出现');
  
  console.log('\n📊 预期结果:');
  console.log('- 简洁的MapSearchScreen应该能正确卸载');
  console.log('- 没有复杂的子组件干扰生命周期');
  console.log('- 导航切换应该稳定');
};

// 4. 执行完整检查
const runCleanVersionTest = () => {
  console.log('\n📊 清理版本测试报告:');
  console.log('======================');
  
  const mapScreenClean = checkMapSearchScreen();
  const navigationConfigOk = checkNavigationConfig();
  
  console.log('\n🎯 检查结果:');
  console.log(`✅ MapSearchScreen简洁版本: ${mapScreenClean ? '通过' : '失败'}`);
  console.log(`✅ 导航配置正确: ${navigationConfigOk ? '通过' : '失败'}`);
  
  if (mapScreenClean && navigationConfigOk) {
    console.log('\n🎉 清理版本检查通过！');
    console.log('💡 这个版本应该能正确处理组件生命周期');
    generateCleanTestGuide();
  } else {
    console.log('\n⚠️  清理版本仍有问题');
    
    if (!mapScreenClean) {
      console.log('❌ MapSearchScreen仍然包含复杂组件');
    }
    
    if (!navigationConfigOk) {
      console.log('❌ 导航配置仍有问题');
    }
  }
  
  console.log('\n✅ 检查完成！');
};

// 执行测试
runCleanVersionTest();
