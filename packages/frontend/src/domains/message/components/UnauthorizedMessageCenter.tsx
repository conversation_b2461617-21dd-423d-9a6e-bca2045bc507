/**
 * 未登录消息中心组件
 * 保持原有设计风格和功能
 */
import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Dimensions,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// 导入功能预览组件
import { FeaturePreview } from './FeaturePreview';
import { EmptyState } from './EmptyState';

// 响应式设计 - 获取屏幕尺寸
const { width: screenWidth } = Dimensions.get('window');

export const UnauthorizedMessageCenter: React.FC = () => {
  const insets = useSafeAreaInsets();
  const [activeRole, setActiveRole] = useState<'tenant_buyer' | 'landlord'>('tenant_buyer');

  // 角色切换处理
  const handleRoleSwitch = (role: 'tenant_buyer' | 'landlord') => {
    setActiveRole(role);
  };

  // 渲染角色切换栏
  const renderRoleSwitch = () => {
    return (
      <>
        <TouchableOpacity
          style={styles.roleOption}
          onPress={() => handleRoleSwitch('tenant_buyer')}
        >
          <Text style={[
            styles.roleText,
            activeRole === 'tenant_buyer' && styles.activeRoleText
          ]}>
            租买消息
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.roleOption}
          onPress={() => handleRoleSwitch('landlord')}
        >
          <Text style={[
            styles.roleText,
            activeRole === 'landlord' && styles.activeRoleText
          ]}>
            业主消息
          </Text>
        </TouchableOpacity>
      </>
    );
  };

  return (
    <View style={styles.container}>
      {/* 沉浸式状态栏 */}
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent={true}
      />

      {/* 角色切换栏（沉浸式到顶） */}
      <View style={[styles.roleSwitchContainerImmersive, { paddingTop: insets.top + 16 }]}>
        <View style={styles.roleSwitchContent}>
          {renderRoleSwitch()}
        </View>

        {/* 右上角设置图标 */}
        <TouchableOpacity style={styles.settingsButton}>
          <Text style={styles.settingsIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 功能预览区域 */}
        <FeaturePreview activeRole={activeRole} />

        {/* 未登录时不显示空状态区域 */}

        {/* 底部间距 */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = {
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  roleSwitchContainerImmersive: {
    flexDirection: 'row' as const,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingBottom: 30,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
  },
  roleSwitchContent: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 30,
  },
  roleOption: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    position: 'relative' as const,
  },
  roleText: {
    fontSize: 16,
    color: '#999999',
    fontWeight: '500' as const,
  },
  activeRoleText: {
    fontSize: 26, // 🔧 激活状态字体大小
    color: '#FF6B35',
    fontWeight: '600' as const,
  },
  settingsButton: {
    padding: 8,
  },
  settingsIcon: {
    fontSize: 20,
    color: '#666666',
  },
  bottomSpacing: {
    height: 20,
  },
};

// 导出组件
export default UnauthorizedMessageCenter;
