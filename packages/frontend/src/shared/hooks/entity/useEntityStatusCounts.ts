/**
 * 通用实体状态计数Hook
 * 解决循环依赖问题，提供独立的状态计数查询
 * 遵循企业级架构规范 - Hook层
 */
import { useQuery } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';
import PropertyAPI from '../../../domains/property/services/propertyAPI';
import { DemandAPI } from '../../../domains/demand/services/demandAPI';

// 🏗️ 企业级架构：类型定义
export interface EntityStatusCounts {
  active: number;
  draft: number;
  inactive: number;
}

export interface EntityStatusCountsData extends EntityStatusCounts {
  activeItems: any[];
  draftItems: any[];
  inactiveItems: any[];
}

export interface UseEntityStatusCountsOptions {
  entityType: 'property' | 'demand';
  enabled?: boolean;
  staleTime?: number;
  cacheTime?: number;
}

export interface UseEntityStatusCountsResult {
  statusCounts: EntityStatusCounts;
  statusCountsData: EntityStatusCountsData | undefined;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
}

// 🏗️ 企业级架构：通用状态计数Hook
export const useEntityStatusCounts = (
  options: UseEntityStatusCountsOptions
): UseEntityStatusCountsResult => {
  const {
    entityType,
    enabled = true,
    staleTime = 30 * 1000, // 30秒缓存
    cacheTime = 5 * 60 * 1000, // 5分钟缓存
  } = options;

  // 🔧 独立的状态计数查询（避免循环依赖）
  const {
    data: statusCountsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: [`${entityType}-status-counts`],
    queryFn: async (): Promise<EntityStatusCountsData> => {
      try {
        console.log(`[useEntityStatusCounts] 🔄 获取${entityType}状态计数`);

        if (entityType === 'property') {
          // 🏠 房源状态计数 - 🚀 企业级纯服务器草稿机制（数据库枚举已修复）
          const [activeResponse, draftResponse, inactiveResponse] =
            await Promise.all([
              PropertyAPI.getUserProperties({ status: 'ACTIVE' }),
              PropertyAPI.getUserProperties({ status: 'DRAFT' }), // 🔥 企业级：纯服务器数据源
              PropertyAPI.getUserProperties({ status: 'INACTIVE' }),
            ]);

          const activeItems = activeResponse.success
            ? activeResponse.data?.items || []
            : [];
          const draftItems = draftResponse.success
            ? draftResponse.data?.items || []
            : []; // 🔥 企业级：API数据
          const inactiveItems = inactiveResponse.success
            ? inactiveResponse.data?.items || []
            : [];

          const result = {
            active: activeItems.length,
            draft: draftItems.length,
            inactive: inactiveItems.length,
            activeItems,
            draftItems,
            inactiveItems,
          };

          console.log(
            `[useEntityStatusCounts] ✅ 房源状态计数 (企业级纯服务器):`,
            result
          );
          return result;
        } else {
          // 🏢 需求状态计数 - 🚀 纯服务器草稿机制
          const [activeResponse, draftResponse, inactiveResponse] =
            await Promise.all([
              DemandAPI.getMyDemands({ status: 'ACTIVE' }),
              DemandAPI.getMyDemands({ status: 'DRAFT' }), // 🔥 改为API调用
              DemandAPI.getMyDemands({ status: 'OFFLINE' }),
            ]);

          const activeItems = activeResponse.success
            ? activeResponse.data?.items || [] // 🔧 修复：DemandListDTO转换后是items字段
            : [];
          const draftItems = draftResponse.success
            ? draftResponse.data?.items || [] // 🔧 修复：DemandListDTO转换后是items字段
            : [];
          const inactiveItems = inactiveResponse.success
            ? inactiveResponse.data?.items || [] // 🔧 修复：DemandListDTO转换后是items字段
            : [];

          const result = {
            active: activeItems.length,
            draft: draftItems.length,
            inactive: inactiveItems.length,
            activeItems,
            draftItems,
            inactiveItems,
          };

          console.log(
            `[useEntityStatusCounts] ✅ 需求状态计数 (纯服务器):`,
            result
          );
          return result;
        }
      } catch (error) {
        console.error(
          `[useEntityStatusCounts] ❌ 获取${entityType}状态计数失败:`,
          error
        );

        // 🔧 降级处理：返回默认值
        return {
          active: 0,
          draft: 0,
          inactive: 0,
          activeItems: [],
          draftItems: [],
          inactiveItems: [],
        };
      }
    },
    enabled,
    staleTime,
    cacheTime,
    retry: 2,
    retryDelay: 1000,
  });

  // 🔧 提取状态计数（不包含原始数据）
  const statusCounts: EntityStatusCounts = {
    active: statusCountsData?.active || 0,
    draft: statusCountsData?.draft || 0,
    inactive: statusCountsData?.inactive || 0,
  };

  return {
    statusCounts,
    statusCountsData,
    isLoading,
    error: error as Error | null,
    refetch,
  };
};

// 🏗️ 企业级架构：房源专用Hook
export const usePropertyStatusCounts = (
  options?: Omit<UseEntityStatusCountsOptions, 'entityType'>
) => {
  return useEntityStatusCounts({
    entityType: 'property',
    ...options,
  });
};

// 🏗️ 企业级架构：需求专用Hook
export const useDemandStatusCounts = (
  options?: Omit<UseEntityStatusCountsOptions, 'entityType'>
) => {
  return useEntityStatusCounts({
    entityType: 'demand',
    ...options,
  });
};
