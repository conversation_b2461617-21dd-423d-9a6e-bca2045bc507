/**
 * 用户中心数据转换器
 * 继承统一转换层，处理用户中心相关数据转换
 * 遵循企业级架构规范 - DTO层
 */
import { BaseTransformer } from '../../../../shared/services/dataTransform/core/BaseTransformer';
import {
  TransformResult,
  TransformOptions,
} from '../../../../shared/services/dataTransform/types/TransformTypes';

// 🏗️ 企业级架构：用户中心数据类型定义

// 前端用户统计数据
export interface UserCenterStats {
  // 房源统计
  propertyStats: {
    active: number;
    draft: number;
    inactive: number;
    totalViews: number;
    totalInquiries: number;
  };

  // 需求统计
  demandStats: {
    active: number;
    draft: number;
    inactive: number;
    totalMatches: number;
    totalInterested: number;
  };

  // 订单统计
  orderStats: {
    favoriteCount: number;
    appointmentCount: number;
    orderCount: number;
    tenantResourceCount: number;
    completedOrders: number;
    pendingOrders: number;
  };

  // 用户信息
  userInfo: {
    id: string;
    phoneNumber: string;
    userName?: string;
    avatar?: string;
    isVerified: boolean;
    userType: string;
    memberLevel?: string;
    joinDate: Date;
  };
}

// 后端API统计数据
export interface UserCenterStatsAPI {
  // 房源统计
  property_stats: {
    active_count: number;
    draft_count: number;
    inactive_count: number;
    total_views: number;
    total_inquiries: number;
  };

  // 需求统计
  demand_stats: {
    active_count: number;
    draft_count: number;
    inactive_count: number;
    total_matches: number;
    total_interested: number;
  };

  // 订单统计
  order_stats: {
    favorite_count: number;
    appointment_count: number;
    order_count: number;
    tenant_resource_count: number;
    completed_orders: number;
    pending_orders: number;
  };

  // 用户信息
  user_info: {
    id: string;
    phone_number: string;
    user_name?: string;
    avatar?: string;
    is_verified: boolean;
    user_type: string;
    member_level?: string;
    created_at: string;
  };
}

// 计数配置类型
export interface CountConfig {
  entityType: 'property' | 'demand' | 'order' | 'user';
  countType: 'status' | 'interaction' | 'general';
  displayFormat: 'number' | 'formatted' | 'percentage';
}

/**
 * 用户中心转换器类
 * 处理用户中心相关的所有数据转换
 */
export class UserCenterTransformer extends BaseTransformer<
  UserCenterStats,
  UserCenterStatsAPI
> {
  /**
   * 前端数据转换为API格式
   */
  toAPI(
    input: UserCenterStats,
    options?: TransformOptions
  ): TransformResult<UserCenterStatsAPI> {
    return this.safeTransform(() => {
      const apiData: UserCenterStatsAPI = {
        property_stats: {
          active_count: input.propertyStats.active,
          draft_count: input.propertyStats.draft,
          inactive_count: input.propertyStats.inactive,
          total_views: input.propertyStats.totalViews,
          total_inquiries: input.propertyStats.totalInquiries,
        },

        demand_stats: {
          active_count: input.demandStats.active,
          draft_count: input.demandStats.draft,
          inactive_count: input.demandStats.inactive,
          total_matches: input.demandStats.totalMatches,
          total_interested: input.demandStats.totalInterested,
        },

        order_stats: {
          favorite_count: input.orderStats.favoriteCount,
          appointment_count: input.orderStats.appointmentCount,
          order_count: input.orderStats.orderCount,
          tenant_resource_count: input.orderStats.tenantResourceCount,
          completed_orders: input.orderStats.completedOrders,
          pending_orders: input.orderStats.pendingOrders,
        },

        user_info: {
          id: input.userInfo.id,
          phone_number: input.userInfo.phoneNumber,
          user_name: input.userInfo.userName,
          avatar: input.userInfo.avatar,
          is_verified: input.userInfo.isVerified,
          user_type: input.userInfo.userType,
          member_level: input.userInfo.memberLevel,
          created_at: input.userInfo.joinDate.toISOString(),
        },
      };

      return apiData;
    }, 'UserCenterTransformer.toAPI');
  }

  /**
   * API数据转换为前端格式
   */
  fromAPI(
    input: UserCenterStatsAPI,
    options?: TransformOptions
  ): TransformResult<UserCenterStats> {
    return this.safeTransform(() => {
      const frontendData: UserCenterStats = {
        propertyStats: {
          active: input.property_stats.active_count,
          draft: input.property_stats.draft_count,
          inactive: input.property_stats.inactive_count,
          totalViews: input.property_stats.total_views,
          totalInquiries: input.property_stats.total_inquiries,
        },

        demandStats: {
          active: input.demand_stats.active_count,
          draft: input.demand_stats.draft_count,
          inactive: input.demand_stats.inactive_count,
          totalMatches: input.demand_stats.total_matches,
          totalInterested: input.demand_stats.total_interested,
        },

        orderStats: {
          favoriteCount: input.order_stats.favorite_count,
          appointmentCount: input.order_stats.appointment_count,
          orderCount: input.order_stats.order_count,
          tenantResourceCount: input.order_stats.tenant_resource_count,
          completedOrders: input.order_stats.completed_orders,
          pendingOrders: input.order_stats.pending_orders,
        },

        userInfo: {
          id: input.user_info.id,
          phoneNumber: input.user_info.phone_number,
          userName: input.user_info.user_name,
          avatar: input.user_info.avatar,
          isVerified: input.user_info.is_verified,
          userType: input.user_info.user_type,
          memberLevel: input.user_info.member_level,
          joinDate: new Date(input.user_info.created_at),
        },
      };

      return frontendData;
    }, 'UserCenterTransformer.fromAPI');
  }

  /**
   * 转换计数配置
   */
  transformCountConfig(config: CountConfig): TransformResult<any> {
    return this.safeTransform(() => {
      // 根据配置生成计数显示参数
      const displayConfig = {
        variant: this.getCountVariant(config),
        maxCount: this.getMaxCount(config),
        showZero: this.shouldShowZero(config),
        hideWhenZero: this.shouldHideWhenZero(config),
      };

      return displayConfig;
    }, 'UserCenterTransformer.transformCountConfig');
  }

  // 🔧 私有辅助方法

  private getCountVariant(config: CountConfig): string {
    switch (config.countType) {
      case 'status':
        return 'tab';
      case 'interaction':
        return 'stat-card';
      case 'general':
      default:
        return 'inline';
    }
  }

  private getMaxCount(config: CountConfig): number {
    switch (config.entityType) {
      case 'property':
      case 'demand':
        return 999;
      case 'order':
        return 99;
      case 'user':
      default:
        return 9999;
    }
  }

  private shouldShowZero(config: CountConfig): boolean {
    return config.countType === 'status';
  }

  private shouldHideWhenZero(config: CountConfig): boolean {
    return config.countType === 'interaction';
  }
}

// 🔧 导出转换器实例
export const userCenterTransformer = new UserCenterTransformer();

export default userCenterTransformer;
