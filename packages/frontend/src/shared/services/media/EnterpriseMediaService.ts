/**
 * 企业级媒体服务
 * 
 * 功能特性：
 * - STS凭证智能缓存和复用
 * - CDN/OSS双模式URL生成
 * - 分层缓存策略
 * - 直接OSS上传/删除
 * - 微信风格上传进度
 * 
 * 架构设计：
 * - 支持CDN但可配置关闭
 * - 渐进式开发策略
 * - 企业级错误处理
 * - 完整的审计日志
 */

import { apiClient } from '../client';
import { ossClientService } from './OSSClientService.js';
import {
  STSCredentials,
  UploadProgress,
  UploadResult,
  OSSUploadOptions,
  DeleteResult,
  MediaCategory,
  FileInfo,
  UploadTask,
  UploadTaskStatus
} from './types';

// 配置接口
interface MediaConfig {
  cdn: {
    enabled: boolean;
    domain: string;
    fallbackToOSS: boolean;
  };
  oss: {
    domain: string;
    bucket: string;
    region: string;
  };
  cache: {
    enabled: boolean;
    ttl: {
      thumbnail: number;
      medium: number;
      large: number;
      original: number;
    };
  };
}

// 缓存的凭证
interface CachedCredentials {
  credentials: STSCredentials;
  expiresAt: number;
  type: 'upload' | 'delete';
}

// URL缓存
interface CachedUrl {
  url: string;
  expiresAt: number;
  type: 'cdn' | 'signed';
}

export class EnterpriseMediaService {
  private static instance: EnterpriseMediaService;
  
  // 配置
  private getConfig(): MediaConfig {
    return {
      cdn: {
        enabled: process.env.REACT_APP_ENABLE_CDN === 'true',
        domain: process.env.REACT_APP_CDN_DOMAIN || '',
        fallbackToOSS: true,
      },
      oss: {
        domain: process.env.REACT_APP_OSS_DOMAIN || 'huixuanzhi-main.oss-cn-guangzhou.aliyuncs.com',
        bucket: process.env.REACT_APP_OSS_BUCKET || 'huixuanzhi-main',
        region: process.env.REACT_APP_OSS_REGION || 'cn-guangzhou',
      },
      cache: {
        enabled: true,
        ttl: {
          thumbnail: 24 * 60 * 60 * 1000, // 24小时
          medium: 24 * 60 * 60 * 1000,    // 24小时
          large: 12 * 60 * 60 * 1000,     // 12小时
          original: 2 * 60 * 60 * 1000,   // 2小时
        }
      }
    };
  }
  
  // 缓存
  private stsCache = new Map<string, CachedCredentials>();
  private urlCache = new Map<string, CachedUrl>();
  
  /**
   * 单例模式
   */
  public static getInstance(): EnterpriseMediaService {
    if (!EnterpriseMediaService.instance) {
      EnterpriseMediaService.instance = new EnterpriseMediaService();
    }
    return EnterpriseMediaService.instance;
  }
  
  /**
   * 获取查看URL - 智能CDN/OSS选择
   */
  async getViewUrl(objectKey: string, size: 'thumbnail' | 'medium' | 'large' | 'original' = 'medium'): Promise<string> {
    const config = this.getConfig();
    const cacheKey = `${objectKey}_${size}`;

    // 检查缓存
    if (config.cache.enabled) {
      const cached = this.urlCache.get(cacheKey);
      if (cached && Date.now() < cached.expiresAt) {
        return cached.url;
      }
    }

    let url: string;
    let cacheTime: number;
    let urlType: 'cdn' | 'signed';

    if (size === 'original' || this.isPrivateContent(objectKey)) {
      // 原图或私有内容使用签名URL
      url = await this.getSignedUrl(objectKey);
      cacheTime = config.cache.ttl.original;
      urlType = 'signed';
    } else {
      // 缩略图和中等尺寸使用CDN或OSS
      url = this.buildImageUrl(objectKey, size);
      cacheTime = config.cache.ttl[size];
      urlType = 'cdn';
    }

    // 缓存URL
    if (config.cache.enabled) {
      this.urlCache.set(cacheKey, {
        url,
        expiresAt: Date.now() + cacheTime,
        type: urlType
      });
    }

    return url;
  }
  
  /**
   * 获取上传凭证 - 智能缓存
   */
  async getUploadCredentials(): Promise<STSCredentials> {
    const cacheKey = 'upload_credentials';
    
    // 检查缓存（提前5分钟刷新）
    const cached = this.stsCache.get(cacheKey);
    if (cached && Date.now() < cached.expiresAt - 5 * 60 * 1000) {
      console.log('[EnterpriseMediaService] 使用缓存的上传凭证');
      return cached.credentials;
    }
    
    // 请求新凭证
    console.log('[EnterpriseMediaService] 生成新的上传凭证');
    try {
      const response = await apiClient.post('/sts/upload-credentials', {
        fileName: 'temp.jpg',
        fileSize: 1024,
        fileType: 'image/jpeg',
        propertyId: 'temp'
      });
      
      const credentials = response.data.credentials;
      
      // 缓存凭证
      this.stsCache.set(cacheKey, {
        credentials,
        expiresAt: Date.now() + response.data.expires_in * 1000,
        type: 'upload'
      });
      
      return credentials;
    } catch (error) {
      console.error('[EnterpriseMediaService] 获取上传凭证失败:', error);
      throw new Error('获取上传凭证失败');
    }
  }
  
  /**
   * 获取删除凭证
   */
  async getDeleteCredentials(objectKey: string): Promise<STSCredentials> {
    try {
      const response = await apiClient.post('/sts/delete-credentials', {
        objectKey
      });

      return response.data.credentials;
    } catch (error) {
      console.error('[EnterpriseMediaService] 获取删除凭证失败:', error);
      throw new Error('获取删除凭证失败');
    }
  }

  /**
   * 上传文件 - 直接上传到OSS
   */
  async uploadFile(
    file: { uri: string; name: string; type: string; size: number },
    options: {
      propertyId?: string;
      onProgress?: (progress: UploadProgress) => void;
    } = {}
  ): Promise<UploadResult> {
    try {
      console.log(`[EnterpriseMediaService] 开始上传文件: ${file.name}`);

      // 1. 获取上传凭证
      const credentials = await this.getUploadCredentials();

      // 2. 使用OSS客户端上传
      const uploadOptions: OSSUploadOptions = {
        onProgress: options.onProgress,
        timeout: 60000,
        retries: 3
      };

      const result = await ossClientService.uploadFile(file, credentials, uploadOptions);

      // 3. 通知后端上传完成
      if (result.success && result.objectKey) {
        await this.notifyUploadComplete(result.objectKey, options.propertyId);
      }

      console.log(`[EnterpriseMediaService] 文件上传完成: ${file.name}, 成功: ${result.success}`);
      return result;

    } catch (error) {
      console.error(`[EnterpriseMediaService] 文件上传失败: ${file.name}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      };
    }
  }

  /**
   * 删除文件 - 直接从OSS删除
   */
  async deleteFile(objectKey: string): Promise<DeleteResult> {
    try {
      console.log(`[EnterpriseMediaService] 开始删除文件: ${objectKey}`);

      // 1. 获取删除凭证
      const credentials = await this.getDeleteCredentials(objectKey);

      // 2. 使用OSS客户端删除
      const result = await ossClientService.deleteFile(objectKey, credentials);

      // 3. 通知后端删除完成
      if (result.success) {
        await this.notifyDeleteComplete(objectKey);
      }

      console.log(`[EnterpriseMediaService] 文件删除完成: ${objectKey}, 成功: ${result.success}`);
      return result;

    } catch (error) {
      console.error(`[EnterpriseMediaService] 文件删除失败: ${objectKey}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除失败'
      };
    }
  }

  /**
   * 批量上传文件
   */
  async uploadFiles(
    files: Array<{ uri: string; name: string; type: string; size: number }>,
    options: {
      propertyId?: string;
      onProgress?: (fileIndex: number, progress: UploadProgress) => void;
      onComplete?: (fileIndex: number, result: UploadResult) => void;
    } = {}
  ): Promise<UploadResult[]> {
    console.log(`[EnterpriseMediaService] 开始批量上传 ${files.length} 个文件`);

    const results: UploadResult[] = [];

    // 并发上传，但限制并发数量
    const concurrency = 3;
    for (let i = 0; i < files.length; i += concurrency) {
      const batch = files.slice(i, i + concurrency);
      const batchPromises = batch.map((file, batchIndex) => {
        const fileIndex = i + batchIndex;
        return this.uploadFile(file, {
          propertyId: options.propertyId,
          onProgress: (progress) => options.onProgress?.(fileIndex, progress)
        }).then(result => {
          options.onComplete?.(fileIndex, result);
          return result;
        });
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    console.log(`[EnterpriseMediaService] 批量上传完成，成功: ${results.filter(r => r.success).length}/${files.length}`);
    return results;
  }
  
  /**
   * 构建图片URL - CDN/OSS双模式
   */
  private buildImageUrl(objectKey: string, size: string): string {
    const config = this.getConfig();
    const sizeParams = {
      thumbnail: '?x-oss-process=image/resize,w_300,h_200,m_lfit/quality,q_80',
      medium: '?x-oss-process=image/resize,w_800,h_600,m_lfit/quality,q_85',
      large: '?x-oss-process=image/resize,w_1200,h_800,m_lfit/quality,q_90',
      original: ''
    };

    const param = sizeParams[size as keyof typeof sizeParams] || '';

    if (config.cdn.enabled && config.cdn.domain) {
      // CDN模式
      return `https://${config.cdn.domain}/${objectKey}${param}`;
    } else {
      // 直接OSS模式
      return `https://${config.oss.domain}/${objectKey}${param}`;
    }
  }
  
  /**
   * 获取签名URL
   */
  private async getSignedUrl(objectKey: string): Promise<string> {
    try {
      const response = await apiClient.post('/sts/view-url', {
        object_key: objectKey,
        expires: 7200 // 2小时
      });
      
      return response.data.view_url;
    } catch (error) {
      console.error('[EnterpriseMediaService] 获取签名URL失败:', error);
      throw new Error('获取签名URL失败');
    }
  }
  
  /**
   * 判断是否为私有内容
   */
  private isPrivateContent(objectKey: string): boolean {
    // 这里可以根据业务规则判断
    // 例如：身份证、营业执照等敏感文件
    return objectKey.includes('/identity/') || objectKey.includes('/business/');
  }
  
  /**
   * 清除缓存
   */
  clearCache(): void {
    this.stsCache.clear();
    this.urlCache.clear();
    console.log('[EnterpriseMediaService] 缓存已清除');
  }
  
  /**
   * 获取缓存统计
   */
  getCacheStats(): { stsCache: number; urlCache: number } {
    return {
      stsCache: this.stsCache.size,
      urlCache: this.urlCache.size
    };
  }

  /**
   * 通知后端上传完成
   */
  private async notifyUploadComplete(objectKey: string, propertyId?: string): Promise<void> {
    try {
      await apiClient.post('/media/upload-complete', {
        objectKey,
        propertyId
      });
    } catch (error) {
      console.error('[EnterpriseMediaService] 通知上传完成失败:', error);
      // 不抛出错误，因为文件已经上传成功
    }
  }

  /**
   * 通知后端删除完成
   */
  private async notifyDeleteComplete(objectKey: string): Promise<void> {
    try {
      await apiClient.post('/media/delete-complete', {
        objectKey
      });
    } catch (error) {
      console.error('[EnterpriseMediaService] 通知删除完成失败:', error);
      // 不抛出错误，因为文件已经删除成功
    }
  }
}

// 导出单例实例
export const enterpriseMediaService = EnterpriseMediaService.getInstance();
