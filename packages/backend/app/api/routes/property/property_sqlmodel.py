"""
房源管理模块的 API 路由 (SQLModel 版本)
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api import deps
from app.models.user import User
from app.models.property.enums import PropertyType, TransactionType, PropertyStatus
from app.schemas.property.init_sqlmodel import (
    PropertyCreate, PropertyUpdate, PropertyStatusUpdate, VerificationStatusUpdate,
    PropertyResponse, PropertyListResponse, PaginatedPropertyResponse
)
# 使用SQLModel适配版本的PropertyService  
from app.services.property.property_service import PropertyService
from pydantic import BaseModel


router = APIRouter()


class TagRecommendationsResponse(BaseModel):
    tags: List[str]


@router.get("/tags/recommendations", response_model=TagRecommendationsResponse)
async def get_tag_recommendations(
    property_type: str = Query(..., description="房源类型, 例如 '商铺'"),
    count: int = Query(8, ge=1, le=20, description="需要获取的标签数量")
):
    """
    获取指定房源类型的推荐标签，用于“换一换”功能。
    该功能不涉及数据库操作，因此移除了DB依赖以避免潜在的会话问题。
    """
    # 此服务方法不使用数据库会话，因此可以安全地传入None
    property_service = PropertyService(session=None)
    tags = await property_service.get_tag_recommendations(property_type, count)
    return TagRecommendationsResponse(tags=tags)


@router.post("/", response_model=PropertyResponse, status_code=status.HTTP_201_CREATED)
async def create_property(
    property_data: PropertyCreate,
    db: AsyncSession = Depends(deps.get_async_session),
    current_user: User = Depends(deps.current_active_user)
):
    """
    创建房源 - 智能角色分配

    用户发布房源时自动获得房东身份，实现基于行为的动态角色管理。
    - **property_data**: 房源信息
    """
    # 🔄 智能角色分配：发布房源自动赋予房东身份
    if current_user.current_role == "TENANT":
        # 如果用户当前是租客，发布房源时自动切换为房东
        current_user.current_role = "LANDLORD"
        current_user.is_landlord_certified = True  # 自动认证为房东
        db.add(current_user)
        await db.commit()
        await db.refresh(current_user)

    # 现在检查权限（房东或经纪人可以创建房源）
    if current_user.current_role not in ["LANDLORD", "MANAGER"]:  # 🔧 修复角色常量不一致
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有房东或经纪人可以创建房源"
        )

    try:
        property_service = PropertyService(db)
        property_obj = await property_service.create_property(
            property_data=property_data,
            owner_id=current_user.id
        )
        return PropertyResponse.from_property(property_obj)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建房源失败: {str(e)}"
        )


@router.get("/{property_id}", response_model=PropertyResponse)
async def get_property(
    property_id: str = Path(..., description="房源ID"),
    db: AsyncSession = Depends(deps.get_async_session),
    current_user: Optional[User] = Depends(deps.get_current_user_optional)  # 🔄 改为可选认证
):
    """
    获取房源详情

    🔓 **公开访问**: 此API允许匿名访问，无需登录即可查看房源详情

    - **property_id**: 房源ID
    """
    property_service = PropertyService(db)
    property_obj = await property_service.get_property_by_id(property_id)
    if not property_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房源不存在"
        )

    # 检查房源状态和用户权限
    property_status = property_obj.get('status') if isinstance(property_obj, dict) else property_obj.status
    property_owner_id = property_obj.get('owner_id') if isinstance(property_obj, dict) else property_obj.owner_id
    property_agent_id = property_obj.get('agent_id') if isinstance(property_obj, dict) else property_obj.agent_id
    
    # 转换UUID为字符串进行比较
    current_user_id = str(current_user.id) if current_user else None
    property_owner_id_str = str(property_owner_id) if property_owner_id else None
    property_agent_id_str = str(property_agent_id) if property_agent_id else None
    
    # 调试：打印权限检查信息（生产环境可移除）
    print(f"[PropertyDetail] 房源状态: {property_status}, 用户ID: {current_user_id}, 房东ID: {property_owner_id_str}")
    
    if property_status != PropertyStatus.ACTIVE.value:
        # 非活跃状态的房源只有房东、经纪人、管理员可以查看
        if not current_user or (
            current_user_id != property_owner_id_str and
            current_user_id != property_agent_id_str and
            current_user.current_role != "ADMIN"
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="该房源当前不可见"
            )

    # 如果property_obj是Property对象，使用from_property方法转换
    if hasattr(property_obj, 'id'):
        return PropertyResponse.from_property(property_obj)
    else:
        # 如果是dict格式，直接返回
        return property_obj


@router.get("/", response_model=PaginatedPropertyResponse)
async def get_properties(
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(10, description="返回的记录数"),
    property_type: Optional[PropertyType] = Query(None, description="房源类型"),
    transaction_type: Optional[TransactionType] = Query(None, description="交易类型"),
    min_area: Optional[float] = Query(None, description="最小面积"),
    max_area: Optional[float] = Query(None, description="最大面积"),
    min_price: Optional[float] = Query(None, description="最低价格"),
    max_price: Optional[float] = Query(None, description="最高价格"),
    status: Optional[PropertyStatus] = Query(PropertyStatus.ACTIVE, description="房源状态"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段: created_at, price, area"),
    sort_order: str = Query("desc", description="排序方向: asc, desc"),
    db: AsyncSession = Depends(deps.get_async_session),
    current_user: Optional[User] = Depends(deps.get_current_user_optional)  # 🔄 改为可选认证
):
    """
    获取房源列表，支持筛选、排序和分页

    🔓 **公开访问**: 此API允许匿名访问，无需登录即可浏览房源列表

    - **skip**: 跳过的记录数
    - **limit**: 返回的记录数
    - **property_type**: 房源类型
    - **transaction_type**: 交易类型
    - **min_area**: 最小面积
    - **max_area**: 最大面积
    - **min_price**: 最低价格
    - **max_price**: 最高价格
    - **status**: 房源状态
    - **search**: 搜索关键词
    - **sort_by**: 排序字段
    - **sort_order**: 排序方向
    """
    # 非管理员用户只能查看活跃状态的房源
    if current_user and current_user.current_role == "ADMIN":
        # 管理员可以查看所有状态的房源
        pass
    else:
        # 普通用户只能查看活跃状态的房源
        status = PropertyStatus.ACTIVE

    property_service = PropertyService(db)
    properties, total = await property_service.get_properties(
        skip=skip,
        limit=limit,
        property_type=property_type,
        transaction_type=transaction_type,
        min_area=min_area,
        max_area=max_area,
        min_price=min_price,
        max_price=max_price,
        status=status,
        search_term=search,
        sort_by=sort_by,
        sort_order=sort_order
    )

    # 计算总页数
    pages = (total + limit - 1) // limit if limit > 0 else 0

    # 转换为PropertyResponse格式
    property_responses = []
    for property_obj in properties:
        if hasattr(property_obj, 'id'):
            property_responses.append(PropertyResponse.from_property(property_obj))
        else:
            property_responses.append(property_obj)

    return {
        "items": property_responses,
        "total": total,
        "page": skip // limit + 1,
        "size": limit,
        "pages": pages
    }


@router.get("/user/my", response_model=PaginatedPropertyResponse)
async def get_my_properties(
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(10, description="返回的记录数"),
    status: Optional[PropertyStatus] = Query(None, description="房源状态"),
    db: AsyncSession = Depends(deps.get_async_session),
    current_user: User = Depends(deps.current_active_user)
):
    """
    获取当前用户的房源列表

    - **skip**: 跳过的记录数
    - **limit**: 返回的记录数
    - **status**: 房源状态
    """
    property_service = PropertyService(db)
    properties, total = await property_service.get_properties(
        skip=skip,
        limit=limit,
        owner_id=current_user.id,
        status=status
    )

    # 计算总页数
    pages = (total + limit - 1) // limit if limit > 0 else 0

    # 转换为PropertyResponse格式
    property_responses = []
    for property_obj in properties:
        if hasattr(property_obj, 'id'):
            property_responses.append(PropertyResponse.from_property(property_obj))
        else:
            property_responses.append(property_obj)

    return {
        "items": property_responses,
        "total": total,
        "page": skip // limit + 1,
        "size": limit,
        "pages": pages
    }


@router.put("/{property_id}", response_model=PropertyResponse)
async def update_property(
    property_data: PropertyUpdate,
    property_id: str = Path(..., description="房源ID"),
    db: AsyncSession = Depends(deps.get_async_session),
    current_user: User = Depends(deps.current_active_user)
):
    """
    更新房源信息

    - **property_id**: 房源ID
    - **property_data**: 更新的房源信息
    """
    # 获取房源
    property_obj = await PropertyService.get_property(db=db, property_id=property_id)
    if not property_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房源不存在"
        )

    # 检查用户是否有权限更新房源
    if current_user.id != property_obj.owner_id and current_user.id != property_obj.agent_id and current_user.current_role != "ADMIN":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有房源所有者、经纪人或管理员可以更新房源"
        )

    try:
        updated_property = await PropertyService.update_property(
            db=db,
            property_id=property_id,
            property_data=property_data
        )
        return updated_property
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"更新房源失败: {str(e)}"
        )


@router.delete("/{property_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_property(
    property_id: str = Path(..., description="房源ID"),
    db: AsyncSession = Depends(deps.get_async_session),
    current_user: User = Depends(deps.current_active_user)
):
    """
    删除房源

    - **property_id**: 房源ID
    """
    # 获取房源
    property_obj = await PropertyService.get_property(db=db, property_id=property_id)
    if not property_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房源不存在"
        )

    # 检查用户是否有权限删除房源
    if current_user.id != property_obj.owner_id and current_user.current_role != "ADMIN":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有房源所有者或管理员可以删除房源"
        )

    success = await PropertyService.delete_property(db=db, property_id=property_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="删除房源失败"
        )


@router.patch("/{property_id}/status", response_model=PropertyResponse)
async def update_property_status(
    status_data: PropertyStatusUpdate,
    property_id: str = Path(..., description="房源ID"),
    db: AsyncSession = Depends(deps.get_async_session),
    current_user: User = Depends(deps.current_active_user)
):
    """
    更新房源状态

    - **property_id**: 房源ID
    - **status_data**: 更新的状态信息
    """
    # 获取房源
    property_obj = await PropertyService.get_property(db=db, property_id=property_id)
    if not property_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房源不存在"
        )

    # 检查用户是否有权限更新房源状态
    if current_user.id != property_obj.owner_id and current_user.id != property_obj.agent_id and current_user.current_role != "ADMIN":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有房源所有者、经纪人或管理员可以更新房源状态"
        )

    try:
        updated_property = await PropertyService.update_property_status(
            db=db,
            property_id=property_id,
            status_data=status_data
        )
        return updated_property
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"更新房源状态失败: {str(e)}"
        )


@router.patch("/{property_id}/verification", response_model=PropertyResponse)
async def update_verification_status(
    status_data: VerificationStatusUpdate,
    property_id: str = Path(..., description="房源ID"),
    db: AsyncSession = Depends(deps.get_async_session),
    current_user: User = Depends(deps.current_active_user)
):
    """
    更新审核状态

    - **property_id**: 房源ID
    - **status_data**: 更新的审核状态信息
    """
    # 检查用户是否有权限更新审核状态（只有管理员可以）
    if current_user.current_role != "ADMIN":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以更新审核状态"
        )

    # 获取房源
    property_obj = await PropertyService.get_property(db=db, property_id=property_id)
    if not property_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房源不存在"
        )

    try:
        updated_property = await PropertyService.update_verification_status(
            db=db,
            property_id=property_id,
            status_data=status_data
        )
        return updated_property
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"更新审核状态失败: {str(e)}"
        )
