import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { wp, hp, fp } from '../../utils/responsive';

interface TagSelectorProps {
  selectedTags: string[];
  availableTags: string[];
  onTagSelect: (tag: string) => void;
  onTagRemove: (tag: string) => void;
  onRefreshTags: () => void;
  isGeneratingTags?: boolean;
  title?: string;
  refreshText?: string;
  maxSelectedTags?: number;
}

export const TagSelector: React.FC<TagSelectorProps> = ({
  selectedTags,
  availableTags,
  onTagSelect,
  onTagRemove,
  onRefreshTags,
  isGeneratingTags = false,
  title = "房源相关标签",
  refreshText = "不合适，换一换",
  maxSelectedTags = 10
}) => {
  const [showMaxTagsWarning, setShowMaxTagsWarning] = useState(false);
  
  const handleTagSelect = (tag: string) => {
    if (selectedTags.length < maxSelectedTags) {
      onTagSelect(tag);
    } else {
      // 如果已选满，显示提醒
      setShowMaxTagsWarning(true);
      // 3秒后自动隐藏提醒
      setTimeout(() => {
        setShowMaxTagsWarning(false);
      }, 3000);
    }
  };
  return (
    <View style={styles.container}>
      {/* 已选标签显示 */}
      {selectedTags.length > 0 && (
        <View style={styles.selectedTagsContainer}>
          <Text style={styles.selectedTagsTitle}>已选标签：</Text>
          <View style={styles.selectedTagsWrapper}>
            {selectedTags.map((tag, index) => (
              <View key={`selected-${tag}-${index}`} style={styles.selectedTagItem}>
                <Text style={styles.selectedTagText}>{tag}</Text>
                <TouchableOpacity
                  onPress={() => onTagRemove(tag)}
                  style={styles.deleteTagButton}
                >
                  <Ionicons name="close" size={wp(12)} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* 可选标签区域 */}
      <View style={styles.availableTagsSection}>
        <View style={styles.availableTagsHeader}>
          <Text style={styles.availableTagsTitle}>{title}：</Text>
          <View style={styles.tagActions}>
            <TouchableOpacity 
              onPress={onRefreshTags}
              disabled={isGeneratingTags}
            >
              <Text style={[
                styles.refreshText,
                isGeneratingTags && styles.refreshTextDisabled
              ]}>
                {isGeneratingTags ? '生成中...' : refreshText}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        {/* 10个标签上限提醒 - 只在点击相关标签时显示 */}
        {showMaxTagsWarning && (
          <Text style={styles.maxTagsWarning}>最多选择{maxSelectedTags}个标签，删除可替换！</Text>
        )}

        {/* 可选标签显示 */}
        {availableTags.length > 0 && (
          <View style={styles.availableTagsContainer}>
            {availableTags.map((tag, index) => (
              <TouchableOpacity
                key={`available-${tag}-${index}`}
                style={[
                  styles.availableTagItem,
                  selectedTags.length >= maxSelectedTags && styles.availableTagItemDisabled
                ]}
                onPress={() => handleTagSelect(tag)}
                disabled={false} // 移除禁用，让点击能够触发提醒
              >
                <Text style={[
                  styles.availableTagText,
                  selectedTags.length >= maxSelectedTags && styles.availableTagTextDisabled
                ]}>
                  {tag}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: hp(8)
  },
  
  // 已选标签样式
  selectedTagsContainer: {
    marginBottom: hp(16),
  },
  selectedTagsTitle: {
    fontSize: fp(14),
    fontWeight: '600',
    color: '#000000',
    marginBottom: hp(8),
  },
  selectedTagsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: wp(8),
  },
  selectedTagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF6B35',
    borderRadius: wp(16),
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
  },
  selectedTagText: {
    fontSize: fp(12),
    color: '#FFFFFF',
    fontWeight: '500',
    marginRight: wp(4),
  },
  deleteTagButton: {
    width: wp(16),
    height: wp(16),
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // 可选标签样式
  availableTagsSection: {
    marginTop: hp(8),
  },
  availableTagsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(12),
  },
  availableTagsTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#000000',
  },
  maxTagsWarning: {
    fontSize: fp(12),
    color: '#FF6B35',
    marginTop: hp(4),
    marginBottom: hp(8),
    fontWeight: '500',
  },
  tagActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  refreshText: {
    fontSize: fp(12),
    color: '#FF6B35',
    textDecorationLine: 'underline',
  },
  refreshTextDisabled: {
    color: '#CCCCCC',
  },
  availableTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: wp(8),
  },
  availableTagItem: {
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    borderRadius: wp(16),
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  availableTagItemDisabled: {
    backgroundColor: '#F5F5F5',
    borderColor: '#E0E0E0',
  },
  availableTagText: {
    fontSize: fp(12),
    color: '#666666',
    fontWeight: '500',
  },
  availableTagTextDisabled: {
    color: '#CCCCCC',
  },
  maxTagsHint: {
    fontSize: fp(11),
    color: '#999999',
    textAlign: 'center',
    marginTop: hp(8),
  }
});

export default TagSelector;
