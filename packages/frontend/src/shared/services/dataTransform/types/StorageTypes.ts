/**
 * 存储抽象层类型定义
 * 
 * 参考主流APP存储架构：
 * - 微信：双存储桶策略，敏感数据隔离
 * - 抖音：智能路由，根据内容类型选择存储策略
 * - 淘宝：多云存储支持，统一接口抽象
 * - 阿里云OSS：企业级对象存储最佳实践
 * 
 * 核心设计原则：
 * - 统一接口：屏蔽底层存储差异
 * - 智能路由：根据业务类型自动选择存储策略
 * - 权限控制：细粒度访问权限管理
 * - 多云支持：支持阿里云、腾讯云等多个云服务商
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月16日
 */

import { MediaBusinessType } from '../transformers/MediaTransformer';

/**
 * 存储提供商类型
 */
export type StorageProvider = 'aliyun-oss' | 'tencent-cos' | 'aws-s3' | 'local' | 'memory';

/**
 * 存储桶类型
 */
export type BucketType = 'main' | 'sensitive' | 'temp' | 'backup';

/**
 * 存储策略类型
 */
export type StorageStrategy = 'standard' | 'infrequent' | 'archive' | 'cold';

/**
 * 权限类型
 */
export type Permission = 'read' | 'write' | 'delete' | 'list' | 'admin';

/**
 * 存储配置
 */
export interface StorageConfig {
  provider: StorageProvider;
  bucket: string;
  bucketType: BucketType;
  region: string;
  endpoint: string;
  strategy: StorageStrategy;
  permissions: Permission[];
  encryption?: boolean;
  lifecycle?: LifecycleRule[];
}

/**
 * 生命周期规则
 */
export interface LifecycleRule {
  id: string;
  prefix: string;
  enabled: boolean;
  transitions: {
    days: number;
    storageClass: StorageStrategy;
  }[];
  expiration?: {
    days: number;
  };
}

/**
 * 上传选项
 */
export interface UploadOptions {
  contentType?: string;
  metadata?: Record<string, string>;
  tags?: Record<string, string>;
  storageClass?: StorageStrategy;
  encryption?: boolean;
  callback?: {
    url: string;
    body: string;
    contentType?: string;
  };
  progressCallback?: (progress: number) => void;
}

/**
 * 批量上传选项
 */
export interface BatchUploadOptions extends UploadOptions {
  concurrency?: number;
  retryCount?: number;
  failFast?: boolean;
}

/**
 * 下载选项
 */
export interface DownloadOptions {
  range?: {
    start: number;
    end: number;
  };
  responseHeaders?: Record<string, string>;
  progressCallback?: (progress: number) => void;
}

/**
 * 上传结果
 */
export interface UploadResult {
  success: boolean;
  objectKey: string;
  url: string;
  etag?: string;
  size: number;
  contentType: string;
  metadata?: Record<string, string>;
  error?: string;
}

/**
 * 批量上传结果
 */
export interface BatchUploadResult {
  success: boolean;
  results: UploadResult[];
  failed: {
    file: File;
    error: string;
  }[];
  totalCount: number;
  successCount: number;
  failedCount: number;
}

/**
 * 下载结果
 */
export interface DownloadResult {
  success: boolean;
  data: Blob | Buffer;
  contentType: string;
  size: number;
  metadata?: Record<string, string>;
  error?: string;
}

/**
 * 批量删除结果
 */
export interface BatchDeleteResult {
  success: boolean;
  deleted: string[];
  failed: {
    objectKey: string;
    error: string;
  }[];
  totalCount: number;
  deletedCount: number;
  failedCount: number;
}

/**
 * STS凭证
 */
export interface STSCredentials {
  accessKeyId: string;
  accessKeySecret: string;
  securityToken: string;
  expiration: string;
  bucket: string;
  region: string;
  endpoint: string;
  permissions: Permission[];
}

/**
 * 对象元数据
 */
export interface ObjectMetadata {
  objectKey: string;
  size: number;
  contentType: string;
  lastModified: Date;
  etag: string;
  storageClass: StorageStrategy;
  metadata?: Record<string, string>;
  tags?: Record<string, string>;
}

/**
 * 存储统计信息
 */
export interface StorageStats {
  totalObjects: number;
  totalSize: number;
  bucketStats: {
    [bucket: string]: {
      objectCount: number;
      size: number;
      storageClass: StorageStrategy;
    };
  };
  costEstimate?: {
    storage: number;
    requests: number;
    traffic: number;
    total: number;
  };
}

/**
 * 统一存储提供商接口
 */
export interface IStorageProvider {
  /**
   * 基础文件操作
   */
  upload(file: File, path: string, options?: UploadOptions): Promise<UploadResult>;
  download(objectKey: string, options?: DownloadOptions): Promise<DownloadResult>;
  delete(objectKey: string): Promise<boolean>;
  exists(objectKey: string): Promise<boolean>;
  
  /**
   * 批量操作
   */
  batchUpload(files: File[], paths: string[], options?: BatchUploadOptions): Promise<BatchUploadResult>;
  batchDelete(objectKeys: string[]): Promise<BatchDeleteResult>;
  
  /**
   * URL生成
   */
  getViewUrl(objectKey: string, expires?: number): Promise<string>;
  getDownloadUrl(objectKey: string, expires?: number): Promise<string>;
  
  /**
   * 权限管理
   */
  generateSTS(permissions: Permission[], expires?: number): Promise<STSCredentials>;
  generatePresignedUrl(objectKey: string, operation: 'GET' | 'PUT', expires?: number): Promise<string>;
  
  /**
   * 元数据操作
   */
  getMetadata(objectKey: string): Promise<ObjectMetadata>;
  updateMetadata(objectKey: string, metadata: Record<string, string>): Promise<boolean>;
  
  /**
   * 列表操作
   */
  listObjects(prefix?: string, maxKeys?: number): Promise<ObjectMetadata[]>;
  
  /**
   * 统计信息
   */
  getStats(): Promise<StorageStats>;
}

/**
 * 存储路由器接口
 */
export interface IStorageRouter {
  /**
   * 根据业务类型路由到对应的存储提供商
   */
  route(businessType: MediaBusinessType): IStorageProvider;
  
  /**
   * 根据对象键路由到对应的存储提供商
   */
  routeByPath(objectKey: string): IStorageProvider;
  
  /**
   * 获取存储配置
   */
  getConfig(businessType: MediaBusinessType): StorageConfig;
  
  /**
   * 注册存储提供商
   */
  registerProvider(type: BucketType, provider: IStorageProvider): void;
}

/**
 * 权限控制器接口
 */
export interface IPermissionController {
  /**
   * 检查上传权限
   */
  checkUploadPermission(userId: string, path: string, businessType: MediaBusinessType): Promise<void>;
  
  /**
   * 检查查看权限
   */
  checkViewPermission(objectKey: string, userId?: string): Promise<void>;
  
  /**
   * 检查删除权限
   */
  checkDeletePermission(objectKey: string, userId: string): Promise<void>;
  
  /**
   * 生成权限策略
   */
  generatePolicy(userId: string, businessType: MediaBusinessType): Promise<Permission[]>;
  
  /**
   * 验证权限
   */
  validatePermission(userId: string, objectKey: string, permission: Permission): Promise<boolean>;
}

/**
 * 存储事件类型
 */
export type StorageEventType = 'upload' | 'download' | 'delete' | 'error';

/**
 * 存储事件
 */
export interface StorageEvent {
  type: StorageEventType;
  objectKey: string;
  userId?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
  error?: string;
}

/**
 * 存储事件监听器接口
 */
export interface IStorageEventListener {
  onEvent(event: StorageEvent): Promise<void>;
}

/**
 * 存储监控接口
 */
export interface IStorageMonitor {
  /**
   * 添加事件监听器
   */
  addEventListener(listener: IStorageEventListener): void;
  
  /**
   * 移除事件监听器
   */
  removeEventListener(listener: IStorageEventListener): void;
  
  /**
   * 触发事件
   */
  emitEvent(event: StorageEvent): Promise<void>;
  
  /**
   * 获取监控指标
   */
  getMetrics(): Promise<{
    uploadCount: number;
    downloadCount: number;
    errorCount: number;
    averageResponseTime: number;
  }>;
}
