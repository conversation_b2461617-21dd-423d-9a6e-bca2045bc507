/**
 * DemandForm 验证逻辑Hook
 * @fileoverview 封装需求发布表单的验证逻辑
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🔒 功能保证：100%保留原有DemandFormScreen.tsx中的验证逻辑
 */

import { useCallback, useMemo } from 'react';
import { z } from 'zod';
import { validateChineseMobile } from '../../../../../shared/utils/phoneValidation';
import {
  useDemandFormData,
  useDemandFormValidation,
  useDemandFormActions,
} from '../stores/DemandFormStore';

// 导入类型
import type {
  ValidationRule,
  ValidationRules,
} from '../types/demandForm.types';

/**
 * 🔒 完整保留原有的zod验证Schema
 */
const DemandFormSchema = z.object({
  demandType: z.enum(['RENTAL', 'PURCHASE'], {
    required_error: '请选择需求类型',
  }),
  propertyType: z.array(z.string()).min(1, '请至少选择一种房源类型'),
  location: z.object({
    city: z.string().min(1, '请选择城市'),
    districts: z.array(z.string()).min(1, '请至少选择一个区域'),
    landmarks: z.array(z.string()).optional(),
  }),
  areaRange: z
    .object({
      min: z.number().min(0, '面积不能为负数'),
      max: z.number().min(0, '面积不能为负数'),
      unit: z.string(),
    })
    .refine(data => data.min <= data.max, {
      message: '最小面积不能大于最大面积',
    }),
  budgetRange: z
    .object({
      min: z.number().min(0, '预算不能为负数'),
      max: z.number().min(0, '预算不能为负数'),
      unit: z.string(),
    })
    .refine(data => data.min <= data.max, {
      message: '最小预算不能大于最大预算',
    }),
  industryType: z.array(z.string()).min(1, '请选择行业类型'),
  layoutType: z.array(z.string()).optional(),
  specialRequirements: z.string().optional(),
  contactInfo: z.object({
    phone: z
      .string()
      .min(11, '手机号必须为11位数字')
      .max(11, '手机号必须为11位数字')
      .refine(phone => validateChineseMobile(phone), {
        message: '请输入正确的中国大陆手机号码',
      }),
    surname: z.string().min(1, '请输入姓氏').max(5, '姓氏不能超过5个字符'),
    gender: z.enum(['male', 'female'], { required_error: '请选择性别' }),
    wechat: z.string().optional(),
    preferredContactTime: z.string(),
    contactMethod: z.string(),
    additionalContacts: z
      .array(
        z.object({
          phone: z
            .string()
            .min(11, '手机号必须为11位数字')
            .max(11, '手机号必须为11位数字')
            .refine(phone => validateChineseMobile(phone), {
              message: '请输入正确的中国大陆手机号码',
            }),
          verificationCode: z.string().min(6, '验证码必须为6位数字'),
          surname: z
            .string()
            .min(1, '请输入姓氏')
            .max(5, '姓氏不能超过5个字符'),
          gender: z.enum(['male', 'female'], { required_error: '请选择性别' }),
          isVerified: z.boolean().default(false),
        })
      )
      .max(2, '最多只能添加2位联络人')
      .optional(),
  }),
  agreeToRules: z.boolean().refine(val => val === true, {
    message: '必须同意发布规则才能提交',
  }),
});

/**
 * 验证逻辑Hook - 完整保留原有DemandFormScreen.tsx的验证逻辑
 */
export const useDemandValidation = () => {
  // === Store状态 ===
  const formData = useDemandFormData();
  const validation = useDemandFormValidation();
  const actions = useDemandFormActions();

  // === 验证规则定义 - 保留原有逻辑 ===
  const validationRules: ValidationRules = useMemo(
    () => ({
      propertyType: {
        required: true,
        custom: (value: string[]) => {
          if (!value || value.length === 0) {
            return '请至少选择一种房源类型';
          }
          return null;
        },
      },
      'location.districts': {
        required: true,
        custom: (value: string[]) => {
          if (!value || value.length === 0) {
            return '请至少选择一个区域';
          }
          return null;
        },
      },
      industryType: {
        required: true,
        custom: (value: string[]) => {
          if (!value || value.length === 0) {
            return '请选择行业类型';
          }
          return null;
        },
      },
      'contactInfo.surname': {
        required: true,
        minLength: 1,
        maxLength: 5,
        custom: (value: string) => {
          if (!value || !value.trim()) {
            return '请输入姓氏';
          }
          if (value.trim().length > 5) {
            return '姓氏不能超过5个字符';
          }
          return null;
        },
      },
      'contactInfo.phone': {
        required: true,
        custom: (value: string) => {
          if (!value || !value.trim()) {
            return '请输入联系电话';
          }
          if (!validateChineseMobile(value)) {
            return '请输入正确的中国大陆手机号码';
          }
          return null;
        },
      },
      'contactInfo.gender': {
        required: true,
        custom: (value: string) => {
          if (!value) {
            return '请选择性别';
          }
          return null;
        },
      },
      areaRange: {
        custom: (value: { min: number; max: number }) => {
          if (value.min < 0 || value.max < 0) {
            return '面积不能为负数';
          }
          if (value.min > 0 && value.max > 0 && value.min > value.max) {
            return '最小面积不能大于最大面积';
          }
          return null;
        },
      },
      budgetRange: {
        custom: (value: { min: number; max: number }) => {
          if (value.min < 0 || value.max < 0) {
            return '预算不能为负数';
          }
          if (value.min > 0 && value.max > 0 && value.min > value.max) {
            return '最小预算不能大于最大预算';
          }
          return null;
        },
      },
      agreeToRules: {
        required: true,
        custom: (value: boolean) => {
          if (!value) {
            return '必须同意发布规则才能提交';
          }
          return null;
        },
      },
    }),
    []
  );

  // === 获取嵌套字段值 - 保留原有工具函数 ===
  const getNestedValue = useCallback((obj: any, path: string) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }, []);

  // === 验证单个字段 - 保留原有validateSingleField逻辑 ===
  const validateField = useCallback(
    (fieldPath: string, value?: any): string | null => {
      console.log(`🔍 [验证] 验证字段: ${fieldPath}`, value);

      const rule = validationRules[fieldPath];
      if (!rule) return null;

      const fieldValue =
        value !== undefined ? value : getNestedValue(formData, fieldPath);

      // 必填验证
      if (
        rule.required &&
        (!fieldValue || (Array.isArray(fieldValue) && fieldValue.length === 0))
      ) {
        return '此字段为必填项';
      }

      // 长度验证
      if (rule.minLength && fieldValue && fieldValue.length < rule.minLength) {
        return `最少需要${rule.minLength}个字符`;
      }

      if (rule.maxLength && fieldValue && fieldValue.length > rule.maxLength) {
        return `最多允许${rule.maxLength}个字符`;
      }

      // 正则验证
      if (rule.pattern && fieldValue && !rule.pattern.test(fieldValue)) {
        return '格式不正确';
      }

      // 自定义验证
      if (rule.custom) {
        return rule.custom(fieldValue);
      }

      return null;
    },
    [validationRules, getNestedValue, formData]
  );

  // === 验证所有字段 - 保留原有逻辑 ===
  const validateAllFields = useCallback(() => {
    const errors: Record<string, string> = {};

    Object.keys(validationRules).forEach(fieldPath => {
      const error = validateField(fieldPath);
      if (error) {
        errors[fieldPath] = error;
      }
    });

    actions.setFormErrors(errors);
    return Object.keys(errors).length === 0;
  }, [validationRules, validateField, actions]);

  // === 智能表单验证 - 保留原有validateFormWithSmartScroll逻辑 ===
  const validateFormWithSmartScroll = useCallback(
    async (data: any) => {
      console.log('🔍 [验证] 开始智能表单验证:', data);

      try {
        // 使用zod进行完整验证
        const validationResult = DemandFormSchema.safeParse(data);

        if (!validationResult.success) {
          const errors: Record<string, string> = {};

          validationResult.error.errors.forEach(error => {
            const fieldPath = error.path.join('.');
            errors[fieldPath] = error.message;
          });

          actions.setFormErrors(errors);

          console.log('❌ [验证] 表单验证失败:', errors);
          return { isValid: false, errors };
        }

        console.log('✅ [验证] 表单验证成功');
        actions.setFormErrors({});
        return { isValid: true, errors: {} };
      } catch (error) {
        console.error('❌ [验证] 验证过程出错:', error);
        const errorMessage = '验证过程出错，请重试';
        actions.setFormErrors({ general: errorMessage });
        return { isValid: false, errors: { general: errorMessage } };
      }
    },
    [actions]
  );

  // === 验证当前步骤 - 保留原有逻辑 ===
  const validateCurrentStep = useCallback(
    (currentStep: number) => {
      const stepFields: Record<number, string[]> = {
        1: ['propertyType'],
        2: ['location.districts'],
        3: ['areaRange', 'budgetRange', 'industryType'],
        4: [], // 详细要求为可选
        5: ['contactInfo.surname', 'contactInfo.phone', 'contactInfo.gender'],
        6: ['agreeToRules'], // 标签和协议
      };

      const currentStepFields = stepFields[currentStep] || [];
      const errors: Record<string, string> = {};

      currentStepFields.forEach(fieldPath => {
        const error = validateField(fieldPath);
        if (error) {
          errors[fieldPath] = error;
        }
      });

      // 更新错误状态
      Object.keys(errors).forEach(field => {
        actions.setFieldError(field, errors[field]);
      });

      return Object.keys(errors).length === 0;
    },
    [validateField, actions]
  );

  // === 实时验证字段更新 - 保留原有updateFieldError逻辑 ===
  const updateFieldError = useCallback(
    (fieldName: string, value: any) => {
      const error = validateField(fieldName, value);

      if (error) {
        actions.setFieldError(fieldName, error);
      } else {
        actions.clearFieldError(fieldName);
      }

      // 标记字段为已触摸
      actions.markFieldTouched(fieldName);

      console.log(`🔍 [实时验证] ${fieldName}:`, { value, error });
    },
    [validateField, actions]
  );

  // === 清除所有验证错误 ===
  const clearAllErrors = useCallback(() => {
    actions.setFormErrors({});
  }, [actions]);

  // === 检查字段是否有错误 ===
  const hasFieldError = useCallback(
    (fieldPath: string): boolean => {
      return (
        !!validation.formErrors[fieldPath] ||
        !!validation.validationErrors[fieldPath]
      );
    },
    [validation.formErrors, validation.validationErrors]
  );

  // === 获取字段错误信息 ===
  const getFieldError = useCallback(
    (fieldPath: string): string | undefined => {
      return (
        validation.formErrors[fieldPath] ||
        validation.validationErrors[fieldPath]
      );
    },
    [validation.formErrors, validation.validationErrors]
  );

  // === 返回接口 ===
  return {
    // === 验证方法 ===
    validateField,
    validateAllFields,
    validateFormWithSmartScroll,
    validateCurrentStep,
    updateFieldError,
    clearAllErrors,

    // === 状态查询方法 ===
    hasFieldError,
    getFieldError,

    // === 验证规则 ===
    validationRules,

    // === 验证状态 ===
    formErrors: validation.formErrors,
    validationErrors: validation.validationErrors, // 兼容原有命名
    touchedFields: validation.touchedFields,
    isValid: validation.isValid,

    // === zod Schema（用于完整验证） ===
    DemandFormSchema,
  };
};
