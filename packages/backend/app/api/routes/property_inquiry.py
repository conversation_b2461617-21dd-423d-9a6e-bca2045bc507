"""
房源咨询API路由
实现企业级房源咨询消息系统的REST API接口
"""

import uuid
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field
from sqlmodel import Session
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.db import get_async_session
from app.api.deps import CurrentUser, get_current_active_superuser
from app.models.user import User
from app.models.property_inquiry import InquiryType, InquiryStatus, PropertyInquiry
from app.models.property.property import Property
from app.services.property_inquiry_service import PropertyInquiryService
from app.services.paid_sms_service import PaidSMSService
from app.services.notification_service import NotificationService


router = APIRouter()


# 请求和响应模型
class CreateInquiryRequest(BaseModel):
    """创建咨询请求"""
    property_id: str = Field(..., description="房源ID")
    message_content: str = Field(..., min_length=1, max_length=2000, description="咨询内容")
    inquiry_type: InquiryType = Field(default=InquiryType.MESSAGED, description="咨询类型")


class CreateInquiryResponse(BaseModel):
    """创建咨询响应"""
    success: bool
    inquiry_id: Optional[str] = None
    existing_inquiry_id: Optional[str] = None  # 添加existing_inquiry_id字段
    is_free_slot: Optional[bool] = None
    free_slot_number: Optional[int] = None
    requires_payment: Optional[bool] = None
    remaining_slots: Optional[int] = None
    message: Optional[str] = None
    error: Optional[str] = None


class InquiryDetailResponse(BaseModel):
    """咨询详情响应"""
    success: bool
    inquiry: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class InquiryListResponse(BaseModel):
    """咨询列表响应"""
    success: bool
    inquiries: Optional[List[Dict[str, Any]]] = None
    pagination: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class InquiryStatsResponse(BaseModel):
    """咨询统计响应"""
    success: bool
    property_id: Optional[str] = None
    quota: Optional[Dict[str, Any]] = None
    stats: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


# 服务依赖
def get_property_inquiry_service(session: AsyncSession = Depends(get_async_session)) -> PropertyInquiryService:
    """获取房源咨询服务"""
    notification_service = NotificationService(session)
    return PropertyInquiryService(session, notification_service)


def get_paid_sms_service(session: AsyncSession = Depends(get_async_session)) -> PaidSMSService:
    """获取付费短信服务"""
    return PaidSMSService(session)


@router.post("/inquiries", response_model=CreateInquiryResponse)
async def create_inquiry(
    request: CreateInquiryRequest,
    current_user: CurrentUser,
    inquiry_service: PropertyInquiryService = Depends(get_property_inquiry_service)
):
    """
    创建房源咨询
    
    - **property_id**: 房源ID
    - **message_content**: 咨询内容
    - **inquiry_type**: 咨询类型 (VIEWED/FAVORITED/MESSAGED)
    
    返回创建结果和免费名额信息
    """
    try:
        # 转换property_id为UUID
        property_uuid = uuid.UUID(request.property_id)
        
        # 调用服务创建咨询
        result = await inquiry_service.create_inquiry(
            property_id=property_uuid,
            tenant_id=current_user.id,
            message_content=request.message_content,
            inquiry_type=request.inquiry_type
        )
        
        if result["success"]:
            return CreateInquiryResponse(
                success=True,
                inquiry_id=result["inquiry_id"],
                is_free_slot=result["is_free_slot"],
                free_slot_number=result.get("free_slot_number"),
                requires_payment=result["requires_payment"],
                remaining_slots=result["remaining_slots"],
                message="咨询已创建，业主将收到通知"
            )
        else:
            # 检查是否是重复咨询，如果是则返回existing_inquiry_id
            response = CreateInquiryResponse(
                success=False,
                error=result["error"]
            )
            
            # 如果有existing_inquiry_id，添加到响应中
            if "existing_inquiry_id" in result:
                response.existing_inquiry_id = result["existing_inquiry_id"]
                
            return response
            
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的房源ID: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建咨询失败: {str(e)}"
        )


@router.get("/inquiries/{inquiry_id}", response_model=InquiryDetailResponse)
async def get_inquiry_detail(
    inquiry_id: str,
    current_user: CurrentUser,
    inquiry_service: PropertyInquiryService = Depends(get_property_inquiry_service)
):
    """
    获取咨询详情
    
    - **inquiry_id**: 咨询ID
    
    只有业主可以查看咨询详情，如果是付费咨询需要扣费
    """
    try:
        # 调用服务查看咨询
        result = await inquiry_service.view_inquiry(
            inquiry_id=inquiry_id,
            landlord_id=current_user.id
        )
        
        if result["success"]:
            return InquiryDetailResponse(
                success=True,
                inquiry=result["inquiry"]
            )
        else:
            # 检查是否需要付费
            if result.get("requires_payment"):
                raise HTTPException(
                    status_code=status.HTTP_402_PAYMENT_REQUIRED,
                    detail={
                        "error": result["error"],
                        "requires_payment": True,
                        "payment_amount": result.get("payment_amount", 5.0)
                    }
                )
            else:
                return InquiryDetailResponse(
                    success=False,
                    error=result["error"]
                )
                
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取咨询详情失败: {str(e)}"
        )


@router.get("/inquiries/landlord/list", response_model=InquiryListResponse)
async def get_landlord_inquiries(
    current_user: CurrentUser,
    inquiry_service: PropertyInquiryService = Depends(get_property_inquiry_service),
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    status_filter: Optional[InquiryStatus] = Query(default=None, description="状态过滤")
):
    """
    获取业主的咨询列表
    
    - **page**: 页码 (默认1)
    - **page_size**: 每页数量 (默认20，最大100)
    - **status_filter**: 状态过滤 (可选)
    
    返回分页的咨询列表
    """
    try:
        # 调用服务获取咨询列表
        result = await inquiry_service.get_landlord_inquiries(
            landlord_id=current_user.id,
            page=page,
            page_size=page_size,
            status_filter=status_filter
        )
        
        if result["success"]:
            return InquiryListResponse(
                success=True,
                inquiries=result["inquiries"],
                pagination=result["pagination"]
            )
        else:
            return InquiryListResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取咨询列表失败: {str(e)}"
        )


@router.get("/inquiries/stats/{property_id}", response_model=InquiryStatsResponse)
async def get_inquiry_stats(
    property_id: str,
    current_user: CurrentUser,
    inquiry_service: PropertyInquiryService = Depends(get_property_inquiry_service)
):
    """
    获取房源咨询统计
    
    - **property_id**: 房源ID
    
    返回该房源的咨询统计信息，包括免费名额使用情况
    """
    try:
        # 转换property_id为UUID
        property_uuid = uuid.UUID(property_id)
        
        # 调用服务获取咨询统计
        result = await inquiry_service.get_inquiry_stats(
            property_id=property_uuid,
            landlord_id=current_user.id
        )
        
        if result["success"]:
            return InquiryStatsResponse(
                success=True,
                property_id=result["property_id"],
                quota=result["quota"],
                stats=result["stats"]
            )
        else:
            return InquiryStatsResponse(
                success=False,
                error=result["error"]
            )
            
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的房源ID: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取咨询统计失败: {str(e)}"
        )


@router.post("/inquiries/{inquiry_id}/pay")
async def pay_for_inquiry(
    inquiry_id: str,
    current_user: CurrentUser,
    inquiry_service: PropertyInquiryService = Depends(get_property_inquiry_service),
    sms_service: PaidSMSService = Depends(get_paid_sms_service)
):
    """
    付费查看咨询
    
    - **inquiry_id**: 咨询ID
    
    扣除积分后允许业主查看付费咨询的详情
    """
    try:
        # 1. 先检查咨询是否存在和需要付费
        pre_check = await inquiry_service.view_inquiry(
            inquiry_id=inquiry_id,
            landlord_id=current_user.id
        )
        
        if pre_check["success"]:
            # 如果已经可以查看，直接返回
            return {
                "success": True,
                "message": "咨询已可查看",
                "inquiry": pre_check["inquiry"]
            }
        
        if not pre_check.get("requires_payment"):
            # 如果不需要付费但查看失败，返回错误
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=pre_check["error"]
            )
        
        # 2. 检查用户积分余额
        quota = await sms_service.get_user_sms_quota(current_user.id)
        required_credits = 5  # 查看一条咨询需要5积分
        
        if quota["total_available"] < required_credits:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail={
                    "error": "积分不足",
                    "required_credits": required_credits,
                    "available_credits": quota["total_available"]
                }
            )
        
        # 3. 扣除积分 (模拟发送短信通知)
        sms_result = await sms_service.send_notification_sms(
            user_id=current_user.id,
            sms_type="inquiry_viewed",  # 自定义类型
            phone_number=current_user.phone,
            template_code="SMS_INQUIRY_PAYMENT",
            template_params={
                "inquiry_id": inquiry_id,
                "cost": required_credits
            }
        )
        
        if not sms_result["success"]:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail=f"扣费失败: {sms_result['error']}"
            )
        
        # 4. 标记咨询为已付费查看 (这里需要在实际实现中添加付费记录)
        # TODO: 添加付费记录到数据库
        
        # 5. 再次获取咨询详情
        final_result = await inquiry_service.view_inquiry(
            inquiry_id=inquiry_id,
            landlord_id=current_user.id
        )
        
        return {
            "success": True,
            "message": "付费成功，咨询已解锁",
            "payment_info": {
                "cost_credits": required_credits,
                "remaining_credits": quota["total_available"] - required_credits
            },
            "inquiry": final_result.get("inquiry")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"付费查看失败: {str(e)}"
        )


@router.get("/inquiries/types")
async def get_inquiry_types():
    """
    获取咨询类型列表
    
    返回所有可用的咨询类型
    """
    return {
        "success": True,
        "inquiry_types": [
            {
                "value": InquiryType.VIEWED,
                "label": "查看房源",
                "description": "用户查看了房源详情"
            },
            {
                "value": InquiryType.FAVORITED,
                "label": "收藏房源",
                "description": "用户收藏了房源"
            },
            {
                "value": InquiryType.MESSAGED,
                "label": "发送消息",
                "description": "用户发送了咨询消息"
            }
        ]
    }


@router.get("/inquiries/statuses")
async def get_inquiry_statuses():
    """
    获取咨询状态列表
    
    返回所有可用的咨询状态
    """
    return {
        "success": True,
        "inquiry_statuses": [
            {
                "value": InquiryStatus.PENDING,
                "label": "待处理",
                "description": "咨询已创建，等待业主处理"
            },
            {
                "value": InquiryStatus.VIEWED,
                "label": "已查看",
                "description": "业主已查看咨询"
            },
            {
                "value": InquiryStatus.REPLIED,
                "label": "已回复",
                "description": "业主已回复咨询"
            },
            {
                "value": InquiryStatus.CONTACT_EXCHANGED,
                "label": "已交换联系方式",
                "description": "双方已交换联系方式"
            },
            {
                "value": InquiryStatus.CLOSED,
                "label": "已关闭",
                "description": "咨询已关闭"
            }
        ]
    }


# 获取聊天信息端点
@router.get("/inquiries/{inquiry_id}/chat-info")
async def get_chat_info(
    inquiry_id: str,
    current_user: CurrentUser,
    db: AsyncSession = Depends(get_async_session)
):
    """
    获取聊天所需信息
    
    - **inquiry_id**: 咨询ID
    
    返回聊天房间、业主信息、房源信息等
    """
    try:
        from sqlmodel import select
        
        # 查询咨询记录
        inquiry_result = await db.exec(
            select(PropertyInquiry)
            .where(PropertyInquiry.id == inquiry_id)
        )
        inquiry = inquiry_result.first()
        
        if not inquiry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="咨询记录不存在"
            )
        
        # 验证用户权限（只有咨询双方可以查看）
        if inquiry.tenant_id != current_user.id and inquiry.landlord_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此咨询记录"
            )
        
        # 查询房源信息
        property_result = await db.exec(
            select(Property)
            .where(Property.id == inquiry.property_id)
        )
        property_info = property_result.first()
        
        if not property_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="房源不存在"
            )
        
        # 查询业主信息
        landlord_result = await db.exec(
            select(User)
            .where(User.id == inquiry.landlord_id)
        )
        landlord = landlord_result.first()
        
        if not landlord:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="业主信息不存在"
            )
        
        # 查询租客信息
        tenant_result = await db.exec(
            select(User)
            .where(User.id == inquiry.tenant_id)
        )
        tenant = tenant_result.first()
        
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="租客信息不存在"
            )
        
        # 返回聊天信息
        return {
            "success": True,
            "inquiry_id": inquiry.id,
            "chat_room_id": f"inquiry_{inquiry.id}",  # 使用咨询ID作为聊天房间ID
            "landlord_info": {
                "id": str(landlord.id),
                "name": landlord.name or landlord.phone,
                "avatar": landlord.avatar_url,
                "phone": landlord.phone
            },
            "tenant_info": {
                "id": str(tenant.id),
                "name": tenant.name or tenant.phone,
                "avatar": tenant.avatar_url,
                "phone": tenant.phone
            },
            "property_info": {
                "id": str(property_info.id),
                "title": property_info.title,
                "location": f"{property_info.city}{property_info.district}",
                "price": f"{property_info.price}元/月" if property_info.listing_type == "rent" else f"{property_info.price}万",
                "area": f"{property_info.area}㎡",
                "property_type": property_info.property_type,
                "cover_image": property_info.main_image_url
            },
            "inquiry_info": {
                "id": str(inquiry.id),
                "status": inquiry.status,
                "message_content": inquiry.message_content,
                "created_at": inquiry.created_at.isoformat(),
                "is_free_slot": inquiry.is_free_slot,
                "requires_payment": inquiry.requires_payment
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取聊天信息失败: {str(e)}"
        )


# 健康检查端点
@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "property_inquiry_api",
        "version": "1.0.0"
    }