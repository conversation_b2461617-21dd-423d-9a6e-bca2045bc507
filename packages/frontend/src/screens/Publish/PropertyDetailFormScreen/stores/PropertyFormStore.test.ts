/**
 * PropertyFormStore基础功能验证测试
 * 
 * 验证项目：
 * - Store初始化状态
 * - 字段更新功能
 * - 验证逻辑
 * - UI状态管理
 * - 媒体文件操作
 * - 草稿管理功能
 */

import { act, renderHook } from '@testing-library/react-native';
import { usePropertyFormStore } from './PropertyFormStore';

// 模拟storageService
jest.mock('../../../../shared/services/storage', () => ({
  storageService: {
    getUIState: jest.fn().mockResolvedValue(null),
    storeUIState: jest.fn().mockResolvedValue(undefined),
  },
}));

describe('PropertyFormStore 基础功能验证', () => {
  beforeEach(() => {
    // 重置store状态
    const { result } = renderHook(() => usePropertyFormStore());
    act(() => {
      result.current.resetForm();
    });
  });

  describe('初始化状态验证', () => {
    it('应该有正确的初始状态', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      expect(result.current.formData.title).toBe('');
      expect(result.current.formData.description).toBe('');
      expect(result.current.formData.transaction_types).toEqual([]);
      expect(result.current.errors).toEqual({});
      expect(result.current.isFormValid).toBe(false);
      expect(result.current.ui.isSubmitting).toBe(false);
      expect(result.current.media.localMediaFiles).toEqual([]);
    });
  });

  describe('字段更新功能验证', () => {
    it('应该能更新单个字段', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      act(() => {
        result.current.updateField('title', '测试房源标题');
      });
      
      expect(result.current.formData.title).toBe('测试房源标题');
      expect(result.current.isDraftSaved).toBe(false);
    });

    it('应该能更新多个字段', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      act(() => {
        result.current.updateField('title', '测试房源');
        result.current.updateField('area', '100');
        result.current.updateField('sub_type', '1室1厅');
      });
      
      expect(result.current.formData.title).toBe('测试房源');
      expect(result.current.formData.area).toBe('100');
      expect(result.current.formData.sub_type).toBe('1室1厅');
    });

    it('应该能加载表单数据', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      const testData = {
        title: '预置标题',
        description: '预置描述',
        area: '120',
      };
      
      act(() => {
        result.current.loadFormData(testData);
      });
      
      expect(result.current.formData.title).toBe('预置标题');
      expect(result.current.formData.description).toBe('预置描述');
      expect(result.current.formData.area).toBe('120');
    });
  });

  describe('验证功能验证', () => {
    it('应该能设置字段错误', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      act(() => {
        result.current.setError('title', '标题不能为空');
      });
      
      expect(result.current.errors.title).toBe('标题不能为空');
      expect(result.current.isFormValid).toBe(false);
    });

    it('应该能清除字段错误', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      act(() => {
        result.current.setError('title', '标题错误');
        result.current.clearError('title');
      });
      
      expect(result.current.errors.title).toBeUndefined();
      expect(result.current.isFormValid).toBe(true);
    });

    it('应该能清除所有错误', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      act(() => {
        result.current.setError('title', '标题错误');
        result.current.setError('description', '描述错误');
        result.current.clearAllErrors();
      });
      
      expect(result.current.errors).toEqual({});
      expect(result.current.isFormValid).toBe(true);
    });

    it('必填字段验证应该工作', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      // 尝试更新为空值
      act(() => {
        result.current.updateField('title', '');
      });
      
      expect(result.current.errors.title).toBe('此字段为必填项');
    });

    it('字段长度验证应该工作', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      // 标题过长
      act(() => {
        result.current.updateField('title', 'a'.repeat(60));
      });
      
      expect(result.current.errors.title).toBe('标题长度不能超过50个字符');
    });
  });

  describe('UI状态管理验证', () => {
    it('应该能设置下拉菜单状态', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      act(() => {
        result.current.setDropdown('sub_type');
      });
      
      expect(result.current.ui.showDropdown).toBe('sub_type');
      
      act(() => {
        result.current.setDropdown(null);
      });
      
      expect(result.current.ui.showDropdown).toBe(null);
    });

    it('应该能设置提交状态', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      act(() => {
        result.current.setSubmitting(true);
      });
      
      expect(result.current.ui.isSubmitting).toBe(true);
      
      act(() => {
        result.current.setSubmitting(false);
      });
      
      expect(result.current.ui.isSubmitting).toBe(false);
    });

    it('应该能设置上传状态', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      act(() => {
        result.current.setUploadingMedia(true);
        result.current.setUploadProgress(50);
      });
      
      expect(result.current.ui.isUploadingMedia).toBe(true);
      expect(result.current.ui.uploadProgress).toBe(50);
    });

    it('应该能更新步骤', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      act(() => {
        result.current.updateStep(2);
      });
      
      expect(result.current.ui.currentStep).toBe(2);
    });
  });

  describe('媒体文件操作验证', () => {
    it('应该能添加本地媒体文件', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      const testFile = { uri: 'test://image.jpg', type: 'image' };
      
      act(() => {
        result.current.addLocalMediaFile(testFile);
      });
      
      expect(result.current.media.localMediaFiles).toContain(testFile);
      expect(result.current.isDraftSaved).toBe(false);
    });

    it('应该能移除本地媒体文件', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      const testFile1 = { uri: 'test://image1.jpg', type: 'image' };
      const testFile2 = { uri: 'test://image2.jpg', type: 'image' };
      
      act(() => {
        result.current.addLocalMediaFile(testFile1);
        result.current.addLocalMediaFile(testFile2);
      });
      
      expect(result.current.media.localMediaFiles).toHaveLength(2);
      
      act(() => {
        result.current.removeLocalMediaFile(0);
      });
      
      expect(result.current.media.localMediaFiles).toHaveLength(1);
      expect(result.current.media.localMediaFiles[0]).toEqual(testFile2);
    });

    it('应该能添加已上传文件', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      const uploadedFile = { url: 'https://example.com/image.jpg', id: '123' };
      
      act(() => {
        result.current.addUploadedFile(uploadedFile);
      });
      
      expect(result.current.media.uploadedFiles).toContain(uploadedFile);
    });

    it('应该能标记上传失败', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      const failedFile = { uri: 'test://image.jpg' };
      const error = '上传失败';
      
      act(() => {
        result.current.markUploadFailed(failedFile, error);
      });
      
      expect(result.current.media.failedUploads).toContainEqual({
        file: failedFile,
        error: error,
      });
    });

    it('应该能清除媒体文件', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      act(() => {
        result.current.addLocalMediaFile({ uri: 'test://image.jpg' });
        result.current.addUploadedFile({ url: 'https://example.com/image.jpg' });
        result.current.clearMediaFiles();
      });
      
      expect(result.current.media.localMediaFiles).toEqual([]);
      expect(result.current.media.uploadedFiles).toEqual([]);
      expect(result.current.media.failedUploads).toEqual([]);
    });
  });

  describe('交易类型操作验证', () => {
    it('应该能切换交易类型', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      act(() => {
        result.current.toggleTransactionType('RENT');
      });
      
      expect(result.current.formData.transaction_types).toContain('RENT');
      
      act(() => {
        result.current.toggleTransactionType('SALE');
      });
      
      expect(result.current.formData.transaction_types).toContain('SALE');
      expect(result.current.formData.transaction_types).toContain('RENT');
      
      act(() => {
        result.current.toggleTransactionType('RENT');
      });
      
      expect(result.current.formData.transaction_types).not.toContain('RENT');
      expect(result.current.formData.transaction_types).toContain('SALE');
    });
  });

  describe('表单重置功能验证', () => {
    it('应该能完全重置表单', () => {
      const { result } = renderHook(() => usePropertyFormStore());
      
      // 设置一些状态
      act(() => {
        result.current.updateField('title', '测试标题');
        result.current.setError('description', '描述错误');
        result.current.addLocalMediaFile({ uri: 'test://image.jpg' });
        result.current.setSubmitting(true);
        result.current.resetForm();
      });
      
      expect(result.current.formData.title).toBe('');
      expect(result.current.errors).toEqual({});
      expect(result.current.media.localMediaFiles).toEqual([]);
      expect(result.current.ui.isSubmitting).toBe(false);
      expect(result.current.isDraftSaved).toBe(false);
      expect(result.current.draftId).toBe(null);
    });
  });
});

console.log('✅ PropertyFormStore基础功能验证测试文件创建完成');