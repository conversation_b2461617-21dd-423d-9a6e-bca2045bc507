/**
 * 🏗️ 企业级架构：通用Tab列表组件类型定义
 * 遵循前端企业级架构规范 - 类型安全层
 */

export type TabStatus = 'active' | 'draft' | 'inactive' | 'published';

export interface TabConfig {
  key: TabStatus;
  title: string;
  count: number;
}

export interface SearchConfig {
  placeholder: string;
  searchFields: string[]; // 搜索的字段名
  filterFn?: (item: any, searchText: string) => boolean; // 自定义过滤函数
}

export interface UniversalTabListProps<T = any> {
  // 🎯 核心配置
  type: 'property' | 'demand'; // 区分房源和需求
  tabs: TabConfig[];
  
  // 🔍 搜索配置
  searchConfig: SearchConfig;
  onSearch: (searchText: string, activeTab: TabStatus) => void;
  
  // 📊 数据配置
  dataFetcher: (status: TabStatus) => Promise<T[]>;
  renderItem: (item: T) => React.ReactElement;
  keyExtractor: (item: T) => string;
  
  // 🎨 UI配置
  headerComponent?: React.ComponentType;
  emptyComponent?: React.ComponentType<{ status: TabStatus }>;
  loadingComponent?: React.ComponentType;
  
  // 📱 导航配置
  navigation?: any;
  initialTab?: TabStatus;
  
  // 🔧 事件回调
  onTabChange?: (tab: TabStatus) => void;
  onItemPress?: (item: T) => void;
  onRefresh?: (tab: TabStatus) => Promise<void>;
}

export interface TabListState {
  activeTab: TabStatus;
  currentPage: number;
  searchText: string;
  isSearching: boolean;
  searchResults: any[];
}

export interface UseTabListLogicProps {
  type: 'property' | 'demand';
  tabs: TabConfig[];
  initialTab?: TabStatus;
  onTabChange?: (tab: TabStatus) => void;
}

export interface UseUniversalSearchProps {
  type: 'property' | 'demand';
  searchConfig: SearchConfig;
  dataFetcher: (status: TabStatus) => Promise<any[]>;
  onSearchResults?: (results: any[], searchText: string) => void;
}

// 🏗️ 企业级架构：主流APP参考的搜索模式
export interface SearchModalProps {
  visible: boolean;
  onClose: () => void;
  type: 'property' | 'demand';
  searchConfig: SearchConfig;
  onSearch: (searchText: string) => void;
  recentSearches?: string[];
  hotSearches?: string[];
  // 🔍 搜索结果相关
  searchResults?: any[];
  isSearching?: boolean;
  hasSearched?: boolean;
  searchText?: string;
  // 🔧 列表渲染相关
  renderItem?: (item: any) => React.ReactElement;
  keyExtractor?: (item: any) => string;
  onItemPress?: (item: any) => void;
}

// 🎨 主题配置（参考微信、支付宝的设计规范）
export interface TabListTheme {
  primary: string;
  secondary: string;
  background: string;
  text: {
    primary: string;
    secondary: string;
    active: string;
  };
  tab: {
    activeBackground: string;
    inactiveBackground: string;
    borderColor: string;
  };
  search: {
    background: string;
    iconColor: string;
    placeholderColor: string;
  };
}

export const defaultTheme: TabListTheme = {
  primary: '#FF6B35',
  secondary: '#999999',
  background: '#FFFFFF', // 改为白色背景，参考闲鱼风格
  text: {
    primary: '#333333',
    secondary: '#666666',
    active: '#FF6B35',
  },
  tab: {
    activeBackground: '#FFFFFF',
    inactiveBackground: 'transparent',
    borderColor: '#FF6B35',
  },
  search: {
    background: '#F5F5F5',
    iconColor: '#666666',
    placeholderColor: '#999999',
  },
};
