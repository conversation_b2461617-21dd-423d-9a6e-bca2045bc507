/**
 * 消息系统功能完整性测试
 * 验证重构后的消息系统所有功能是否正常
 */
import { renderHook, act } from '@testing-library/react-native';
import { useMessageLogic } from '../hooks/useMessageLogic';
import { useMessageUI } from '../hooks/useMessageUI';
import { useRoleSwitch } from '../hooks/useRoleSwitch';
import { getMessageTransformers } from '../transformers/TransformerFactory';
import type { MessageQueryInput, MessageData } from '../types/message.types';

// Mock API services
jest.mock('../services/messageAPI');
jest.mock('../services/smsAPI');

describe('消息系统功能完整性测试', () => {
  
  describe('1. 双角色消息中心功能', () => {
    test('应该支持角色切换', () => {
      const { result } = renderHook(() => useRoleSwitch());
      
      // 初始状态应该是租买角色
      expect(result.current.activeRole).toBe('tenant_buyer');
      expect(result.current.isTenantBuyerActive).toBe(true);
      expect(result.current.isLandlordActive).toBe(false);
      
      // 切换到业主角色
      act(() => {
        result.current.switchToLandlord();
      });
      
      expect(result.current.activeRole).toBe('landlord');
      expect(result.current.isTenantBuyerActive).toBe(false);
      expect(result.current.isLandlordActive).toBe(true);
      expect(result.current.roleDisplayName).toBe('业主消息');
    });

    test('应该支持通用角色切换方法', () => {
      const { result } = renderHook(() => useRoleSwitch());
      
      act(() => {
        result.current.switchRole('landlord');
      });
      
      expect(result.current.activeRole).toBe('landlord');
      
      act(() => {
        result.current.switchRole('tenant_buyer');
      });
      
      expect(result.current.activeRole).toBe('tenant_buyer');
    });
  });

  describe('2. 消息分类系统功能', () => {
    test('应该支持消息分类切换', () => {
      const { result } = renderHook(() => useMessageLogic());
      
      // 初始分类应该是全部
      expect(result.current.activeCategory).toBe('all');
      
      // 切换到咨询分类
      act(() => {
        result.current.handleCategorySwitch('inquiry');
      });
      
      expect(result.current.activeCategory).toBe('inquiry');
    });
  });

  describe('3. UI状态管理功能', () => {
    test('应该支持刷新状态管理', async () => {
      const { result } = renderHook(() => useMessageUI());
      
      // 初始状态
      expect(result.current.refreshing).toBe(false);
      
      // 开始刷新
      act(() => {
        result.current.startRefreshing();
      });
      
      expect(result.current.refreshing).toBe(true);
      
      // 停止刷新
      act(() => {
        result.current.stopRefreshing();
      });
      
      expect(result.current.refreshing).toBe(false);
    });

    test('应该支持付费弹窗状态管理', () => {
      const { result } = renderHook(() => useMessageUI());
      
      // 初始状态
      expect(result.current.showPaymentModal).toBe(false);
      
      // 打开弹窗
      act(() => {
        result.current.openPaymentModal();
      });
      
      expect(result.current.showPaymentModal).toBe(true);
      
      // 关闭弹窗
      act(() => {
        result.current.closePaymentModal();
      });
      
      expect(result.current.showPaymentModal).toBe(false);
    });
  });

  describe('4. 数据转换层功能', () => {
    test('消息查询转换器应该正常工作', () => {
      const transformers = getMessageTransformers();
      const queryTransformer = transformers.messageQuery;
      
      const input: MessageQueryInput = {
        role: 'tenant_buyer',
        category: 'inquiry',
        skip: 0,
        limit: 20,
      };
      
      const result = queryTransformer.toAPI(input);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        user_role: 'tenant_buyer',
        message_category: 'inquiry',
        pagination: {
          offset: 0,
          limit: 20,
        }
      });
    });

    test('消息数据转换器应该正常工作', () => {
      const transformers = getMessageTransformers();
      const dataTransformer = transformers.messageData;
      
      const mockAPIData = {
        message_id: '123',
        message_title: '测试消息',
        message_content: '这是一条测试消息',
        message_category: 'inquiry',
        message_status: 'unread',
        message_priority: 'normal',
        sender_id: 'sender123',
        sender_name: '发送者',
        recipient_id: 'recipient123',
        recipient_role: 'tenant_buyer',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };
      
      const result = dataTransformer.fromAPI(mockAPIData);
      
      expect(result.success).toBe(true);
      expect(result.data?.id).toBe('123');
      expect(result.data?.title).toBe('测试消息');
      expect(result.data?.category).toBe('inquiry');
      expect(result.data?.createdAt).toBeInstanceOf(Date);
    });
  });

  describe('5. 错误处理功能', () => {
    test('转换器应该处理无效输入', () => {
      const transformers = getMessageTransformers();
      const queryTransformer = transformers.messageQuery;
      
      const invalidInput = {
        role: 'invalid_role',
        category: 'invalid_category',
      } as any;
      
      const result = queryTransformer.toAPI(invalidInput);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('6. 性能测试', () => {
    test('转换器工厂应该复用实例', () => {
      const transformers1 = getMessageTransformers();
      const transformers2 = getMessageTransformers();
      
      // 应该是同一个实例
      expect(transformers1.messageQuery).toBe(transformers2.messageQuery);
      expect(transformers1.messageData).toBe(transformers2.messageData);
    });
  });
});

/**
 * 集成测试：完整的消息流程
 */
describe('消息系统集成测试', () => {
  test('完整的消息查询流程', async () => {
    // 1. 初始化Hook
    const { result: logicResult } = renderHook(() => useMessageLogic());
    const { result: uiResult } = renderHook(() => useMessageUI());
    
    // 2. 切换角色
    act(() => {
      logicResult.current.handleRoleSwitch('landlord');
    });
    
    expect(logicResult.current.activeRole).toBe('landlord');
    
    // 3. 切换分类
    act(() => {
      logicResult.current.handleCategorySwitch('appointment');
    });
    
    expect(logicResult.current.activeCategory).toBe('appointment');
    
    // 4. 启用数据加载
    act(() => {
      logicResult.current.enableDataLoading();
    });
    
    // 5. 刷新数据
    await act(async () => {
      await uiResult.current.handleRefresh();
    });
    
    // 验证状态
    expect(uiResult.current.refreshing).toBe(false);
  });
});

/**
 * 架构合规性测试
 */
describe('架构合规性测试', () => {
  test('Hook应该遵循单一职责原则', () => {
    // useRoleSwitch只负责角色切换
    const { result: roleResult } = renderHook(() => useRoleSwitch());
    expect(typeof roleResult.current.switchRole).toBe('function');
    expect(typeof roleResult.current.activeRole).toBe('string');
    
    // useMessageUI只负责UI状态
    const { result: uiResult } = renderHook(() => useMessageUI());
    expect(typeof uiResult.current.refreshing).toBe('boolean');
    expect(typeof uiResult.current.handleRefresh).toBe('function');
    
    // useMessageLogic负责业务逻辑
    const { result: logicResult } = renderHook(() => useMessageLogic());
    expect(typeof logicResult.current.handleRoleSwitch).toBe('function');
    expect(typeof logicResult.current.activeRole).toBe('string');
  });

  test('转换器应该有完整的类型定义', () => {
    const transformers = getMessageTransformers();
    
    // 每个转换器都应该有toAPI和fromAPI方法
    expect(typeof transformers.messageQuery.toAPI).toBe('function');
    expect(typeof transformers.messageQuery.fromAPI).toBe('function');
    expect(typeof transformers.messageData.toAPI).toBe('function');
    expect(typeof transformers.messageData.fromAPI).toBe('function');
  });
});
