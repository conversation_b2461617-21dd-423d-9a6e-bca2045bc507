/**
 * 🚀 地图显示组件
 * 企业级组件拆分 - 从PropertyNavigationMap中提取
 * 职责：地图渲染、标记显示、路线绘制
 */

import React, { useCallback, useMemo } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, MapType } from 'react-native-amap3d';

// ==================== 类型定义 ====================

export interface LocationData {
  latitude: number;
  longitude: number;
  address: string;
}

export interface RouteInfo {
  mode: string;
  distance: string;
  duration: string;
  cost?: string;
  coordinates?: Array<{ latitude: number; longitude: number }>;
}

export interface MapDisplayProps {
  // 位置数据
  currentStartLocation: LocationData;
  currentEndLocation: LocationData;

  // 地图状态
  mapReady: boolean;

  // 路线数据
  selectedRoute?: {
    mode: string;
    alternativeIndex?: number;
  };
  routeResult?: {
    routes: RouteInfo[];
    drivingAlternatives?: Array<{
      coordinates: Array<{ latitude: number; longitude: number }>;
    }>;
  };

  // 事件处理
  onMapReady: () => void;
}

// ==================== 常量定义 ====================

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const MAP_INITIAL_REGION = {
  latitude: 22.547,
  longitude: 114.085947,
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};

// ==================== 组件实现 ====================

export const MapDisplay: React.FC<MapDisplayProps> = ({
  currentStartLocation,
  currentEndLocation,
  mapReady,
  selectedRoute,
  routeResult,
  onMapReady,
}) => {
  // ==================== 计算属性 ====================

  const mapRegion = useMemo(() => {
    // 如果有起点终点，计算显示区域
    if (currentStartLocation.latitude && currentEndLocation.latitude) {
      const minLat = Math.min(
        currentStartLocation.latitude,
        currentEndLocation.latitude
      );
      const maxLat = Math.max(
        currentStartLocation.latitude,
        currentEndLocation.latitude
      );
      const minLng = Math.min(
        currentStartLocation.longitude,
        currentEndLocation.longitude
      );
      const maxLng = Math.max(
        currentStartLocation.longitude,
        currentEndLocation.longitude
      );

      const centerLat = (minLat + maxLat) / 2;
      const centerLng = (minLng + maxLng) / 2;
      const deltaLat = Math.max((maxLat - minLat) * 1.5, 0.01);
      const deltaLng = Math.max((maxLng - minLng) * 1.5, 0.01);

      return {
        latitude: centerLat,
        longitude: centerLng,
        latitudeDelta: deltaLat,
        longitudeDelta: deltaLng,
      };
    }

    return MAP_INITIAL_REGION;
  }, [currentStartLocation, currentEndLocation]);

  const routeCoordinates = useMemo(() => {
    if (!routeResult || !selectedRoute) return [];

    // 如果是驾车模式且有备选路线
    if (
      selectedRoute.mode === 'driving' &&
      routeResult?.drivingAlternatives &&
      routeResult.drivingAlternatives.length > 0
    ) {
      const alternativeIndex = selectedRoute.alternativeIndex || 0;
      const alternative = routeResult.drivingAlternatives[alternativeIndex];
      return alternative?.coordinates || [];
    }

    // 普通路线
    const route = routeResult?.routes?.find(r => r.mode === selectedRoute.mode);
    return route?.coordinates || [];
  }, [routeResult?.routes, selectedRoute]);

  // ==================== 渲染方法 ====================

  const renderStartMarker = useCallback(() => {
    if (!currentStartLocation.latitude || !currentStartLocation.longitude) {
      return null;
    }

    return (
      <Marker
        position={{
          latitude: currentStartLocation.latitude,
          longitude: currentStartLocation.longitude,
        }}
        title="起点"
        description={currentStartLocation.address}
        // image={require('../../../../../../assets/markers/start_marker.png')}
      />
    );
  }, [currentStartLocation]);

  const renderEndMarker = useCallback(() => {
    if (!currentEndLocation.latitude || !currentEndLocation.longitude) {
      return null;
    }

    return (
      <Marker
        position={{
          latitude: currentEndLocation.latitude,
          longitude: currentEndLocation.longitude,
        }}
        title="终点"
        description={currentEndLocation.address}
        // image={require('../../../../../../assets/markers/end_marker.png')}
      />
    );
  }, [currentEndLocation]);

  const renderRoutePolyline = useCallback(() => {
    if (routeCoordinates.length === 0) {
      return null;
    }

    // 根据路线模式选择颜色
    const getRouteColor = (mode: string) => {
      switch (mode) {
        case 'driving':
          return '#1E90FF'; // 蓝色
        case 'taxi':
          return '#FFD700'; // 金色
        case 'transit':
          return '#32CD32'; // 绿色
        case 'walking':
          return '#FF6347'; // 橙红色
        case 'cycling':
          return '#8A2BE2'; // 紫色
        default:
          return '#1E90FF';
      }
    };

    return (
      <Polyline
        points={routeCoordinates}
        width={6}
        color={getRouteColor(selectedRoute?.mode || 'driving')}
        onPress={() => console.log('🗺️ [MapDisplay] 路线被点击')}
      />
    );
  }, [routeCoordinates, selectedRoute]);

  // ==================== 事件处理 ====================

  const handleMapReady = useCallback(() => {
    console.log('🗺️ [MapDisplay] 地图加载完成');
    onMapReady();
  }, [onMapReady]);

  const handleMapError = useCallback((error: any) => {
    console.error('❌ [MapDisplay] 地图加载错误:', error);
  }, []);

  // ==================== 调试日志 ====================

  // 🔧 紧急修复：移除每次渲染的日志，避免无限循环
  // console.log('🗺️ [MapDisplay] 渲染参数:', {
  //   startLocation: currentStartLocation,
  //   endLocation: currentEndLocation,
  //   mapReady,
  //   selectedRoute,
  //   routeCoordinatesLength: routeCoordinates.length,
  //   mapRegion,
  // });

  // ==================== 主渲染 ====================

  return (
    <View style={styles.container}>
      <MapView
        style={styles.map}
        center={mapRegion}
        myLocationEnabled={true}
        myLocationButtonEnabled={false}
        compassEnabled={true}
        scaleControlsEnabled={true}
        zoomGesturesEnabled={true}
        scrollGesturesEnabled={true}
        tiltGesturesEnabled={false}
        rotateGesturesEnabled={false}
        onLoad={handleMapReady}
        mapType={MapType.Standard}
        trafficEnabled={false}
        buildingsEnabled={true}
        labelsEnabled={true}
      >
        {/* 起点标记 */}
        {renderStartMarker()}

        {/* 终点标记 */}
        {renderEndMarker()}

        {/* 路线多段线 */}
        {renderRoutePolyline()}
      </MapView>

      {/* 地图覆盖层指示器 */}
      {!mapReady && (
        <View style={styles.loadingOverlay}>
          {/* 可以添加地图加载指示器 */}
        </View>
      )}
    </View>
  );
};

// ==================== 样式定义 ====================

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  map: {
    flex: 1,
    width: screenWidth,
    height: screenHeight * 0.6, // 占屏幕高度60%
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MapDisplay;
