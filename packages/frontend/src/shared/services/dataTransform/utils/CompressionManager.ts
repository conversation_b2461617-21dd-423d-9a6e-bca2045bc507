/**
 * 压缩管理器 - 智能两层压缩策略
 *
 * 架构设计（参考微信/抖音最佳实践）：
 *
 * 第一层：客户端智能预处理
 * - 目的：减少上传时间，提升用户体验
 * - 策略：根据业务场景和网络状况适度压缩
 * - 存储：不单独存储，优化后直接上传
 *
 * 第二层：OSS动态处理
 * - 目的：按需生成不同规格，节省存储成本
 * - 策略：实时处理，支持缩略图、水印、格式转换
 * - 存储：不占用额外存储，通过URL参数控制
 *
 * 文件组织结构：
 * /properties/{propertyId}/{date}/{uuid}_{filename}  # 房源文件
 * /users/{userId}/avatar/{uuid}_{filename}           # 用户头像
 * /documents/{userId}/{type}/{uuid}_{filename}       # 证件文档
 *
 * 作者: AI编码助手
 * 创建时间: 2025年7月15日
 */

import { SecurityLevel, AccessLevel, MediaType, MediaCategory, MediaBusinessType, CompressionConfig } from '../transformers/MediaTransformer';

// 客户端压缩选项
export interface ClientCompressionOptions {
  quality: number;          // 压缩质量 0-1
  maxWidth: number;         // 最大宽度
  maxHeight: number;        // 最大高度
  format: 'jpeg' | 'png' | 'webp';  // 输出格式
  enableResize: boolean;    // 是否启用尺寸调整
}

// OSS压缩参数
export interface OSSCompressionParams {
  thumbnail: string;        // 缩略图参数
  medium: string;          // 中等尺寸参数
  large: string;           // 大图参数
  webp: string;            // WebP格式参数
}

// 压缩结果
export interface CompressionResult {
  success: boolean;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  uri: string;
  error?: string;
}

export class CompressionManager {
  private static instance: CompressionManager;
  
  // 业务场景的客户端压缩配置
  private readonly clientConfigs: Record<MediaBusinessType, ClientCompressionOptions> = {
    [MediaBusinessType.PROPERTY_IMAGE]: {
      quality: 0.85,
      maxWidth: 1920,
      maxHeight: 1440,
      format: 'jpeg',
      enableResize: true
    },
    [MediaBusinessType.PROPERTY_VIDEO]: {
      quality: 0.75,
      maxWidth: 1920,
      maxHeight: 1080,
      format: 'jpeg', // 视频缩略图
      enableResize: true
    },
    [MediaBusinessType.PROPERTY_VR_PANORAMA]: {
      quality: 0.9,
      maxWidth: 2048,
      maxHeight: 1024,
      format: 'jpeg',
      enableResize: true
    },
    [MediaBusinessType.PROPERTY_FLOOR_PLAN]: {
      quality: 0.9,
      maxWidth: 1920,
      maxHeight: 1440,
      format: 'jpeg',
      enableResize: true
    },
    [MediaBusinessType.PROPERTY_THUMBNAIL]: {
      quality: 0.8,
      maxWidth: 400,
      maxHeight: 300,
      format: 'jpeg',
      enableResize: true
    },
    [MediaBusinessType.IDENTITY_CARD]: {
      quality: 0.95,
      maxWidth: 2048,
      maxHeight: 1536,
      format: 'jpeg',
      enableResize: false // 敏感图片不调整尺寸
    },
    [MediaBusinessType.BUSINESS_LICENSE]: {
      quality: 0.95,
      maxWidth: 2048,
      maxHeight: 1536,
      format: 'jpeg',
      enableResize: false
    },
    [MediaBusinessType.PROPERTY_CERT]: {
      quality: 0.95,
      maxWidth: 2048,
      maxHeight: 1536,
      format: 'jpeg',
      enableResize: false
    },
    [MediaBusinessType.USER_AVATAR]: {
      quality: 0.9,
      maxWidth: 400,
      maxHeight: 400,
      format: 'jpeg',
      enableResize: true
    },
    [MediaBusinessType.FINANCIAL_DOC]: {
      quality: 1.0,
      maxWidth: 2048,
      maxHeight: 2048,
      format: 'jpeg',
      enableResize: false // 文档不调整尺寸
    },
    [MediaBusinessType.OTHER_DOC]: {
      quality: 0.9,
      maxWidth: 1920,
      maxHeight: 1440,
      format: 'jpeg',
      enableResize: true
    }
  };
  
  // OSS压缩参数配置
  private readonly ossParams: Record<MediaBusinessType, OSSCompressionParams> = {
    [MediaBusinessType.PROPERTY_IMAGE]: {
      thumbnail: 'x-oss-process=image/resize,w_300,h_200,m_lfit/quality,q_80/format,webp',
      medium: 'x-oss-process=image/resize,w_800,h_600,m_lfit/quality,q_85/format,webp',
      large: 'x-oss-process=image/resize,w_1200,h_800,m_lfit/quality,q_90',
      webp: 'x-oss-process=image/format,webp/quality,q_85'
    },
    [MediaBusinessType.PROPERTY_VIDEO]: {
      thumbnail: 'x-oss-process=video/snapshot,t_1000,w_300,h_200,m_fast',
      medium: 'x-oss-process=video/snapshot,t_1000,w_800,h_600,m_fast',
      large: 'x-oss-process=video/snapshot,t_1000,w_1200,h_800,m_fast',
      webp: 'x-oss-process=video/snapshot,t_1000,f_webp'
    },
    [MediaBusinessType.PROPERTY_VR_PANORAMA]: {
      thumbnail: 'x-oss-process=image/resize,w_400,h_200,m_lfit/quality,q_90/format,webp',
      medium: 'x-oss-process=image/resize,w_1024,h_512,m_lfit/quality,q_90/format,webp',
      large: 'x-oss-process=image/resize,w_2048,h_1024,m_lfit/quality,q_90',
      webp: 'x-oss-process=image/format,webp/quality,q_90'
    },
    [MediaBusinessType.PROPERTY_FLOOR_PLAN]: {
      thumbnail: 'x-oss-process=image/resize,w_300,h_225,m_lfit/quality,q_90/format,webp',
      medium: 'x-oss-process=image/resize,w_800,h_600,m_lfit/quality,q_90/format,webp',
      large: 'x-oss-process=image/resize,w_1920,h_1440,m_lfit/quality,q_90',
      webp: 'x-oss-process=image/format,webp/quality,q_90'
    },
    [MediaBusinessType.PROPERTY_THUMBNAIL]: {
      thumbnail: 'x-oss-process=image/resize,w_200,h_150,m_fill/quality,q_80/format,webp',
      medium: 'x-oss-process=image/resize,w_400,h_300,m_fill/quality,q_85/format,webp',
      large: 'x-oss-process=image/resize,w_400,h_300,m_fill/quality,q_85',
      webp: 'x-oss-process=image/format,webp/quality,q_85'
    },
    [MediaBusinessType.IDENTITY_CARD]: {
      thumbnail: 'x-oss-process=image/resize,w_300,h_200,m_lfit/quality,q_95',
      medium: 'x-oss-process=image/resize,w_800,h_600,m_lfit/quality,q_95',
      large: 'x-oss-process=image/quality,q_95',
      webp: 'x-oss-process=image/format,webp/quality,q_95'
    },
    [MediaBusinessType.BUSINESS_LICENSE]: {
      thumbnail: 'x-oss-process=image/resize,w_300,h_200,m_lfit/quality,q_95',
      medium: 'x-oss-process=image/resize,w_800,h_600,m_lfit/quality,q_95',
      large: 'x-oss-process=image/quality,q_95',
      webp: 'x-oss-process=image/format,webp/quality,q_95'
    },
    [MediaBusinessType.PROPERTY_CERT]: {
      thumbnail: 'x-oss-process=image/resize,w_300,h_200,m_lfit/quality,q_95',
      medium: 'x-oss-process=image/resize,w_800,h_600,m_lfit/quality,q_95',
      large: 'x-oss-process=image/quality,q_95',
      webp: 'x-oss-process=image/format,webp/quality,q_95'
    },
    [MediaBusinessType.USER_AVATAR]: {
      thumbnail: 'x-oss-process=image/resize,w_128,h_128,m_fill/quality,q_90/format,webp',
      medium: 'x-oss-process=image/resize,w_256,h_256,m_fill/quality,q_90/format,webp',
      large: 'x-oss-process=image/resize,w_512,h_512,m_fill/quality,q_90',
      webp: 'x-oss-process=image/format,webp/quality,q_90'
    },
    [MediaBusinessType.FINANCIAL_DOC]: {
      thumbnail: 'x-oss-process=image/resize,w_300,h_200,m_lfit/quality,q_90',
      medium: 'x-oss-process=image/resize,w_800,h_600,m_lfit/quality,q_90',
      large: 'x-oss-process=image/quality,q_90',
      webp: 'x-oss-process=image/format,webp/quality,q_90'
    },
    [MediaBusinessType.OTHER_DOC]: {
      thumbnail: 'x-oss-process=image/resize,w_300,h_200,m_lfit/quality,q_80',
      medium: 'x-oss-process=image/resize,w_800,h_600,m_lfit/quality,q_85',
      large: 'x-oss-process=image/quality,q_85',
      webp: 'x-oss-process=image/format,webp/quality,q_85'
    }
  };
  
  /**
   * 单例模式
   */
  public static getInstance(): CompressionManager {
    if (!CompressionManager.instance) {
      CompressionManager.instance = new CompressionManager();
    }
    return CompressionManager.instance;
  }
  
  /**
   * 客户端智能压缩文件
   *
   * 压缩策略：
   * - 房源图片：适度压缩，保持视觉质量
   * - 身份证件：轻度压缩，保持清晰度
   * - 用户头像：标准压缩，优化大小
   * - 其他文档：根据文件大小动态调整
   */
  async compressFile(
    fileUri: string,
    businessType: MediaBusinessType,
    originalSize: number,
    customOptions?: Partial<ClientCompressionOptions>
  ): Promise<CompressionResult> {
    try {
      const config = { ...this.clientConfigs[businessType], ...customOptions };

      // 检查是否需要压缩
      if (!this.shouldCompress(originalSize, businessType)) {
        console.log(`[CompressionManager] 文件无需压缩: ${fileUri}`);
        return {
          success: true,
          originalSize,
          compressedSize: originalSize,
          compressionRatio: 1.0,
          uri: fileUri
        };
      }

      console.log(`[CompressionManager] 开始客户端压缩: ${fileUri}`, {
        businessType,
        originalSize: `${(originalSize / 1024 / 1024).toFixed(2)}MB`,
        config
      });

      // TODO: 集成React Native图片压缩库
      // 推荐使用: react-native-image-resizer 或 expo-image-manipulator
      /*
      import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';

      const result = await manipulateAsync(
        fileUri,
        config.enableResize ? [{ resize: { width: config.maxWidth, height: config.maxHeight } }] : [],
        {
          compress: config.quality,
          format: config.format === 'jpeg' ? SaveFormat.JPEG : SaveFormat.PNG,
        }
      );

      return {
        success: true,
        originalSize,
        compressedSize: result.width * result.height * 3, // 估算
        compressionRatio: result.width * result.height * 3 / originalSize,
        uri: result.uri
      };
      */

      // 暂时返回模拟结果
      const estimatedCompressedSize = Math.round(originalSize * config.quality);

      return {
        success: true,
        originalSize,
        compressedSize: estimatedCompressedSize,
        compressionRatio: config.quality,
        uri: fileUri // 实际应该返回压缩后的URI
      };

    } catch (error) {
      console.error(`[CompressionManager] 压缩失败: ${fileUri}`, error);
      return {
        success: false,
        originalSize,
        compressedSize: 0,
        compressionRatio: 0,
        uri: fileUri,
        error: error instanceof Error ? error.message : '压缩失败'
      };
    }
  }
  
  /**
   * 构建OSS压缩URL
   */
  buildOSSUrl(
    baseUrl: string,
    businessType: MediaBusinessType,
    size: 'thumbnail' | 'medium' | 'large' | 'webp' = 'medium'
  ): string {
    const params = this.ossParams[businessType];
    const processParam = params[size];
    
    // 如果URL已有参数，用&连接，否则用?
    const separator = baseUrl.includes('?') ? '&' : '?';
    
    return `${baseUrl}${separator}${processParam}`;
  }
  
  /**
   * 获取客户端压缩配置
   */
  getClientConfig(businessType: MediaBusinessType): ClientCompressionOptions {
    return { ...this.clientConfigs[businessType] };
  }
  
  /**
   * 获取OSS压缩参数
   */
  getOSSParams(businessType: MediaBusinessType): OSSCompressionParams {
    return { ...this.ossParams[businessType] };
  }
  
  /**
   * 估算压缩后大小
   */
  estimateCompressedSize(
    originalSize: number,
    businessType: MediaBusinessType
  ): number {
    const config = this.clientConfigs[businessType];
    
    // 根据质量设置估算压缩比
    const compressionRatio = config.quality * 0.8; // 经验值
    
    return Math.round(originalSize * compressionRatio);
  }
  
  /**
   * 检查是否需要压缩
   */
  shouldCompress(
    fileSize: number,
    businessType: MediaBusinessType
  ): boolean {
    // 小于100KB的文件通常不需要压缩
    if (fileSize < 100 * 1024) {
      return false;
    }
    
    // 身份证等重要文档谨慎压缩
    if ([
      MediaBusinessType.IDENTITY_CARD,
      MediaBusinessType.BUSINESS_LICENSE,
      MediaBusinessType.PROPERTY_CERT
    ].includes(businessType) && fileSize < 2 * 1024 * 1024) {
      return false;
    }
    
    return true;
  }
}

// 导出单例实例
export const compressionManager = CompressionManager.getInstance();
