/**
 * React Native配置 - 兼容性和警告抑制
 * 
 * 🔧 **PropTypes兼容性配置**
 * 解决React Native 0.73+版本的PropTypes废弃警告
 */

module.exports = {
  // 依赖配置
  dependencies: {
    // 兼容性包配置
    'deprecated-react-native-prop-types': {
      platforms: {
        android: {
          sourceDir: '../node_modules/deprecated-react-native-prop-types/android',
          packageImportPath: 'import io.github.react-native-community.deprecated-react-native-prop-types.DeprecatedReactNativePropTypesPackage;',
        },
        ios: {
          podspecPath: '../node_modules/deprecated-react-native-prop-types/deprecated-react-native-prop-types.podspec',
        },
      },
    },
  },
  
  // 项目配置
  project: {
    ios: {},
    android: {},
  },
  
  // 资源配置
  assets: ['./assets/fonts/'],
};
