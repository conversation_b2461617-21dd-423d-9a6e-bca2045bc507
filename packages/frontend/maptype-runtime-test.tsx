/**
 * 运行时测试 - 验证mapType修复效果
 */
import React from 'react';
import { View } from 'react-native';
import { MapView, MapType } from 'react-native-amap3d';

const MapTypeTest = () => {
  console.log('🗺️ [Test] MapType枚举值:');
  console.log('Standard:', MapType.Standard); // 应该输出 0
  console.log('Satellite:', MapType.Satellite); // 应该输出 1
  console.log('Night:', MapType.Night); // 应该输出 2

  return (
    <View style={{ flex: 1 }}>
      <MapView
        style={{ flex: 1 }}
        mapType={MapType.Standard} // 使用数字值而不是字符串
        center={{ latitude: 22.547, longitude: 114.085947 }}
        onLoad={() => console.log('✅ [Test] 地图加载成功，mapType错误已修复')}
      />
    </View>
  );
};

export default MapTypeTest;
