/**
 * useStatusToggle - 统一的上架/下架Hook
 * @fileoverview 提供统一的上架/下架功能，支持需求和房源
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🎯 功能：
 * - 统一的上架/下架操作
 * - 自动乐观更新和错误回滚
 * - 自动缓存失效和事件通知
 */

import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import { useUniversalDataManager } from '../services/dataUpdate/UniversalDataManager';
import { useStatusUpdateEvent } from '../services/dataUpdate/EventBusService';
import { useCacheInvalidation } from '../services/dataUpdate/CacheInvalidationService';
import FeedbackService from '../services/FeedbackService';

// 类型定义
export type EntityType = 'demand' | 'property';

export interface StatusToggleConfig {
  entityType: EntityType;
  entityId: string;
  currentStatus: string;
  entityName?: string; // 用于显示确认对话框
  source?: string; // 操作来源页面
}

export interface StatusToggleResult {
  isLoading: boolean;
  error: string | null;
  toggleStatus: (reason?: string) => Promise<boolean>;
  publishEntity: () => Promise<boolean>;
  unpublishEntity: (reason?: string) => Promise<boolean>;
}

/**
 * 统一的上架/下架Hook
 */
export const useStatusToggle = (
  config: StatusToggleConfig
): StatusToggleResult => {
  const { entityType, entityId, currentStatus, entityName, source } = config;
  const { publishEntity, unpublishEntity } = useUniversalDataManager();
  const { invalidateEntity } = useCacheInvalidation();

  // 本地状态
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 监听状态更新事件（用于同步其他页面的状态变化）
  useStatusUpdateEvent(
    entityType,
    event => {
      if (event.entityId === entityId) {
        console.log(
          `🔄 [StatusToggle] 收到状态更新事件: ${event.oldStatus} → ${event.newStatus}`
        );
        // 这里可以添加额外的处理逻辑，比如显示通知
      }
    },
    entityId
  );

  /**
   * 获取实体类型的中文名称
   */
  const getEntityTypeName = useCallback(() => {
    return entityType === 'demand' ? '需求' : '房源';
  }, [entityType]);

  /**
   * 获取状态操作的中文名称
   */
  const getActionName = useCallback((targetStatus: string) => {
    return targetStatus === 'ACTIVE' ? '上架' : '下架';
  }, []);

  /**
   * 显示确认对话框
   */
  const showConfirmDialog = useCallback(
    (action: string, onConfirm: () => void): void => {
      const entityTypeName = getEntityTypeName();
      const title = `确认${action}`;
      const message = entityName
        ? `确定要${action}${entityTypeName}「${entityName}」吗？`
        : `确定要${action}这个${entityTypeName}吗？`;

      Alert.alert(title, message, [
        { text: '取消', style: 'cancel' },
        {
          text: '确定',
          style: action === '下架' ? 'destructive' : 'default',
          onPress: onConfirm,
        },
      ]);
    },
    [entityName, getEntityTypeName]
  );

  /**
   * 上架操作
   */
  const handlePublish = useCallback(async (): Promise<boolean> => {
    if (isLoading) return false;

    setIsLoading(true);
    setError(null);

    try {
      console.log(`📤 [StatusToggle] 开始上架: ${entityType}/${entityId}`);

      const result = await publishEntity(entityType, entityId, source);

      if (result.success) {
        const entityTypeName = getEntityTypeName();
        FeedbackService.showSuccess(`${entityTypeName}上架成功`);
        console.log(`📤 [StatusToggle] 上架成功: ${entityType}/${entityId}`);

        // 手动失效缓存
        await invalidateEntity(entityType, entityId, 'statusChange');
        console.log(
          `📤 [StatusToggle] 缓存失效完成: ${entityType}/${entityId}`
        );

        return true;
      } else {
        const errorMsg = result.error || '上架失败，请重试';
        setError(errorMsg);
        FeedbackService.showError(errorMsg);
        console.error(`📤 [StatusToggle] 上架失败:`, result.error);
        return false;
      }
    } catch (error) {
      const errorMsg =
        error instanceof Error ? error.message : '上架失败，请重试';
      setError(errorMsg);
      FeedbackService.showError(errorMsg);
      console.error(`📤 [StatusToggle] 上架异常:`, error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [
    isLoading,
    entityType,
    entityId,
    source,
    publishEntity,
    getEntityTypeName,
  ]);

  /**
   * 下架操作
   */
  const handleUnpublish = useCallback(
    async (reason?: string): Promise<boolean> => {
      if (isLoading) return false;

      setIsLoading(true);
      setError(null);

      try {
        console.log(
          `📥 [StatusToggle] 开始下架: ${entityType}/${entityId}`,
          reason
        );

        const result = await unpublishEntity(
          entityType,
          entityId,
          reason,
          source
        );

        if (result.success) {
          const entityTypeName = getEntityTypeName();
          FeedbackService.showSuccess(`${entityTypeName}下架成功`);
          console.log(`📥 [StatusToggle] 下架成功: ${entityType}/${entityId}`);

          // 手动失效缓存
          await invalidateEntity(entityType, entityId, 'statusChange');
          console.log(
            `📥 [StatusToggle] 缓存失效完成: ${entityType}/${entityId}`
          );

          return true;
        } else {
          const errorMsg = result.error || '下架失败，请重试';
          setError(errorMsg);
          FeedbackService.showError(errorMsg);
          console.error(`📥 [StatusToggle] 下架失败:`, result.error);
          return false;
        }
      } catch (error) {
        const errorMsg =
          error instanceof Error ? error.message : '下架失败，请重试';
        setError(errorMsg);
        FeedbackService.showError(errorMsg);
        console.error(`📥 [StatusToggle] 下架异常:`, error);
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [
      isLoading,
      entityType,
      entityId,
      source,
      unpublishEntity,
      getEntityTypeName,
    ]
  );

  /**
   * 智能切换状态（根据当前状态自动选择上架或下架）
   */
  const toggleStatus = useCallback(
    async (reason?: string): Promise<boolean> => {
      const isCurrentlyActive =
        currentStatus === 'ACTIVE' || currentStatus === 'active';
      const targetAction = isCurrentlyActive ? '下架' : '上架';

      return new Promise(resolve => {
        showConfirmDialog(targetAction, async () => {
          const success = isCurrentlyActive
            ? await handleUnpublish(reason)
            : await handlePublish();
          resolve(success);
        });
      });
    },
    [currentStatus, showConfirmDialog, handlePublish, handleUnpublish]
  );

  /**
   * 直接上架（带确认对话框）
   */
  const publishWithConfirm = useCallback(async (): Promise<boolean> => {
    return new Promise(resolve => {
      showConfirmDialog('上架', async () => {
        const success = await handlePublish();
        resolve(success);
      });
    });
  }, [showConfirmDialog, handlePublish]);

  /**
   * 直接下架（带确认对话框）
   */
  const unpublishWithConfirm = useCallback(
    async (reason?: string): Promise<boolean> => {
      return new Promise(resolve => {
        showConfirmDialog('下架', async () => {
          const success = await handleUnpublish(reason);
          resolve(success);
        });
      });
    },
    [showConfirmDialog, handleUnpublish]
  );

  return {
    isLoading,
    error,
    toggleStatus,
    publishEntity: publishWithConfirm,
    unpublishEntity: unpublishWithConfirm,
  };
};

/**
 * 简化版Hook：只提供切换功能，不带确认对话框
 */
export const useSimpleStatusToggle = (config: StatusToggleConfig) => {
  const { publishEntity, unpublishEntity } = useUniversalDataManager();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const toggleStatus = useCallback(
    async (reason?: string): Promise<boolean> => {
      if (isLoading) return false;

      setIsLoading(true);
      setError(null);

      try {
        const isCurrentlyActive =
          config.currentStatus === 'ACTIVE' ||
          config.currentStatus === 'active';

        const result = isCurrentlyActive
          ? await unpublishEntity(
              config.entityType,
              config.entityId,
              reason,
              config.source
            )
          : await publishEntity(
              config.entityType,
              config.entityId,
              config.source
            );

        if (!result.success) {
          setError(result.error || '操作失败');
          return false;
        }

        return true;
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : '操作失败';
        setError(errorMsg);
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [config, isLoading, publishEntity, unpublishEntity]
  );

  return {
    isLoading,
    error,
    toggleStatus,
  };
};
