/**
 * 房源导航栈
 */

import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text } from 'react-native';

const Stack = createStackNavigator();

// 临时占位组件，避免children错误
const PlaceholderScreen: React.FC = () => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <Text>房源页面开发中...</Text>
  </View>
);

export const PropertyStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="PropertyPlaceholder" component={PlaceholderScreen} />
    </Stack.Navigator>
  );
};
