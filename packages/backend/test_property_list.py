import asyncio
import aiohttp
import json

async def test_property_listing():
    """测试房源在列表中的显示"""
    
    # 获取认证token
    login_data = {
        'phone_number': '18377100296',
        'password': '123456'
    }
    
    async with aiohttp.ClientSession() as session:
        # 登录
        async with session.post(
            'http://backend/api/v1/auth/login/phone',
            json=login_data,
            headers={'Content-Type': 'application/json'}
        ) as resp:
            if resp.status != 200:
                print(f'登录失败: {resp.status}')
                print(await resp.text())
                return
            
            login_result = await resp.json()
            token = login_result['access_token']
            print(f'登录成功，token: {token[:20]}...')
        
        # 获取房源列表（写字楼类型）
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        async with session.get(
            'http://backend/api/v1/properties/?property_type=OFFICE',
            headers=headers
        ) as resp:
            print(f'房源列表API响应状态: {resp.status}')
            if resp.status == 200:
                response_data = await resp.json()
                print('✅ 房源列表获取成功！')
                print(f'找到 {len(response_data.get("items", []))} 条房源')
                
                # 检查是否包含我们刚发布的房源
                for item in response_data.get('items', []):
                    if item.get('title') == '高端商务写字楼出租':
                        print(f'✅ 找到发布的房源: {item.get("title")}')
                        print(f'   地址: {item.get("address")}')
                        print(f'   状态: {item.get("status")}')
                        print(f'   类型: {item.get("property_type")}')
                        return True
                
                print('❌ 未找到刚发布的房源')
                print('当前房源列表:')
                for item in response_data.get('items', []):
                    print(f'  - {item.get("title")} ({item.get("status")})')
            else:
                print('❌ 房源列表获取失败')
                print(await resp.text())
        
        # 测试"我的房源"API
        async with session.get(
            'http://backend/api/v1/properties/user/my',
            headers=headers
        ) as resp:
            print(f'\n我的房源API响应状态: {resp.status}')
            if resp.status == 200:
                response_data = await resp.json()
                print('✅ 我的房源获取成功！')
                print(f'我的房源数量: {len(response_data.get("items", []))}')
                
                for item in response_data.get('items', []):
                    print(f'  - {item.get("title")} ({item.get("status")})')
            else:
                print('❌ 我的房源获取失败')
                print(await resp.text())

asyncio.run(test_property_listing())