/**
 * 测试高德地图定位防抖功能深度修复
 * 验证双层防抖架构和精度信息完整性
 */

console.log('🔧 测试高德地图定位防抖功能深度修复');

// 模拟MapContainer层面的防抖逻辑
class MockMapContainerDebounce {
  constructor() {
    this.lastLocationRef = null;
  }

  shouldProcessLocation(coords) {
    if (!coords?.latitude || !coords?.longitude) {
      return { shouldProcess: false, reason: '坐标数据无效' };
    }

    const currentLocation = {
      latitude: coords.latitude,
      longitude: coords.longitude,
      accuracy: coords.accuracy || 0,
      timestamp: coords.timestamp || Date.now(),
    };

    // 只有位置变化超过10米才处理更新，减少频繁回调
    const shouldProcess = !this.lastLocationRef ||
      Math.abs(this.lastLocationRef.latitude - currentLocation.latitude) > 0.0001 ||
      Math.abs(this.lastLocationRef.longitude - currentLocation.longitude) > 0.0001;

    if (!shouldProcess) {
      return { shouldProcess: false, reason: '位置变化过小，MapContainer层防抖跳过' };
    }

    this.lastLocationRef = currentLocation;
    return { shouldProcess: true, reason: 'MapContainer层防抖通过' };
  }
}

// 模拟LocationService层面的防抖逻辑（修复后）
class MockLocationServiceFixed {
  constructor() {
    this.lastProcessedCoordinates = null;
    this.currentCity = { name: '南宁市' };
    this.COORDINATE_THRESHOLD = 0.0001; // 约10米
    this.ACCURACY_THRESHOLD = 10; // 10米
  }

  handleNativeLocationUpdate(nativeEvent) {
    const { coords } = nativeEvent;
    if (!coords) {
      throw new Error('定位数据无效');
    }

    const coordinates = {
      latitude: coords.latitude,
      longitude: coords.longitude,
      accuracy: coords.accuracy,
    };

    // 智能防抖：参考房源详情页的成熟防抖机制
    if (this.lastProcessedCoordinates) {
      const latDiff = Math.abs(
        coordinates.latitude - this.lastProcessedCoordinates.latitude
      );
      const lngDiff = Math.abs(
        coordinates.longitude - this.lastProcessedCoordinates.longitude
      );

      const hasSignificantLocationChange = 
        latDiff > this.COORDINATE_THRESHOLD || 
        lngDiff > this.COORDINATE_THRESHOLD;
      
      const hasSignificantAccuracyImprovement = 
        coordinates.accuracy < this.lastProcessedCoordinates.accuracy - this.ACCURACY_THRESHOLD;

      // 如果位置变化很小且精度没有显著提升，静默跳过
      if (!hasSignificantLocationChange && !hasSignificantAccuracyImprovement) {
        // 🔧 修复：静默跳过时包含完整精度信息
        return {
          success: true,
          coordinates: this.lastProcessedCoordinates,
          city: this.currentCity,
          accuracy: this.lastProcessedCoordinates.accuracy, // ✅ 包含精度信息
          reason: 'LocationService层防抖跳过，但包含完整精度信息'
        };
      }
    }

    console.log('🎯 [LocationService] 收到高德原生定位数据:', coordinates);
    console.log(`📊 [LocationService] 定位精度: ±${coordinates.accuracy?.toFixed(0) || '未知'}米`);

    // 更新最后处理的坐标
    this.lastProcessedCoordinates = coordinates;

    const result = {
      success: true,
      city: this.currentCity,
      coordinates: {
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
      },
      accuracy: coordinates.accuracy, // ✅ 确保包含精度信息
      reason: 'LocationService层处理成功'
    };

    console.log('✅ [LocationService] 高德原生定位处理完成');
    return result;
  }
}

// 测试双层防抖架构
console.log('\n📋 测试1: 双层防抖架构验证');
const mapDebounce = new MockMapContainerDebounce();
const locationService = new MockLocationServiceFixed();

// 模拟高德SDK频繁回调的场景
const testCallbacks = [
  { coords: { latitude: 22.807413, longitude: 108.421136, accuracy: 15 }, timestamp: 1000 },
  { coords: { latitude: 22.807413, longitude: 108.421136, accuracy: 15 }, timestamp: 1100 }, // 重复数据
  { coords: { latitude: 22.807413, longitude: 108.421136, accuracy: 15 }, timestamp: 1200 }, // 重复数据
  { coords: { latitude: 22.807414, longitude: 108.421137, accuracy: 15 }, timestamp: 1300 }, // 微小变化
  { coords: { latitude: 22.808413, longitude: 108.422136, accuracy: 15 }, timestamp: 1400 }, // 显著变化
];

console.log('\n🔄 模拟高德SDK频繁回调:');
testCallbacks.forEach((callback, index) => {
  console.log(`\n--- 回调 ${index + 1} ---`);
  
  // 第一层：MapContainer防抖
  const mapResult = mapDebounce.shouldProcessLocation(callback.coords);
  console.log(`MapContainer防抖: ${mapResult.shouldProcess ? '✅ 通过' : '🚫 跳过'} - ${mapResult.reason}`);
  
  if (mapResult.shouldProcess) {
    // 第二层：LocationService防抖
    try {
      const serviceResult = locationService.handleNativeLocationUpdate(callback);
      console.log(`LocationService处理: ✅ 成功 - ${serviceResult.reason}`);
      console.log(`精度信息: ±${serviceResult.accuracy?.toFixed(0) || '未知'}米`);
    } catch (error) {
      console.log(`LocationService处理: ❌ 失败 - ${error.message}`);
    }
  }
});

console.log('\n📋 测试2: 精度信息完整性验证');

// 测试防抖跳过时的精度信息
const testCoords = { latitude: 22.807413, longitude: 108.421136, accuracy: 20 };
locationService.handleNativeLocationUpdate({ coords: testCoords }); // 首次处理

// 微小变化，应该被防抖跳过，但要包含精度信息
const smallChangeCoords = { latitude: 22.807414, longitude: 108.421137, accuracy: 18 };
const result = locationService.handleNativeLocationUpdate({ coords: smallChangeCoords });

console.log('防抖跳过时的返回值:');
console.log('- success:', result.success);
console.log('- coordinates:', result.coordinates);
console.log('- accuracy:', result.accuracy, '米');
console.log('- 精度信息完整:', result.accuracy !== undefined ? '✅ 是' : '❌ 否');

console.log('\n📋 测试3: 性能优化效果模拟');

let mapContainerProcessCount = 0;
let locationServiceProcessCount = 0;

// 模拟100次相同坐标的回调
const sameCoords = { latitude: 22.807413, longitude: 108.421136, accuracy: 15 };
for (let i = 0; i < 100; i++) {
  const mapResult = mapDebounce.shouldProcessLocation(sameCoords);
  if (mapResult.shouldProcess) {
    mapContainerProcessCount++;
    const serviceResult = locationService.handleNativeLocationUpdate({ coords: sameCoords });
    if (serviceResult.reason !== 'LocationService层防抖跳过，但包含完整精度信息') {
      locationServiceProcessCount++;
    }
  }
}

console.log('性能优化效果:');
console.log(`- 总回调次数: 100次`);
console.log(`- MapContainer处理次数: ${mapContainerProcessCount}次`);
console.log(`- LocationService处理次数: ${locationServiceProcessCount}次`);
console.log(`- 性能提升: ${((100 - mapContainerProcessCount) / 100 * 100).toFixed(1)}%`);

console.log('\n🎉 深度修复测试完成');
console.log('\n📊 测试总结:');
console.log('✅ 双层防抖架构: MapContainer + LocationService');
console.log('✅ 精度信息完整性: 防抖跳过时也包含accuracy字段');
console.log('✅ 性能优化显著: 大幅减少无效处理');
console.log('✅ 静默跳过机制: 无意义更新不产生日志');
console.log('✅ 参考成熟实现: 与PropertyNavigationMapSimple保持一致');
