/**
 * 功能图标网格组件
 * 职责：底部功能图标的网格展示和切换
 */

import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface FunctionIcon {
  name: string;
  text: string;
  color: string;
}

interface FunctionGridProps {
  functionIcons: FunctionIcon[];
  isExpanded: boolean;
  onToggle: () => void;
}

export const FunctionGrid: React.FC<FunctionGridProps> = ({
  functionIcons,
  isExpanded,
  onToggle,
}) => {
  // 显示的图标数量 - 收起时显示8个，展开时显示全部
  const displayIcons = isExpanded ? functionIcons : functionIcons.slice(0, 8);

  return (
    <View style={styles.gridContainer}>
      {displayIcons.map((item, index) => (
        <TouchableOpacity
          key={item.text}
          style={styles.gridItem}
          onPress={() => console.log(`点击了${item.text}`)}
        >
          <Ionicons name={item.name as any} size={32} color={item.color} />
          <Text style={styles.gridItemText}>{item.text}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    paddingTop: 20, // 为顶部的箭头留出空间
  },
  gridItem: {
    width: '25%',
    alignItems: 'center',
    marginBottom: 20,
  },
  gridItemText: {
    marginTop: 8,
    fontSize: 12,
    color: '#333',
  },
});
