import { z } from 'zod';

// 基础信息验证
export const basicInfoSchema = z.object({
  title: z.string()
    .min(5, { message: '标题至少需要5个字符' })
    .max(50, { message: '标题最多50个字符' }),
  transactionType: z.string({ required_error: '请选择交易类型' }),
  propertyType: z.string({ required_error: '请选择物业类型' }),
  propertySubType: z.string({ required_error: '请选择物业子类型' }),
});

// 位置信息验证
export const locationSchema = z.object({
  province: z.string({ required_error: '请选择省份' }),
  city: z.string({ required_error: '请选择城市' }),
  district: z.string({ required_error: '请选择区县' }),
  address: z.string()
    .min(5, { message: '详细地址至少需要5个字符' })
    .max(100, { message: '详细地址最多100个字符' }),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
});

// 面积和价格验证
export const areaAndPriceSchema = z.object({
  area: z.number({ required_error: '请输入面积' })
    .min(1, { message: '面积必须大于0' })
    .max(100000, { message: '面积不能超过100000平方米' }),
  price: z.number({ required_error: '请输入价格' })
    .min(1, { message: '价格必须大于0' }),
  priceUnit: z.string({ required_error: '请选择价格单位' }),
});

// 详细信息验证
export const detailsSchema = z.object({
  description: z.string()
    .min(20, { message: '描述至少需要20个字符' })
    .max(2000, { message: '描述最多2000个字符' }),
  floor: z.number().optional(),
  totalFloors: z.number().optional(),
  rooms: z.number().optional(),
  halls: z.number().optional(),
  bathrooms: z.number().optional(),
  orientation: z.string().optional(),
  decorationLevel: z.string().optional(),
  buildingAge: z.number().optional(),
  tags: z.array(z.string()).optional(),
});

// 联系信息验证
export const contactSchema = z.object({
  contactName: z.string()
    .min(2, { message: '联系人姓名至少需要2个字符' })
    .max(20, { message: '联系人姓名最多20个字符' }),
  contactPhone: z.string()
    .regex(/^1[3-9]\d{9}$/, { message: '请输入有效的手机号码' }),
  contactGender: z.enum(['male', 'female']),
  contactRole: z.enum(['owner', 'agent', 'other']),
});

// 媒体信息验证
export const mediaSchema = z.object({
  images: z.array(z.any()).min(1, { message: '至少上传1张图片' }),
  videos: z.array(z.any()).optional(),
  vrTours: z.array(z.any()).optional(),
});

// 完整表单验证
export const propertyFormSchema = z.object({
  ...basicInfoSchema.shape,
  ...locationSchema.shape,
  ...areaAndPriceSchema.shape,
  ...detailsSchema.shape,
  ...contactSchema.shape,
  ...mediaSchema.shape,
});
