/**
 * 🧪 Phase 2.2验证：TypeScript类型安全性测试
 * 验证类型定义完善和any类型移除的效果
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 [TypeSafetyTest] 开始Phase 2.2类型安全性验证...');

// 测试文件路径
const BASE_PATH =
  '/data/my-real-estate-app/packages/frontend/src/domains/property/components/detail';
const TYPE_FILE = path.join(BASE_PATH, 'types/navigation.types.ts');
const HOOK_FILE = path.join(BASE_PATH, 'hooks/usePropertyNavigation.ts');
const STORE_FILE = path.join(BASE_PATH, 'stores/MapNavigationStore.ts');
const MAIN_COMPONENT = path.join(
  BASE_PATH,
  'PropertyNavigationMapRefactored.tsx'
);

const testResults = {
  typeDefinitions: [],
  anyTypeRemoval: [],
  typeConsistency: [],
  typeGuards: [],
};

// 1. 统一类型定义验证
console.log('\n📋 [Test 1] 统一类型定义文件验证...');

if (fs.existsSync(TYPE_FILE)) {
  const typeContent = fs.readFileSync(TYPE_FILE, 'utf8');

  const typeChecks = [
    {
      name: 'RouteMode枚举定义',
      pattern: /export enum RouteMode/,
      required: true,
    },
    {
      name: 'AddressData接口',
      pattern: /export interface AddressData/,
      required: true,
    },
    {
      name: 'GPSLocation接口',
      pattern: /export interface GPSLocation/,
      required: true,
    },
    {
      name: 'AppNavigationProp接口',
      pattern: /export interface AppNavigationProp/,
      required: true,
    },
    {
      name: '类型守卫函数',
      pattern: /export const isValidCoordinate.*: coord is/,
      required: true,
    },
    {
      name: '错误处理类型',
      pattern: /export interface MapError/,
      required: true,
    },
    {
      name: '组件Props类型',
      pattern: /export interface.*Props.*extends.*BaseMapComponentProps/,
      required: true,
    },
    {
      name: '默认配置常量',
      pattern: /export const DEFAULT_ROUTE_CONFIG/,
      required: true,
    },
  ];

  typeChecks.forEach(({ name, pattern, required }) => {
    const found = pattern.test(typeContent);
    testResults.typeDefinitions.push({
      name,
      status: found ? 'PASS' : 'FAIL',
      required,
    });

    console.log(
      `${found ? '✅' : '❌'} ${name}: ${found ? 'DEFINED' : 'MISSING'}`
    );
  });
} else {
  console.log('❌ navigation.types.ts: FILE_NOT_FOUND');
  testResults.typeDefinitions.push({
    name: '统一类型文件存在',
    status: 'FAIL',
    required: true,
  });
}

// 2. any类型移除验证
console.log('\n🚫 [Test 2] any类型移除验证...');

const filesToCheck = [
  { name: 'usePropertyNavigation Hook', path: HOOK_FILE },
  { name: 'MapNavigationStore', path: STORE_FILE },
  { name: 'PropertyNavigationMapRefactored', path: MAIN_COMPONENT },
];

filesToCheck.forEach(({ name, path: filePath }) => {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');

    // 检查any类型使用（排除合法用例和注释）
    const anyMatches = content.match(/:\s*any(?!\w)/g) || [];
    const commentAny =
      content.match(/\/\/.*any|\/\*[\s\S]*?any[\s\S]*?\*\//g) || [];
    const legitimateAny = content.match(/NavigationProp<any>/g) || [];

    // 计算实际问题的any使用（排除注释和合法用例）
    const problematicAny = Math.max(
      0,
      anyMatches.length - legitimateAny.length
    );

    const status = problematicAny === 0 ? 'PASS' : 'FAIL';
    testResults.anyTypeRemoval.push({
      name,
      status,
      anyCount: problematicAny,
      details: anyMatches.slice(0, 3), // 显示前3个问题
    });

    console.log(
      `${status === 'PASS' ? '✅' : '❌'} ${name}: ${problematicAny === 0 ? 'NO_ANY_TYPES' : `${problematicAny} ANY_TYPES_FOUND`}`
    );

    if (problematicAny > 0 && anyMatches.length > 0) {
      console.log(`    示例: ${anyMatches[0]}`);
    }
  } else {
    console.log(`❌ ${name}: FILE_NOT_FOUND`);
    testResults.anyTypeRemoval.push({
      name,
      status: 'FAIL',
      anyCount: -1,
    });
  }
});

// 3. 类型一致性验证
console.log('\n🔗 [Test 3] 类型一致性验证...');

const consistencyChecks = [
  {
    name: 'RouteMode枚举使用',
    hook: /RouteMode\.(DRIVING|TAXI|TRANSIT|WALKING|CYCLING)/,
    store: /RouteMode\.(DRIVING|TAXI|TRANSIT|WALKING|CYCLING)/,
    description: '检查枚举值替代字符串字面量',
  },
  {
    name: 'AddressReturnKey枚举使用',
    hook: /AddressReturnKey\.(START_LOCATION|END_LOCATION)/,
    store: null, // Store不直接使用
    description: '检查地址返回键类型安全',
  },
  {
    name: '类型导入一致性',
    hook: /import.*{[\s\S]*RouteMode[\s\S]*}.*from.*navigation\.types/,
    store: /import.*{[\s\S]*RouteMode[\s\S]*}.*from.*navigation\.types/,
    description: '检查统一类型导入',
  },
  {
    name: '类型守卫使用',
    hook: /isValidAddressData\(.*\)/,
    store: null,
    description: '检查运行时类型验证',
  },
];

consistencyChecks.forEach(({ name, hook, store, description }) => {
  let hookMatch = false;
  let storeMatch = true; // 默认通过，如果不需要检查store

  if (fs.existsSync(HOOK_FILE)) {
    const hookContent = fs.readFileSync(HOOK_FILE, 'utf8');
    hookMatch = hook.test(hookContent);
  }

  if (store && fs.existsSync(STORE_FILE)) {
    const storeContent = fs.readFileSync(STORE_FILE, 'utf8');
    storeMatch = store.test(storeContent);
  }

  const consistent = hookMatch && storeMatch;
  testResults.typeConsistency.push({
    name,
    status: consistent ? 'PASS' : 'FAIL',
    hookMatch,
    storeMatch,
    description,
  });

  console.log(
    `${consistent ? '✅' : '❌'} ${name}: ${consistent ? 'CONSISTENT' : 'INCONSISTENT'}`
  );
  console.log(`    ${description}`);
});

// 4. 类型守卫和运行时验证
console.log('\n🛡️ [Test 4] 类型守卫和运行时验证...');

if (fs.existsSync(TYPE_FILE)) {
  const typeContent = fs.readFileSync(TYPE_FILE, 'utf8');

  const guardChecks = [
    {
      name: '坐标验证守卫',
      pattern: /isValidCoordinate.*: coord is BasicLocation/,
      required: true,
    },
    {
      name: '地址数据验证守卫',
      pattern: /isValidAddressData.*: data is AddressData/,
      required: true,
    },
    {
      name: '路线模式验证守卫',
      pattern: /isValidRouteMode.*: mode is RouteMode/,
      required: true,
    },
    {
      name: 'GPS位置验证守卫',
      pattern: /isValidGPSLocation.*: location is GPSLocation/,
      required: true,
    },
  ];

  guardChecks.forEach(({ name, pattern, required }) => {
    const found = pattern.test(typeContent);
    testResults.typeGuards.push({
      name,
      status: found ? 'PASS' : 'FAIL',
      required,
    });

    console.log(
      `${found ? '✅' : '❌'} ${name}: ${found ? 'IMPLEMENTED' : 'MISSING'}`
    );
  });

  // 检查类型守卫在Hook中的使用
  if (fs.existsSync(HOOK_FILE)) {
    const hookContent = fs.readFileSync(HOOK_FILE, 'utf8');
    const guardUsage = /isValidAddressData\(/.test(hookContent);

    testResults.typeGuards.push({
      name: '类型守卫实际使用',
      status: guardUsage ? 'PASS' : 'FAIL',
      required: true,
    });

    console.log(
      `${guardUsage ? '✅' : '❌'} 类型守卫实际使用: ${guardUsage ? 'USED' : 'UNUSED'}`
    );
  }
}

// 5. 生成类型安全报告
console.log('\n📊 [Type Safety Report] 类型安全性验证汇总:');

const generateCategoryReport = (tests, categoryName) => {
  const total = tests.length;
  const passed = tests.filter(t => t.status === 'PASS').length;
  const required = tests.filter(t => t.required).length;
  const requiredPassed = tests.filter(
    t => t.required && t.status === 'PASS'
  ).length;
  const percentage = Math.round((passed / total) * 100);

  console.log(`\n${categoryName}:`);
  console.log(`  ✅ 通过: ${passed}/${total} (${percentage}%)`);

  if (required > 0) {
    console.log(
      `  🔥 必需项: ${requiredPassed}/${required} (${Math.round((requiredPassed / required) * 100)}%)`
    );
  }

  if (passed < total) {
    console.log(`  ❌ 失败项目:`);
    tests
      .filter(t => t.status === 'FAIL')
      .forEach(test => {
        if (test.anyCount !== undefined) {
          console.log(`    - ${test.name}: ${test.anyCount} any类型使用`);
        } else {
          console.log(`    - ${test.name}${test.required ? ' (必需)' : ''}`);
        }
      });
  }

  return percentage;
};

const typeDefScore = generateCategoryReport(
  testResults.typeDefinitions,
  '📋 统一类型定义'
);
const anyRemovalScore = generateCategoryReport(
  testResults.anyTypeRemoval,
  '🚫 any类型移除'
);
const consistencyScore = generateCategoryReport(
  testResults.typeConsistency,
  '🔗 类型一致性'
);
const guardsScore = generateCategoryReport(
  testResults.typeGuards,
  '🛡️ 类型守卫'
);

const overallScore = Math.round(
  (typeDefScore + anyRemovalScore + consistencyScore + guardsScore) / 4
);

console.log(
  `\n🎯 [OVERALL TYPE SAFETY SCORE] 类型安全质量评分: ${overallScore}% 🎯`
);

// 6. 类型安全等级评估
if (overallScore >= 95) {
  console.log('🚀 [STATUS] 类型安全A级标准！100%类型安全，无any类型！');
} else if (overallScore >= 90) {
  console.log('⭐ [STATUS] 类型安全A-级标准！优秀的类型定义和使用！');
} else if (overallScore >= 80) {
  console.log('✅ [STATUS] 类型安全B+级标准！良好的类型定义，需少量优化！');
} else if (overallScore >= 70) {
  console.log('⚠️ [STATUS] 类型安全B级标准！基本类型安全，需要改进！');
} else {
  console.log('❌ [STATUS] 类型安全不达标！需要大幅改进类型定义！');
}

// 7. 下一步行动建议
console.log('\n📋 [Next Actions] 类型安全改进建议:');

if (overallScore < 100) {
  console.log('1. 🔧 修复剩余的any类型使用');
  console.log('2. 🛡️ 完善类型守卫的实际使用');
  console.log('3. 🔗 确保所有文件使用统一类型定义');
  console.log('4. 📝 更新组件Props使用新的类型接口');
}

if (overallScore >= 90) {
  console.log('1. ✅ 验证TypeScript编译通过');
  console.log('2. 🎯 继续Phase 2.3任务 (深度集成验证)');
  console.log('3. 📊 运行性能基准测试');
  console.log('4. 🏁 准备整体架构验收测试');
}

// 8. 类型覆盖率统计
const totalFilesChecked = filesToCheck.length;
const filesWithoutAny = testResults.anyTypeRemoval.filter(
  t => t.anyCount === 0
).length;
const typeCoverageRate = Math.round(
  (filesWithoutAny / totalFilesChecked) * 100
);

console.log(`\n📈 [Type Coverage] 类型覆盖统计:`);
console.log(`  📁 检查文件: ${totalFilesChecked}`);
console.log(`  🚫 无any类型文件: ${filesWithoutAny}`);
console.log(`  📊 类型覆盖率: ${typeCoverageRate}%`);

console.log('\n🔚 [TypeSafetyTest] Phase 2.2类型安全性验证完成！');
