/**
 * 求租求购表单页面
 * @fileoverview 通用的求租求购信息填写页面，支持橙色背景和响应式设计
 * <AUTHOR> Assistant
 * @since 2025-06-30
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import FeedbackService from '../../../shared/services/FeedbackService';
// import { LinearGradient } from 'expo-linear-gradient'; // 临时注释以解决原生模块问题
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// 导航类型 - safeNavigate暂时未使用
import { useForm, Controller } from 'react-hook-form';
// import { zodResolver } from '@hookform/resolvers/zod'; // 临时注释以解决模块问题
import { z } from 'zod';
import { useQueryClient } from '@tanstack/react-query';

// 响应式工具
import { wp, hp, fp } from '../../../shared/utils/responsiveUtils';

// 手机号验证工具
import {
  validateChineseMobile,
  formatMobileInput,
  maskMobile,


} from '../../../shared/utils/phoneValidation';

// 组件导入
import { DemandDropdown } from '../components/FormInputs/DemandDropdown';

// 认证相关
import { useAuth } from '../../../contexts/AuthContext';
import { storage } from '../../../shared/services/client';



// API服务
import { demandService } from '../services/demandService';

// 🚀 统一转换层
import { Transformers } from '../../../shared/services/dataTransform';
import useTagRecommendations from '../../../shared/hooks/useTagRecommendations';
import useAITagRecommendations from '../../../shared/hooks/useAITagRecommendations';

// 🚀 智能滚动系统
import { useSmartScrollToError } from '../../../shared/hooks/useSmartScrollToError';
import { SmartFormField } from '../../../shared/components/form/SmartFormField';
// 🚀 键盘适配组件 - KeyboardAvoidingView已在上面导入

// 常量和类型
import {
  PROPERTY_TYPE_OPTIONS,
  LAYOUT_TYPE_OPTIONS,
  AREA_RANGE_OPTIONS,
  RENT_RANGE_OPTIONS,
  PURCHASE_PRICE_OPTIONS,
  TRANSFER_FEE_OPTIONS,
  FLOOR_PREFERENCE_OPTIONS,
  ORIENTATION_OPTIONS,
  DECORATION_OPTIONS,
  INDUSTRY_TYPE_OPTIONS,
  LEASE_TERM_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  NANNING_DISTRICTS,


} from '../constants/demandConstants';

import type { DemandType, DemandForm } from '../types/demand.types';

// 路由参数类型
type DemandFormRouteProp = RouteProp<
  {
    DemandForm: {
      demandType: DemandType;
      editMode?: boolean;
      demandId?: string;
      demandData?: any;
    };
  },
  'DemandForm'
>;

// 表单验证 Schema
const demandFormSchema = z.object({
  demandType: z.enum(['RENTAL', 'PURCHASE']),
  propertyType: z.array(z.string()).min(1, '请至少选择一种房源类型'),
  location: z.object({
    city: z.string().min(1, '请选择城市'),
    districts: z.array(z.string()).min(1, '请至少选择一个区域'),
    landmarks: z.array(z.string()).optional(),
    transportRequirements: z.array(z.string()).optional(),
  }),
  areaRange: z.object({
    min: z.number().min(1, '最小面积不能少于1平米'),
    max: z.number().min(1, '最大面积不能少于1平米'),
    unit: z.string(),
  }),
  budgetRange: z.object({
    min: z.number().min(1, '预算范围无效'),
    max: z.number().min(1, '预算范围无效'),
    unit: z.string(),
  }),
  industryType: z.array(z.string()).min(1, '请选择行业类型'),
  layoutType: z.array(z.string()).optional(),
  specialRequirements: z.string().optional(),
  contactInfo: z.object({
    phone: z
      .string()
      .min(11, '手机号必须为11位数字')
      .max(11, '手机号必须为11位数字')
      .refine(phone => validateChineseMobile(phone), {
        message: '请输入正确的中国大陆手机号码',
      }),
    surname: z.string().min(1, '请输入姓氏').max(5, '姓氏不能超过5个字符'),
    gender: z.enum(['male', 'female'], { required_error: '请选择性别' }),
    wechat: z.string().optional(),
    preferredContactTime: z.string(),
    contactMethod: z.string(),
    additionalContacts: z
      .array(
        z.object({
          phone: z
            .string()
            .min(11, '手机号必须为11位数字')
            .max(11, '手机号必须为11位数字')
            .refine(phone => validateChineseMobile(phone), {
              message: '请输入正确的中国大陆手机号码',
            }),
          verificationCode: z.string().min(6, '验证码必须为6位数字'),
          surname: z
            .string()
            .min(1, '请输入姓氏')
            .max(5, '姓氏不能超过5个字符'),
          gender: z.enum(['male', 'female'], { required_error: '请选择性别' }),
          isVerified: z.boolean().default(false),
        })
      )
      .max(2, '最多只能添加2位联络人')
      .optional(),
  }),
  agreeToRules: z.boolean().refine(val => val === true, {
    message: '必须同意发布规则才能提交',
  }),
  // 新增缺失的字段，支持多选
  floorPreference: z.array(z.string()).optional(),
  orientation: z.array(z.string()).optional(),
  decoration: z.string().optional(),
  leaseTerm: z.string().optional(),
  paymentMethod: z.string().optional(),
  transferFee: z.string().optional(),
});

type FormData = z.infer<typeof demandFormSchema>;

/**
 * 求租求购表单页面组件
 */
const DemandFormScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute<DemandFormRouteProp>();
  const insets = useSafeAreaInsets();
  const { state } = useAuth();
  const user = state.user;
  const queryClient = useQueryClient();
  const isAuthenticated = !!state.userToken; // 通过userToken判断是否已认证


  // 获取需求类型（求租/求购）
  const initialDemandType = route.params?.demandType || 'RENTAL';

  // 🔧 编辑模式支持
  const isEditMode = route.params?.editMode || false;
  const editDemandId = route.params?.demandId;
  const editDemandData = route.params?.demandData;

  // 🔥 草稿编辑：如果是编辑模式，设置draftId
  useEffect(() => {
    if (isEditMode && editDemandId) {
      setDraftId(editDemandId);
      console.log('[DemandForm] 🔥 设置草稿ID:', editDemandId);
    }
  }, [isEditMode, editDemandId]);

  // 🔧 编辑模式日志（仅在开发模式下输出，避免循环）
  useEffect(() => {
    if (__DEV__) {
      console.log('[DemandForm] 🚀 页面初始化:', {
        isEditMode,
        editDemandId,
        initialDemandType,
        hasEditData: !!editDemandData
      });

      if (isEditMode && editDemandData) {
        console.log('[DemandForm] 📊 编辑数据详情:', JSON.stringify(editDemandData, null, 2));
      }
    }
  }, [editDemandData, editDemandId, initialDemandType, isEditMode]); // 添加缺失的依赖

  // 表单状态
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingEditData, setIsLoadingEditData] = useState(isEditMode);
  const [isSavingDraft, setIsSavingDraft] = useState(false);
  const [lastSavedTime, setLastSavedTime] = useState<Date | null>(null);
  const [draftId, setDraftId] = useState<string | null>(null);
  const [currentDemandType, setCurrentDemandType] =
    useState<DemandType>(initialDemandType);

  // 🚀 需求状态跟踪：用于决定是创建副本还是直接更新 - 参考开发日志8.3第606-610行
  const [originalDemandStatus, setOriginalDemandStatus] = useState<string | null>(null);
  const [selectedFeatureTags, setSelectedFeatureTags] = useState<string[]>([]);
  const [selectedPropertyTypes, setSelectedPropertyTypes] = useState<string[]>(
    []
  );
  const [hasInitialTagsGenerated, setHasInitialTagsGenerated] = useState(false);
  const [showTags, setShowTags] = useState(false);
  const [additionalContacts, setAdditionalContacts] = useState<any[]>([]);
  const [removedTags, setRemovedTags] = useState<string[]>([]); // 存储被删除的标签
  const [showMaxTagsWarning, setShowMaxTagsWarning] = useState(false); // 控制小红字提醒显示

  // 🚀 智能验证错误状态
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // 🔧 实时验证：单个字段验证函数（参考房源发布页面）
  const validateSingleField = useCallback((fieldName: string, value: any): string | null => {
    console.log(`🔍 [实时验证] 验证字段: ${fieldName}`, value);

    switch (fieldName) {
      case 'propertyType':
        if (!value || value.length === 0) {
          return '请至少选择一种房源类型';
        }
        break;

      case 'location.districts':
        if (!value || value.length === 0) {
          return '请至少选择一个目标区域';
        }
        break;

      case 'areaRange':
        if (!value || value.min <= 0 || value.max <= 0 || value.min >= value.max) {
          return '请设置有效的面积范围';
        }
        break;

      case 'budgetRange':
        if (!value || value.min <= 0 || value.max <= 0 || value.min >= value.max) {
          return '请设置有效的预算范围';
        }
        break;

      case 'industryType':
        if (!value || value.length === 0) {
          return '请至少选择一种行业类型';
        }
        break;

      case 'contactInfo.surname':
        if (!value || value.trim().length === 0) {
          return '请填写姓氏';
        }
        break;

      case 'contactInfo.gender':
        if (!value) {
          return '请选择性别';
        }
        break;

      case 'agreeToRules':
        if (!value) {
          return '请同意发布规则';
        }
        break;
    }

    return null;
  }, []);

  // 🔧 实时更新字段错误状态
  const updateFieldError = useCallback((fieldName: string, value: any) => {
    const error = validateSingleField(fieldName, value);
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      if (error) {
        newErrors[fieldName] = error;
      } else {
        delete newErrors[fieldName];
      }
      return newErrors;
    });
  }, [validateSingleField]);



  // 使用AI标签推荐Hook
  const {
    recommendedTags,
    relatedTags: availableNewTags,
    isLoading: isGeneratingTags,
    generateAITags,
    refreshRelatedTags,
  } = useAITagRecommendations();

  // 🚀 智能滚动定位系统
  const {
    scrollViewRef,
    registerField,
    scrollToFirstError,
    updateScrollPosition,
  } = useSmartScrollToError();

  // 保持原有的refreshTags函数兼容性
  const refreshTags = () => {
    refreshRelatedTags(selectedPropertyTypes[0] || '');
  };

  // 🚀 智能表单验证函数（按DOM顺序）
  const validateFormWithSmartScroll = async (data: FormData) => {
    // 🔍 调试：打印当前表单数据结构
    console.log('🔍 [DemandForm] 验证数据结构:', {
      propertyType: data.propertyType,
      location: data.location,
      areaRange: data.areaRange,
      budgetRange: data.budgetRange,
      industryType: data.industryType,
      contactInfo: data.contactInfo,
      agreeToRules: data.agreeToRules,
    });

    // 🎯 定义字段在表单中的DOM顺序（从上到下）
    const fieldDOMOrder = [
      'contactInfo.surname',           // 1. 姓氏和性别（最上方，合并字段）
      'demandType',                   // 2. 需求类型
      'propertyType',                 // 3. 房源类型
      'location.districts',           // 4. 期望区域
      'areaRange',                    // 5. 面积范围
      'budgetRange',                  // 6. 预算范围
      'industryType',                 // 7. 行业类型
      'layoutType',                   // 8. 户型偏好
      'floorPreference',              // 9. 楼层偏好（求租特有）
      'orientation',                  // 10. 朝向偏好（求租特有）
      'decoration',                  // 11. 装修要求（求租特有）
      'leaseTerm',                    // 12. 租期要求（求租特有）
      'paymentMethod',                // 13. 付款方式（求购特有）
      'transferFee',                  // 14. 转让费（可选）
      'specialRequirements',          // 15. 特殊要求
      'agreeToRules',                // 16. 同意规则（最下方）
    ];

    // 🎯 按定义的顺序检查每个字段
    const fieldErrors = [];
    const newErrors: Record<string, string> = {};

    // 按定义的顺序检查每个字段
    for (const fieldName of fieldDOMOrder) {
      // 根据字段名称进行相应的验证
      if (fieldName === 'contactInfo.surname') {
        // 🔧 合并姓氏和性别验证（它们在同一个SmartFormField中）
        const surnameEmpty = !data.contactInfo?.surname || data.contactInfo.surname.trim().length === 0;
        const genderEmpty = !data.contactInfo?.gender;

        if (surnameEmpty && genderEmpty) {
          fieldErrors.push({ fieldName, message: '请输入姓氏并选择性别' });
          newErrors[fieldName] = '请输入姓氏并选择性别';
        } else if (surnameEmpty) {
          fieldErrors.push({ fieldName, message: '请输入姓氏' });
          newErrors[fieldName] = '请输入姓氏';
        } else if (genderEmpty) {
          fieldErrors.push({ fieldName, message: '请选择性别' });
          newErrors[fieldName] = '请选择性别';
        }
      }
      if (fieldName === 'propertyType' && (!data.propertyType || data.propertyType.length === 0)) {
        fieldErrors.push({ fieldName, message: '请至少选择一种房源类型' });
        newErrors[fieldName] = '请至少选择一种房源类型';
      }
      if (fieldName === 'location.districts' && (!data.location?.districts || data.location.districts.length === 0)) {
        fieldErrors.push({ fieldName, message: '请至少选择一个目标区域' });
        newErrors[fieldName] = '请至少选择一个目标区域';
      }
      if (fieldName === 'areaRange') {
        // 🔧 编辑模式下，只要有数据就认为有效；新建模式下需要userSelected标记
        const hasValidRange = data.areaRange && data.areaRange.min > 0 && data.areaRange.max > 0 && data.areaRange.min < data.areaRange.max;
        const isUserSelected = (data.areaRange as any)?.userSelected;

        if (!hasValidRange || (!isEditMode && !isUserSelected)) {
          fieldErrors.push({ fieldName, message: '请选择面积范围' });
          newErrors[fieldName] = '请选择面积范围';
        }
      }
      if (fieldName === 'budgetRange') {
        // 🔧 编辑模式下，只要有数据就认为有效；新建模式下需要userSelected标记
        const hasValidRange = data.budgetRange && data.budgetRange.min > 0 && data.budgetRange.max > 0 && data.budgetRange.min < data.budgetRange.max;
        const isUserSelected = (data.budgetRange as any)?.userSelected;

        if (!hasValidRange || (!isEditMode && !isUserSelected)) {
          fieldErrors.push({ fieldName, message: '请选择预算范围' });
          newErrors[fieldName] = '请选择预算范围';
        }
      }
      if (fieldName === 'industryType' && (!data.industryType || data.industryType.length === 0)) {
        fieldErrors.push({ fieldName, message: '请至少选择一种行业类型' });
        newErrors[fieldName] = '请至少选择一种行业类型';
      }
      if (fieldName === 'agreeToRules' && !data.agreeToRules) {
        fieldErrors.push({ fieldName, message: '请同意发布规则' });
        newErrors[fieldName] = '请同意发布规则';
      }
    }

    // 如果有错误，设置错误状态并智能滚动到第一个错误字段
    if (fieldErrors.length > 0) {
      console.log('🎯 [DemandForm] 验证失败，错误字段:', fieldErrors.map(e => e.fieldName));

      // 🔧 设置错误状态到Store，显示红色边框
      setValidationErrors(newErrors);

      // 🚀 使用智能滚动定位到第一个错误字段
      try {
        await scrollToFirstError(fieldErrors, {
          showErrorMessage: false, // 不显示弹窗，只滚动定位
        });
      } catch (error) {
        console.error('❌ [DemandForm] 智能滚动失败:', error);
      }

      return { isValid: false, errors: fieldErrors };
    }

    // 🎯 所有验证通过，清空错误状态
    setValidationErrors({});
    return { isValid: true, errors: [] };
  };

  // 🚀 参考房源发布页面的成功滚动逻辑
  const scrollToFirstErrorByDOMOrder = useCallback(async (fieldErrors: Array<{fieldName: string, message: string}>) => {
    if (!fieldErrors.length || !scrollViewRef.current) return;

    console.log('🎯 [DemandForm] 开始按DOM顺序滚动到第一个错误字段:', fieldErrors.map(e => e.fieldName));

    // 🔧 定义字段在表单中的DOM顺序（从上到下，按实际布局顺序）
    const fieldDOMOrder = [
      'propertyType',        // 1. 房源类型 (行1228)
      'location.districts',  // 2. 目标区域 (行1285)
      'areaRange',          // 3. 面积范围 (行1318)
      'industryType',       // 4. 行业类型 (行1458)
      'budgetRange',        // 5. 预算范围 (行1491) - 🔧 修正：在industryType之后
      'contactInfo.surname', // 6. 姓氏 (行1589)
      'contactInfo.gender',  // 7. 性别
      'agreeToRules',       // 8. 同意规则 (行2014)
    ];

    // 🎯 按DOM顺序找到第一个错误字段
    let firstErrorField = null;
    for (const fieldName of fieldDOMOrder) {
      const errorFound = fieldErrors.find(error => error.fieldName === fieldName);
      if (errorFound) {
        firstErrorField = errorFound;
        break;
      }
    }

    if (!firstErrorField) {
      console.warn('❌ [DemandForm] 未找到第一个错误字段');
      return;
    }

    console.log('🎯 [DemandForm] 找到第一个错误字段:', firstErrorField.fieldName);

    // 🚀 使用现有的滚动系统滚动到该字段
    try {
      await scrollToFirstError([firstErrorField], {
        showErrorMessage: false,
      });
      console.log('✅ [DemandForm] 成功滚动到第一个错误字段:', firstErrorField.fieldName);
    } catch (error) {
      console.error('❌ [DemandForm] 滚动失败:', error);
      // 备用方案：滚动到顶部
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({ y: 0, animated: true });
      }
    }
  }, [scrollToFirstError, scrollViewRef]);

  // 为额外联络人发送验证码
  const sendContactVerificationCode = async (phone: string) => {
    try {
      await demandService.sendSMSVerification(phone, 'contact_person');
      FeedbackService.showInfo(`验证码已发送到 ${maskMobile(phone)}`);
    } catch (error: any) {
      FeedbackService.showCodeSendFailed(error.message || '验证码发送失败，请重试');
    }
  };

  // 🔧 编辑模式：异步加载需求详情
  const [editDemandDetails, setEditDemandDetails] = useState<any>(null);

  // 🔧 编辑模式：生成表单默认值
  const getEditFormDefaultValues = (demandData: any): Partial<FormData> => {
    return {
      demandType: demandData.demand_type || initialDemandType,
      propertyType: demandData.property_type ? [demandData.property_type, ...(demandData.property_subtypes || [])] : [],
      location: {
        city: 'nanning',
        districts: demandData.target_regions || [],
        landmarks: [],
        transportRequirements: [],
      },
      areaRange: {
        min: demandData.area_min || 50,
        max: demandData.area_max || 200,
        unit: demandData.area_unit || '平方米',
      },
      budgetRange: {
        min: demandData.price_min || (initialDemandType === 'RENTAL' ? 3000 : 100),
        max: demandData.price_max || (initialDemandType === 'RENTAL' ? 10000 : 500),
        unit: demandData.price_unit || (initialDemandType === 'RENTAL' ? '元/月' : '万元'),
      },
      industryType: demandData.industry_types || [],
      layoutType: demandData.layout_types || [],
      specialRequirements: demandData.description || '', // 使用description作为特殊要求
      contactInfo: {
        phone: demandData.contact_phone || user?.phone_number || '',
        surname: demandData.contact_surname || '',
        gender: demandData.contact_gender || undefined as any,
        wechat: demandData.contact_wechat || '',
        preferredContactTime: 'ANYTIME',
        contactMethod: 'BOTH',
        additionalContacts: demandData.additional_contacts || [],
      },
      agreeToRules: true,
      // 新增字段默认值
      floorPreference: demandData.floor_preferences || [],
      orientation: demandData.orientations || [],
      decoration: demandData.decoration_levels?.[0] || '',
      leaseTerm: demandData.lease_term || '',
      paymentMethod: demandData.payment_method || '',
      transferFee: demandData.transfer_fee || '',
    };
  };

  // 🔧 编辑模式：生成默认值
  const getDefaultValues = (): Partial<FormData> => {
    // 编辑模式下，如果还没有加载详情，返回基础默认值
    if (isEditMode && !editDemandDetails) {
      return getNewFormDefaultValues();
    }

    // 编辑模式下，如果已经加载了详情，使用详情数据
    if (isEditMode && editDemandDetails) {
      return getEditFormDefaultValues(editDemandDetails);
    }

    // 默认值（新建模式）
    return getNewFormDefaultValues();
  };

  // 🔧 新建模式：默认值
  const getNewFormDefaultValues = (): Partial<FormData> => ({
    demandType: initialDemandType,
    propertyType: [],
    location: {
      city: 'nanning',
      districts: [],
      landmarks: [],
      transportRequirements: [],
    },
    areaRange: {
      min: 50,
      max: 200,
      unit: '平方米',
    },
    budgetRange: {
      min: initialDemandType === 'RENTAL' ? 3000 : 100,
      max: initialDemandType === 'RENTAL' ? 10000 : 500,
      unit: initialDemandType === 'RENTAL' ? '元/月' : '万元',
    },
    industryType: [],
    layoutType: [],
    specialRequirements: '',
    contactInfo: {
      phone: user?.phone_number || '',
      surname: '',
      gender: undefined as any,
      wechat: '',
      preferredContactTime: 'ANYTIME',
      contactMethod: 'BOTH',
      additionalContacts: [],
    },
    agreeToRules: false,
    // 新增字段默认值
    floorPreference: [],
    orientation: [],
    decoration: '',
    leaseTerm: '',
    paymentMethod: '',
    transferFee: '',
  });

  // React Hook Form 配置 - 使用正确的初始化方式
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<FormData>({
    mode: 'onChange',
    defaultValues: getDefaultValues(),
  });

  // 🔧 编辑模式：加载需求详情（使用ref避免循环依赖）
  const hasLoadedEditData = useRef(false);

  useEffect(() => {
    const loadDemandDetails = async () => {
      if (isEditMode && editDemandId && !hasLoadedEditData.current) {
        try {
          hasLoadedEditData.current = true; // 标记已加载，避免重复加载
          setIsLoadingEditData(true);
          console.log('[DemandForm] 🔄 加载需求详情:', editDemandId);

          // 调用API获取完整的需求详情
          const response = await demandService.getDemandById(editDemandId);
          console.log('[DemandForm] ✅ 需求详情加载成功:', response);

          setEditDemandDetails(response);

          // 🚀 设置原始需求状态 - 参考开发日志8.3第612-618行
          setOriginalDemandStatus(response.status || null);
          console.log('[DemandForm] 🏷️ 原始需求状态:', response.status);

          // 重新设置表单默认值
          console.log('[DemandForm] 🔄 开始生成表单默认值...');
          const formDefaults = getEditFormDefaultValues(response);
          console.log('[DemandForm] 📋 生成的表单默认值:', JSON.stringify(formDefaults, null, 2));

          console.log('[DemandForm] 🔄 开始重置表单...');
          reset(formDefaults);
          console.log('[DemandForm] ✅ 表单重置完成');

          // 设置其他状态
          setCurrentDemandType(response.demand_type || initialDemandType);
          // 注意：DemandResponse可能没有feature_tags字段，使用安全访问
          if ((response as any).custom_tags) {
            setSelectedFeatureTags((response as any).custom_tags);
          }

        } catch (error) {
          console.error('[DemandForm] ❌ 加载需求详情失败:', error);
          FeedbackService.showError('加载需求详情失败，请重试');
          hasLoadedEditData.current = false; // 加载失败时重置标记
        } finally {
          setIsLoadingEditData(false);
        }
      }
    };

    loadDemandDetails();
  }, [isEditMode, editDemandId, getEditFormDefaultValues, initialDemandType, reset]); // 添加缺失的依赖

  // 监听表单字段变化，自动生成AI标签
  const watchedFields = watch();





  // 检查并恢复保存的表单数据（用户登录后）
  useEffect(() => {
    const restoreFormData = async () => {
      try {
        // 只有在用户已登录的情况下才尝试恢复数据
        if (isAuthenticated && user) {
          const savedFormData = await storage.getString('pendingDemandForm');
          if (savedFormData) {
            const parsedData = JSON.parse(savedFormData);
            
            // 检查数据时效性（使用智能过期时间）
            const now = Date.now();
            const expiresAt = parsedData.expiresAt || (parsedData.timestamp + 30 * 60 * 1000); // 默认30分钟
            
            if (now < expiresAt) {
              // 恢复表单数据
              reset(parsedData);
              setCurrentDemandType(parsedData.demandType || 'RENTAL');
              setSelectedFeatureTags(parsedData.selectedFeatureTags || []);
              setAdditionalContacts(parsedData.additionalContacts || []);
              
              // 显示成功消息
              FeedbackService.showInfo('已为您恢复之前填写的表单内容，请检查后提交。');
            }
            
            // 清除保存的数据
            await storage.delete('pendingDemandForm');
          }
        }
      } catch (error) {
        console.error('恢复表单数据失败:', error);
        // 清除可能损坏的数据
        try {
          await storage.delete('pendingDemandForm');
        } catch (clearError) {
          console.error('清除损坏数据失败:', clearError);
        }
      }
    };

    restoreFormData();
  }, [isAuthenticated, user, reset, handleSubmit]);

  // 监听表单必填项，以触发AI标签生成
  // 🔧 在编辑模式下，直接使用已有标签，避免AI标签生成循环
  useEffect(() => {
    if (isEditMode && editDemandDetails?.custom_tags?.length > 0 && !hasInitialTagsGenerated) {
      console.log('[DemandForm] 编辑模式：使用已有标签', editDemandDetails.custom_tags);
      setSelectedFeatureTags(editDemandDetails.custom_tags);
      setShowTags(true);
      setHasInitialTagsGenerated(true);
      return;
    }
  }, [isEditMode, editDemandDetails, hasInitialTagsGenerated]);

  useEffect(() => {
    const arePrerequisitesMet = () => {
      const values = {
        propertyType: watch('propertyType'),
        districts: watch('location.districts'),
        areaRange: watch('areaRange'),
        budgetRange: watch('budgetRange'),
        industryType: watch('industryType'),
      };
      return (
        values.propertyType?.length > 0 &&
        values.districts?.length > 0 &&
        values.areaRange?.min > 0 &&
        values.budgetRange?.min > 0 &&
        values.industryType?.length > 0
      );
    };

    const generateInitialTags = async () => {
      const values = {
        demandType: currentDemandType,  // 传递需求类型
        propertyType: watch('propertyType')?.[0] || '',
        districts: watch('location.districts') || [],
        areaRange: watch('areaRange') || { min: 0, max: 0 },
        budgetRange: watch('budgetRange') || { min: 0, max: 0 },
        industryType: watch('industryType')?.[0] || '',
      };

      console.log('[DemandForm] 必填项已完成，开始生成AI推荐标签...', values);
      
      // 调用AI标签生成
      const result = await generateAITags(values);
      
      if (result.recommendedTags.length > 0) {
        // 自动将AI推荐的10个标签设置为已选标签
        setSelectedFeatureTags(result.recommendedTags);
        console.log('[DemandForm] AI推荐标签已自动选择:', result.recommendedTags);
      }
      
      setShowTags(true);
      setHasInitialTagsGenerated(true);
    };

    if (arePrerequisitesMet() && !hasInitialTagsGenerated) {
      generateInitialTags();
    }
  }, [hasInitialTagsGenerated, generateAITags, currentDemandType, watch]); // 添加watch依赖

  // 页面配置 - 支持编辑模式
  const pageConfig = {
    title: isEditMode
      ? `编辑${currentDemandType === 'RENTAL' ? '求租' : '求购'}需求`
      : (currentDemandType === 'RENTAL' ? '快速找房' : '快速找房购买'),
    subtitle: isEditMode
      ? `修改您的${currentDemandType === 'RENTAL' ? '求租' : '求购'}需求信息`
      : (currentDemandType === 'RENTAL'
        ? '现业为另有房在找房，房源也在找你呢！\n快速写你的需求，选址也需要双向奔赴！'
        : '找到心仪的房源，投资未来！\n快速填写购买需求，精准匹配优质房源！'),
    budgetLabel: currentDemandType === 'RENTAL' ? '租金' : '总价',
    budgetOptions:
      currentDemandType === 'RENTAL'
        ? RENT_RANGE_OPTIONS
        : PURCHASE_PRICE_OPTIONS,
  };

  // 获取可用的户型选项（基于选择的房源类型）
  const getAvailableLayoutTypes = () => {
    if (selectedPropertyTypes.length === 0) {
      return [];
    }

    // 如果只选择了一种类型，返回该类型的户型选项
    if (selectedPropertyTypes.length === 1) {
      return LAYOUT_TYPE_OPTIONS[selectedPropertyTypes[0]] || [];
    }

    // 如果选择了多种类型，合并所有户型选项并去重
    const allLayoutTypes: any[] = [];
    selectedPropertyTypes.forEach(type => {
      const layoutTypes = LAYOUT_TYPE_OPTIONS[type] || [];
      layoutTypes.forEach(layout => {
        if (!allLayoutTypes.find(existing => existing.value === layout.value)) {
          allLayoutTypes.push(layout);
        }
      });
    });

    return allLayoutTypes;
  };

  // 处理返回
  const handleBack = () => {
    navigation.goBack();
  };

  // 🚀 智能表单提交处理（绕过react-hook-form的默认验证）
  const handleSmartFormSubmit = async () => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);

      // 🔐 首先检查用户登录状态 - 企业级业务规则
      if (!user || !isAuthenticated) {
        FeedbackService.showInfo('您填写的内容很棒！请登录后即可发布成功。');
        setIsSubmitting(false);
        return;
      }

      // 🚀 获取当前表单数据并进行智能验证
      const currentData = watch(); // 获取当前表单数据
      console.log('🔍 [DemandForm] 当前表单数据:', currentData);

      const validationResult = await validateFormWithSmartScroll(currentData);
      if (!validationResult.isValid) {
        console.log('❌ [DemandForm] 表单验证失败，已滚动到错误字段');
        setIsSubmitting(false);
        return; // 不显示弹窗，直接返回
      }

      // 继续原有的提交逻辑
      await handleActualFormSubmit(currentData);

    } catch (error) {
      console.error('❌ [DemandForm] 表单提交失败:', error);
      FeedbackService.showError('提交失败，请重试');
      setIsSubmitting(false);
    }
  };

  // 实际的表单提交逻辑
  const handleActualFormSubmit = async (data: FormData) => {
    try {
      // 验证额外联络人信息（可选，但如果填写了必须完整）
      for (let i = 0; i < additionalContacts.length; i++) {
        const contact = additionalContacts[i];
        // 只有当联络人有任何信息时才验证
        const hasAnyInfo =
          contact.phone ||
          contact.surname ||
          contact.gender ||
          contact.verificationCode;
        if (hasAnyInfo) {
          if (!contact.phone || contact.phone.length !== 11) {
            FeedbackService.showInfo('请完整填写联络人手机号');
            return;
          }

          // 🚨 强制验证：联系人电话不能是用户自己的电话
          if (contact.phone === user?.phone_number) {
            FeedbackService.showInfo(`第${i + 1}个增加联系人的电话不能是您自己的手机号(${user?.phone_number})，请使用其他人的电话`
            );
            return;
          }

          if (!contact.surname || !contact.gender) {
            FeedbackService.showInfo('请完整填写联络人姓氏和性别');
            return;
          }
          if (
            !contact.verificationCode ||
            contact.verificationCode.length !== 6
          ) {
            FeedbackService.showInfo('请完成联络人手机号验证');
            return;
          }

          // 检查联系人之间的电话不能重复
          for (let j = i + 1; j < additionalContacts.length; j++) {
            const otherContact = additionalContacts[j];
            if (contact.phone === otherContact.phone && otherContact.phone) {
              FeedbackService.showInfo('增加联系人之间的电话不能重复');
              return;
            }
          }
        }
      }

      // 🚀 使用企业级数据转换层
      const { Transformers } = await import('../../../shared/services/dataTransform');

      // 准备转换选项
      const transformOptions = {
        demandType: data.demandType,
        selectedTags: selectedFeatureTags || [],
        context: 'publish'
      };

      // 🔧 合并additionalContacts数据到表单数据中
      const dataWithContacts = {
        ...data,
        contactInfo: {
          ...data.contactInfo,
          additionalContacts: additionalContacts || []
        }
      };

      console.log('🔄 [DemandForm] 使用统一转换层转换数据...');
      console.log('📝 [DemandForm] 原始表单数据:', dataWithContacts);
      console.log('⚙️ [DemandForm] 转换选项:', transformOptions);

      // 🚀 使用企业级转换器转换数据
      const transformResult = Transformers.demand.toAPI(dataWithContacts as any, {
        context: 'publish',
        selectedTags: transformOptions.selectedTags
      });

      if (!transformResult.success) {
        throw new Error(transformResult.error || '数据转换失败');
      }

      const transformedData = transformResult.data;
      console.log('✅ [DemandForm] 统一转换层转换完成:', transformedData);

      console.log('统一转换层转换成功:', {
        转换器: 'DemandTransformer',
        需求类型: currentDemandType,
        标签数量: selectedFeatureTags.length,
        API数据: transformedData
      });

      // 🔧 编辑模式：更新需求 vs 创建需求 - 直接调用API避免双重转换
      const { apiClient } = await import('../../../shared/services/client');
      let result;

      if (isEditMode && editDemandId) {
        // 编辑模式：更新现有需求
        console.log('🚀 [DemandForm] 开始更新需求:', editDemandId, transformedData);
        const apiResponse = await apiClient.put(`/demands/${editDemandId}`, transformedData);
        result = { success: true, data: apiResponse.data, message: `您的${currentDemandType === 'RENTAL' ? '求租' : '求购'}需求已成功更新！` };

        if (result.success) {
          // 🔥 失效相关缓存，确保列表更新
          queryClient.invalidateQueries({ queryKey: ['demand-status-counts'] });
          queryClient.invalidateQueries({ queryKey: ['demand', 'list'] });
          queryClient.invalidateQueries({ queryKey: ['demands', 'my'] });

          FeedbackService.showSuccess(result.message);
          // 返回到需求列表页面
          navigation.goBack();
        } else {
          FeedbackService.showError('更新失败，请稍后重试');
          return;
        }
      } else {
        // 新建模式：创建新需求
        console.log('🚀 [DemandForm] 开始调用API创建需求:', transformedData);
        const apiResponse = await apiClient.post('/demands', transformedData);
        result = { success: true, data: apiResponse.data };
        console.log('📡 [DemandForm] API调用结果:', result);

        if (result.success) {
          // 🔥 失效相关缓存，确保列表更新
          queryClient.invalidateQueries({ queryKey: ['demand-status-counts'] });
          queryClient.invalidateQueries({ queryKey: ['demand', 'list'] });
          queryClient.invalidateQueries({ queryKey: ['demands', 'my'] });

          // 🔧 修复：跳转到专门的需求发布成功页面
          navigation.navigate('DemandInfo', {
            formData: {
              demandId: result.data?.id || 'unknown',
              title: data.contactInfo?.surname
                ? `${data.contactInfo.surname}${data.contactInfo.gender === 'male' ? '先生' : '女士'}的需求`
                : '我的需求',
              propertyType: data.propertyType,
              location: data.location,
              areaRange: data.areaRange,
              budgetRange: data.budgetRange,
              industryType: data.industryType,
              contactInfo: data.contactInfo,
              tags: selectedFeatureTags || [],
              status: 'ACTIVE',
              ...result.data,
            },
            demandType: data.demandType
          });
        } else {
          FeedbackService.showError('提交失败，请稍后重试');
          return;
        }
      }
    } catch (error) {
      console.error('❌ [DemandForm] 提交失败:', error);
      FeedbackService.showInfo('网络错误，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 🚀 企业级草稿保存功能 - 参考开发日志实现
  const handleSaveDraft = useCallback(async () => {
    try {
      setIsSavingDraft(true);
      console.log('💾 [DemandForm] 开始保存草稿到服务器');

      // 🔐 首先检查用户登录状态
      if (!user || !isAuthenticated) {
        FeedbackService.showInfo('请登录后保存草稿');
        return;
      }

      // 🔧 合并所有数据源：React Hook Form + 状态管理
      const watchedData = watch();
      const completeFormData = {
        ...watchedData,
        demandType: currentDemandType,
        // 🔧 优先使用React Hook Form的数据，如果为空才使用状态管理的数据
        propertyType: watchedData.propertyType && watchedData.propertyType.length > 0
          ? watchedData.propertyType
          : selectedPropertyTypes,
        contactInfo: {
          ...watchedData.contactInfo,
          additionalContacts: additionalContacts,
        },
      };

      console.log('[DemandForm] 🔍 保存草稿 - 数据源对比:');
      console.log('  - watchedData.propertyType:', watchedData.propertyType);
      console.log('  - selectedPropertyTypes:', selectedPropertyTypes);
      console.log('  - 最终使用:', completeFormData.propertyType);
      console.log('[DemandForm] 🔍 保存草稿 - 合并数据:', completeFormData);

      // 🚀 企业级统一转换层 - 参考开发日志8.2第238-281行成功案例
      const draftFormData = {
        ...completeFormData,
        status: 'DRAFT' as const,  // 🔥 明确设置为草稿状态
      };

      // 🔧 使用统一转换层转换数据 - 参考房源模块成功架构
      const { Transformers } = await import('../../../shared/services/dataTransform');
      const transformOptions = {
        context: 'draft' as const,  // 🔥 草稿上下文 - 跳过严格验证
        selectedTags: selectedFeatureTags,
        validateSchema: false,      // 草稿模式跳过Schema验证
        cleanData: true,           // 启用数据清理
        ensureConstraints: true,   // 确保数据库约束
      };

      const transformResult = Transformers.demand.toAPI(draftFormData as any, transformOptions);
      if (!transformResult.success) {
        throw new Error(transformResult.error || '数据转换失败');
      }
      const transformedData = transformResult.data;
      console.log('[DemandForm] 🔄 统一转换层转换完成:', transformedData);

      // 🚀 智能草稿保存策略 - 参考开发日志8.3第619-639行成功案例
      const { apiClient } = await import('../../../shared/services/client');

      // 🚀 企业级智能草稿保存策略 - 后端Schema已修复，恢复正确逻辑
      let response;
      if (isEditMode && editDemandId) {
        if (originalDemandStatus === 'DRAFT') {
          // 🔄 原本就是草稿，直接更新（使用修复后的PUT接口）
          console.log('🔄 [DemandForm] 更新草稿需求:', editDemandId);
          const apiResponse = await apiClient.put(`/demands/${editDemandId}`, transformedData);
          response = { success: true, data: apiResponse.data };
          console.log('✅ [DemandForm] 草稿更新成功:', apiResponse.data);
        } else {
          // 🆕 原本是已发布需求，创建新的草稿副本，保持原需求不变
          console.log('🆕 [DemandForm] 编辑已发布需求，创建草稿副本');
          const apiResponse = await apiClient.post('/demands', transformedData);
          response = { success: true, data: apiResponse.data };

          // 更新为新创建的草稿ID
          if (apiResponse.data?.id) {
            setDraftId(apiResponse.data.id);
            console.log('✅ [DemandForm] 草稿副本创建成功，新ID:', apiResponse.data.id);
          }
        }
      } else {
        // 🔥 新建模式：创建新草稿
        console.log('🆕 [DemandForm] 新建模式，创建新草稿');
        const apiResponse = await apiClient.post('/demands', transformedData);
        response = { success: true, data: apiResponse.data };
        if (apiResponse.data?.id) {
          setDraftId(apiResponse.data.id);
          console.log(`[DemandForm] 创建新草稿成功: ${apiResponse.data.id}`);
        }
      }

      if (response.success) {
        setLastSavedTime(new Date());

        // 🔥 失效相关缓存，确保计数更新
        queryClient.invalidateQueries({ queryKey: ['demand-status-counts'] });
        queryClient.invalidateQueries({ queryKey: ['demand', 'list'] });

        FeedbackService.showSuccess('草稿已保存到云端');
      } else {
        throw new Error('保存失败');
      }

    } catch (error) {
      console.error('[DemandForm] 保存草稿失败:', error);
      FeedbackService.showError('保存草稿失败，请重试');
    } finally {
      setIsSavingDraft(false);
    }
  }, [currentDemandType, selectedFeatureTags, additionalContacts, draftId, watch, user, isAuthenticated, isEditMode, editDemandDetails?.status, editDemandId]);

  // 🔧 获取草稿按钮文字
  const getDraftButtonText = useCallback(() => {
    // 新建模式
    if (!isEditMode) {
      return '保存草稿';
    }

    // 编辑模式：根据原始状态判断
    if (editDemandDetails?.status === 'DRAFT') {
      return '更新草稿';
    } else {
      // 编辑已发布/已下架 → 保存为新草稿
      return '保存草稿';
    }
  }, [isEditMode, editDemandDetails?.status]);

  // 🔧 获取发布按钮文字
  const getPublishButtonText = useCallback(() => {
    // 新建模式
    if (!isEditMode) {
      return '发布';
    }

    // 编辑模式：根据原始状态判断
    if (editDemandDetails?.status === 'ACTIVE') {
      return '更新发布'; // 🔧 已发布状态编辑后应该是"更新发布"
    } else {
      return '发布'; // 草稿和已下架状态编辑后是"发布"
    }
  }, [isEditMode, editDemandDetails?.status]);

  // 从新增标签移动到已选标签
  const moveToSelected = (tagValue: string) => {
    if (selectedFeatureTags.length < 10 && !selectedFeatureTags.includes(tagValue)) {
      setSelectedFeatureTags(prev => [...prev, tagValue]);
      // 从被删除的标签列表中移除（如果存在）
      setRemovedTags(prev => prev.filter(tag => tag !== tagValue));
    } else if (selectedFeatureTags.length >= 10) {
      // 如果已选10个标签，显示提醒
      setShowMaxTagsWarning(true);
      // 3秒后自动隐藏提醒
      setTimeout(() => {
        setShowMaxTagsWarning(false);
      }, 3000);
    }
  };

  // 从已选标签移回新增标签
  const moveToAvailable = (tagValue: string) => {
    setSelectedFeatureTags(prev => prev.filter(tag => tag !== tagValue));
    // 将删除的标签添加到可选标签区域
    setRemovedTags(prev => {
      if (!prev.includes(tagValue)) {
        return [...prev, tagValue];
      }
      return prev;
    });
  };

  // 计算表单完整度（0-100）
  const calculateFormCompleteness = (formData: any): number => {
    const requiredFields = [
      'propertyType',
      'location.districts', 
      'industryType',
      'contactInfo.phone',
      'contactInfo.surname',
      'contactInfo.gender'
    ];
    
    const optionalFields = [
      'areaRange',
      'budgetRange',
      'specialRequirements',
      'layoutType'
    ];
    
    let filledRequired = 0;
    let filledOptional = 0;
    
    // 检查必填字段
    requiredFields.forEach(field => {
      const value = getNestedValue(formData, field);
      if (value && (Array.isArray(value) ? value.length > 0 : value)) {
        filledRequired++;
      }
    });
    
    // 检查可选字段
    optionalFields.forEach(field => {
      const value = getNestedValue(formData, field);
      if (value && (Array.isArray(value) ? value.length > 0 : value)) {
        filledOptional++;
      }
    });
    
    // 必填字段占70%权重，可选字段占30%权重
    const completeness = (filledRequired / requiredFields.length) * 70 + 
                        (filledOptional / optionalFields.length) * 30;
    
    return Math.round(completeness);
  };

  // 获取嵌套对象的值
  const getNestedValue = (obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  // 根据完整度计算缓存时间
  const calculateCacheTimeout = (completeness: number): number => {
    if (completeness >= 80) return 30 * 60 * 1000; // 30分钟
    if (completeness >= 50) return 20 * 60 * 1000; // 20分钟
    if (completeness >= 20) return 15 * 60 * 1000; // 15分钟
    return 10 * 60 * 1000; // 10分钟
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent
      />

      {/* 橙色背景 - 临时使用纯色替代LinearGradient */}
      <View style={styles.gradientBackground} />

      {/* 顶部导航带标题 */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="chevron-back" size={wp(24)} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{pageConfig.title}</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView
        ref={scrollViewRef}
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        onScroll={(event) => {
          const scrollY = event.nativeEvent.contentOffset.y;
          updateScrollPosition(scrollY); // 🔧 关键修复：更新滚动位置状态
        }}
        scrollEventThrottle={16}
      >
        {/* 页面副标题 */}
        <View style={styles.titleSection}>
          <Text style={styles.subtitle}>{pageConfig.subtitle}</Text>
        </View>

        {/* 表单区域 */}
        <View style={styles.formCard}>
          {/* 发布类型选择 */}
          <Text style={styles.sectionTitle}>发布类型</Text>
          <View style={styles.currentDemandTypeContainer}>
            <TouchableOpacity
              style={[
                styles.currentDemandTypeButton,
                currentDemandType === 'RENTAL' &&
                  styles.currentDemandTypeButtonSelected,
              ]}
              onPress={() => {
                setCurrentDemandType('RENTAL');
                // 重置表单以反映新的需求类型
                reset({
                  ...watch(),
                  demandType: 'RENTAL',
                  budgetRange: {
                    min: 3000,
                    max: 10000,
                    unit: '元/月',
                  },
                });
              }}
            >
              <Text
                style={[
                  styles.currentDemandTypeText,
                  currentDemandType === 'RENTAL' &&
                    styles.currentDemandTypeTextSelected,
                ]}
              >
                求租
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.currentDemandTypeButton,
                currentDemandType === 'PURCHASE' &&
                  styles.currentDemandTypeButtonSelected,
              ]}
              onPress={() => {
                setCurrentDemandType('PURCHASE');
                // 重置表单以反映新的需求类型
                reset({
                  ...watch(),
                  demandType: 'PURCHASE',
                  budgetRange: {
                    min: 100,
                    max: 500,
                    unit: '万元',
                  },
                });
              }}
            >
              <Text
                style={[
                  styles.currentDemandTypeText,
                  currentDemandType === 'PURCHASE' &&
                    styles.currentDemandTypeTextSelected,
                ]}
              >
                求购
              </Text>
            </TouchableOpacity>
          </View>

          {/* 🚀 类型选择 - 使用SmartFormField包装 */}
          <SmartFormField
            fieldName="propertyType"
            label="" // 🔧 去掉重复标题，由DemandDropdown显示
            required
            error={validationErrors.propertyType}
            onRegisterField={registerField}
          >
            <Controller
              name="propertyType"
              control={control}
              render={({ field: { onChange, value } }) => (
                <DemandDropdown
                  label="房源类型"
                  value={Array.isArray(value) ? value[0] || '' : value || ''}
                  options={PROPERTY_TYPE_OPTIONS}
                  error={validationErrors.propertyType} // 🔧 传递错误状态给DemandDropdown用于红框
                  hideErrorText={true} // 🔧 隐藏DemandDropdown自己的错误文字，由SmartFormField显示
                  onSelect={selectedType => {
                    const typeValue = Array.isArray(selectedType)
                      ? selectedType[0]
                      : selectedType;
                    const newValue = [typeValue];
                    onChange(newValue);
                    setSelectedPropertyTypes(newValue);
                    // 🔧 实时验证
                    updateFieldError('propertyType', newValue);
                  }}
                  placeholder="请选择房源类型"
                  multiSelect={false}
                  required
                  disabled={false}
                />
              )}
            />
          </SmartFormField>

          {/* 户型选择 */}
          {selectedPropertyTypes.length > 0 && (
            <Controller
              name="layoutType"
              control={control}
              render={({ field: { onChange, value } }) => (
                <DemandDropdown
                  label="户型"
                  value={value || []}
                  options={getAvailableLayoutTypes()}
                  onSelect={onChange}
                  placeholder="请选择户型"
                  multiSelect={true}
                  disabled={false}
                  onReset={() => onChange([])}
                />
              )}
            />
          )}

          {/* 🚀 区域选择 - 使用SmartFormField包装 */}
          <SmartFormField
            fieldName="location.districts"
            label="" // 🔧 去掉重复标题，由DemandDropdown显示
            required
            error={validationErrors['location.districts']}
            onRegisterField={registerField}
          >
            <Controller
              name="location.districts"
              control={control}
              render={({ field: { onChange, value } }) => (
                <DemandDropdown
                  label="目标区域"
                  value={Array.isArray(value) ? value : []}
                  options={NANNING_DISTRICTS}
                  onSelect={(newValue) => {
                    onChange(newValue);
                    // 🔧 实时验证
                    updateFieldError('location.districts', newValue);
                  }}
                  placeholder="请选择你想要的区域的房源"
                  multiSelect={true}
                  required
                  disabled={false}
                  error={validationErrors['location.districts']} // 🔧 传递错误状态给DemandDropdown用于红框
                  hideErrorText={true} // 🔧 隐藏DemandDropdown自己的错误文字，由SmartFormField显示
                  onReset={() => onChange([])}
                />
              )}
            />
          </SmartFormField>

          {/* 🚀 面积范围 - 使用SmartFormField包装 */}
          <SmartFormField
            fieldName="areaRange"
            label="" // 🔧 去掉重复标题，由DemandDropdown显示
            required
            error={validationErrors.areaRange}
            onRegisterField={registerField}
          >
            <Controller
              name="areaRange"
              control={control}
              render={({ field: { onChange, value } }) => {
                const safeValue = value || { min: 50, max: 200, unit: '平方米' };
                const displayValue =
                  typeof safeValue === 'object' && safeValue.min && safeValue.max
                    ? `${safeValue.min}-${safeValue.max}`
                    : '';

                return (
                  <DemandDropdown
                    label="面积范围"
                    error={validationErrors.areaRange} // 🔧 传递错误状态给DemandDropdown用于红框
                    hideErrorText={true} // 🔧 隐藏DemandDropdown自己的错误文字，由SmartFormField显示
                      value={displayValue}
                      options={AREA_RANGE_OPTIONS}
                      onSelect={val => {
                        let newValue;
                        if (typeof val === 'object' && val.min && val.max) {
                          // 来自自定义输入的范围对象
                          newValue = { ...val, userSelected: true };
                          onChange(newValue);
                        } else if (typeof val === 'string' && val.includes('-')) {
                          // 来自预设选项的字符串
                          const [min, max] = val.split('-').map(v => Math.floor(Number(v)));
                          newValue = { min, max, unit: '平方米', userSelected: true };
                          onChange(newValue);
                        }
                        // 🔧 实时验证
                        if (newValue) {
                          updateFieldError('areaRange', newValue);
                        }
                      }}
                      placeholder="请选择面积区间"
                      required
                      disabled={false}
                      customType="area"
                    />
                  );
                }}
              />
          </SmartFormField>

          {/* 楼层偏好 */}
          <Controller
            name="floorPreference"
            control={control}
            render={({ field: { onChange, value } }) => (
              <DemandDropdown
                label="楼层"
                value={Array.isArray(value) ? value : []}
                options={FLOOR_PREFERENCE_OPTIONS}
                onSelect={onChange}
                placeholder="请选择楼层"
                multiSelect={true}
                disabled={false}
                onReset={() => onChange([])}
              />
            )}
          />

          {/* 朝向 */}
          <Controller
            name="orientation"
            control={control}
            render={({ field: { onChange, value } }) => (
              <DemandDropdown
                label="朝向"
                value={Array.isArray(value) ? value : []}
                options={ORIENTATION_OPTIONS}
                onSelect={onChange}
                placeholder="请选择朝向"
                multiSelect={true}
                disabled={false}
                onReset={() => onChange([])}
              />
            )}
          />

          {/* 装修程度 */}
          <Controller
            name="decoration"
            control={control}
            render={({ field: { onChange, value } }) => (
              <DemandDropdown
                label="装修"
                value={value || ''}
                options={DECORATION_OPTIONS}
                onSelect={onChange}
                placeholder="请选择装修情况"
                disabled={false}
              />
            )}
          />

          {/* 类型（求租特定） */}
          {currentDemandType === 'RENTAL' && (
            <Controller
              name="leaseTerm"
              control={control}
              render={({ field: { onChange, value } }) => (
                <DemandDropdown
                  label="租期偏好"
                  value={value || ''}
                  options={LEASE_TERM_OPTIONS}
                  onSelect={onChange}
                  placeholder="请选择租期偏好"
                  disabled={false}
                />
              )}
            />
          )}

          {/* 类型（求购特定） */}
          {currentDemandType === 'PURCHASE' && (
            <Controller
              name="paymentMethod"
              control={control}
              render={({ field: { onChange, value } }) => (
                <DemandDropdown
                  label="付款方式"
                  value={value || ''}
                  options={PAYMENT_METHOD_OPTIONS}
                  onSelect={onChange}
                  placeholder="请选择付款方式"
                  disabled={false}
                />
              )}
            />
          )}

          {/* 🚀 业态 - 使用SmartFormField包装 */}
          <SmartFormField
            fieldName="industryType"
            label="" // 🔧 去掉重复标题，由DemandDropdown显示
            required
            error={validationErrors.industryType}
            onRegisterField={registerField}
          >
            <Controller
              name="industryType"
              control={control}
              render={({ field: { onChange, value } }) => (
                <DemandDropdown
                  label="行业类型"
                  value={Array.isArray(value) ? value : []}
                  options={INDUSTRY_TYPE_OPTIONS}
                  onSelect={(newValue) => {
                    onChange(newValue);
                    // 🔧 实时验证
                    updateFieldError('industryType', newValue);
                  }}
                  placeholder="请选择业态"
                  required
                  multiSelect={true}
                  disabled={false}
                  error={validationErrors.industryType} // 🔧 传递错误状态给DemandDropdown用于红框
                  hideErrorText={true} // 🔧 隐藏DemandDropdown自己的错误文字，由SmartFormField显示
                  onReset={() => onChange([])}
                />
              )}
            />
          </SmartFormField>

          {/* 🚀 价格/租金范围 - 使用SmartFormField包装 */}
          <SmartFormField
            fieldName="budgetRange"
            label="" // 🔧 去掉重复标题，由DemandDropdown显示
            required
            error={validationErrors.budgetRange}
            onRegisterField={registerField}
          >
            <Controller
              name="budgetRange"
              control={control}
              render={({ field: { onChange, value } }) => {
                const safeValue = value || {
                  min: currentDemandType === 'RENTAL' ? 3000 : 100,
                  max: currentDemandType === 'RENTAL' ? 10000 : 500,
                  unit: currentDemandType === 'RENTAL' ? '元/月' : '万元',
                };
                const displayValue =
                  typeof safeValue === 'object' && safeValue.min && safeValue.max
                    ? `${safeValue.min}-${safeValue.max}`
                    : '';

                return (
                  <DemandDropdown
                    label={currentDemandType === 'RENTAL' ? '租金范围' : '总价范围'}
                    error={validationErrors.budgetRange} // 🔧 传递错误状态给DemandDropdown用于红框
                    hideErrorText={true} // 🔧 隐藏DemandDropdown自己的错误文字，由SmartFormField显示
                      value={displayValue}
                      options={
                        currentDemandType === 'RENTAL'
                          ? RENT_RANGE_OPTIONS
                          : PURCHASE_PRICE_OPTIONS
                      }
                      onSelect={val => {
                        let newValue;
                        if (typeof val === 'object' && val.min && val.max) {
                          // 来自自定义输入的范围对象
                          newValue = { ...val, userSelected: true };
                          onChange(newValue);
                        } else if (typeof val === 'string' && val.includes('-')) {
                          // 来自预设选项的字符串
                          const [min, max] = val.split('-').map(v => Math.floor(Number(v)));
                          newValue = {
                            min,
                            max,
                            unit: currentDemandType === 'RENTAL' ? '元/月' : '万元',
                            userSelected: true,
                          };
                          onChange(newValue);
                        }
                        // 🔧 实时验证
                        if (newValue) {
                          updateFieldError('budgetRange', newValue);
                        }
                      }}
                      placeholder={`请选择${currentDemandType === 'RENTAL' ? '租金' : '总价'}范围`}
                      required
                      disabled={false}
                      customType={currentDemandType === 'RENTAL' ? 'rent' : 'price'}
                    />
                  );
                }}
              />
          </SmartFormField>

          {/* 转让费（可选） */}
          <Controller
            name="transferFee"
            control={control}
            render={({ field: { onChange, value } }) => (
              <DemandDropdown
                label="转让费（可选）"
                value={value || ''}
                options={TRANSFER_FEE_OPTIONS}
                onSelect={onChange}
                placeholder="请选择转让费区间"
                multiSelect={false}
                disabled={false}
              />
            )}
          />

          {/* 联系方式 */}
          <Text style={styles.sectionTitle}>
            联系方式
            <Text style={styles.requiredOrange}> *</Text>
          </Text>

          {/* 本机号码（一行显示） */}
          <View style={styles.inputGroup}>
            <Text style={styles.phoneOnlyDisplay}>
              本机号码：
              {user?.phone_number
                ? maskMobile(user.phone_number)
                : '未设置手机号'}
            </Text>
          </View>

          {/* 🚀 姓氏和性别一行显示 - 使用智能表单字段 */}
          <SmartFormField
            fieldName="contactInfo.surname"
            label="姓氏和性别"
            required
            error={validationErrors['contactInfo.surname'] || validationErrors['contactInfo.gender']}
            onRegisterField={registerField}
          >
            <View style={styles.surnameGenderRow}>
              {/* 姓氏输入（占一小半） */}
              <Controller
                name="contactInfo.surname"
                control={control}
                rules={{
                  required: '请输入姓氏',
                  minLength: { value: 1, message: '姓氏不能为空' },
                  maxLength: { value: 5, message: '姓氏不能超过5个字符' },
                }}
                render={({
                  field: { onChange, value },
                }) => (
                  <View style={styles.surnameInputWrapper}>
                    <TextInput
                      style={[
                        styles.surnameInput,
                        validationErrors['contactInfo.surname'] ? styles.textInputError : undefined,
                      ]}
                      value={value}
                      onChangeText={(newValue) => {
                        onChange(newValue);
                        // 🔧 实时验证
                        updateFieldError('contactInfo.surname', newValue);
                      }}
                      placeholder="请输入姓氏"
                      placeholderTextColor="#C7C7CC"
                      maxLength={5}
                    />
                    {/* 🔧 移除重复的错误提示，SmartFormField已经处理 */}
                  </View>
                )}
              />

              {/* 性别选择（占右侧） */}
              <Controller
                name="contactInfo.gender"
                control={control}
                render={({
                  field: { onChange, value },
                }) => (
                  <View style={styles.genderWrapper}>
                    <View style={styles.genderSimpleContainer}>
                      <TouchableOpacity
                        style={styles.genderSimpleOption}
                        onPress={() => {
                          onChange('male');
                          // 🔧 实时验证
                          updateFieldError('contactInfo.gender', 'male');
                        }}
                      >
                        <View
                          style={[
                            styles.genderSimpleCheckbox,
                            value === 'male' &&
                              styles.genderSimpleCheckboxSelected,
                          ]}
                        >
                          {value === 'male' && (
                            <Ionicons
                              name="checkmark"
                              size={wp(12)}
                              color="#FF6B35"
                            />
                          )}
                        </View>
                        <Text
                          style={[
                            styles.genderSimpleText,
                            value === 'male' && styles.genderSimpleTextSelected,
                          ]}
                        >
                          先生
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.genderSimpleOption}
                        onPress={() => {
                          onChange('female');
                          // 🔧 实时验证
                          updateFieldError('contactInfo.gender', 'female');
                        }}
                      >
                        <View
                          style={[
                            styles.genderSimpleCheckbox,
                            value === 'female' &&
                              styles.genderSimpleCheckboxSelected,
                          ]}
                        >
                          {value === 'female' && (
                            <Ionicons
                              name="checkmark"
                              size={wp(12)}
                              color="#FF6B35"
                            />
                          )}
                        </View>
                        <Text
                          style={[
                            styles.genderSimpleText,
                            value === 'female' &&
                              styles.genderSimpleTextSelected,
                          ]}
                        >
                          女士
                        </Text>
                      </TouchableOpacity>
                    </View>
                    {/* 🔧 移除重复的错误提示，SmartFormField已经处理 */}
                  </View>
                )}
              />
            </View>
          </SmartFormField>

          {/* 增加联络人功能 */}
          <View style={styles.inputGroup}>
            <View style={styles.addContactHeader}>
              <Text style={styles.inputLabel}>增加联络人（可选）</Text>
              <TouchableOpacity
                style={styles.addContactButton}
                onPress={() => {
                  if (additionalContacts.length < 2) {
                    setAdditionalContacts([
                      ...additionalContacts,
                      {
                        id: Date.now(),
                        phone: '',
                        verificationCode: '',
                        surname: '',
                        gender: undefined,
                        isVerified: false,
                      },
                    ]);
                  }
                }}
                disabled={additionalContacts.length >= 2}
              >
                <Ionicons
                  name="add"
                  size={wp(20)}
                  color={additionalContacts.length >= 2 ? '#CCC' : '#FF6B35'}
                />
              </TouchableOpacity>
            </View>
            <Text style={styles.addContactNote}>
              最多可以增加2位联络人，每位联络人需要验证手机号
            </Text>

            {/* 渲染额外联络人列表 */}
            {additionalContacts.map((contact, index) => (
              <View key={contact.id} style={styles.additionalContactItem}>
                <View style={styles.contactHeader}>
                  <Text style={styles.contactTitle}>联络人{index + 1}</Text>
                  <TouchableOpacity
                    onPress={() => {
                      setAdditionalContacts(
                        additionalContacts.filter(c => c.id !== contact.id)
                      );
                    }}
                    style={styles.removeContactButton}
                  >
                    <Ionicons name="close" size={wp(16)} color="#FF6B6B" />
                  </TouchableOpacity>
                </View>

                {/* 联络人手机号 */}
                <View style={styles.contactInputGroup}>
                  <Text style={styles.contactInputLabel}>手机号</Text>
                  <TextInput
                    style={styles.textInput}
                    value={contact.phone}
                    onChangeText={text => {
                      const formattedPhone = formatMobileInput(text);
                      setAdditionalContacts(prev =>
                        prev.map(c =>
                          c.id === contact.id
                            ? { ...c, phone: formattedPhone }
                            : c
                        )
                      );
                    }}
                    placeholder="请输入11位手机号"
                    placeholderTextColor="#C7C7CC"
                    keyboardType="phone-pad"
                    maxLength={11}
                  />
                </View>

                {/* 验证码输入 */}
                <View style={styles.contactInputGroup}>
                  <Text style={styles.contactInputLabel}>验证码</Text>
                  <View style={styles.verificationRow}>
                    <TextInput
                      style={[styles.textInput, { flex: 1 }]}
                      value={contact.verificationCode}
                      onChangeText={text => {
                        setAdditionalContacts(prev =>
                          prev.map(c =>
                            c.id === contact.id
                              ? { ...c, verificationCode: text }
                              : c
                          )
                        );
                      }}
                      placeholder="请输入验证码"
                      placeholderTextColor="#C7C7CC"
                      keyboardType="number-pad"
                      maxLength={6}
                    />
                    <TouchableOpacity
                      style={styles.sendCodeBtn}
                      onPress={() => sendContactVerificationCode(contact.phone)}
                      disabled={!contact.phone || contact.phone.length !== 11}
                    >
                      <Text style={styles.sendCodeText}>发送验证码</Text>
                    </TouchableOpacity>
                  </View>
                </View>

                {/* 联络人姓氏和性别一行显示 */}
                <View style={styles.contactInputGroup}>
                  <Text style={styles.contactInputLabel}>姓氏和性别</Text>
                  <View style={styles.surnameGenderRow}>
                    {/* 联络人姓氏输入 */}
                    <View style={styles.surnameInputWrapper}>
                      <TextInput
                        style={styles.surnameInput}
                        value={contact.surname}
                        onChangeText={text => {
                          setAdditionalContacts(prev =>
                            prev.map(c =>
                              c.id === contact.id ? { ...c, surname: text } : c
                            )
                          );
                        }}
                        placeholder="请输入姓氏"
                        placeholderTextColor="#C7C7CC"
                        maxLength={5}
                      />
                    </View>

                    {/* 联络人性别选择 */}
                    <View style={styles.genderWrapper}>
                      <View style={styles.genderSimpleContainer}>
                        <TouchableOpacity
                          style={styles.genderSimpleOption}
                          onPress={() => {
                            setAdditionalContacts(prev =>
                              prev.map(c =>
                                c.id === contact.id
                                  ? { ...c, gender: 'male' }
                                  : c
                              )
                            );
                          }}
                        >
                          <View
                            style={[
                              styles.genderSimpleCheckbox,
                              contact.gender === 'male' &&
                                styles.genderSimpleCheckboxSelected,
                            ]}
                          >
                            {contact.gender === 'male' && (
                              <Ionicons
                                name="checkmark"
                                size={wp(12)}
                                color="#FF6B35"
                              />
                            )}
                          </View>
                          <Text
                            style={[
                              styles.genderSimpleText,
                              contact.gender === 'male' &&
                                styles.genderSimpleTextSelected,
                            ]}
                          >
                            先生
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={styles.genderSimpleOption}
                          onPress={() => {
                            setAdditionalContacts(prev =>
                              prev.map(c =>
                                c.id === contact.id
                                  ? { ...c, gender: 'female' }
                                  : c
                              )
                            );
                          }}
                        >
                          <View
                            style={[
                              styles.genderSimpleCheckbox,
                              contact.gender === 'female' &&
                                styles.genderSimpleCheckboxSelected,
                            ]}
                          >
                            {contact.gender === 'female' && (
                              <Ionicons
                                name="checkmark"
                                size={wp(12)}
                                color="#FF6B35"
                              />
                            )}
                          </View>
                          <Text
                            style={[
                              styles.genderSimpleText,
                              contact.gender === 'female' &&
                                styles.genderSimpleTextSelected,
                            ]}
                          >
                            女士
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            ))}
          </View>

          <Controller
            name="contactInfo.wechat"
            control={control}
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>微信号（可选）</Text>
                <TextInput
                  style={styles.textInput}
                  value={value}
                  onChangeText={onChange}
                  placeholder="请输入微信号"
                  placeholderTextColor="#C7C7CC"
                />
              </View>
            )}
          />

          {/* 标签模块 - 条件渲染 */}
          {showTags && (
            <>
              {/* 已选标签显示 */}
              {selectedFeatureTags.length > 0 && (
                <View style={styles.selectedTagsContainer}>
                  <View style={styles.sectionHeaderWithHint}>
                    <Text style={styles.selectedTagsTitle}>特点标签</Text>
                    <Text style={styles.aiHint}>AI智能推荐，已选{selectedFeatureTags.length}个</Text>
                  </View>
                  <View style={styles.selectedTagsWrapper}>
                    {selectedFeatureTags.map((tag, index) => (
                      <View key={`${tag}-${index}`} style={styles.selectedTagItem}>
                        <Text style={styles.selectedTagText}>{tag}</Text>
                        <TouchableOpacity
                          onPress={() => moveToAvailable(tag)}
                          style={styles.deleteTagButton}
                        >
                          <Ionicons name="close" size={wp(12)} color="#FFFFFF" />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                </View>
              )}

              {/* 房源相关标签 */}
              <View style={styles.newTagsSection}>
                <View style={styles.newTagsHeader}>
                  <Text style={styles.newTagsTitle}>相关标签</Text>
                  <TouchableOpacity onPress={refreshTags}>
                    <Text style={styles.refreshText}>不合适，换一换</Text>
                  </TouchableOpacity>
                </View>
                {/* 10个标签上限提醒 - 只在点击相关标签时显示 */}
                {showMaxTagsWarning && (
                  <Text style={styles.maxTagsWarning}>最多选择10个标签，删除可替换！</Text>
                )}

                {(() => {
                  // 合并API获取的相关标签和用户删除的标签
                  const allAvailableTags = [...availableNewTags, ...removedTags];
                  
                  // 过滤掉已选择的标签，确保相关标签区域不显示重复标签
                  const filteredAvailableTags = allAvailableTags.filter(tag => 
                    !selectedFeatureTags.includes(tag)
                  );
                  
                  // 去除重复标签
                  const uniqueFilteredTags = [...new Set(filteredAvailableTags)];
                  
                  return uniqueFilteredTags.length > 0 ? (
                    <View style={styles.newTagsContainer}>
                      {uniqueFilteredTags.map((tag, index) => (
                        <TouchableOpacity
                          key={`new-${tag}-${index}`}
                          style={[styles.newTagItem, selectedFeatureTags.length >= 10 && styles.newTagItemDisabled]}
                          onPress={() => moveToSelected(tag)}
                          disabled={false} // 移除禁用，让点击能够触发提醒
                        >
                          <Text style={[styles.newTagText, selectedFeatureTags.length >= 10 && styles.newTagTextDisabled]}>{tag}</Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  ) : (
                    <Text style={styles.noMoreTagsText}>没有更多相关标签了</Text>
                  );
                })()}
              </View>
            </>
          )}

          {/* 同意发布规则 */}
          <SmartFormField
            fieldName="agreeToRules"
            label=""
            required
            error={validationErrors.agreeToRules}
            onRegisterField={registerField}
          >
            <Controller
              name="agreeToRules"
              control={control}
              render={({ field: { onChange, value } }) => (
                <View>
                  <TouchableOpacity
                    style={styles.checkboxContainer}
                    onPress={() => {
                      const newValue = !value;
                      onChange(newValue);
                      // 🔧 实时验证
                      updateFieldError('agreeToRules', newValue);
                    }}
                  >
                    <View
                      style={[
                        styles.checkbox,
                        value && styles.checkboxChecked,
                        validationErrors.agreeToRules ? styles.checkboxError : undefined
                      ]}
                    >
                      {value && (
                        <Ionicons
                          name="checkmark"
                          size={wp(16)}
                          color="#FFFFFF"
                        />
                      )}
                    </View>
                    <Text style={styles.checkboxLabel}>
                      同意
                      <Text
                        style={styles.ruleLink}
                        onPress={() => {
                          // TODO: 跳转到发布规则说明页面
                          console.log('跳转到发布规则说明页面');
                        }}
                      >
                        《求租贴发布规则》
                      </Text>
                    </Text>
                  </TouchableOpacity>
                  {/* 错误提示由SmartFormField统一处理 */}
                </View>
              )}
            />
          </SmartFormField>

          {/* AI提示 */}
          <View style={styles.aiSection}>
            <Text style={styles.aiText}>
              AI嘛陈案好，更准催促及时发现的房源
            </Text>
            <TouchableOpacity style={styles.aiButton}>
              <Text style={styles.aiButtonText}>去了解</Text>
            </TouchableOpacity>
          </View>

          {/* 底部操作按钮 */}
          <View style={styles.bottomButtonContainer}>
            {/* 草稿保存按钮 - 新建和编辑模式都显示 */}
            <TouchableOpacity
              style={styles.draftButton}
              onPress={handleSaveDraft}
              disabled={isSavingDraft}
            >
              <Text style={styles.draftButtonText}>
                {isSavingDraft ? '保存中...' : getDraftButtonText()}
              </Text>
            </TouchableOpacity>

            {/* 提交按钮 */}
            <TouchableOpacity
              style={[
                styles.submitButton,
                isSubmitting && styles.submitButtonDisabled,
                styles.submitButtonWithDraft, // 🔧 编辑模式下也有草稿按钮，所以都需要调整宽度
              ]}
              onPress={handleSmartFormSubmit}
              disabled={isSubmitting}
            >
              <Text style={styles.submitButtonText}>
                {isSubmitting ? '发布中...' : getPublishButtonText()}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradientBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#FF6B35', // 临时使用纯色橙色背景
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(16),
    paddingVertical: hp(8),
  },
  backButton: {
    width: wp(44),
    height: wp(44),
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: fp(20),
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    flex: 1,
  },
  headerRight: {
    width: wp(44),
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: wp(16),
    paddingBottom: hp(20),
  },
  titleSection: {
    alignItems: 'center',
    marginBottom: hp(12),
    marginTop: hp(4),
  },
  title: {
    fontSize: fp(24),
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: hp(8),
  },
  subtitle: {
    fontSize: fp(14),
    color: '#F5F5F5',
    textAlign: 'center',
    lineHeight: fp(20),
  },
  formCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: wp(16),
    padding: wp(16),
    marginBottom: hp(16),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  // 发布类型选择样式
  currentDemandTypeContainer: {
    flexDirection: 'row',
    backgroundColor: '#F8F9FA',
    borderRadius: wp(8),
    padding: wp(4),
    marginBottom: hp(16),
  },
  currentDemandTypeButton: {
    flex: 1,
    paddingVertical: hp(12),
    paddingHorizontal: wp(16),
    borderRadius: wp(6),
    alignItems: 'center',
  },
  currentDemandTypeButtonSelected: {
    backgroundColor: '#FF6B35',
  },
  currentDemandTypeText: {
    fontSize: fp(16),
    fontWeight: '500',
    color: '#666666',
  },
  currentDemandTypeTextSelected: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  // 选择容器样式
  // 已选标签样式
  selectedTagsContainer: {
    marginTop: hp(16),
  },
  selectedTagsTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#000000',
    marginBottom: hp(8),
  },
  selectedTagsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: wp(8),
  },
  selectedTagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF6B35',
    borderRadius: wp(16),
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
  },
  selectedTagText: {
    fontSize: fp(12),
    color: '#FFFFFF',
    fontWeight: '500',
    marginRight: wp(4),
  },
  deleteTagButton: {
    width: wp(16),
    height: wp(16),
    alignItems: 'center',
    justifyContent: 'center',
  },
  // 新增标签区域样式
  newTagsSection: {
    marginTop: hp(16),
  },
  newTagsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(12),
  },
  newTagsTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#000000',
  },
  maxTagsWarning: {
    fontSize: fp(12),
    color: '#FF6B35',
    marginTop: hp(4),
    marginBottom: hp(8),
    fontWeight: '500',
  },
  tagActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  aiGenerateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    borderWidth: 1,
    borderColor: '#FF6B35',
    borderRadius: wp(6),
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    marginRight: wp(12),
  },
  aiGenerateButtonText: {
    fontSize: fp(12),
    color: '#FF6B35',
    fontWeight: '600',
    marginLeft: wp(4),
  },
  refreshText: {
    fontSize: fp(12),
    color: '#FF6B35',
    textDecorationLine: 'underline',
  },
  newTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: wp(8),
  },
  newTagItem: {
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    borderRadius: wp(16),
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  newTagItemDisabled: {
    backgroundColor: '#F5F5F5',
    borderColor: '#E0E0E0',
  },
  newTagText: {
    fontSize: fp(12),
    color: '#666666',
    fontWeight: '500',
  },
  newTagTextDisabled: {
    color: '#CCCCCC',
  },
  sectionHeaderWithHint: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(8),
  },
  aiHint: {
    fontSize: fp(12),
    color: '#8E8E93',
    fontWeight: '400',
  },
  noMoreTagsText: {
    fontSize: fp(14),
    color: '#999999',
    textAlign: 'center',
    paddingVertical: hp(20),
  },
  maxTagsHint: {
    fontSize: fp(11),
    color: '#999999',
    textAlign: 'center',
    marginTop: hp(8),
  },
  sectionTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#000000',
    marginBottom: hp(16),
  },
  inputGroup: {
    marginBottom: hp(12),
  },
  inputLabel: {
    fontSize: fp(14),
    fontWeight: '500',
    color: '#000000',
    marginBottom: hp(8),
  },
  required: {
    color: '#FF6B35',
  },
  // 自定义输入样式
  customInputSection: {
    backgroundColor: '#F8F9FA',
    borderRadius: wp(8),
    padding: wp(16),
    marginTop: hp(8),
    marginBottom: hp(16),
  },
  customInputTitle: {
    fontSize: fp(14),
    fontWeight: '600',
    color: '#333333',
    marginBottom: hp(12),
    textAlign: 'center',
  },
  customInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(16),
  },
  customInput: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: wp(6),
    paddingHorizontal: wp(12),
    paddingVertical: hp(8),
    fontSize: fp(14),
    textAlign: 'center',
  },
  customSeparator: {
    fontSize: fp(16),
    color: '#666666',
    marginHorizontal: wp(8),
    fontWeight: '500',
  },
  customUnit: {
    fontSize: fp(12),
    color: '#666666',
    marginLeft: wp(8),
    minWidth: wp(30),
  },
  customConfirmBtn: {
    backgroundColor: '#FF6B35',
    borderRadius: wp(6),
    paddingHorizontal: wp(20),
    paddingVertical: hp(8),
    alignSelf: 'center',
  },
  customConfirmText: {
    fontSize: fp(14),
    color: '#FFFFFF',
    fontWeight: '600',
  },
  textInput: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: wp(8),
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    fontSize: fp(16),
    color: '#000000',
  },
  textInputError: {
    borderColor: '#FF6B6B',
  },
  errorContainer: {
    marginTop: hp(6),
    paddingHorizontal: wp(2),
  },
  errorText: {
    fontSize: fp(12),
    color: '#FF6B6B',
    lineHeight: fp(16),
    fontWeight: '400',
  },
  successText: {
    fontSize: fp(12),
    color: '#4CAF50',
    marginTop: hp(4),
  },
  requiredOrange: {
    color: '#FF6B35',
  },
  changePhoneHint: {
    fontSize: fp(12),
    color: '#FF6B35',
    marginTop: hp(4),
    fontStyle: 'italic',
  },
  verificationSection: {
    marginTop: hp(12),
    padding: wp(16),
    backgroundColor: '#FFF8F0',
    borderRadius: wp(8),
    borderWidth: 1,
    borderColor: '#FFE0B2',
  },
  verificationTitle: {
    fontSize: fp(14),
    fontWeight: '600',
    color: '#FF6B35',
    marginBottom: hp(12),
    textAlign: 'center',
  },
  verificationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(12),
    gap: wp(8),
  },
  verificationInput: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: wp(6),
    paddingHorizontal: wp(12),
    paddingVertical: hp(10),
    fontSize: fp(16),
    textAlign: 'center',
  },
  sendCodeBtn: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: wp(12),
    paddingVertical: hp(10),
    borderRadius: wp(6),
    minWidth: wp(80),
  },
  sendCodeBtnDisabled: {
    backgroundColor: '#C7C7CC',
  },
  sendCodeText: {
    color: '#FFFFFF',
    fontSize: fp(12),
    fontWeight: '600',
    textAlign: 'center',
  },
  sendCodeTextDisabled: {
    color: '#999999',
  },
  verifyBtn: {
    backgroundColor: '#FF6B35',
    paddingVertical: hp(12),
    borderRadius: wp(6),
    alignItems: 'center',
  },
  verifyBtnText: {
    color: '#FFFFFF',
    fontSize: fp(16),
    fontWeight: '600',
  },
  verifiedText: {
    fontSize: fp(12),
    color: '#4CAF50',
    marginTop: hp(4),
    fontWeight: '600',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: wp(20),
    height: wp(20),
    borderWidth: 2,
    borderColor: '#E9ECEF',
    borderRadius: wp(4),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: wp(12),
  },
  checkboxChecked: {
    backgroundColor: '#FF6B35',
    borderColor: '#FF6B35',
  },
  checkboxError: {
    borderColor: '#FF6B6B',
    borderWidth: 2,
  },
  checkboxLabel: {
    flex: 1,
    fontSize: fp(14),
    color: '#666666',
  },
  ruleLink: {
    color: '#FF6B35',
    textDecorationLine: 'underline',
  },
  aiSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(8),
  },
  aiText: {
    flex: 1,
    fontSize: fp(12),
    color: '#666666',
  },
  aiButton: {
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    backgroundColor: '#FF6B35',
    borderRadius: wp(16),
  },
  aiButtonText: {
    fontSize: fp(12),
    color: '#FFFFFF',
    fontWeight: '500',
  },
  submitButton: {
    backgroundColor: '#FF6B35',
    borderRadius: 8, // 🔧 与草稿按钮保持一致
    paddingVertical: hp(16),
    alignItems: 'center',
    justifyContent: 'center', // 🔧 与草稿按钮保持一致
    // 🔧 移除阴影，与草稿按钮对齐
  },
  submitButtonDisabled: {
    opacity: 0.6,
  },
  submitButtonText: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#FFFFFF',
  },
  // 本机号码显示样式
  phoneDisplayContainer: {
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: wp(8),
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  phoneDisplayText: {
    fontSize: fp(16),
    color: '#333333',
    fontWeight: '500',
  },
  phoneNote: {
    fontSize: fp(12),
    color: '#666666',
    fontStyle: 'italic',
  },
  // 性别选择样式
  genderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: wp(20),
  },
  genderOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(12),
    paddingHorizontal: wp(20),
    borderRadius: wp(8),
    borderWidth: 1,
    borderColor: '#E9ECEF',
    backgroundColor: '#FFFFFF',
    minWidth: wp(100),
  },
  genderOptionSelected: {
    borderColor: '#FF6B35',
    backgroundColor: '#FFF8F0',
  },
  genderCheckbox: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(10),
    borderWidth: 2,
    borderColor: '#E9ECEF',
    marginRight: wp(8),
    alignItems: 'center',
    justifyContent: 'center',
  },
  genderCheckboxSelected: {
    borderColor: '#FF6B35',
    backgroundColor: '#FF6B35',
  },
  genderText: {
    fontSize: fp(14),
    color: '#666666',
  },
  genderTextSelected: {
    color: '#FF6B35',
    fontWeight: '600',
  },
  // 增加联络人功能样式
  addContactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  addContactButton: {
    width: wp(32),
    height: wp(32),
    borderRadius: wp(16),
    borderWidth: 0, // 移除边框
    borderColor: 'transparent', // 透明边框
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent', // 透明背景
  },
  addContactNote: {
    fontSize: fp(12),
    color: '#666666',
    marginTop: hp(4),
    fontStyle: 'italic',
  },
  additionalContactItem: {
    marginTop: hp(16),
    padding: wp(16),
    backgroundColor: 'transparent', // 透明背景
    borderRadius: 0,
    borderWidth: 0, // 移除边框
    borderColor: 'transparent', // 透明边框
  },
  contactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(12),
  },
  contactTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#FF6B35',
  },
  removeContactButton: {
    width: wp(24),
    height: wp(24),
    borderRadius: wp(12),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFE0E0',
  },
  contactInputGroup: {
    marginBottom: hp(12),
  },
  contactInputLabel: {
    fontSize: fp(14),
    fontWeight: '500',
    color: '#333333',
    marginBottom: hp(8),
  },
  // 姓氏和性别一行布局样式
  surnameGenderRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: wp(12),
  },
  surnameInputWrapper: {
    flex: 0.4, // 占一小半宽度
  },
  surnameInput: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: wp(8),
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    fontSize: fp(16),
    color: '#000000',
  },
  genderWrapper: {
    flex: 0.6, // 占大半宽度
    justifyContent: 'center', // 垂直居中对齐
  },
  genderInlineContainer: {
    flexDirection: 'row',
    gap: wp(8),
  },
  genderInlineOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(10),
    paddingHorizontal: wp(12),
    borderRadius: wp(6),
    borderWidth: 1,
    borderColor: '#E9ECEF',
    backgroundColor: '#FFFFFF',
    flex: 1,
  },
  genderInlineOptionSelected: {
    borderColor: '#FF6B35',
    backgroundColor: '#FFF8F0',
  },
  genderInlineCheckbox: {
    width: wp(16),
    height: wp(16),
    borderRadius: wp(8),
    borderWidth: 2,
    borderColor: '#E9ECEF',
    marginRight: wp(6),
    alignItems: 'center',
    justifyContent: 'center',
  },
  genderInlineCheckboxSelected: {
    borderColor: '#FF6B35',
    backgroundColor: '#FF6B35',
  },
  genderInlineText: {
    fontSize: fp(13),
    color: '#666666',
    textAlign: 'center',
    flex: 1,
  },
  genderInlineTextSelected: {
    color: '#FF6B35',
    fontWeight: '600',
  },
  // 本机号码一行显示样式
  phoneOnlyDisplay: {
    fontSize: fp(16),
    color: '#333333',
    fontWeight: '500',
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: wp(8),
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
  },
  // 简化的性别选择样式（无白色容器）
  genderSimpleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: wp(12),
    minHeight: hp(48), // 与输入框高度一致
  },
  genderSimpleOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(8),
    paddingHorizontal: wp(8),
  },
  genderSimpleCheckbox: {
    width: wp(16),
    height: wp(16),
    borderRadius: wp(8),
    borderWidth: 2,
    borderColor: '#E9ECEF',
    marginRight: wp(6),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  genderSimpleCheckboxSelected: {
    borderColor: '#FF6B35',
    backgroundColor: 'transparent',
  },
  genderSimpleText: {
    fontSize: fp(14),
    color: '#666666',
  },
  genderSimpleTextSelected: {
    color: '#FF6B35',
    fontWeight: '600',
  },

  // 🔧 草稿保存按钮样式
  bottomButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: hp(20),
    gap: wp(12),
  },
  draftButton: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    paddingVertical: hp(16),
    paddingHorizontal: wp(20),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  draftButtonText: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#666666',
  },
  submitButtonWithDraft: {
    flex: 2, // 提交按钮占更大比例
  },
});

export default DemandFormScreen;
