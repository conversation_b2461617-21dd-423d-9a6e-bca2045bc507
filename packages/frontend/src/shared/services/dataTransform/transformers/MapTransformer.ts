/**
 * 地图数据转换器 - 企业级统一转换层
 * 
 * 职责：
 * 1. PostGIS查询结果 → 前端地图标记数据
 * 2. 前端搜索参数 → 后端API参数
 * 3. 地理坐标数据格式转换
 * 4. 地图UI状态数据转换
 * 
 * 遵循项目统一转换层架构规范
 */

import { BaseTransformer } from '../core/BaseTransformer';
import { TransformResult } from '../types/TransformTypes';
import { z } from 'zod';

// ===== 类型定义 =====

/**
 * PostGIS查询结果类型
 */
export interface PostGISPropertyResult {
  id: string;
  title: string;
  price: number;
  property_type: string;
  latitude: number;
  longitude: number;
  distance?: number;
  address?: string;
  district?: string;
  status: string;
  created_at: string;
}

/**
 * 前端地图标记类型
 */
export interface MapMarker {
  id: string;
  title: string;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  price: string;
  type: string;
  distance?: string;
  address?: string;
  district?: string;
  status: 'available' | 'rented' | 'sold';
}

/**
 * 地图搜索参数类型
 */
export interface MapSearchParams {
  lat: number;
  lng: number;
  radius: number;
  property_type?: string;
  min_price?: number;
  max_price?: number;
  limit: number;
}

/**
 * 前端搜索状态类型
 */
export interface FrontendSearchState {
  center: { latitude: number; longitude: number };
  searchRadius: number;
  selectedPropertyType: string;
  priceRange: { min?: number; max?: number };
  maxResults: number;
}

// ===== Zod验证模式 =====

const PostGISResultSchema = z.object({
  id: z.string(),
  title: z.string(),
  price: z.number(),
  property_type: z.string(),
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
  distance: z.number().optional(),
  address: z.string().optional(),
  district: z.string().optional(),
  status: z.string(),
  created_at: z.string(),
});

const MapMarkerSchema = z.object({
  id: z.string(),
  title: z.string(),
  coordinate: z.object({
    latitude: z.number(),
    longitude: z.number(),
  }),
  price: z.string(),
  type: z.string(),
  distance: z.string().optional(),
  address: z.string().optional(),
  district: z.string().optional(),
  status: z.enum(['available', 'rented', 'sold']),
});

const SearchParamsSchema = z.object({
  lat: z.number().min(-90).max(90),
  lng: z.number().min(-180).max(180),
  radius: z.number().min(100).max(50000),
  property_type: z.string().optional(),
  min_price: z.number().min(0).optional(),
  max_price: z.number().min(0).optional(),
  limit: z.number().min(1).max(100).default(20),
});

// ===== 转换器实现 =====

export class MapTransformer extends BaseTransformer {
  constructor() {
    super('MapTransformer', '1.0.0');
  }

  protected initializeValidationRules(): void {
    // 地图转换器的验证规则
    this.validationRules.set('postgisResult', {
      name: 'postgisResult',
      schema: PostGISResultSchema,
      description: 'PostGIS查询结果验证'
    });
    this.validationRules.set('mapMarker', {
      name: 'mapMarker',
      schema: MapMarkerSchema,
      description: '地图标记数据验证'
    });
    this.validationRules.set('searchParams', {
      name: 'searchParams',
      schema: SearchParamsSchema,
      description: '搜索参数验证'
    });
  }

  // 实现抽象方法（暂时简单实现）
  toAPI(input: any, options?: any): any {
    return this.searchStateToAPI(input, options);
  }

  fromAPI(input: any, options?: any): any {
    return this.postgisToMarkers(input, options);
  }
  /**
   * PostGIS查询结果转换为前端地图标记
   */
  public postgisToMarkers(
    postgisResults: PostGISPropertyResult[],
    context?: TransformContext
  ): TransformResult<MapMarker[]> {
    try {
      console.log('🗺️ [MapTransformer] postgisToMarkers开始转换', { count: postgisResults.length });

      const markers: MapMarker[] = postgisResults.map((result) => {
        // 验证输入数据
        const validatedResult = PostGISResultSchema.parse(result);

        // 转换状态枚举
        const status = this.convertPropertyStatus(validatedResult.status);

        // 格式化价格
        const formattedPrice = this.formatPrice(validatedResult.price);

        // 格式化距离
        const formattedDistance = validatedResult.distance 
          ? this.formatDistance(validatedResult.distance)
          : undefined;

        // 转换房源类型
        const propertyType = this.convertPropertyType(validatedResult.property_type);

        return {
          id: validatedResult.id,
          title: validatedResult.title,
          coordinate: {
            latitude: validatedResult.latitude,
            longitude: validatedResult.longitude,
          },
          price: formattedPrice,
          type: propertyType,
          distance: formattedDistance,
          address: validatedResult.address,
          district: validatedResult.district,
          status,
        };
      });

      // 验证输出数据
      markers.forEach(marker => MapMarkerSchema.parse(marker));

      console.log('✅ [MapTransformer] postgisToMarkers转换成功', { outputCount: markers.length });

      return {
        success: true,
        data: markers,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ [MapTransformer] postgisToMarkers转换失败', { error: errorMessage });
      return {
        success: false,
        error: `地图标记转换失败: ${errorMessage}`,
        data: [],
      };
    }
  }

  /**
   * 前端搜索状态转换为后端API参数
   */
  public searchStateToAPI(
    searchState: FrontendSearchState,
    context?: TransformContext
  ): TransformResult<MapSearchParams> {
    try {
      console.log('🗺️ [MapTransformer] searchStateToAPI开始转换', searchState);

      const apiParams: MapSearchParams = {
        lat: searchState.center.latitude,
        lng: searchState.center.longitude,
        radius: searchState.searchRadius,
        property_type: searchState.selectedPropertyType || undefined,
        min_price: searchState.priceRange.min,
        max_price: searchState.priceRange.max,
        limit: searchState.maxResults,
      };

      // 验证输出数据
      const validatedParams = SearchParamsSchema.parse(apiParams);

      console.log('✅ [MapTransformer] searchStateToAPI转换成功', validatedParams);

      return {
        success: true,
        data: validatedParams,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ [MapTransformer] searchStateToAPI转换失败', { error: errorMessage });
      return {
        success: false,
        error: `搜索参数转换失败: ${errorMessage}`,
        data: {} as MapSearchParams,
      };
    }
  }

  // ===== 私有辅助方法 =====

  private convertPropertyStatus(status: string): 'available' | 'rented' | 'sold' {
    const statusMap: Record<string, 'available' | 'rented' | 'sold'> = {
      'ACTIVE': 'available',
      'PUBLISHED': 'available',
      'RENTED': 'rented',
      'SOLD': 'sold',
      'TRANSFERRED': 'sold',
    };
    return statusMap[status] || 'available';
  }

  private convertPropertyType(type: string): string {
    const typeMap: Record<string, string> = {
      'SHOP': '商铺',
      'OFFICE': '写字楼',
      'FACTORY': '厂房',
      'CLUBHOUSE': '会所',
      'MEETING_ROOM': '会议室',
      'LAND': '土地',
      'TERRACE': '露台',
    };
    return typeMap[type] || type;
  }

  private formatPrice(price: number): string {
    if (price >= 10000) {
      return `${(price / 10000).toFixed(1)}万元/月`;
    }
    return `${price}元/月`;
  }

  private formatDistance(distance: number): string {
    if (distance >= 1000) {
      return `${(distance / 1000).toFixed(1)}km`;
    }
    return `${Math.round(distance)}m`;
  }
}

// ===== 导出单例 =====
export const mapTransformer = new MapTransformer();
