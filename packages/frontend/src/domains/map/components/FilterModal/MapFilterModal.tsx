/**
 * 地图筛选弹窗组件
 * 职责：从底部弹出的详细筛选面板，集成房源类型页面的筛选功能
 *
 * 设计理念：
 * 1. 复用房源类型页面的筛选逻辑和UI
 * 2. 适配地图页面的使用场景
 * 3. 底部弹窗样式，支持手势关闭
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableWithoutFeedback,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { wp, hp, fp } from '@shared/utils/responsive';

// 导入房源类型页面的筛选相关组件和类型
import {
  FilterType,
  FilterState,
  FilterOptions,
} from '../../../../screens/PropertyType/types/filterTypes';
import { usePropertyFilter } from '../../../../screens/PropertyType/hooks/usePropertyFilter';
import { getDefaultFilterOptions } from '../../../../screens/PropertyType/config/filterOptions';

interface MapFilterModalProps {
  visible: boolean;
  onClose: () => void;
  propertyType: string;
  onFilterApply: (filterState: FilterState) => void;
}

export const MapFilterModal: React.FC<MapFilterModalProps> = ({
  visible,
  onClose,
  propertyType,
  onFilterApply,
}) => {
  // 使用房源类型页面的筛选Hook
  const {
    filterState,
    panelState,
    selectedTags,
    hasActiveFilters,
    openPanel,
    closePanels,
    togglePanel,
    handleFilterSelect,
    resetAllFilters,
  } = usePropertyFilter(propertyType);

  // 获取筛选选项配置
  const filterOptions = useMemo(
    () => getDefaultFilterOptions(propertyType),
    [propertyType]
  );

  // 动画值
  const [slideAnim] = useState(new Animated.Value(0));

  // 当弹窗显示/隐藏时的动画
  React.useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim]);

  // 应用筛选
  const handleApply = useCallback(() => {
    onFilterApply(filterState);
    onClose();
  }, [filterState, onFilterApply, onClose]);

  // 重置筛选
  const handleReset = useCallback(() => {
    resetAllFilters();
    closePanels();
  }, [resetAllFilters, closePanels]);

  // 筛选按钮配置
  const filterButtons = [
    { key: 'region' as FilterType, label: '区域', icon: 'location-outline' },
    { key: 'price' as FilterType, label: '价格', icon: 'cash-outline' },
    { key: 'area' as FilterType, label: '面积', icon: 'resize-outline' },
    { key: 'more' as FilterType, label: '更多', icon: 'options-outline' },
    { key: 'sort' as FilterType, label: '排序', icon: 'swap-vertical-outline' },
  ];

  // 获取筛选按钮的显示文本
  const getFilterButtonText = (filterType: FilterType): string => {
    const tag = selectedTags.find(tag => tag.type === filterType);
    return tag
      ? tag.label
      : filterButtons.find(btn => btn.key === filterType)?.label || '';
  };

  // 检查筛选按钮是否激活
  const isFilterActive = (filterType: FilterType): boolean => {
    return selectedTags.some(tag => tag.type === filterType);
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.modalContainer,
                {
                  transform: [
                    {
                      translateY: slideAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [hp(600), 0],
                      }),
                    },
                  ],
                },
              ]}
            >
              {/* 头部 */}
              <View style={styles.header}>
                <Text style={styles.title}>筛选条件</Text>
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>

              {/* 筛选按钮栏 */}
              <View style={styles.filterBar}>
                {filterButtons.map(button => (
                  <TouchableOpacity
                    key={button.key}
                    style={[
                      styles.filterButton,
                      isFilterActive(button.key) && styles.filterButtonActive,
                      panelState.activePanel === button.key &&
                        styles.filterButtonSelected,
                    ]}
                    onPress={() => togglePanel(button.key)}
                  >
                    <Ionicons
                      name={button.icon as any}
                      size={16}
                      color={
                        isFilterActive(button.key) ||
                        panelState.activePanel === button.key
                          ? '#FF4F19'
                          : '#666'
                      }
                      style={styles.filterIcon}
                    />
                    <Text
                      style={[
                        styles.filterButtonText,
                        isFilterActive(button.key) &&
                          styles.filterButtonTextActive,
                        panelState.activePanel === button.key &&
                          styles.filterButtonTextSelected,
                      ]}
                    >
                      {getFilterButtonText(button.key)}
                    </Text>
                    <Ionicons
                      name="chevron-down"
                      size={12}
                      color={
                        isFilterActive(button.key) ||
                        panelState.activePanel === button.key
                          ? '#FF4F19'
                          : '#999'
                      }
                    />
                  </TouchableOpacity>
                ))}
              </View>

              {/* 筛选面板内容区域 */}
              <ScrollView
                style={styles.contentArea}
                showsVerticalScrollIndicator={false}
              >
                {panelState.isVisible && panelState.activePanel && (
                  <View style={styles.filterPanel}>
                    <Text style={styles.panelTitle}>
                      {
                        filterButtons.find(
                          btn => btn.key === panelState.activePanel
                        )?.label
                      }
                      筛选
                    </Text>
                    {/* 这里可以根据activePanel渲染对应的筛选面板 */}
                    <Text style={styles.panelPlaceholder}>
                      {panelState.activePanel} 筛选面板内容
                    </Text>
                  </View>
                )}
              </ScrollView>

              {/* 底部操作栏 */}
              <View style={styles.bottomBar}>
                <TouchableOpacity
                  style={styles.resetButton}
                  onPress={handleReset}
                >
                  <Text style={styles.resetButtonText}>重置</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.applyButton}
                  onPress={handleApply}
                >
                  <Text style={styles.applyButtonText}>
                    查看房源{hasActiveFilters ? `(${selectedTags.length})` : ''}
                  </Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: wp(20),
    borderTopRightRadius: wp(20),
    maxHeight: hp(600),
    minHeight: hp(400),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: wp(16),
    paddingVertical: hp(16),
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  title: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: wp(4),
  },
  filterBar: {
    flexDirection: 'row',
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  filterButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(8),
    paddingHorizontal: wp(8),
    marginHorizontal: wp(2),
    borderRadius: wp(6),
    backgroundColor: '#F8F8F8',
  },
  filterButtonActive: {
    backgroundColor: '#FFF5F2',
    borderWidth: 1,
    borderColor: '#FF4F19',
  },
  filterButtonSelected: {
    backgroundColor: '#FF4F19',
  },
  filterIcon: {
    marginRight: wp(4),
  },
  filterButtonText: {
    fontSize: fp(12),
    color: '#666',
    flex: 1,
    textAlign: 'center',
  },
  filterButtonTextActive: {
    color: '#FF4F19',
    fontWeight: '500',
  },
  filterButtonTextSelected: {
    color: '#fff',
    fontWeight: '500',
  },
  contentArea: {
    flex: 1,
    paddingHorizontal: wp(16),
  },
  filterPanel: {
    paddingVertical: hp(16),
  },
  panelTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#333',
    marginBottom: hp(12),
  },
  panelPlaceholder: {
    fontSize: fp(14),
    color: '#999',
    textAlign: 'center',
    paddingVertical: hp(40),
  },
  bottomBar: {
    flexDirection: 'row',
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    backgroundColor: '#fff',
  },
  resetButton: {
    flex: 1,
    paddingVertical: hp(12),
    marginRight: wp(12),
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: wp(6),
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: fp(14),
    color: '#666',
  },
  applyButton: {
    flex: 2,
    paddingVertical: hp(12),
    backgroundColor: '#FF4F19',
    borderRadius: wp(6),
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: fp(14),
    color: '#fff',
    fontWeight: '600',
  },
});
