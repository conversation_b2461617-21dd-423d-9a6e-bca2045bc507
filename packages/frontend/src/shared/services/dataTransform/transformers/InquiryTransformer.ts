/**
 * 房源咨询转换器 - 企业级版本
 * 
 * 职责：
 * 1. 前端咨询表单数据 ↔ 后端API格式转换
 * 2. 咨询状态和类型的标准化处理
 * 3. 聊天初始化数据转换
 * 4. 用户角色信息转换
 * 5. 消息列表数据转换
 * 
 * 遵循企业级架构原则：
 * - 使用统一转换层架构
 * - 类型安全和错误处理
 * - 业务逻辑封装
 * - 可扩展性设计
 */

import { z } from 'zod';
import { BaseTransformer } from '../core/BaseTransformer';
import { TransformResult, TransformOptions } from '../types/TransformTypes';

// ==================== 类型定义 ====================

/**
 * 前端咨询请求数据
 */
export interface InquiryFormData {
  propertyId: string;
  messageContent: string;
  inquiryType: 'viewed' | 'favorited' | 'messaged';
}

/**
 * 后端API咨询请求格式
 */
export interface InquiryAPIRequest {
  property_id: string;
  message_content: string;
  inquiry_type: 'viewed' | 'favorited' | 'messaged';
}

/**
 * 后端API咨询响应格式
 */
export interface InquiryAPIResponse {
  success: boolean;
  inquiry_id?: string;
  existing_inquiry_id?: string;  // 重复咨询时返回的现有咨询ID
  is_free_slot?: boolean;
  free_slot_number?: number;
  requires_payment?: boolean;
  remaining_slots?: number;
  message?: string;
  error?: string;
}

/**
 * 前端咨询响应数据
 */
export interface InquiryFormResponse {
  success: boolean;
  inquiryId?: string;
  isFreeSlot?: boolean;
  freeSlotNumber?: number;
  requiresPayment?: boolean;
  remainingSlots?: number;
  message?: string;
  error?: string;
}

/**
 * 用户角色信息 - API格式
 */
export interface UserRoleInfoAPI {
  isTenantBuyer: boolean;
  isLandlord: boolean;
  defaultRole: string;
  tenantBuyerUnreadCount: number;
  landlordUnreadCount: number;
}

/**
 * 用户角色信息 - 前端格式
 */
export interface UserRoleInfoUI {
  isTenantBuyer: boolean;
  isLandlord: boolean;
  defaultRole: 'tenant_buyer' | 'landlord';
  tenantBuyerUnreadCount: number;
  landlordUnreadCount: number;
}

/**
 * 聊天初始化响应 - API格式
 */
export interface ChatInitAPIResponse {
  success: boolean;
  inquiry_id: string;
  chat_room_id: string;
  landlord_info: {
    id: string;
    name: string;
    avatar?: string;
    phone?: string;
  };
  tenant_info: {
    id: string;
    name: string;
    avatar?: string;
    phone?: string;
  };
  property_info: {
    id: string;
    title: string;
    location: string;
    price: string;
    area: string;
    property_type: string;
    cover_image?: string;
  };
  inquiry_info: {
    id: string;
    status: string;
    message_content: string;
    created_at: string;
    is_free_slot: boolean;
    requires_payment: boolean;
  };
}

/**
 * 聊天初始化响应 - 前端格式
 */
export interface ChatInitUIResponse {
  inquiryId: string;
  chatRoomId: string;
  landlordInfo: {
    id: string;
    name: string;
    avatar?: string;
    phone?: string;
  };
  tenantInfo: {
    id: string;
    name: string;
    avatar?: string;
    phone?: string;
  };
  propertyInfo: {
    id: string;
    title: string;
    location: string;
    price: string;
    area: string;
    propertyType: string;
    coverImage?: string;
  };
  inquiryInfo: {
    id: string;
    status: string;
    messageContent: string;
    createdAt: string;
    isFreeSlot: boolean;
    requiresPayment: boolean;
  };
}

// ==================== 转换器实现 ====================

export class InquiryTransformer extends BaseTransformer<InquiryFormData, InquiryAPIRequest> {
  
  constructor() {
    super('InquiryTransformer', '1.0.0');
  }

  /**
   * 初始化验证规则
   */
  protected initializeValidationRules(): void {
    // 前端表单数据验证
    this.addValidationRule('inquiryForm', {
      name: 'inquiryForm',
      description: '咨询表单数据验证',
      schema: z.object({
        propertyId: z.string().uuid('房源ID必须是有效的UUID'),
        messageContent: z.string().min(1, '咨询内容不能为空').max(2000, '咨询内容不能超过2000字符'),
        inquiryType: z.enum(['viewed', 'favorited', 'messaged'], {
          errorMap: () => ({ message: '咨询类型必须是 viewed、favorited 或 messaged' })
        })
      }),
      required: true
    });

    // API请求数据验证
    this.addValidationRule('inquiryAPI', {
      name: 'inquiryAPI',
      description: 'API请求数据验证',
      schema: z.object({
        property_id: z.string().uuid(),
        message_content: z.string().min(1).max(2000),
        inquiry_type: z.enum(['viewed', 'favorited', 'messaged'])
      }),
      required: true
    });

    // API响应数据验证 - 允许错误情况下的null值
    this.addValidationRule('inquiryResponse', {
      name: 'inquiryResponse',
      description: 'API响应数据验证',
      schema: z.object({
        success: z.boolean(),
        inquiry_id: z.string().nullable().optional(),
        is_free_slot: z.boolean().nullable().optional(),
        free_slot_number: z.number().nullable().optional(),
        requires_payment: z.boolean().nullable().optional(),
        remaining_slots: z.number().nullable().optional(),
        message: z.string().nullable().optional(),
        error: z.string().nullable().optional()
      }),
      required: true
    });

    // 用户角色信息验证
    this.addValidationRule('userRoleInfo', {
      name: 'userRoleInfo',
      description: '用户角色信息验证',
      schema: z.object({
        isTenantBuyer: z.boolean(),
        isLandlord: z.boolean(),
        defaultRole: z.string(),
        tenantBuyerUnreadCount: z.number(),
        landlordUnreadCount: z.number()
      }),
      required: true
    });

    // 聊天初始化响应验证
    this.addValidationRule('chatInitResponse', {
      name: 'chatInitResponse',
      description: '聊天初始化响应验证',
      schema: z.object({
        inquiry_id: z.string(),
        chat_room_id: z.string(),
        landlord_info: z.object({
          id: z.string(),
          name: z.string(),
          avatar: z.string().optional()
        }),
        property_info: z.object({
          id: z.string(),
          title: z.string(),
          location: z.string(),
          price: z.string(),
          area: z.string(),
          property_type: z.string()
        })
      }),
      required: true
    });
  }

  /**
   * 转换为API格式
   */
  toAPI(input: InquiryFormData, options?: TransformOptions): TransformResult<InquiryAPIRequest> {
    return this.safeTransform(() => {
      // 验证输入数据
      const validationResult = this.validateInput(input, 'inquiryForm');
      if (!validationResult.success) {
        throw new Error(validationResult.error);
      }

      // 转换为API格式
      const apiRequest: InquiryAPIRequest = {
        property_id: input.propertyId,
        message_content: input.messageContent,
        inquiry_type: input.inquiryType
      };

      return apiRequest;
    }, 'InquiryTransformer.toAPI');
  }

  /**
   * 从API格式转换
   */
  fromAPI(input: InquiryAPIRequest, options?: TransformOptions): TransformResult<InquiryFormData> {
    return this.safeTransform(() => {
      // 验证输入数据
      const validationResult = this.validateInput(input, 'inquiryAPI');
      if (!validationResult.success) {
        throw new Error(validationResult.error);
      }

      // 转换为前端格式
      const formData: InquiryFormData = {
        propertyId: input.property_id,
        messageContent: input.message_content,
        inquiryType: input.inquiry_type
      };

      return formData;
    }, 'InquiryTransformer.fromAPI');
  }

  // ==================== 专用转换方法 ====================

  /**
   * 转换API响应为前端格式
   */
  transformAPIResponseToUI(input: InquiryAPIResponse): TransformResult<InquiryFormResponse> {
    return this.safeTransform(() => {
      // 验证输入数据
      const validationResult = this.validateInput(input, 'inquiryResponse');
      if (!validationResult.success) {
        throw new Error(validationResult.error);
      }

      // 转换为前端格式 - 处理null值和重复咨询情况
      const uiResponse: InquiryFormResponse = {
        success: input.success,
        // 处理重复咨询：优先使用existing_inquiry_id，其次使用inquiry_id
        inquiryId: input.existing_inquiry_id || input.inquiry_id || undefined,
        isFreeSlot: input.is_free_slot || undefined,
        freeSlotNumber: input.free_slot_number || undefined,
        requiresPayment: input.requires_payment || undefined,
        remainingSlots: input.remaining_slots || undefined,
        message: input.message || undefined,
        error: input.error || undefined
      };

      return uiResponse;
    }, 'InquiryTransformer.transformAPIResponseToUI');
  }

  /**
   * 转换用户角色信息
   */
  transformUserRoleInfoToUI(input: UserRoleInfoAPI): TransformResult<UserRoleInfoUI> {
    return this.safeTransform(() => {
      // 验证输入数据
      const validationResult = this.validateInput(input, 'userRoleInfo');
      if (!validationResult.success) {
        throw new Error(validationResult.error);
      }

      // 转换为前端格式
      const uiRoleInfo: UserRoleInfoUI = {
        isTenantBuyer: input.isTenantBuyer,
        isLandlord: input.isLandlord,
        defaultRole: input.defaultRole as 'tenant_buyer' | 'landlord',
        tenantBuyerUnreadCount: input.tenantBuyerUnreadCount,
        landlordUnreadCount: input.landlordUnreadCount
      };

      return uiRoleInfo;
    }, 'InquiryTransformer.transformUserRoleInfoToUI');
  }

  /**
   * 转换聊天初始化响应
   */
  transformChatInitResponseToUI(input: ChatInitAPIResponse): TransformResult<ChatInitUIResponse> {
    return this.safeTransform(() => {
      // 验证输入数据
      const validationResult = this.validateInput(input, 'chatInitResponse');
      if (!validationResult.success) {
        throw new Error(validationResult.error);
      }

      // 转换为前端格式
      const uiResponse: ChatInitUIResponse = {
        inquiryId: input.inquiry_id,
        chatRoomId: input.chat_room_id,
        landlordInfo: {
          id: input.landlord_info.id,
          name: input.landlord_info.name,
          avatar: input.landlord_info.avatar,
          phone: input.landlord_info.phone
        },
        tenantInfo: {
          id: input.tenant_info.id,
          name: input.tenant_info.name,
          avatar: input.tenant_info.avatar,
          phone: input.tenant_info.phone
        },
        propertyInfo: {
          id: input.property_info.id,
          title: input.property_info.title,
          location: input.property_info.location,
          price: input.property_info.price,
          area: input.property_info.area,
          propertyType: input.property_info.property_type,
          coverImage: input.property_info.cover_image
        },
        inquiryInfo: {
          id: input.inquiry_info.id,
          status: input.inquiry_info.status,
          messageContent: input.inquiry_info.message_content,
          createdAt: input.inquiry_info.created_at,
          isFreeSlot: input.inquiry_info.is_free_slot,
          requiresPayment: input.inquiry_info.requires_payment
        }
      };

      return uiResponse;
    }, 'InquiryTransformer.transformChatInitResponseToUI');
  }
}

export default InquiryTransformer;
