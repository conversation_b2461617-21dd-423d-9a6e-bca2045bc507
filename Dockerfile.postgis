# PostGIS Dockerfile - 基于官方PostgreSQL镜像
# 严格按照PostGIS官方安装指导
FROM postgres:14

# 设置环境变量 - 按照官方指导
ENV POSTGIS_MAJOR=3

# 安装PostGIS扩展 - 按照官方指导，不指定具体版本号
RUN set -eux \
    && apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-$PG_MAJOR-postgis-$POSTGIS_MAJOR \
        postgresql-$PG_MAJOR-postgis-$POSTGIS_MAJOR-scripts \
    && rm -rf /var/lib/apt/lists/*

# 创建PostGIS初始化脚本 - 官方推荐方式
RUN mkdir -p /docker-entrypoint-initdb.d
COPY <<EOF /docker-entrypoint-initdb.d/10_postgis.sql
-- 启用PostGIS扩展
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;
CREATE EXTENSION IF NOT EXISTS postgis_tiger_geocoder;

-- 验证PostGIS安装
SELECT PostGIS_Version();
EOF

# 设置权限
RUN chmod +x /docker-entrypoint-initdb.d/10_postgis.sql
