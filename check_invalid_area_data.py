#!/usr/bin/env python3
"""
检查数据库中是否存在面积为0或无效的房源数据
"""

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'packages/backend'))

from sqlmodel import select, and_, or_
from sqlmodel.ext.asyncio.session import AsyncSession
from app.core.db import get_async_session
from app.models.property import Property

async def check_invalid_area_data():
    """检查数据库中的无效面积数据"""
    
    async for db in get_async_session():
        try:
            print("🔍 正在检查数据库中的面积数据...")
            
            # 1. 检查面积为0的记录
            print("\n📊 检查面积为0的记录:")
            zero_area_query = select(Property).where(Property.total_area == 0)
            zero_area_result = await db.exec(zero_area_query)
            zero_area_properties = zero_area_result.all()
            
            if zero_area_properties:
                print(f"❌ 发现 {len(zero_area_properties)} 条面积为0的记录:")
                for prop in zero_area_properties[:5]:  # 只显示前5条
                    print(f"  - ID: {prop.id}, 标题: {prop.title}, 面积: {prop.total_area}")
            else:
                print("✅ 没有发现面积为0的记录")
            
            # 2. 检查面积为负数的记录
            print("\n📊 检查面积为负数的记录:")
            negative_area_query = select(Property).where(Property.total_area < 0)
            negative_area_result = await db.exec(negative_area_query)
            negative_area_properties = negative_area_result.all()
            
            if negative_area_properties:
                print(f"❌ 发现 {len(negative_area_properties)} 条面积为负数的记录:")
                for prop in negative_area_properties[:5]:
                    print(f"  - ID: {prop.id}, 标题: {prop.title}, 面积: {prop.total_area}")
            else:
                print("✅ 没有发现面积为负数的记录")
            
            # 3. 检查面积为NULL的记录
            print("\n📊 检查面积为NULL的记录:")
            null_area_query = select(Property).where(Property.total_area.is_(None))
            null_area_result = await db.exec(null_area_query)
            null_area_properties = null_area_result.all()
            
            if null_area_properties:
                print(f"⚠️ 发现 {len(null_area_properties)} 条面积为NULL的记录:")
                for prop in null_area_properties[:5]:
                    print(f"  - ID: {prop.id}, 标题: {prop.title}, 面积: {prop.total_area}")
            else:
                print("✅ 没有发现面积为NULL的记录")
            
            # 4. 检查异常大的面积值
            print("\n📊 检查异常大的面积值 (>100000平方米):")
            large_area_query = select(Property).where(Property.total_area > 100000)
            large_area_result = await db.exec(large_area_query)
            large_area_properties = large_area_result.all()
            
            if large_area_properties:
                print(f"⚠️ 发现 {len(large_area_properties)} 条面积异常大的记录:")
                for prop in large_area_properties[:5]:
                    print(f"  - ID: {prop.id}, 标题: {prop.title}, 面积: {prop.total_area}")
            else:
                print("✅ 没有发现面积异常大的记录")
            
            # 5. 统计面积数据的分布
            print("\n📊 面积数据统计:")
            
            # 总记录数
            total_count_query = select(Property)  
            total_count_result = await db.exec(total_count_query)
            total_properties = total_count_result.all()
            total_count = len(total_properties)
            print(f"总房源数量: {total_count}")
            
            if total_count > 0:
                # 面积范围统计
                areas = [prop.total_area for prop in total_properties if prop.total_area is not None]
                if areas:
                    print(f"有效面积数据: {len(areas)} 条")
                    print(f"面积范围: {min(areas):.2f} - {max(areas):.2f} 平方米")
                    print(f"平均面积: {sum(areas)/len(areas):.2f} 平方米")
                    
                    # 按面积区间统计
                    ranges = {
                        "0-50": len([a for a in areas if 0 < a <= 50]),
                        "50-100": len([a for a in areas if 50 < a <= 100]), 
                        "100-500": len([a for a in areas if 100 < a <= 500]),
                        "500-1000": len([a for a in areas if 500 < a <= 1000]),
                        "1000+": len([a for a in areas if a > 1000])
                    }
                    
                    print("面积分布:")
                    for range_name, count in ranges.items():
                        percentage = (count / len(areas)) * 100 if areas else 0
                        print(f"  {range_name}平米: {count} 条 ({percentage:.1f}%)")
            
            # 6. 检查可能的数据质量问题
            print("\n🔍 数据质量检查:")
            
            # 检查标题为空的记录
            empty_title_query = select(Property).where(or_(Property.title.is_(None), Property.title == ""))
            empty_title_result = await db.exec(empty_title_query)
            empty_title_count = len(empty_title_result.all())
            
            if empty_title_count > 0:
                print(f"⚠️ 发现 {empty_title_count} 条标题为空的记录")
            else:
                print("✅ 所有记录都有标题")
                
            print("\n" + "="*60)
            print("🏁 检查完成")
            
        except Exception as e:
            print(f"❌ 检查过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await db.close()

if __name__ == "__main__":
    asyncio.run(check_invalid_area_data())