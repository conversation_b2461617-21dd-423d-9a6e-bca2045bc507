/**
 * DemandFormScreen Zustand Store
 * @fileoverview 需求发布表单的统一状态管理
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🔒 功能保证：100%保留原有DemandFormScreen.tsx中的所有状态管理逻辑
 */

import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { storage } from '../../../../../shared/services/client';

// 导入类型定义
import type {
  DemandFormState,
  DemandFormActions,
  DemandFormData,
  FormErrors,
} from '../types/demandForm.types';

/**
 * 获取默认表单数据 - 完整保留原有getDefaultValues逻辑
 */
const getDefaultFormData = (userPhone?: string): DemandFormData => ({
  // === 基础信息 ===
  demandType: 'RENTAL',
  propertyType: [],

  // === 位置信息 ===
  location: {
    city: '南宁市',
    districts: [],
    landmarks: [],
  },

  // === 面积和预算 ===
  areaRange: {
    min: 0,
    max: 0,
    unit: '平方米',
  },
  budgetRange: {
    min: 0,
    max: 0,
    unit: '元/月',
  },
  transferFee: '',

  // === 行业和布局 ===
  industryType: [],
  layoutType: [],

  // === 详细要求（可选） ===
  floorPreference: [],
  orientation: [],
  decoration: '',
  leaseTerm: '',
  paymentMethod: '',
  specialRequirements: '',

  // === 联系信息 ===
  contactInfo: {
    phone: userPhone || '',
    surname: '',
    gender: 'male', // 设置默认值避免undefined
    wechat: '',
    preferredContactTime: 'ANYTIME',
    contactMethod: 'BOTH',
    additionalContacts: [],
  },

  // === 协议 ===
  agreeToRules: false,
});

/**
 * 获取嵌套对象的值 - 保留原有工具函数
 */
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

/**
 * 设置嵌套对象的值 - 保留原有工具函数
 */
const setNestedValue = (obj: any, path: string, value: any): any => {
  const pathArray = path.split('.');
  const newObj = { ...obj };
  let current = newObj;

  for (let i = 0; i < pathArray.length - 1; i++) {
    const key = pathArray[i];
    current[key] = { ...current[key] };
    current = current[key];
  }

  current[pathArray[pathArray.length - 1]] = value;
  return newObj;
};

/**
 * 计算智能过期时间 - 保留原有缓存逻辑
 */
const calculateSmartExpiry = (formData: DemandFormData): number => {
  // 计算表单完成度
  const requiredFields = [
    'propertyType',
    'location.districts',
    'industryType',
    'contactInfo.phone',
    'contactInfo.surname',
    'contactInfo.gender',
  ];

  let filledRequired = 0;
  requiredFields.forEach(field => {
    const value = getNestedValue(formData, field);
    if (value && (Array.isArray(value) ? value.length > 0 : value)) {
      filledRequired++;
    }
  });

  const completeness = filledRequired / requiredFields.length;

  // 根据完成度设置过期时间
  if (completeness >= 0.8) {
    return Date.now() + 2 * 60 * 60 * 1000; // 2小时
  } else if (completeness >= 0.5) {
    return Date.now() + 60 * 60 * 1000; // 1小时
  } else {
    return Date.now() + 30 * 60 * 1000; // 30分钟
  }
};

/**
 * DemandForm Zustand Store
 * 🔒 完整保留原有DemandFormScreen.tsx中的所有状态管理逻辑
 */
export const useDemandFormStore = create<DemandFormState & DemandFormActions>()(
  devtools(
    persist(
      subscribeWithSelector((set, get) => ({
        // === 初始状态 ===
        formData: getDefaultFormData(),

        ui: {
          currentStep: 1,
          totalSteps: 6,
          loading: false,
          isSubmitting: false,
          isSavingDraft: false,
          error: null,
        },

        editMode: {
          isEditMode: false,
          editDemandId: null,
          editDemandDetails: null,
        },

        tags: {
          selectedFeatureTags: [],
          availableTags: [],
          removedTags: [],
          showTags: false,
          hasInitialTagsGenerated: false,
          showMaxTagsWarning: false,
        },

        cache: {
          cacheEnabled: true,
          lastSaveTime: null,
          cacheExpiresAt: null,
          draftId: null,
          lastSavedTime: null,
        },

        validation: {
          formErrors: {},
          touchedFields: new Set(),
          isValid: false,
          validationErrors: {}, // 兼容原有命名
        },

        // === 表单数据操作 ===
        updateFormField: (field, value) =>
          set(
            state => {
              const newFormData = { ...state.formData, [field]: value };

              // 🔒 保留原有业务联动逻辑：需求类型变化时更新预算单位
              if (field === 'demandType') {
                newFormData.budgetRange = {
                  ...newFormData.budgetRange,
                  unit: value === 'RENTAL' ? '元/月' : '万元',
                };
              }

              return {
                formData: newFormData,
                cache: {
                  ...state.cache,
                  lastSaveTime: Date.now(),
                },
              };
            },
            false,
            'updateFormField'
          ),

        updateNestedField: (path, value) =>
          set(
            state => ({
              formData: setNestedValue(state.formData, path, value),
              cache: {
                ...state.cache,
                lastSaveTime: Date.now(),
              },
            }),
            false,
            'updateNestedField'
          ),

        resetForm: () =>
          set(
            state => ({
              formData: getDefaultFormData(),
              validation: {
                ...state.validation,
                formErrors: {},
                validationErrors: {},
                touchedFields: new Set(),
                isValid: false,
              },
              ui: {
                ...state.ui,
                currentStep: 1,
                error: null,
              },
            }),
            false,
            'resetForm'
          ),

        setFormData: data =>
          set(
            state => ({
              formData: { ...state.formData, ...data },
            }),
            false,
            'setFormData'
          ),

        // === UI状态操作 ===
        setLoading: loading =>
          set(
            state => ({
              ui: { ...state.ui, loading },
            }),
            false,
            'setLoading'
          ),

        setSubmitting: submitting =>
          set(
            state => ({
              ui: { ...state.ui, isSubmitting: submitting },
            }),
            false,
            'setSubmitting'
          ),

        setSavingDraft: saving =>
          set(
            state => ({
              ui: { ...state.ui, isSavingDraft: saving },
            }),
            false,
            'setSavingDraft'
          ),

        setError: error =>
          set(
            state => ({
              ui: { ...state.ui, error },
            }),
            false,
            'setError'
          ),

        setCurrentStep: step =>
          set(
            state => ({
              ui: { ...state.ui, currentStep: step },
            }),
            false,
            'setCurrentStep'
          ),

        nextStep: () =>
          set(
            state => ({
              ui: {
                ...state.ui,
                currentStep: Math.min(
                  state.ui.currentStep + 1,
                  state.ui.totalSteps
                ),
              },
            }),
            false,
            'nextStep'
          ),

        prevStep: () =>
          set(
            state => ({
              ui: {
                ...state.ui,
                currentStep: Math.max(state.ui.currentStep - 1, 1),
              },
            }),
            false,
            'prevStep'
          ),

        // === 编辑模式操作 ===
        enterEditMode: (demandId, demandDetails) =>
          set(
            {
              editMode: {
                isEditMode: true,
                editDemandId: demandId,
                editDemandDetails: demandDetails,
              },
            },
            false,
            'enterEditMode'
          ),

        exitEditMode: () =>
          set(
            {
              editMode: {
                isEditMode: false,
                editDemandId: null,
                editDemandDetails: null,
              },
            },
            false,
            'exitEditMode'
          ),

        setEditDemandDetails: details =>
          set(
            state => ({
              editMode: {
                ...state.editMode,
                editDemandDetails: details,
              },
            }),
            false,
            'setEditDemandDetails'
          ),

        // === 标签管理操作 ===
        setSelectedFeatureTags: tags =>
          set(
            state => ({
              tags: { ...state.tags, selectedFeatureTags: tags },
            }),
            false,
            'setSelectedFeatureTags'
          ),

        addFeatureTag: tag =>
          set(
            state => {
              if (state.tags.selectedFeatureTags.length >= 10) {
                return {
                  tags: { ...state.tags, showMaxTagsWarning: true },
                };
              }

              if (!state.tags.selectedFeatureTags.includes(tag)) {
                return {
                  tags: {
                    ...state.tags,
                    selectedFeatureTags: [
                      ...state.tags.selectedFeatureTags,
                      tag,
                    ],
                    removedTags: state.tags.removedTags.filter(t => t !== tag),
                  },
                };
              }

              return state;
            },
            false,
            'addFeatureTag'
          ),

        removeFeatureTag: tag =>
          set(
            state => ({
              tags: {
                ...state.tags,
                selectedFeatureTags: state.tags.selectedFeatureTags.filter(
                  t => t !== tag
                ),
                removedTags: state.tags.removedTags.includes(tag)
                  ? state.tags.removedTags
                  : [...state.tags.removedTags, tag],
              },
            }),
            false,
            'removeFeatureTag'
          ),

        setAvailableTags: tags =>
          set(
            state => ({
              tags: { ...state.tags, availableTags: tags },
            }),
            false,
            'setAvailableTags'
          ),

        setShowTags: show =>
          set(
            state => ({
              tags: { ...state.tags, showTags: show },
            }),
            false,
            'setShowTags'
          ),

        setHasInitialTagsGenerated: generated =>
          set(
            state => ({
              tags: { ...state.tags, hasInitialTagsGenerated: generated },
            }),
            false,
            'setHasInitialTagsGenerated'
          ),

        setShowMaxTagsWarning: show =>
          set(
            state => ({
              tags: { ...state.tags, showMaxTagsWarning: show },
            }),
            false,
            'setShowMaxTagsWarning'
          ),

        // === 缓存操作 ===
        enableCache: () =>
          set(
            state => ({
              cache: { ...state.cache, cacheEnabled: true },
            }),
            false,
            'enableCache'
          ),

        disableCache: () =>
          set(
            state => ({
              cache: { ...state.cache, cacheEnabled: false },
            }),
            false,
            'disableCache'
          ),

        saveToCache: () => {
          const state = get();
          if (state.cache.cacheEnabled) {
            try {
              const cacheData = {
                formData: state.formData,
                selectedFeatureTags: state.tags.selectedFeatureTags,
                timestamp: Date.now(),
                expiresAt: calculateSmartExpiry(state.formData),
              };

              // 🔒 保留原有缓存逻辑：使用storage服务
              storage.setString('pendingDemandForm', JSON.stringify(cacheData));

              set(
                state => ({
                  cache: {
                    ...state.cache,
                    lastSaveTime: Date.now(),
                    cacheExpiresAt: cacheData.expiresAt,
                  },
                }),
                false,
                'saveToCache'
              );
            } catch (error) {
              console.error('[DemandFormStore] 保存缓存失败:', error);
            }
          }
        },

        loadFromCache: () => {
          try {
            const cached = storage.getString('pendingDemandForm');
            if (cached) {
              const cacheData = JSON.parse(cached);
              const now = Date.now();

              if (now < cacheData.expiresAt) {
                set(
                  state => ({
                    formData: { ...state.formData, ...cacheData.formData },
                    tags: {
                      ...state.tags,
                      selectedFeatureTags: cacheData.selectedFeatureTags || [],
                    },
                    cache: {
                      ...state.cache,
                      lastSaveTime: cacheData.timestamp,
                      cacheExpiresAt: cacheData.expiresAt,
                    },
                  }),
                  false,
                  'loadFromCache'
                );
                return true;
              }
            }
          } catch (error) {
            console.error('[DemandFormStore] 加载缓存失败:', error);
          }
          return false;
        },

        clearCache: () => {
          try {
            storage.delete('pendingDemandForm');
            set(
              state => ({
                cache: {
                  ...state.cache,
                  lastSaveTime: null,
                  cacheExpiresAt: null,
                },
              }),
              false,
              'clearCache'
            );
          } catch (error) {
            console.error('[DemandFormStore] 清除缓存失败:', error);
          }
        },

        setDraftId: id =>
          set(
            state => ({
              cache: { ...state.cache, draftId: id },
            }),
            false,
            'setDraftId'
          ),

        setLastSavedTime: time =>
          set(
            state => ({
              cache: { ...state.cache, lastSavedTime: time },
            }),
            false,
            'setLastSavedTime'
          ),

        // === 验证操作 ===
        setFieldError: (field, error) =>
          set(
            state => ({
              validation: {
                ...state.validation,
                formErrors: { ...state.validation.formErrors, [field]: error },
                validationErrors: {
                  ...state.validation.validationErrors,
                  [field]: error,
                },
              },
            }),
            false,
            'setFieldError'
          ),

        clearFieldError: field =>
          set(
            state => {
              const newFormErrors = { ...state.validation.formErrors };
              const newValidationErrors = {
                ...state.validation.validationErrors,
              };
              delete newFormErrors[field];
              delete newValidationErrors[field];

              return {
                validation: {
                  ...state.validation,
                  formErrors: newFormErrors,
                  validationErrors: newValidationErrors,
                },
              };
            },
            false,
            'clearFieldError'
          ),

        setFormErrors: errors =>
          set(
            state => ({
              validation: {
                ...state.validation,
                formErrors: errors,
                validationErrors: errors, // 兼容原有命名
              },
            }),
            false,
            'setFormErrors'
          ),

        markFieldTouched: field =>
          set(
            state => ({
              validation: {
                ...state.validation,
                touchedFields: new Set([
                  ...state.validation.touchedFields,
                  field,
                ]),
              },
            }),
            false,
            'markFieldTouched'
          ),

        validateForm: () => {
          const state = get();
          const errors: FormErrors = {};

          // 🔒 保留原有验证逻辑
          if (!state.formData.propertyType.length) {
            errors.propertyType = '请至少选择一种房源类型';
          }

          if (!state.formData.location.districts.length) {
            errors['location.districts'] = '请至少选择一个区域';
          }

          if (!state.formData.industryType.length) {
            errors.industryType = '请选择行业类型';
          }

          if (!state.formData.contactInfo.surname.trim()) {
            errors['contactInfo.surname'] = '请输入姓氏';
          }

          if (!state.formData.contactInfo.phone.trim()) {
            errors['contactInfo.phone'] = '请输入联系电话';
          }

          if (!state.formData.agreeToRules) {
            errors.agreeToRules = '必须同意发布规则才能提交';
          }

          const isValid = Object.keys(errors).length === 0;

          set(
            state => ({
              validation: {
                ...state.validation,
                formErrors: errors,
                validationErrors: errors,
                isValid,
              },
            }),
            false,
            'validateForm'
          );

          return isValid;
        },

        setValidationErrors: errors =>
          set(
            state => ({
              validation: {
                ...state.validation,
                validationErrors: errors,
                formErrors: errors, // 保持同步
              },
            }),
            false,
            'setValidationErrors'
          ),

        // === 复合操作 ===
        reset: () =>
          set(
            () => ({
              formData: getDefaultFormData(),
              ui: {
                currentStep: 1,
                totalSteps: 6,
                loading: false,
                isSubmitting: false,
                isSavingDraft: false,
                error: null,
              },
              editMode: {
                isEditMode: false,
                editDemandId: null,
                editDemandDetails: null,
              },
              tags: {
                selectedFeatureTags: [],
                availableTags: [],
                removedTags: [],
                showTags: false,
                hasInitialTagsGenerated: false,
                showMaxTagsWarning: false,
              },
              cache: {
                cacheEnabled: true,
                lastSaveTime: null,
                cacheExpiresAt: null,
                draftId: null,
                lastSavedTime: null,
              },
              validation: {
                formErrors: {},
                touchedFields: new Set(),
                isValid: false,
                validationErrors: {},
              },
            }),
            false,
            'reset'
          ),
      })),
      {
        name: 'demand-form-store',
        // 🔒 选择性持久化：只持久化表单数据，不持久化UI状态
        partialize: state => ({
          formData: state.formData,
          tags: {
            selectedFeatureTags: state.tags.selectedFeatureTags,
          },
          cache: {
            cacheEnabled: state.cache.cacheEnabled,
          },
        }),
      }
    ),
    {
      name: 'DemandFormStore',
    }
  )
);

// === 性能优化的选择器Hook ===
export const useDemandFormData = () =>
  useDemandFormStore(state => state.formData);
export const useDemandFormUI = () => useDemandFormStore(state => state.ui);
export const useDemandFormEditMode = () =>
  useDemandFormStore(state => state.editMode);
export const useDemandFormTags = () => useDemandFormStore(state => state.tags);
export const useDemandFormCache = () =>
  useDemandFormStore(state => state.cache);
export const useDemandFormValidation = () =>
  useDemandFormStore(state => state.validation);

// === 操作选择器Hook ===
export const useDemandFormActions = () =>
  useDemandFormStore(state => ({
    // 表单操作
    updateFormField: state.updateFormField,
    updateNestedField: state.updateNestedField,
    resetForm: state.resetForm,
    setFormData: state.setFormData,

    // UI操作
    setLoading: state.setLoading,
    setSubmitting: state.setSubmitting,
    setSavingDraft: state.setSavingDraft,
    setError: state.setError,
    setCurrentStep: state.setCurrentStep,
    nextStep: state.nextStep,
    prevStep: state.prevStep,

    // 编辑操作
    enterEditMode: state.enterEditMode,
    exitEditMode: state.exitEditMode,
    setEditDemandDetails: state.setEditDemandDetails,

    // 标签操作
    setSelectedFeatureTags: state.setSelectedFeatureTags,
    addFeatureTag: state.addFeatureTag,
    removeFeatureTag: state.removeFeatureTag,
    setAvailableTags: state.setAvailableTags,
    setShowTags: state.setShowTags,
    setHasInitialTagsGenerated: state.setHasInitialTagsGenerated,
    setShowMaxTagsWarning: state.setShowMaxTagsWarning,

    // 缓存操作
    enableCache: state.enableCache,
    disableCache: state.disableCache,
    saveToCache: state.saveToCache,
    loadFromCache: state.loadFromCache,
    clearCache: state.clearCache,
    setDraftId: state.setDraftId,
    setLastSavedTime: state.setLastSavedTime,

    // 验证操作
    setFieldError: state.setFieldError,
    clearFieldError: state.clearFieldError,
    setFormErrors: state.setFormErrors,
    markFieldTouched: state.markFieldTouched,
    validateForm: state.validateForm,
    setValidationErrors: state.setValidationErrors,

    // 复合操作
    reset: state.reset,
  }));
