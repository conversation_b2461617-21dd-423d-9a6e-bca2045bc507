/**
 * FormFooter 组件
 * @fileoverview 需求发布表单的底部操作区域
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🔒 功能保证：100%保留原有DemandFormScreen.tsx中的底部操作UI和交互
 */

import React from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { wp, hp, fp } from '../../../../../../shared/utils/responsiveUtils';

// 样式
import { styles } from './formFooter.styles';

interface FormFooterProps {
  canSubmit: boolean;
  canSaveDraft: boolean;
  isSubmitting: boolean;
  isSavingDraft: boolean;
  isEditMode: boolean;
  onSubmit: () => void;
  onSaveDraft: () => void;
  completeness: number;
  error?: string | null;
}

/**
 * 表单底部组件 - 完整保留原有底部操作设计
 */
export const FormFooter: React.FC<FormFooterProps> = React.memo(
  ({
    canSubmit,
    canSaveDraft,
    isSubmitting,
    isSavingDraft,
    isEditMode,
    onSubmit,
    onSaveDraft,
    completeness,
    error,
  }) => {
    return (
      <View style={styles.container}>
        {/* 🔒 保留原有的错误提示显示 */}
        {error && (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={wp(16)} color="#FF4444" />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {/* 🔒 保留原有的底部操作按钮 */}
        <View style={styles.buttonContainer}>
          {/* 草稿保存按钮 - 新建和编辑模式都显示 */}
          <TouchableOpacity
            style={[
              styles.draftButton,
              (!canSaveDraft || isSavingDraft) && styles.buttonDisabled,
            ]}
            onPress={onSaveDraft}
            disabled={!canSaveDraft || isSavingDraft}
          >
            {isSavingDraft ? (
              <ActivityIndicator size="small" color="#666666" />
            ) : (
              <Text style={styles.draftButtonText}>
                {isSavingDraft ? '保存中...' : '草稿'}
              </Text>
            )}
          </TouchableOpacity>

          {/* 提交按钮 */}
          <TouchableOpacity
            style={[
              styles.submitButton,
              (!canSubmit || isSubmitting) && styles.buttonDisabled,
            ]}
            onPress={onSubmit}
            disabled={!canSubmit || isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.submitButtonText}>
                {isEditMode ? '更新需求' : '发布需求'}
              </Text>
            )}
          </TouchableOpacity>
        </View>

        {/* 🔒 保留原有的完成度提示（可选） */}
        {completeness > 0 && completeness < 1 && (
          <View style={styles.progressHint}>
            <Text style={styles.progressHintText}>
              表单完成度：{Math.round(completeness * 100)}%
            </Text>
          </View>
        )}
      </View>
    );
  }
);

FormFooter.displayName = 'FormFooter';
