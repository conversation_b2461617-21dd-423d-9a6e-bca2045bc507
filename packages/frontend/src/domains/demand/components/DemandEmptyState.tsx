/**
 * 🏗️ 企业级架构：需求空状态组件
 * 遵循前端企业级架构规范 - UI组件层
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// 响应式工具
import { wp, hp, fp } from '../../../shared/utils/responsiveUtils';

interface DemandEmptyStateProps {
  status: string;
  onCreatePress: () => void;
}

export const DemandEmptyState: React.FC<DemandEmptyStateProps> = ({
  status,
  onCreatePress,
}) => {
  // 🎨 UI层：获取空状态配置
  const getEmptyConfig = (status: string) => {
    switch (status) {
      case 'active':
        return {
          icon: 'megaphone-outline',
          title: '暂无已发布需求',
          description: '发布您的求租求购需求，让优质房源主动找到您',
          buttonText: '发布需求',
          color: '#52c41a',
        };
      case 'draft':
        return {
          icon: 'document-text-outline',
          title: '暂无草稿需求',
          description: '保存草稿可以让您随时继续编辑需求信息',
          buttonText: '创建草稿',
          color: '#999999',
        };
      case 'inactive':
        return {
          icon: 'pause-circle-outline',
          title: '暂无已下架需求',
          description: '已下架的需求可以重新编辑后上架',
          buttonText: '发布新需求',
          color: '#faad14',
        };
      default:
        return {
          icon: 'document-outline',
          title: '暂无需求',
          description: '开始发布您的第一个需求吧',
          buttonText: '发布需求',
          color: '#1890ff',
        };
    }
  };

  const config = getEmptyConfig(status);

  return (
    <View style={styles.container}>
      <View style={[styles.iconContainer, { backgroundColor: `${config.color}15` }]}>
        <Ionicons 
          name={config.icon as any} 
          size={64} 
          color={config.color} 
        />
      </View>
      
      <Text style={styles.title}>
        {config.title}
      </Text>
      
      <Text style={styles.description}>
        {config.description}
      </Text>
      
      <TouchableOpacity
        style={[styles.button, { backgroundColor: config.color }]}
        onPress={onCreatePress}
      >
        <Ionicons name="add" size={20} color="#ffffff" />
        <Text style={styles.buttonText}>
          {config.buttonText}
        </Text>
      </TouchableOpacity>

      {/* 功能提示 */}
      <View style={styles.tipsContainer}>
        <Text style={styles.tipsTitle}>💡 发布需求的好处</Text>
        <View style={styles.tipItem}>
          <Ionicons name="checkmark-circle" size={16} color="#52c41a" />
          <Text style={styles.tipText}>房源主动匹配，节省找房时间</Text>
        </View>
        <View style={styles.tipItem}>
          <Ionicons name="checkmark-circle" size={16} color="#52c41a" />
          <Text style={styles.tipText}>专业经纪人主动联系，提供优质服务</Text>
        </View>
        <View style={styles.tipItem}>
          <Ionicons name="checkmark-circle" size={16} color="#52c41a" />
          <Text style={styles.tipText}>AI智能推荐，精准匹配需求</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(32),
    paddingVertical: hp(40),
  },
  iconContainer: {
    width: wp(120),
    height: wp(120),
    borderRadius: wp(60),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(24),
  },
  title: {
    fontSize: fp(20),
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
    marginBottom: hp(12),
  },
  description: {
    fontSize: fp(16),
    color: '#666666',
    textAlign: 'center',
    lineHeight: fp(24),
    marginBottom: hp(32),
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: wp(24),
    paddingVertical: hp(14),
    marginBottom: hp(40),
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonText: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#ffffff',
    marginLeft: wp(8),
  },
  tipsContainer: {
    width: '100%',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: wp(20),
  },
  tipsTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#333333',
    marginBottom: hp(16),
    textAlign: 'center',
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(12),
  },
  tipText: {
    fontSize: fp(14),
    color: '#666666',
    marginLeft: wp(8),
    flex: 1,
  },
});
