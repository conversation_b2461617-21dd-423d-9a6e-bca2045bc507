/**
 * 消息中心头部组件
 * 包含角色切换和设置按钮
 */
import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface MessageHeaderProps {
  activeRole: 'tenant_buyer' | 'landlord';
  onRoleSwitch: (role: 'tenant_buyer' | 'landlord') => void;
  onSettingsPress: () => void;
}

export const MessageHeader: React.FC<MessageHeaderProps> = ({
  activeRole,
  onRoleSwitch,
  onSettingsPress,
}) => {
  const insets = useSafeAreaInsets();

  return (
    <>
      {/* 沉浸式状态栏 */}
      <StatusBar
        barStyle="dark-content"
        backgroundColor="transparent"
        translucent={true}
      />

      {/* 角色切换栏（沉浸式到顶） */}
      <View style={[styles.container, { paddingTop: insets.top + 16 }]}>
        <View style={styles.roleSwitchContent}>
          <TouchableOpacity
            style={styles.roleOption}
            onPress={() => {
              console.log('点击租买消息');
              onRoleSwitch('tenant_buyer');
            }}
            activeOpacity={0.6}
          >
            <Text style={[
              styles.roleText,
              activeRole === 'tenant_buyer' && styles.activeRoleText
            ]}>
              租买消息
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.roleOption}
            onPress={() => {
              console.log('点击业主消息');
              onRoleSwitch('landlord');
            }}
            activeOpacity={0.6}
          >
            <Text style={[
              styles.roleText,
              activeRole === 'landlord' && styles.activeRoleText
            ]}>
              业主消息
            </Text>
          </TouchableOpacity>
        </View>

        {/* 右上角设置图标 */}
        <TouchableOpacity style={styles.settingsButton} onPress={onSettingsPress}>
          <Text style={styles.settingsIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>
    </>
  );
};

const styles = {
  container: {
    flexDirection: 'row' as const,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingBottom: 30,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
  },
  roleSwitchContent: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 30,
  },
  roleOption: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    position: 'relative' as const,
  },
  roleText: {
    fontSize: 16,
    color: '#999999',
    fontWeight: '500' as const,
  },
  activeRoleText: {
    fontSize: 26, // 🔧 激活状态字体大小
    color: '#FF6B35',
    fontWeight: '600' as const,
  },
  settingsButton: {
    padding: 8,
  },
  settingsIcon: {
    fontSize: 20,
    color: '#666666',
  },
};
