-- 企业级数据库迁移脚本
-- 功能：为房源状态枚举添加DRAFT值
-- 作者：系统自动生成
-- 日期：2025-08-02
-- 版本：v1.0.0

-- 迁移说明：
-- 1. 为propertystatus枚举类型添加DRAFT值
-- 2. 确保房源草稿功能的数据库支持
-- 3. 统一房源和需求的状态枚举

-- 开始事务
BEGIN;

-- 检查枚举是否已存在DRAFT值
DO $$
BEGIN
    -- 尝试添加DRAFT枚举值
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'DRAFT' 
        AND enumtypid = (
            SELECT oid FROM pg_type WHERE typname = 'propertystatus'
        )
    ) THEN
        -- 添加DRAFT枚举值
        ALTER TYPE propertystatus ADD VALUE 'DRAFT';
        RAISE NOTICE '✅ 成功添加DRAFT状态到propertystatus枚举';
    ELSE
        RAISE NOTICE '⚠️ DRAFT状态已存在于propertystatus枚举中';
    END IF;
END
$$;

-- 验证枚举值
DO $$
DECLARE
    enum_values TEXT[];
BEGIN
    -- 获取所有枚举值
    SELECT array_agg(enumlabel ORDER BY enumsortorder) 
    INTO enum_values
    FROM pg_enum 
    WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'propertystatus');
    
    RAISE NOTICE '📊 当前propertystatus枚举值: %', array_to_string(enum_values, ', ');
END
$$;

-- 提交事务
COMMIT;

-- 迁移完成日志
SELECT 
    'PropertyStatus枚举迁移完成' as migration_status,
    now() as completed_at,
    '添加DRAFT状态支持房源草稿功能' as description;
