/**
 * 五层架构集成测试
 * 验证UI层、Hook层、Store层、DTO层的完整集成
 */
import { describe, it, expect, beforeEach } from '@jest/globals';
import { renderHook, act } from '@testing-library/react-hooks';

// 导入各层组件
import { useProfileData } from '../hooks/useProfileData';
import { useProfileLogic } from '../hooks/useProfileLogic';
import { useProfileUI } from '../hooks/useProfileUI';
import { useUserStats } from '../hooks/useUserStats';
import { getProfileTransformers } from '../transformers/ProfileTransformerFactory';
import { useProfileStore } from '../stores/ProfileStore';

describe('五层架构集成测试', () => {
  beforeEach(() => {
    // 重置Store状态
    useProfileStore.getState().resetAll();
  });

  describe('DTO层测试', () => {
    it('应该正确创建转换器实例', () => {
      const transformers = getProfileTransformers();
      
      expect(transformers.userInfo).toBeDefined();
      expect(transformers.tenantBuyerStats).toBeDefined();
      expect(transformers.landlordStats).toBeDefined();
      expect(transformers.orderCounts).toBeDefined();
    });

    it('应该正确转换用户信息数据', () => {
      const transformers = getProfileTransformers();
      
      const mockApiData = {
        user_id: 'test-id',
        phone_number: '13800138000',
        user_name: '测试用户',
        user_avatar: 'avatar.jpg',
        is_verified: true,
        user_type: '商业地产租户',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      const result = transformers.userInfo.fromAPI(mockApiData);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        id: 'test-id',
        phoneNumber: '13800138000',
        userName: '测试用户',
        avatar: 'avatar.jpg',
        isVerified: true,
        userType: '商业地产租户',
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-01T00:00:00Z'),
      });
    });
  });

  describe('Store层测试', () => {
    it('应该正确管理状态', () => {
      const store = useProfileStore.getState();
      
      // 初始状态
      expect(store.isLoggedIn).toBe(false);
      expect(store.userInfo).toBeNull();
      
      // 设置登录状态
      act(() => {
        store.setIsLoggedIn(true);
      });
      
      expect(useProfileStore.getState().isLoggedIn).toBe(true);
    });

    it('应该正确计算派生状态', () => {
      const store = useProfileStore.getState();
      
      // 设置加载状态
      act(() => {
        store.setLoadingUserInfo(true);
        store.setLoadingTenantStats(true);
      });
      
      const state = useProfileStore.getState();
      expect(state.isLoading).toBe(true);
    });
  });

  describe('Hook层测试', () => {
    it('useProfileLogic应该提供正确的业务逻辑', () => {
      const { result } = renderHook(() => useProfileLogic());
      
      expect(result.current.isLoggedIn).toBeDefined();
      expect(result.current.handleMenuItemPress).toBeInstanceOf(Function);
      expect(result.current.handleStatsCardPress).toBeInstanceOf(Function);
      expect(result.current.requireLoginAction).toBeInstanceOf(Function);
    });

    it('useProfileUI应该提供正确的UI状态管理', () => {
      const { result } = renderHook(() => useProfileUI());
      
      expect(result.current.showLoginModal).toBe(false);
      expect(result.current.showLogoutModal).toBe(false);
      expect(result.current.refreshing).toBe(false);
      expect(result.current.openLoginModal).toBeInstanceOf(Function);
      expect(result.current.closeLoginModal).toBeInstanceOf(Function);
    });

    it('useUserStats应该正确计算统计数据', () => {
      const mockTenantStats = {
        rentalDemands: 2,
        purchaseDemands: 3,
        interestedProperties: 10,
        totalExposure: 100,
        totalMatches: 25,
      };

      const { result } = renderHook(() => useUserStats({
        tenantBuyerStats: mockTenantStats,
        landlordStats: null,
        orderCounts: null,
      }));
      
      expect(result.current.computedStats.totalDemands).toBe(5);
      expect(result.current.computedStats.hasTenantActivity).toBe(true);
    });
  });

  describe('层级集成测试', () => {
    it('Store层应该与转换器正确集成', async () => {
      const store = useProfileStore.getState();
      
      const mockApiData = {
        rental_demands: 1,
        purchase_demands: 2,
        interested_properties: 15,
        total_exposure: 100,
        total_matches: 25,
      };

      await act(async () => {
        await store.setTenantBuyerStatsFromAPI(mockApiData);
      });

      const state = useProfileStore.getState();
      expect(state.tenantBuyerStats).toEqual({
        rentalDemands: 1,
        purchaseDemands: 2,
        interestedProperties: 15,
        totalExposure: 100,
        totalMatches: 25,
      });
    });

    it('Hook层应该与Store层正确集成', () => {
      // 设置Store状态
      act(() => {
        const store = useProfileStore.getState();
        store.setIsLoggedIn(true);
      });

      // Hook应该反映Store状态
      const { result } = renderHook(() => useProfileLogic());
      expect(result.current.isLoggedIn).toBe(true);
    });
  });

  describe('错误处理测试', () => {
    it('转换器应该正确处理错误', () => {
      const transformers = getProfileTransformers();
      
      const invalidApiData = {
        // 缺少必需字段
        user_id: 'test-id',
      };

      const result = transformers.userInfo.fromAPI(invalidApiData as any);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('Store应该正确处理转换错误', async () => {
      const store = useProfileStore.getState();
      
      const invalidApiData = {
        // 无效数据
        invalid_field: 'invalid',
      };

      await act(async () => {
        await store.setUserInfoFromAPI(invalidApiData);
      });

      const state = useProfileStore.getState();
      expect(state.userInfoError).toBeDefined();
      expect(state.hasError).toBe(true);
    });
  });
});

console.log('✅ 五层架构集成测试文件创建完成');
console.log('📊 测试覆盖：UI层、Hook层、Store层、DTO层');
console.log('🔧 测试类型：单元测试、集成测试、错误处理测试');
