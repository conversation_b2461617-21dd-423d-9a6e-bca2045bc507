/**
 * 区域转换工具函数
 * 提供区域代码到中文显示的转换
 */

/**
 * 南宁区域代码到中文的映射
 */
const REGION_CODE_MAP: Record<string, string> = {
  yongning: '邕宁区',
  qingxiu: '青秀区',
  xingning: '兴宁区',
  jiangnan: '江南区',
  liangqing: '良庆区',
  xixiangtang: '西乡塘区',
  wuming: '武鸣区',
  hengxian: '横县',
  binyang: '宾阳县',
  shanglin: '上林县',
  mashan: '马山县',
  longan: '隆安县',
};

/**
 * 获取区域的中文显示名称
 * @param regionCode 区域代码
 * @returns 中文显示名称
 */
export const getRegionDisplayName = (regionCode: string): string => {
  return REGION_CODE_MAP[regionCode] || regionCode;
};

/**
 * 获取区域数组的中文显示名称
 * @param regionCodes 区域代码数组
 * @returns 中文显示名称数组
 */
export const getRegionsDisplayNames = (regionCodes: string[]): string[] => {
  return regionCodes.map(code => getRegionDisplayName(code));
};

/**
 * 获取区域数组的中文显示字符串（用顿号分隔）
 * @param regionCodes 区域代码数组
 * @returns 中文显示字符串
 */
export const getRegionsDisplayString = (regionCodes: string[]): string => {
  return getRegionsDisplayNames(regionCodes).join('、');
};
