/**
 * 企业级表单容器组件
 * 
 * 基于微信、支付宝等主流APP的表单设计最佳实践
 * 提供完整的键盘响应、字段验证、错误处理解决方案
 * 
 * 核心功能：
 * 1. 智能键盘避让 - 输入框自动显示在键盘上方
 * 2. 验证错误自动滚动 - 提交时自动滚动到第一个错误字段
 * 3. 企业级用户体验 - 符合主流APP的交互标准
 * 4. 跨平台兼容 - iOS/Android一致的表现
 */

import React, { ReactNode, useCallback, useRef, useImperativeHandle, forwardRef } from 'react';
import {
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  ViewStyle,
  Dimensions,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useKeyboardAwareForm, FormFieldError, UseKeyboardAwareFormReturn } from '../hooks/useKeyboardAwareForm';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

export interface EnterpriseFormContainerProps {
  /**
   * 子组件
   */
  children: ReactNode;
  
  /**
   * 容器样式
   */
  style?: ViewStyle;
  
  /**
   * 内容容器样式
   */
  contentContainerStyle?: ViewStyle;
  
  /**
   * 是否显示滚动指示器
   * @default false
   */
  showsVerticalScrollIndicator?: boolean;
  
  /**
   * 键盘弹出时的额外偏移量
   * @default 20
   */
  keyboardExtraOffset?: number;
  
  /**
   * 底部安全区域额外距离
   * @default 20
   */
  bottomSafeAreaExtra?: number;
  
  /**
   * 键盘弹出回调
   */
  onKeyboardShow?: (keyboardHeight: number) => void;
  
  /**
   * 键盘隐藏回调
   */
  onKeyboardHide?: () => void;
  
  /**
   * 表单验证失败时的回调
   */
  onValidationFailed?: (errors: FormFieldError[]) => void;
}

export interface EnterpriseFormContainerRef {
  /**
   * 滚动到第一个错误字段
   */
  scrollToFirstError: (errors: FormFieldError[]) => Promise<void>;
  
  /**
   * 滚动到指定字段
   */
  scrollToField: (fieldName: string) => void;
  
  /**
   * 显示字段错误
   */
  showFieldErrors: (errors: FormFieldError[]) => void;
  
  /**
   * 清除字段错误
   */
  clearFieldErrors: (fieldNames?: string[]) => void;
  
  /**
   * 获取表单控制器
   */
  getFormControl: () => UseKeyboardAwareFormReturn;
  
  /**
   * 滚动到顶部
   */
  scrollToTop: () => void;
  
  /**
   * 滚动到底部
   */
  scrollToBottom: () => void;
}

/**
 * 企业级表单容器组件
 */
export const EnterpriseFormContainer = forwardRef<EnterpriseFormContainerRef, EnterpriseFormContainerProps>(
  ({
    children,
    style,
    contentContainerStyle,
    showsVerticalScrollIndicator = false,
    keyboardExtraOffset = 20,
    bottomSafeAreaExtra = 20,
    onKeyboardShow,
    onKeyboardHide,
    onValidationFailed,
  }, ref) => {
    const insets = useSafeAreaInsets();
    
    // 使用键盘响应表单Hook
    const formControl = useKeyboardAwareForm({
      extraOffset: keyboardExtraOffset,
      enableAutoScroll: true,
      scrollAnimationDuration: 250,
      resetScrollOnKeyboardHide: false,
      onKeyboardShow: (keyboardInfo) => {
        onKeyboardShow?.(keyboardInfo.height);
      },
      onKeyboardHide: () => {
        onKeyboardHide?.();
      },
    });

    // 计算键盘避让行为
    const keyboardBehavior = Platform.select({
      ios: 'padding' as const,
      android: 'height' as const,
      default: 'padding' as const,
    });

    // 计算键盘偏移量
    const keyboardVerticalOffset = Platform.select({
      ios: insets.bottom,
      android: 0,
      default: 0,
    });

    // 滚动到第一个错误字段的增强版实现
    const scrollToFirstError = useCallback(async (errors: FormFieldError[]): Promise<void> => {
      if (!errors.length) return;

      try {
        // 先调用原始的滚动方法
        await formControl.scrollToFirstError(errors);
        
        // 显示错误状态
        formControl.showFieldErrors(errors);
        
        // 触发验证失败回调
        onValidationFailed?.(errors);
        
        console.log('[EnterpriseFormContainer] ✅ 已滚动到第一个错误字段:', errors[0].fieldName);
      } catch (error) {
        console.warn('[EnterpriseFormContainer] 滚动到错误字段失败:', error);
      }
    }, [formControl, onValidationFailed]);

    // 滚动到顶部
    const scrollToTop = useCallback(() => {
      formControl.scrollViewRef.current?.scrollTo({
        y: 0,
        animated: true,
      });
    }, [formControl.scrollViewRef]);

    // 滚动到底部
    const scrollToBottom = useCallback(() => {
      formControl.scrollViewRef.current?.scrollToEnd({
        animated: true,
      });
    }, [formControl.scrollViewRef]);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      scrollToFirstError,
      scrollToField: formControl.scrollToField,
      showFieldErrors: formControl.showFieldErrors,
      clearFieldErrors: formControl.clearFieldErrors,
      getFormControl: () => formControl,
      scrollToTop,
      scrollToBottom,
    }), [formControl, scrollToFirstError, scrollToTop, scrollToBottom]);

    return (
      <KeyboardAvoidingView
        style={[styles.container, style]}
        behavior={keyboardBehavior}
        keyboardVerticalOffset={keyboardVerticalOffset}
      >
        <ScrollView
          ref={formControl.scrollViewRef}
          style={styles.scrollView}
          contentContainerStyle={[
            styles.contentContainer,
            {
              paddingBottom: Math.max(
                insets.bottom + bottomSafeAreaExtra,
                formControl.keyboard.isVisible ? keyboardExtraOffset : 0
              ),
            },
            contentContainerStyle,
          ]}
          showsVerticalScrollIndicator={showsVerticalScrollIndicator}
          keyboardShouldPersistTaps="handled"
          bounces={true}
          overScrollMode="auto"
          scrollEventThrottle={16}
          removeClippedSubviews={false}
          // 企业级优化：确保表单在各种设备上都能正常工作
          automaticallyAdjustContentInsets={false}
          contentInsetAdjustmentBehavior="never"
        >
          {children}
        </ScrollView>
      </KeyboardAvoidingView>
    );
  }
);

/**
 * 企业级输入框组件
 * 自动集成键盘响应功能
 */
export interface EnterpriseTextInputProps {
  /**
   * 字段名称，用于错误定位
   */
  fieldName: string;
  
  /**
   * 表单控制器
   */
  formControl: UseKeyboardAwareFormReturn;
  
  /**
   * 子组件（通常是TextInput或包装的输入组件）
   */
  children: ReactNode;
  
  /**
   * 容器样式
   */
  style?: ViewStyle;
  
  /**
   * 是否为必填字段
   * @default false
   */
  required?: boolean;
  
  /**
   * 错误信息优先级
   * @default 1
   */
  errorPriority?: number;
}

export const EnterpriseTextInput: React.FC<EnterpriseTextInputProps> = ({
  fieldName,
  formControl,
  children,
  style,
  required = false,
  errorPriority = 1,
}) => {
  const handleLayout = useCallback((event: any) => {
    const { y, height } = event.nativeEvent.layout;
    formControl.updateFieldLayout(fieldName, { y, height });
  }, [fieldName, formControl]);

  return (
    <View
      style={style}
      onLayout={handleLayout}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
});

export default EnterpriseFormContainer;