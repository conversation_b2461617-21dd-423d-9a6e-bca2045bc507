/**
 * 统一验证引擎
 * 
 * 遵循AI编码指导规范：
 * - 使用zod进行运行时类型验证
 * - 企业级错误处理
 * - 完整的类型安全
 * 
 * @fileoverview 提供统一的数据验证功能
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */

import { z } from 'zod';
import { TransformResult, ValidationRule } from '../types/TransformTypes';

/**
 * 验证结果
 */
export interface ValidationResult {
  valid: boolean;
  data?: any;
  errors?: string[];
  warnings?: string[];
}

/**
 * 验证上下文
 */
export interface ValidationContext {
  strict?: boolean;
  skipOptional?: boolean;
  customRules?: Record<string, (value: any) => boolean>;
}

/**
 * 统一验证引擎
 * 提供企业级的数据验证功能
 */
export class ValidationEngine {
  private rules: Map<string, ValidationRule> = new Map();
  private readonly version = '1.0.0';

  /**
   * 添加验证规则
   */
  addRule(rule: ValidationRule): void {
    try {
      // 验证规则本身的有效性
      if (!rule.name || !rule.schema) {
        throw new Error('验证规则必须包含name和schema');
      }

      this.rules.set(rule.name, rule);
      console.log(`[ValidationEngine] 验证规则添加成功: ${rule.name}`);
    } catch (error) {
      console.error(`[ValidationEngine] 验证规则添加失败: ${rule.name}`, error);
      throw error;
    }
  }

  /**
   * 移除验证规则
   */
  removeRule(name: string): boolean {
    const existed = this.rules.has(name);
    this.rules.delete(name);
    
    if (existed) {
      console.log(`[ValidationEngine] 验证规则移除成功: ${name}`);
    }
    
    return existed;
  }

  /**
   * 获取验证规则
   */
  getRule(name: string): ValidationRule | undefined {
    return this.rules.get(name);
  }

  /**
   * 验证数据
   */
  validate(data: any, ruleName: string, context?: ValidationContext): ValidationResult {
    try {
      const rule = this.rules.get(ruleName);
      if (!rule) {
        return {
          valid: false,
          errors: [`验证规则不存在: ${ruleName}`]
        };
      }

      // 执行zod验证
      const result = rule.schema.safeParse(data);
      
      if (result.success) {
        return {
          valid: true,
          data: result.data
        };
      } else {
        return {
          valid: false,
          errors: this.formatZodErrors(result.error),
          data: data
        };
      }
    } catch (error) {
      console.error(`[ValidationEngine] 验证执行失败: ${ruleName}`, error);
      return {
        valid: false,
        errors: [`验证执行失败: ${error}`]
      };
    }
  }

  /**
   * 批量验证
   */
  validateBatch(
    items: Array<{ data: any; ruleName: string }>,
    context?: ValidationContext
  ): Array<ValidationResult & { index: number }> {
    return items.map((item, index) => ({
      ...this.validate(item.data, item.ruleName, context),
      index
    }));
  }

  /**
   * 验证并转换
   */
  validateAndTransform<T>(
    data: any,
    ruleName: string,
    context?: ValidationContext
  ): TransformResult<T> {
    const validationResult = this.validate(data, ruleName, context);
    
    if (validationResult.valid) {
      return {
        success: true,
        data: validationResult.data as T,
        originalValue: data
      };
    } else {
      return {
        success: false,
        error: validationResult.errors?.join('; ') || '验证失败',
        originalValue: data
      };
    }
  }

  /**
   * 格式化zod错误信息
   */
  private formatZodErrors(error: z.ZodError): string[] {
    return error.issues.map(issue => {
      const path = issue.path.length > 0 ? issue.path.join('.') : 'root';
      return `${path}: ${issue.message}`;
    });
  }

  /**
   * 获取所有验证规则名称
   */
  getRuleNames(): string[] {
    return Array.from(this.rules.keys());
  }

  /**
   * 获取验证引擎统计信息
   */
  getStats(): {
    totalRules: number;
    requiredRules: number;
    optionalRules: number;
    engineVersion: string;
  } {
    const rules = Array.from(this.rules.values());
    const requiredCount = rules.filter(rule => rule.required).length;
    
    return {
      totalRules: this.rules.size,
      requiredRules: requiredCount,
      optionalRules: this.rules.size - requiredCount,
      engineVersion: this.version
    };
  }

  /**
   * 清空所有验证规则
   */
  clear(): void {
    const count = this.rules.size;
    this.rules.clear();
    console.log(`[ValidationEngine] 清空所有验证规则，共 ${count} 个`);
  }

  /**
   * 健康检查
   */
  healthCheck(): { healthy: boolean; issues: string[] } {
    const issues: string[] = [];
    
    // 检查是否有验证规则
    if (this.rules.size === 0) {
      issues.push('没有注册任何验证规则');
    }

    // 检查必需规则
    const requiredRules = Array.from(this.rules.values()).filter(rule => rule.required);
    if (requiredRules.length === 0) {
      issues.push('没有必需的验证规则');
    }

    return {
      healthy: issues.length === 0,
      issues
    };
  }
}

// ==================== 预定义验证规则 ====================

/**
 * 创建常用的验证规则
 */
export const createCommonValidationRules = () => {
  const rules: ValidationRule[] = [
    {
      name: 'propertyType',
      description: '房源类型验证',
      schema: z.enum(['商铺', '写字楼', '厂房', '仓库', '土地', '会所', '活动会议室']),
      required: true
    },
    {
      name: 'apiPropertyType',
      description: 'API房源类型验证',
      schema: z.enum(['SHOP', 'OFFICE', 'FACTORY', 'WAREHOUSE', 'LAND', 'CLUBHOUSE', 'MEETING_ROOM']),
      required: true
    },
    {
      name: 'pagination',
      description: '分页参数验证',
      schema: z.object({
        page: z.number().min(1),
        size: z.number().min(1).max(100)
      }),
      required: true
    },
    {
      name: 'propertyListItem',
      description: '房源列表项验证',
      schema: z.object({
        id: z.string(),
        title: z.string(),
        price: z.string(),
        area: z.string(),
        location: z.string(),
        imageUrl: z.string().url(),
        tags: z.array(z.string()),
        isVip: z.boolean(),
        isFeatured: z.boolean()
      }),
      required: false
    }
  ];

  return rules;
};

/**
 * 默认验证引擎实例
 */
export const defaultValidationEngine = new ValidationEngine();

// 注册常用验证规则
createCommonValidationRules().forEach(rule => {
  defaultValidationEngine.addRule(rule);
});

export default ValidationEngine;
