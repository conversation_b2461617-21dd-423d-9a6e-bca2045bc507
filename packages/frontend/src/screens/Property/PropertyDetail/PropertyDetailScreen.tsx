/**
 * 重构后的房源详情页面 (PropertyDetailScreen)
 * 企业级五层架构实现
 * 
 * 功能特点：
 * 1. 符合企业级前端五层架构规范
 * 2. 组件职责单一，每个组件<300行
 * 3. Hook层封装业务逻辑
 * 4. 性能优化和错误处理
 */

import React from 'react';
import { View, ScrollView, TouchableOpacity, Text } from 'react-native';

// 导入简化的错误边界组件
import SimpleErrorBoundary from './components/SimpleErrorBoundary';

// 导入拆分后的组件
import { PropertyHeader } from './components/PropertyHeader';
import { ReportBar } from './components/ReportBar';
import { PropertyInfoSection } from './components/PropertyInfoSection';
import { propertyDetailStyles as styles } from './components/styles';

// 导入现有的组件
import { 
  MediaCarousel, 
  ImageViewer,
  DetailedInfoSection,
  PropertyDetailMapSection,
  BottomActionBar,
  HotQuestionsSection
} from '@property/components/detail';
import LazyLoadWrapper from '@components/common/LazyLoadWrapper';
import SimilarPropertiesWithAPI from '@components/SimilarPropertiesWithAPI';

// 导入Hook
import { usePropertyDetailLogic } from './hooks/usePropertyDetailLogic';
import { useNavigation } from '@react-navigation/native';

// 导入工具
import FeedbackService from '@shared/services/FeedbackService';
import { hp } from '@shared/utils/responsive';

/**
 * 重构后的房源详情页面组件
 * 主要逻辑已移至Hook层，组件专注于UI渲染
 * 使用简化的错误处理和现有的懒加载
 */
const PropertyDetailScreen: React.FC = () => {
  // 获取导航实例
  const navigation = useNavigation();
  
  // 使用Hook获取所有业务逻辑和状态
  const {
    // 数据
    propertyId,
    currentData,
    publishedData,
    sduiConfig,
    isSDUIReady,
    
    // 状态
    isLoading,
    error,
    isImageViewerVisible,
    imageViewerIndex,
    isFavorited,
    
    // 操作方法
    handleMediaChange,
    handleTabChange,
    handleImagePress,
    handleFavorite,
    handleMessageView,
    handleShare,
    handleGoBack,
    handleContact,
    handleReserve,
    refetch,
    setIsImageViewerVisible,
  } = usePropertyDetailLogic();

  // 加载状态
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  // 错误状态
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>加载失败</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryButtonText}>重试</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // 数据为空状态
  if (!currentData) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>房源信息不存在</Text>
        <TouchableOpacity style={styles.retryButton} onPress={handleGoBack}>
          <Text style={styles.retryButtonText}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SimpleErrorBoundary
      onError={(error) => {
        console.error('[PropertyDetail] 页面错误:', error);
      }}
      maxRetryCount={2}
    >
      <View style={styles.container}>
        {/* 顶部导航栏 */}
        <PropertyHeader
          onGoBack={handleGoBack}
          onShare={handleShare}
          onMessageView={handleMessageView}
        />

        {/* 房源数据展示 */}
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          {/* 媒体轮播区域 */}
          <MediaCarousel
            mediaData={currentData.media}
            onMediaChange={handleMediaChange}
            onTabChange={handleTabChange}
            onImagePress={handleImagePress}
          />

          {/* 举报纠错滚动条 */}
          <ReportBar />

          {/* 房源基本信息和关键指标 */}
          <PropertyInfoSection
            propertyData={currentData}
            sduiConfig={sduiConfig}
            isSDUIReady={isSDUIReady}
          />

          {/* 位置地图区块 */}
          <LazyLoadWrapper placeholderHeight={hp(80)}>
            <PropertyDetailMapSection
              propertyData={currentData}
              onMapPress={() => FeedbackService.showInfo('详细地图功能开发中')}
              publishedLocation={
                publishedData ? {
                  address: publishedData.address || publishedData.location,
                  // 如果发布数据中有坐标信息，传递给地图组件
                  latitude: publishedData.latitude,
                  longitude: publishedData.longitude,
                } : undefined
              }
            />
          </LazyLoadWrapper>

          {/* 详细信息区域 */}
          <LazyLoadWrapper placeholderHeight={hp(300)}>
            <DetailedInfoSection propertyData={currentData} />
          </LazyLoadWrapper>

          {/* 热门问题区域 */}
          <LazyLoadWrapper placeholderHeight={hp(150)}>
            <HotQuestionsSection
              onQuestionPress={question =>
                FeedbackService.showInfo(`您想咨询: ${question}`)
              }
            />
          </LazyLoadWrapper>

          {/* 相似房源区域 */}
          <LazyLoadWrapper>
            <SimilarPropertiesWithAPI
              propertyId={propertyId}
              propertyData={currentData}
              onPropertyPress={id => {
                // 跳转到其他房源详情页
                (navigation as any).navigate('PropertyDetail', { propertyId: id });
              }}
            />
          </LazyLoadWrapper>

          {/* 占位空间，为底部操作栏留出空间 */}
          <View style={styles.bottomSpacing} />
        </ScrollView>

        {/* 底部操作栏 */}
        <BottomActionBar
          isFavorited={isFavorited}
          onFavorite={handleFavorite}
          onTrack={() => FeedbackService.showInfo('此功能正在开发中...')}
          onContact={handleContact}
          onReserve={handleReserve}
        />

        {/* 图片查看器 */}
        <ImageViewer
          images={currentData.media.images}
          imageIndex={imageViewerIndex}
          visible={isImageViewerVisible}
          onRequestClose={() => setIsImageViewerVisible(false)}
        />
      </View>
    </SimpleErrorBoundary>
  );
};

export { PropertyDetailScreen };
export default PropertyDetailScreen;