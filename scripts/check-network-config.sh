#!/bin/bash

# 网络配置检查脚本
# 检查前端是否能正确访问后端API

echo "🌐 检查网络配置..."

# 检查本地后端API
echo "📡 检查本地后端API（容器内）..."
curl -s -o /dev/null -w "%{http_code}" http://localhost:8082/health || echo "❌ 本地API不可达"

# 检查外部后端API
echo "📡 检查外部后端API..."
curl -s -o /dev/null -w "%{http_code}" http://*************:8082/health || echo "❌ 外部API不可达"

# 检查验证码API端点
echo "📱 检查验证码API端点..."
curl -s -X GET "http://localhost:8082/api/v1/auth/can-send-code?phone_number=13800138000&code_type=login" | head -5

echo "✅ 网络配置检查完成"