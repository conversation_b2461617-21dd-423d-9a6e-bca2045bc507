/**
 * 更多功能区域组件
 * 功能：7个扩展功能展示，3x3网格布局，图标和文字展示
 * 完整保留原始样式，确保UI完全一致，目标：90行
 */
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './styles';

interface MoreMenuItem {
  id: string;
  title: string;
  icon: string;
  color: string;
  onPress: () => void;
}

interface MoreMenuSectionProps {
  moreMenuItems: MoreMenuItem[];
}

export const MoreMenuSection: React.FC<MoreMenuSectionProps> = ({
  moreMenuItems,
}) => {
  return (
    <View style={styles.moreSection}>
      <Text style={styles.sectionTitle}>更多功能</Text>
      <View style={styles.moreGrid}>
        {moreMenuItems.map(item => (
          <TouchableOpacity
            key={item.id}
            style={styles.moreItem}
            onPress={item.onPress}
          >
            <Ionicons
              name={item.icon as any}
              size={32}
              color={item.color}
            />
            <Text style={styles.moreItemText}>{item.title}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};
