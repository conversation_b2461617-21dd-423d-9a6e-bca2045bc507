/**
 * 房源详情页样式统一导出
 * 
 * 将原本900+行的样式代码拆分为多个模块：
 * - BaseStyles: 基础样式（容器、文本、布局等）
 * - ProgressStyles: 进度条相关样式
 * - FormStyles: 表单相关样式
 * - MediaUploadStyles: 媒体上传相关样式
 * - ComponentStyles: 组件相关样式
 */

// 导入各个样式模块
export { baseStyles } from './BaseStyles';
export { progressStyles } from './ProgressStyles';
export { formStyles } from './FormStyles';
export { mediaUploadStyles } from './MediaUploadStyles';
export { componentStyles } from './ComponentStyles';

// 合并所有样式的便捷导出（保持向后兼容）
import { baseStyles } from './BaseStyles';
import { progressStyles } from './ProgressStyles';
import { formStyles } from './FormStyles';
import { mediaUploadStyles } from './MediaUploadStyles';
import { componentStyles } from './ComponentStyles';

/**
 * 合并所有样式，保持与原始 styles 对象的兼容性
 * 这样可以确保现有代码无需修改即可使用新的样式结构
 */
export const allStyles = {
  // 基础样式
  ...baseStyles,
  
  // 进度条样式
  ...progressStyles,
  
  // 表单样式
  ...formStyles,
  
  // 媒体上传样式
  ...mediaUploadStyles,
  
  // 组件样式
  ...componentStyles,
};

/**
 * 默认导出合并后的样式对象
 * 使用方式：import styles from './styles'
 */
export default allStyles;

/**
 * 样式分类导出，便于按需使用
 * 使用方式：import { baseStyles, formStyles } from './styles'
 */
export const styleCategories = {
  base: baseStyles,
  progress: progressStyles,
  form: formStyles,
  mediaUpload: mediaUploadStyles,
  component: componentStyles,
};

/**
 * 样式统计信息（用于性能监控）
 */
export const styleStats = {
  totalStyles: Object.keys(allStyles).length,
  categories: {
    base: Object.keys(baseStyles).length,
    progress: Object.keys(progressStyles).length,
    form: Object.keys(formStyles).length,
    mediaUpload: Object.keys(mediaUploadStyles).length,
    component: Object.keys(componentStyles).length,
  },
  version: '1.0.0',
  lastUpdated: '2025-07-17',
};
