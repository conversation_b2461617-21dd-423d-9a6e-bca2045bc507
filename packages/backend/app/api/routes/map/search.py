"""
地图搜索API - 圆形区域房源搜索
企业级PostGIS查询 + 统一地图服务架构
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
from typing import List, Optional
import logging

from app.core.db import get_async_session
from app.services.map_service import MapService

router = APIRouter()
logger = logging.getLogger(__name__)

class CircleSearchRequest(BaseModel):
    """圆形区域搜索请求"""
    lat: float = Field(..., ge=-90, le=90, description="圆心纬度")
    lng: float = Field(..., ge=-180, le=180, description="圆心经度")
    radius: int = Field(1000, ge=100, le=50000, description="搜索半径（米）")
    query: Optional[str] = Field("", max_length=100, description="搜索关键词")
    property_type: Optional[str] = Field(None, description="房源类型筛选")
    min_price: Optional[float] = Field(None, ge=0, description="最低价格")
    max_price: Optional[float] = Field(None, ge=0, description="最高价格")
    limit: int = Field(50, ge=1, le=100, description="返回数量限制")

class PropertyResponse(BaseModel):
    """房源响应模型"""
    id: str
    title: str
    latitude: float
    longitude: float
    property_type: str
    total_area: float
    price: Optional[float] = None
    distance: Optional[float] = Field(None, description="距离搜索中心的距离（米）")
    address: Optional[str] = None

class MapSearchResponse(BaseModel):
    """地图搜索响应"""
    properties: List[PropertyResponse]
    total: int
    search_center: dict
    search_radius: int

@router.post("/search-circle", response_model=MapSearchResponse)
async def search_properties_in_circle(
    request: CircleSearchRequest,
    db: AsyncSession = Depends(get_async_session)
):
    """
    在圆形区域内搜索房源
    企业级PostGIS查询 + 统一地图服务
    """
    try:
        logger.info(f"地图搜索请求: 中心({request.lat}, {request.lng}), 半径{request.radius}m")

        # 使用统一的地图服务
        map_service = MapService(db)

        # 执行地理位置搜索
        search_result = await map_service.search_properties_by_location(
            center_lat=request.lat,
            center_lng=request.lng,
            radius_meters=request.radius,
            limit=request.limit,
            include_distances=True
        )

        # 转换为兼容的响应格式
        property_responses = []
        for prop in search_result["properties"]:
            property_responses.append(PropertyResponse(
                id=prop["id"],
                title=prop["title"],
                latitude=prop["location"]["lat"],
                longitude=prop["location"]["lng"],
                property_type=prop["property_type"],
                total_area=prop["area"] or 0.0,
                price=prop["rent_price"] or prop["sale_price"],
                distance=prop.get("distance_meters"),
                address=prop["address"]
            ))

        logger.info(f"地图搜索完成: 找到{len(property_responses)}个房源")

        return MapSearchResponse(
            properties=property_responses,
            total=len(property_responses),
            search_center={"lat": request.lat, "lng": request.lng},
            search_radius=request.radius
        )

    except Exception as e:
        logger.error(f"地图搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/search-circle-simple")
async def search_properties_simple(
    lat: float = Query(..., description="圆心纬度"),
    lng: float = Query(..., description="圆心经度"),
    radius: int = Query(1000, description="搜索半径（米）"),
    property_type: Optional[str] = Query(None, description="房源类型筛选"),
    limit: int = Query(50, description="返回数量限制"),
    db: AsyncSession = Depends(get_async_session)
):
    """简化版地图搜索 (兼容专家原始设计)"""
    request = CircleSearchRequest(
        lat=lat, lng=lng, radius=radius,
        property_type=property_type, limit=limit
    )
    return await search_properties_in_circle(request, db)
