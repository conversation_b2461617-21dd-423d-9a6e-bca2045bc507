/**
 * 用户数据转换器
 * 
 * 遵循AI编码指导规范：
 * - 继承BaseTransformer基础类
 * - 处理用户相关数据转换
 * - 企业级错误处理
 * 
 * @fileoverview 处理用户相关的数据转换
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */

import { z } from 'zod';
import { BaseTransformer } from '../core/BaseTransformer';
import { 
  TransformResult, 
  TransformOptions
} from '../types/TransformTypes';

/**
 * 用户信息（前端格式）
 */
export interface UserFormData {
  id?: string;
  phone: string;
  surname?: string;
  gender?: string;
  avatar?: string;
  wechat?: string;
  email?: string;
}

/**
 * 用户信息（API格式）
 */
export interface UserAPIData {
  id?: number;
  phone: string;
  surname?: string;
  gender?: string;
  avatar_url?: string;
  wechat?: string;
  email?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * 用户转换器
 * 负责用户相关数据的双向转换
 */
export class UserTransformer extends BaseTransformer<UserFormData, UserAPIData> {
  
  constructor() {
    super('UserTransformer', '1.0.0');
  }

  /**
   * 初始化验证规则
   */
  protected initializeValidationRules(): void {
    // 用户表单数据验证
    this.addValidationRule('userForm', {
      name: 'userForm',
      description: '用户表单数据验证',
      schema: z.object({
        id: z.string().optional(),
        phone: z.string().regex(/^1[3-9]\d{9}$/, '手机号格式不正确'),
        surname: z.string().optional(),
        gender: z.enum(['男', '女', '']).optional(),
        avatar: z.string().url().optional(),
        wechat: z.string().optional(),
        email: z.string().email().optional()
      }),
      required: true
    });

    // 用户API数据验证
    this.addValidationRule('userAPI', {
      name: 'userAPI',
      description: '用户API数据验证',
      schema: z.object({
        id: z.number().optional(),
        phone: z.string(),
        surname: z.string().optional(),
        gender: z.string().optional(),
        avatar_url: z.string().optional(),
        wechat: z.string().optional(),
        email: z.string().optional(),
        created_at: z.string().optional(),
        updated_at: z.string().optional()
      }),
      required: true
    });
  }

  /**
   * 转换为API格式
   */
  toAPI(input: UserFormData, options?: TransformOptions): TransformResult<UserAPIData> {
    return this.safeTransform(() => {
      // 验证输入数据
      const validationResult = this.validateInput(input, 'userForm');
      if (!validationResult.success) {
        throw new Error(validationResult.error);
      }

      const apiData: UserAPIData = {
        phone: input.phone,
        surname: input.surname,
        gender: input.gender,
        avatar_url: input.avatar,
        wechat: input.wechat,
        email: input.email
      };

      // 如果有ID，添加到API数据中
      if (input.id) {
        apiData.id = parseInt(input.id);
      }

      return apiData;
    }, 'UserTransformer.toAPI');
  }

  /**
   * 从API格式转换
   */
  fromAPI(input: UserAPIData, options?: TransformOptions): TransformResult<UserFormData> {
    return this.safeTransform(() => {
      // 验证输入数据
      const validationResult = this.validateInput(input, 'userAPI');
      if (!validationResult.success) {
        throw new Error(validationResult.error);
      }

      const formData: UserFormData = {
        id: input.id?.toString(),
        phone: input.phone,
        surname: input.surname,
        gender: input.gender,
        avatar: input.avatar_url,
        wechat: input.wechat,
        email: input.email
      };

      return formData;
    }, 'UserTransformer.fromAPI');
  }
}

export default UserTransformer;
