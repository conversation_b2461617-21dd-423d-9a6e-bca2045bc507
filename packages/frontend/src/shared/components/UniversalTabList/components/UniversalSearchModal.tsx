/**
 * 🏗️ 企业级架构：通用搜索模态框组件
 * 遵循前端企业级架构规范 - UI层
 * 参考主流APP搜索界面：微信、支付宝、淘宝的搜索页面
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SearchModalProps, TabListTheme, defaultTheme } from '../types';
import { spacing, fontSize, borderRadius } from '../../../../shared/utils/responsiveUtils';

export const UniversalSearchModal: React.FC<SearchModalProps> = ({
  visible,
  onClose,
  type,
  searchConfig,
  onSearch,
  recentSearches = [],
  hotSearches = [],
  searchResults = [],
  isSearching: externalIsSearching = false,
  hasSearched = false,
  searchText: externalSearchText = '',
  renderItem,
  keyExtractor,
  onItemPress,
}) => {
  const [searchText, setSearchText] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  
  const theme = defaultTheme;
  const styles = createStyles(theme);

  // 清空搜索文本当模态框关闭时
  useEffect(() => {
    if (!visible) {
      setSearchText('');
      setIsSearching(false);
    }
  }, [visible]);

  // 🔧 同步外部搜索状态
  useEffect(() => {
    if (!externalIsSearching && isSearching) {
      setIsSearching(false);
    }
  }, [externalIsSearching, isSearching]);

  const handleSearch = (text: string) => {
    if (!text.trim()) return;

    console.log(`[UniversalSearchModal] 🔍 开始搜索: "${text}"`);
    setIsSearching(true);
    onSearch(text.trim());

    // 🔧 企业级搜索：不关闭模态框，在模态框内显示搜索结果
    // 搜索完成后的状态更新由外部组件通过props控制
    console.log(`[UniversalSearchModal] ✅ 搜索请求已发送，等待结果显示`);
  };

  const handleRecentSearchPress = (text: string) => {
    setSearchText(text);
    handleSearch(text);
  };

  const renderRecentSearchItem = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={styles.searchItem}
      onPress={() => handleRecentSearchPress(item)}
      activeOpacity={0.7}
    >
      <Ionicons name="time-outline" size={16} color={theme.secondary} />
      <Text style={styles.searchItemText}>{item}</Text>
      <Ionicons name="arrow-up-outline" size={16} color={theme.secondary} />
    </TouchableOpacity>
  );

  const renderHotSearchItem = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={styles.hotSearchTag}
      onPress={() => handleRecentSearchPress(item)}
      activeOpacity={0.7}
    >
      <Text style={styles.hotSearchText}>{item}</Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* 搜索头部 */}
        <View style={styles.header}>
          {/* 🔧 返回箭头（仿照其他页面标题栏） */}
          <TouchableOpacity
            style={styles.backButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color={theme.text.primary} />
          </TouchableOpacity>

          <View style={styles.searchBox}>
            <Ionicons name="search" size={20} color={theme.search.iconColor} />
            <TextInput
              style={styles.searchInput}
              placeholder={searchConfig.placeholder}
              placeholderTextColor={theme.search.placeholderColor}
              value={searchText}
              onChangeText={setSearchText}
              onSubmitEditing={() => handleSearch(searchText)}
              returnKeyType="search"
              autoFocus
            />
            {searchText.length > 0 && (
              <TouchableOpacity
                onPress={() => setSearchText('')}
                style={styles.clearButton}
              >
                <Ionicons name="close-circle" size={20} color={theme.secondary} />
              </TouchableOpacity>
            )}
          </View>

          <TouchableOpacity
            style={styles.cancelButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <Text style={styles.cancelText}>取消</Text>
          </TouchableOpacity>
        </View>

        {/* 搜索内容 */}
        <View style={styles.content}>
          {(isSearching || externalIsSearching) ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.primary} />
              <Text style={styles.loadingText}>搜索中...</Text>
            </View>
          ) : hasSearched ? (
            // 🔍 搜索结果显示区域
            <View style={styles.searchResultsContainer}>
              <Text style={styles.searchResultsTitle}>
                找到 {searchResults.length} 条记录
              </Text>
              {searchResults.length === 0 ? (
                <View style={styles.noResultsContainer}>
                  <Ionicons name="search" size={48} color={theme.secondary} />
                  <Text style={styles.noResultsText}>
                    没有找到包含"{externalSearchText}"的{type === 'property' ? '房源' : '需求'}
                  </Text>
                  <Text style={styles.noResultsSubText}>
                    试试其他关键词吧
                  </Text>
                </View>
              ) : (
                // 🔧 搜索结果列表显示
                <FlatList
                  data={searchResults}
                  renderItem={({ item }) => renderItem ? renderItem(item) : null}
                  keyExtractor={keyExtractor || ((item, index) => `search-result-${index}`)}
                  showsVerticalScrollIndicator={false}
                  style={styles.searchResultsList}
                  contentContainerStyle={styles.searchResultsContent}
                  onEndReachedThreshold={0.1}
                />
              )}
            </View>
          ) : (
            <>
              {/* 历史搜索 */}
              {recentSearches.length > 0 && (
                <View style={styles.section}>
                  <View style={styles.sectionHeader}>
                    <Text style={styles.sectionTitle}>最近搜索</Text>
                    <TouchableOpacity>
                      <Ionicons name="trash-outline" size={16} color={theme.secondary} />
                    </TouchableOpacity>
                  </View>
                  <FlatList
                    data={recentSearches}
                    renderItem={renderRecentSearchItem}
                    keyExtractor={(item, index) => `recent-${index}`}
                    showsVerticalScrollIndicator={false}
                  />
                </View>
              )}

              {/* 热门搜索 */}
              {hotSearches.length > 0 && (
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>热门搜索</Text>
                  <View style={styles.hotSearchContainer}>
                    {hotSearches.map((item, index) => (
                      <TouchableOpacity
                        key={`hot-${index}`}
                        style={styles.hotSearchTag}
                        onPress={() => handleRecentSearchPress(item)}
                        activeOpacity={0.7}
                      >
                        <Text style={styles.hotSearchText}>{item}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              )}

              {/* 搜索提示 */}
              <View style={styles.section}>
                <Text style={styles.tipText}>
                  💡 搜索范围：仅限您{type === 'property' ? '发布的房源' : '发布的需求'}
                </Text>
              </View>
            </>
          )}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const createStyles = (theme: TabListTheme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  
  searchBox: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.search.background,
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginRight: spacing.md,
  },
  
  searchInput: {
    flex: 1,
    fontSize: fontSize.base,
    color: theme.text.primary,
    marginLeft: spacing.sm,
  },
  
  clearButton: {
    padding: spacing.xs,
  },
  
  cancelButton: {
    paddingVertical: spacing.sm,
  },
  
  cancelText: {
    fontSize: fontSize.base,
    color: theme.primary,
    fontWeight: '500',
  },
  
  content: {
    flex: 1,
    paddingHorizontal: spacing.md,
  },
  
  section: {
    marginTop: spacing.lg,
  },
  
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  sectionTitle: {
    fontSize: fontSize.lg,
    fontWeight: '600',
    color: theme.text.primary,
  },
  
  searchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  
  searchItemText: {
    flex: 1,
    fontSize: fontSize.base,
    color: theme.text.primary,
    marginLeft: spacing.sm,
  },
  
  hotSearchContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: spacing.sm,
  },
  
  hotSearchTag: {
    backgroundColor: '#F0F0F0',
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginRight: spacing.sm,
    marginBottom: spacing.sm,
  },
  
  hotSearchText: {
    fontSize: fontSize.sm,
    color: theme.text.secondary,
  },
  
  tipText: {
    fontSize: fontSize.sm,
    color: theme.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  loadingText: {
    fontSize: fontSize.base,
    color: theme.text.secondary,
    marginTop: spacing.md,
  },

  // 🔍 搜索结果样式
  searchResultsContainer: {
    flex: 1,
    paddingTop: spacing.md,
  },

  searchResultsTitle: {
    fontSize: fontSize.base,
    color: theme.text.primary,
    fontWeight: '600',
    marginBottom: spacing.md,
  },

  searchResultsText: {
    fontSize: fontSize.base,
    color: theme.text.secondary,
    textAlign: 'center',
    marginTop: spacing.xl,
  },

  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xl * 2,
  },

  noResultsText: {
    fontSize: fontSize.lg,
    color: theme.text.primary,
    textAlign: 'center',
    marginTop: spacing.lg,
    fontWeight: '500',
  },

  noResultsSubText: {
    fontSize: fontSize.base,
    color: theme.text.secondary,
    textAlign: 'center',
    marginTop: spacing.sm,
  },

  // 🔧 返回按钮样式
  backButton: {
    padding: spacing.sm,
    marginRight: spacing.sm,
  },

  // 🔧 搜索结果列表样式
  searchResultsList: {
    flex: 1,
  },

  searchResultsContent: {
    paddingBottom: spacing.lg,
  },
});
