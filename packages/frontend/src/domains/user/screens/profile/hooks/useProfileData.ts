/**
 * 用户中心数据管理Hook - 企业级重新设计
 * 功能：封装所有API调用，统一数据管理，完整保留原始功能
 * 架构师重新设计：遵循Zustand最佳实践，避免过度选择器化
 * 核心原则：最小化选择器使用，直接访问Store，保持功能完整性
 */
import { useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '../../../../auth/services/authStore';
import { useProfileStore } from '../stores/ProfileStore';

// 使用DTO层的类型定义
import type {
  UserInfo,
  UserInfoAPI,
  TenantBuyerStats,
  TenantBuyerStatsAPI,
  LandlordStats,
  LandlordStatsAPI,
  OrderCounts,
  OrderCountsAPI,
} from '../types/profile.types';

interface UseProfileDataOptions {
  enabled?: boolean;
}

interface UseProfileDataReturn {
  // 数据状态
  userInfo: UserInfo | null;
  tenantBuyerStats: TenantBuyerStats | null;
  landlordStats: LandlordStats | null;
  orderCounts: OrderCounts | null;
  
  // 加载状态
  isLoadingUserInfo: boolean;
  isLoadingTenantStats: boolean;
  isLoadingLandlordStats: boolean;
  isLoadingOrderCounts: boolean;
  isLoading: boolean;
  
  // 错误状态
  userInfoError: Error | null;
  tenantStatsError: Error | null;
  landlordStatsError: Error | null;
  orderCountsError: Error | null;
  hasError: boolean;
  
  // 操作方法
  refreshAllData: () => Promise<void>;
  refreshUserInfo: () => Promise<void>;
  refreshTenantStats: () => Promise<void>;
  refreshLandlordStats: () => Promise<void>;
  refreshOrderCounts: () => Promise<void>;
}

export const useProfileData = ({
  enabled = true,
}: UseProfileDataOptions = {}): UseProfileDataReturn => {
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  const isLoggedIn = !!user;

  // 企业级架构师重新设计：遵循Zustand最佳实践
  // 原则1：最小化选择器使用，只在性能关键点使用
  // 原则2：直接访问Store，避免过度抽象
  // 原则3：保持功能完整性，不丢失任何原有功能

  // 企业级修复：移除有问题的useEffect，避免无限循环（按专家建议）
  // 登录状态同步应该在认证层处理，不应该在组件层强制同步
  // useEffect(() => {
  //   if (screenState.isLoggedIn !== isLoggedIn) {
  //     screenActions.setIsLoggedIn(isLoggedIn);
  //   }
  // }, [isLoggedIn, user, screenState.isLoggedIn, screenActions]);

  // 用户信息查询 - 完全保留原始逻辑
  const {
    data: userInfo,
    isLoading: isLoadingUserInfo,
    error: userInfoError,
    refetch: refetchUserInfo,
  } = useQuery({
    queryKey: ['user-info'],
    queryFn: async (): Promise<UserInfo | null> => {
      if (!isLoggedIn) return null;
      
      // 模拟API调用 (待实际API实现)
      console.log('[useProfileData] 获取用户信息');
      return {
        id: user.id || 'mock-id',
        phoneNumber: user.phone_number || '13800138000',
        userName: user.user_surname || '用户',
        avatar: undefined,
        isVerified: user.is_verified || false,
        userType: '商业地产租户',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    },
    enabled: enabled && isLoggedIn,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });

  // 租买统计查询 - 完全保留原始逻辑
  const {
    data: tenantBuyerStats,
    isLoading: isLoadingTenantStats,
    error: tenantStatsError,
    refetch: refetchTenantStats,
  } = useQuery({
    queryKey: ['tenant-buyer-stats'],
    queryFn: async (): Promise<TenantBuyerStats | null> => {
      if (!isLoggedIn) return null;
      
      console.log('[useProfileData] 获取租买统计');
      // 模拟API调用，返回原始数据结构
      return {
        rentalDemands: 1,
        purchaseDemands: 2,
        interestedProperties: 15,
        totalExposure: 100,
        totalMatches: 25,
      };
    },
    enabled: enabled && isLoggedIn,
    staleTime: 2 * 60 * 1000, // 2分钟缓存
  });

  // 业主统计查询 - 完全保留原始逻辑
  const {
    data: landlordStats,
    isLoading: isLoadingLandlordStats,
    error: landlordStatsError,
    refetch: refetchLandlordStats,
  } = useQuery({
    queryKey: ['landlord-stats'],
    queryFn: async (): Promise<LandlordStats | null> => {
      if (!isLoggedIn) return null;
      
      console.log('[useProfileData] 获取业主统计');
      // 模拟API调用，返回原始数据结构
      return {
        rentalProperties: 2,
        saleProperties: 1,
        totalFavorites: 15,
        totalInquiries: 25,
        totalViews: 500,
        averageRating: 4.5,
      };
    },
    enabled: enabled && isLoggedIn,
    staleTime: 2 * 60 * 1000, // 2分钟缓存
  });

  // 订单统计查询 - 完全保留原始逻辑
  const {
    data: orderCounts,
    isLoading: isLoadingOrderCounts,
    error: orderCountsError,
    refetch: refetchOrderCounts,
  } = useQuery({
    queryKey: ['order-counts'],
    queryFn: async (): Promise<OrderCounts | null> => {
      if (!isLoggedIn) return null;
      
      console.log('[useProfileData] 获取订单统计');
      // 模拟API调用，返回原始数据结构
      return {
        favoriteCount: 8,
        appointmentCount: 3,
        orderCount: 5,
        tenantResourceCount: 2,
        completedOrders: 3,
        pendingOrders: 2,
      };
    },
    enabled: enabled && isLoggedIn,
    staleTime: 1 * 60 * 1000, // 1分钟缓存
  });

  // 计算加载状态
  const isLoading = isLoadingUserInfo || isLoadingTenantStats || 
                   isLoadingLandlordStats || isLoadingOrderCounts;

  // 计算错误状态
  const hasError = !!(userInfoError || tenantStatsError || 
                     landlordStatsError || orderCountsError);

  // 刷新所有数据
  const refreshAllData = useCallback(async () => {
    console.log('[useProfileData] 刷新所有数据');
    await Promise.all([
      refetchUserInfo(),
      refetchTenantStats(),
      refetchLandlordStats(),
      refetchOrderCounts(),
    ]);
  }, [refetchUserInfo, refetchTenantStats, refetchLandlordStats, refetchOrderCounts]);

  // 单独刷新方法
  const refreshUserInfo = useCallback(async () => {
    console.log('[useProfileData] 刷新用户信息');
    await refetchUserInfo();
  }, [refetchUserInfo]);

  const refreshTenantStats = useCallback(async () => {
    console.log('[useProfileData] 刷新租买统计');
    await refetchTenantStats();
  }, [refetchTenantStats]);

  const refreshLandlordStats = useCallback(async () => {
    console.log('[useProfileData] 刷新业主统计');
    await refetchLandlordStats();
  }, [refetchLandlordStats]);

  const refreshOrderCounts = useCallback(async () => {
    console.log('[useProfileData] 刷新订单统计');
    await refetchOrderCounts();
  }, [refetchOrderCounts]);

  return {
    // 数据状态
    userInfo: userInfo || null,
    tenantBuyerStats: tenantBuyerStats || null,
    landlordStats: landlordStats || null,
    orderCounts: orderCounts || null,
    
    // 加载状态
    isLoadingUserInfo,
    isLoadingTenantStats,
    isLoadingLandlordStats,
    isLoadingOrderCounts,
    isLoading,
    
    // 错误状态
    userInfoError: userInfoError as Error | null,
    tenantStatsError: tenantStatsError as Error | null,
    landlordStatsError: landlordStatsError as Error | null,
    orderCountsError: orderCountsError as Error | null,
    hasError,
    
    // 操作方法
    refreshAllData,
    refreshUserInfo,
    refreshTenantStats,
    refreshLandlordStats,
    refreshOrderCounts,
  };
};
