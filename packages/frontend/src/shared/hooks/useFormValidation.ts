/**
 * 统一表单验证Hook
 * 
 * 从PropertyDetailFormScreen中抽象出来的表单验证逻辑
 * 支持实时验证、进度计算、错误管理等功能
 * 可复用于其他表单场景
 */

import { useState, useCallback, useMemo } from 'react';
import { useForm, UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// 验证结果接口
export interface ValidationResult {
  valid: boolean;
  hasWarning: boolean;
  errorMessage?: string;
  warningMessage?: string;
}

// 字段验证规则接口
export interface FieldValidationRule<T = any> {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  min?: number;
  max?: number;
  custom?: (value: T) => ValidationResult;
}

// 进度步骤接口
export interface ProgressStep {
  name: string;
  completed: boolean;
  hint: string;
  fields: string[];
}

// Hook配置接口
export interface FormValidationConfig<T extends FieldValues> {
  schema: z.ZodSchema<T>;
  defaultValues: T;
  fieldRules?: Record<string, FieldValidationRule>;
  progressSteps?: ProgressStep[];
  enableRealTimeValidation?: boolean;
  enableProgressTracking?: boolean;
}

// Hook返回值接口
export interface UseFormValidationReturn<T extends FieldValues> {
  // React Hook Form 实例
  form: UseFormReturn<T>;
  
  // 验证状态
  fieldErrors: Record<string, string>;
  fieldWarnings: Record<string, string>;
  isFormValid: boolean;
  
  // 验证方法
  validateField: (fieldName: keyof T, value: any) => ValidationResult;
  validateAllFields: () => boolean;
  clearFieldError: (fieldName: keyof T) => void;
  clearAllErrors: () => void;
  
  // 进度跟踪
  formProgress: {
    steps: ProgressStep[];
    completed: number;
    total: number;
    percentage: number;
    nextStep?: ProgressStep;
    allCompleted: boolean;
  };
  
  // 发布前验证
  validateBeforeSubmit: (additionalChecks?: () => string[]) => {
    valid: boolean;
    errors: string[];
  };
}

/**
 * 统一表单验证Hook
 */
export function useFormValidation<T extends FieldValues>(
  config: FormValidationConfig<T>
): UseFormValidationReturn<T> {
  
  const {
    schema,
    defaultValues,
    fieldRules = {},
    progressSteps = [],
    enableRealTimeValidation = true,
    enableProgressTracking = true,
  } = config;

  // React Hook Form 初始化
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as any,
    mode: enableRealTimeValidation ? 'onChange' : 'onSubmit',
  });

  const { control, handleSubmit, formState: { errors }, reset, watch, setValue } = form;

  // 本地验证状态
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [fieldWarnings, setFieldWarnings] = useState<Record<string, string>>({});

  // 实时字段验证
  const validateField = useCallback((fieldName: keyof T, value: any): ValidationResult => {
    try {
      const fieldName_str = String(fieldName);
      const rule = fieldRules[fieldName_str];
      let errorMessage = '';
      let warningMessage = '';

      // 基础验证规则
      if (rule) {
        // 必填验证
        if (rule.required && (!value || (typeof value === 'string' && value.trim().length === 0))) {
          warningMessage = `请填写${fieldName_str}`;
        }

        // 长度验证
        if (value && typeof value === 'string') {
          if (rule.minLength && value.length < rule.minLength) {
            if (value.length === 0) {
              warningMessage = `请填写${fieldName_str}`;
            } else {
              warningMessage = `${fieldName_str}至少需要${rule.minLength}个字符，当前${value.length}个字符`;
            }
          }
          
          if (rule.maxLength && value.length > rule.maxLength) {
            errorMessage = `${fieldName_str}最多${rule.maxLength}个字符`;
          }
        }

        // 数值验证
        if (value && typeof value === 'string' && !isNaN(parseFloat(value))) {
          const numValue = parseFloat(value);
          if (rule.min !== undefined && numValue < rule.min) {
            errorMessage = `${fieldName_str}不能小于${rule.min}`;
          }
          if (rule.max !== undefined && numValue > rule.max) {
            errorMessage = `${fieldName_str}不能大于${rule.max}`;
          }
        }

        // 正则验证
        if (rule.pattern && value && typeof value === 'string' && !rule.pattern.test(value)) {
          errorMessage = `${fieldName_str}格式不正确`;
        }

        // 自定义验证
        if (rule.custom) {
          const customResult = rule.custom(value);
          if (!customResult.valid) {
            errorMessage = customResult.errorMessage || `${fieldName_str}验证失败`;
          }
          if (customResult.hasWarning) {
            warningMessage = customResult.warningMessage || '';
          }
        }
      }

      // 更新错误和警告状态
      setFieldErrors(prev => ({
        ...prev,
        [fieldName_str]: errorMessage
      }));

      setFieldWarnings(prev => ({
        ...prev,
        [fieldName_str]: warningMessage
      }));

      return { 
        valid: !errorMessage, 
        hasWarning: !!warningMessage,
        errorMessage,
        warningMessage
      };
    } catch (error) {
      console.error('[FormValidation] 字段验证失败:', error);
      return { valid: false, hasWarning: false };
    }
  }, [fieldRules]);

  // 验证所有字段
  const validateAllFields = useCallback((): boolean => {
    const currentValues = watch();
    let allValid = true;

    Object.keys(fieldRules).forEach(fieldName => {
      const result = validateField(fieldName as keyof T, currentValues[fieldName as keyof T]);
      if (!result.valid) {
        allValid = false;
      }
    });

    return allValid;
  }, [fieldRules, validateField, watch]);

  // 清除字段错误
  const clearFieldError = useCallback((fieldName: keyof T) => {
    const fieldName_str = String(fieldName);
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName_str];
      return newErrors;
    });
    setFieldWarnings(prev => {
      const newWarnings = { ...prev };
      delete newWarnings[fieldName_str];
      return newWarnings;
    });
  }, []);

  // 清除所有错误
  const clearAllErrors = useCallback(() => {
    setFieldErrors({});
    setFieldWarnings({});
  }, []);

  // 表单完成度计算
  const formProgress = useMemo(() => {
    if (!enableProgressTracking || progressSteps.length === 0) {
      return {
        steps: [],
        completed: 0,
        total: 0,
        percentage: 0,
        allCompleted: false,
      };
    }

    const currentValues = watch();
    
    // 计算每个步骤的完成状态
    const updatedSteps = progressSteps.map(step => ({
      ...step,
      completed: step.fields.every(fieldName => {
        const value = currentValues[fieldName as keyof T];
        return value && (typeof value !== 'string' || value.trim().length > 0);
      })
    }));

    const completedCount = updatedSteps.filter(step => step.completed).length;
    const totalCount = updatedSteps.length;
    const percentage = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;
    const nextStep = updatedSteps.find(step => !step.completed);
    const allCompleted = completedCount === totalCount;

    return {
      steps: updatedSteps,
      completed: completedCount,
      total: totalCount,
      percentage,
      nextStep,
      allCompleted,
    };
  }, [watch, progressSteps, enableProgressTracking]);

  // 表单整体有效性
  const isFormValid = useMemo(() => {
    return Object.keys(fieldErrors).length === 0 && Object.keys(errors).length === 0;
  }, [fieldErrors, errors]);

  // 发布前完整性检查
  const validateBeforeSubmit = useCallback((additionalChecks?: () => string[]) => {
    const validationErrors: string[] = [];

    // 基础字段验证
    const currentValues = watch();
    Object.keys(fieldRules).forEach(fieldName => {
      const rule = fieldRules[fieldName];
      const value = currentValues[fieldName as keyof T];
      
      if (rule?.required && (!value || (typeof value === 'string' && value.trim().length === 0))) {
        validationErrors.push(`请填写${fieldName}`);
      }
    });

    // React Hook Form 验证错误
    if (Object.keys(errors).length > 0) {
      Object.values(errors).forEach(error => {
        if (error && typeof error === 'object' && 'message' in error && error.message) {
          validationErrors.push(String(error.message));
        }
      });
    }

    // 额外的自定义检查
    if (additionalChecks) {
      const additionalErrors = additionalChecks();
      validationErrors.push(...additionalErrors);
    }

    return {
      valid: validationErrors.length === 0,
      errors: validationErrors,
    };
  }, [fieldRules, watch, errors]);

  return {
    form,
    fieldErrors,
    fieldWarnings,
    isFormValid,
    validateField,
    validateAllFields,
    clearFieldError,
    clearAllErrors,
    formProgress,
    validateBeforeSubmit,
  };
}

export default useFormValidation;
