/**
 * 房源详情页类型定义
 * 
 * 符合企业级前端五层架构的严格类型定义
 */

import type { PropertyDetailData } from '@property/types';

/**
 * 媒体切换回调类型
 */
export type MediaChangeHandler = (index: number) => void;

/**
 * 标签切换回调类型
 */
export type TabChangeHandler = (tab: '图片' | '视频') => void;

/**
 * 图片点击回调类型
 */
export type ImagePressHandler = (index: number) => void;

/**
 * 通用点击回调类型
 */
export type ClickHandler = () => void;

/**
 * 异步操作回调类型
 */
export type AsyncHandler = () => Promise<void>;

/**
 * 房源详情页Props接口
 */
export interface PropertyDetailScreenProps {
  // 导航参数
  propertyId?: string;
  publishedData?: PublishedPropertyData;
}

/**
 * 发布房源数据接口
 * 遵循企业级数据转换层原则：单一事实来源
 */
export interface PublishedPropertyData {
  id?: string;
  propertyId?: string;
  title?: string;
  description?: string;
  propertyType?: string;
  transactionType?: 'SALE' | 'RENT' | 'TRANSFER';
  area?: string;
  price?: string;
  location?: string;
  address?: string;
  images?: string[];
  video?: string;
  tags?: string[];
  aiTags?: string[];
  contact?: {
    name?: string;
    phone?: string;
  };
  landlordId?: string;
  // 发布时间戳和状态（企业级状态管理）
  publishedAt?: string;
  status?: 'PENDING' | 'APPROVED' | 'REJECTED';
  [key: string]: any;
}

/**
 * PropertyHeader组件Props
 */
export interface PropertyHeaderProps {
  onGoBack: ClickHandler;
  onShare: AsyncHandler;
  onMessageView: AsyncHandler;
}

/**
 * PropertyInfoSection组件Props
 */
export interface PropertyInfoSectionProps {
  propertyData: PropertyDetailData;
  sduiConfig: any;
  isSDUIReady: boolean;
}

/**
 * ReportBar组件Props
 */
export interface ReportBarProps {
  // 当前无需额外props，保留接口为未来扩展
}

/**
 * 房源详情页状态接口
 */
export interface PropertyDetailState {
  // 数据状态
  propertyData: PropertyDetailData | null;
  sduiConfig: any;
  loading: boolean;
  error: string | null;
  
  // UI状态
  activeTab: MediaTab;
  isImageViewerVisible: boolean;
  imageViewerIndex: number;
  isFavorited: boolean;
  activeDescriptionTab: number;
  
  // 显示控制
  showSimilarProperties: boolean;
  showNearbyProperties: boolean;
  showMapSection: boolean;
}

/**
 * 媒体标签类型
 */
export type MediaTab = 'info' | 'photos' | 'videos';

/**
 * 房源操作类型
 */
export interface PropertyActions {
  favorite: AsyncHandler;
  share: AsyncHandler;
  contact: AsyncHandler;
  reserve: AsyncHandler;
}

/**
 * 媒体操作类型
 */
export interface MediaActions {
  onMediaChange: MediaChangeHandler;
  onTabChange: TabChangeHandler;
  onImagePress: ImagePressHandler;
}

/**
 * 导航操作类型
 */
export interface NavigationActions {
  goBack: ClickHandler;
  openMessageView: AsyncHandler;
}

/**
 * Hook返回值类型
 */
export interface UsePropertyDetailReturn {
  // 数据
  propertyId: string;
  currentData: PropertyDetailData | null;
  publishedData?: PublishedPropertyData;
  sduiConfig: any;
  isSDUIReady: boolean;
  
  // 状态
  isLoading: boolean;
  error: string | null;
  isImageViewerVisible: boolean;
  imageViewerIndex: number;
  isFavorited: boolean;
  activeDescriptionTab: number;
  
  // 操作方法
  handleMediaChange: MediaChangeHandler;
  handleTabChange: TabChangeHandler;
  handleImagePress: ImagePressHandler;
  handleFavorite: AsyncHandler;
  handleMessageView: AsyncHandler;
  handleShare: AsyncHandler;
  handleGoBack: ClickHandler;
  handleContact: AsyncHandler;
  handleReserve: AsyncHandler;
  refetch: AsyncHandler;
  
  // 图片查看器控制
  setIsImageViewerVisible: (visible: boolean) => void;
  setImageViewerIndex: (index: number) => void;
}

/**
 * SimplifiedSDUI配置类型
 */
export interface SDUIConfig {
  id: string;
  type: string;
  data: Record<string, any>;
  fallback?: React.ReactNode;
}

/**
 * 错误状态类型
 */
export interface ErrorState {
  hasError: boolean;
  message: string | null;
  retry?: ClickHandler;
}

/**
 * 加载状态类型
 */
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

/**
 * 性能监控类型
 */
export interface PerformanceMetrics {
  renderTime: number;
  dataLoadTime: number;
  userInteractions: number;
}

/**
 * 企业级组件基础Props
 */
export interface EnterpriseComponentProps {
  testID?: string;
  accessibilityLabel?: string;
  accessibilityHint?: string;
}

/**
 * 响应式设计断点
 */
export enum ScreenSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
}

/**
 * 主题类型
 */
export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  error: string;
  onPrimary: string;
  onSecondary: string;
  onBackground: string;
  onSurface: string;
}