/**
 * DemandFormScreen 类型定义
 * @fileoverview 需求发布表单的完整类型定义系统
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🔒 功能保证：100%保留原有DemandFormScreen.tsx中的所有类型定义
 */

// 导入基础类型
import type {
  DemandType,
  PropertyType,
  IndustryType,
  NumberRange,
} from '../../../types/demand.types';

/**
 * 额外联系人接口 - 完整保留原有定义
 */
export interface AdditionalContact {
  phone: string;
  verificationCode: string;
  surname: string;
  gender: 'male' | 'female';
  isVerified: boolean;
}

/**
 * 联系信息接口 - 完整保留原有定义
 */
export interface ContactInfo {
  phone: string; // 手机号（预填用户手机号）
  surname: string; // 姓氏
  gender: 'male' | 'female'; // 性别
  wechat?: string; // 微信号（可选）
  preferredContactTime: string; // 联系时间偏好
  contactMethod: string; // 联系方式偏好
  additionalContacts: AdditionalContact[]; // 额外联系人（最多2个）
}

/**
 * 位置信息接口 - 完整保留原有定义
 */
export interface LocationInfo {
  city: string; // 城市
  districts: string[]; // 区域多选
  landmarks?: string[]; // 地标（可选）
}

/**
 * 需求表单数据接口 - 完整保留原有DemandFormScreen.tsx中的FormData类型
 */
export interface DemandFormData {
  // === 基础信息 ===
  demandType: DemandType; // 需求类型：求租/求购
  propertyType: string[]; // 房源类型多选

  // === 位置信息 ===
  location: LocationInfo; // 位置需求

  // === 面积和预算 ===
  areaRange: NumberRange; // 面积范围
  budgetRange: NumberRange; // 预算范围
  transferFee?: string; // 转让费（可选）

  // === 行业和布局 ===
  industryType: string[]; // 行业类型
  layoutType?: string[]; // 布局类型（可选）

  // === 详细要求（可选） ===
  floorPreference?: string[]; // 楼层偏好
  orientation?: string[]; // 朝向
  decoration?: string; // 装修程度
  leaseTerm?: string; // 租期
  paymentMethod?: string; // 付款方式
  specialRequirements?: string; // 特殊要求

  // === 联系信息 ===
  contactInfo: ContactInfo; // 联系方式

  // === 协议 ===
  agreeToRules: boolean; // 同意发布规则
}

/**
 * 表单验证错误接口
 */
export interface FormErrors {
  [key: string]: string;
}

/**
 * 表单UI状态接口
 */
export interface FormUIState {
  currentStep: number; // 当前步骤
  totalSteps: number; // 总步骤数
  loading: boolean; // 加载状态
  isSubmitting: boolean; // 提交状态
  isSavingDraft: boolean; // 保存草稿状态
  error: string | null; // 错误信息
}

/**
 * 编辑模式状态接口
 */
export interface EditModeState {
  isEditMode: boolean; // 是否编辑模式
  editDemandId: string | null; // 编辑的需求ID
  editDemandDetails: any | null; // 编辑的需求详情
}

/**
 * 标签管理状态接口
 */
export interface TagState {
  selectedFeatureTags: string[]; // 已选择的特色标签
  availableTags: string[]; // 可选标签
  removedTags: string[]; // 被删除的标签
  showTags: boolean; // 是否显示标签区域
  hasInitialTagsGenerated: boolean; // 是否已生成初始标签
  showMaxTagsWarning: boolean; // 是否显示最大标签数警告
}

/**
 * 缓存状态接口
 */
export interface CacheState {
  cacheEnabled: boolean; // 是否启用缓存
  lastSaveTime: number | null; // 最后保存时间
  cacheExpiresAt: number | null; // 缓存过期时间
  draftId: string | null; // 草稿ID
  lastSavedTime: Date | null; // 最后保存时间
}

/**
 * 验证状态接口
 */
export interface ValidationState {
  formErrors: FormErrors; // 表单错误
  touchedFields: Set<string>; // 已触摸的字段
  isValid: boolean; // 表单是否有效
  validationErrors: Record<string, string>; // 验证错误（兼容原有命名）
}

/**
 * 完整的需求表单Store状态接口
 */
export interface DemandFormState {
  // === 表单数据 ===
  formData: DemandFormData;

  // === UI状态 ===
  ui: FormUIState;

  // === 编辑模式 ===
  editMode: EditModeState;

  // === 标签管理 ===
  tags: TagState;

  // === 缓存管理 ===
  cache: CacheState;

  // === 验证状态 ===
  validation: ValidationState;
}

/**
 * Store操作接口
 */
export interface DemandFormActions {
  // === 表单数据操作 ===
  updateFormField: (field: string, value: any) => void;
  updateNestedField: (path: string, value: any) => void;
  resetForm: () => void;
  setFormData: (data: Partial<DemandFormData>) => void;

  // === UI状态操作 ===
  setLoading: (loading: boolean) => void;
  setSubmitting: (submitting: boolean) => void;
  setSavingDraft: (saving: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;

  // === 编辑模式操作 ===
  enterEditMode: (demandId: string, demandDetails: any) => void;
  exitEditMode: () => void;
  setEditDemandDetails: (details: any) => void;

  // === 标签管理操作 ===
  setSelectedFeatureTags: (tags: string[]) => void;
  addFeatureTag: (tag: string) => void;
  removeFeatureTag: (tag: string) => void;
  setAvailableTags: (tags: string[]) => void;
  setShowTags: (show: boolean) => void;
  setHasInitialTagsGenerated: (generated: boolean) => void;
  setShowMaxTagsWarning: (show: boolean) => void;

  // === 缓存操作 ===
  enableCache: () => void;
  disableCache: () => void;
  saveToCache: () => void;
  loadFromCache: () => boolean;
  clearCache: () => void;
  setDraftId: (id: string | null) => void;
  setLastSavedTime: (time: Date | null) => void;

  // === 验证操作 ===
  setFieldError: (field: string, error: string) => void;
  clearFieldError: (field: string) => void;
  setFormErrors: (errors: FormErrors) => void;
  markFieldTouched: (field: string) => void;
  validateForm: () => boolean;
  setValidationErrors: (errors: Record<string, string>) => void; // 兼容原有命名

  // === 复合操作 ===
  reset: () => void;
}

/**
 * Hook选项接口
 */
export interface UseDemandFormOptions {
  autoSave?: boolean;
  autoSaveInterval?: number;
  enableValidation?: boolean;
}

/**
 * 验证规则接口
 */
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

/**
 * 验证规则集合接口
 */
export interface ValidationRules {
  [key: string]: ValidationRule;
}

/**
 * 路由参数类型 - 完整保留原有定义
 */
export interface DemandFormRouteParams {
  demandType: DemandType;
  editMode?: boolean;
  demandId?: string;
  demandData?: any;
}

/**
 * 组件Props基础接口
 */
export interface BaseComponentProps {
  formData: DemandFormData;
  formErrors: FormErrors;
  onFieldChange: (field: string, value: any) => void;
  onNestedFieldChange: (path: string, value: any) => void;
}

/**
 * 表单区块组件Props接口
 */
export interface FormSectionProps extends BaseComponentProps {
  title?: string;
  required?: boolean;
  disabled?: boolean;
}

/**
 * 表单控件组件Props接口
 */
export interface FormControlProps {
  value: any;
  onChange: (value: any) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  placeholder?: string;
}

/**
 * 导出所有类型
 */
export type { DemandType, PropertyType, IndustryType, NumberRange };
