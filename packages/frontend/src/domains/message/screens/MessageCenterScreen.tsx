/**
 * 消息中心主屏幕 - 企业级重构版本
 * 严格遵循五层架构：UI层只负责组件组装和渲染
 */
import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
// import { Ionicons } from '@expo/vector-icons'; // 暂时不需要
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAuth } from '../../../contexts/AuthContext';

// 导入样式和组件
import { messageStyles } from '../components/styles';
import { FeaturePreview } from '../components/FeaturePreview';

// 企业级架构师修复：恢复原始的UnauthorizedMessageCenter组件
// 使用相对路径导入，确保路径正确
const UnauthorizedMessageCenter = React.lazy(() =>
  import('../components/UnauthorizedMessageCenter').then(module => ({
    default: module.default
  }))
);

// 导入Hook层
import { useMessageLogic } from '../hooks/useMessageLogic';
import { useMessageUI } from '../hooks/useMessageUI';

/**
 * 消息中心主屏幕组件
 * 重构后的主屏幕 < 150行，符合企业级架构规范
 */
const MessageCenterScreen: React.FC = () => {
  console.log('[MessageCenterScreen] 🔄 组件开始渲染');

  // 🔧 企业级修复：所有Hook必须在条件渲染之前调用，确保Hook顺序一致
  const { isAuthenticated, user } = useAuth();
  const {
    activeRole,
    handleRoleSwitch,
  } = useMessageLogic();
  const {
    handleSettingsPress,
  } = useMessageUI();
  const insets = useSafeAreaInsets();

  console.log('[MessageCenterScreen] 🔐 认证状态:', { isAuthenticated, hasUser: !!user });
  console.log('[MessageCenterScreen] 🎭 业务逻辑Hook完成:', { activeRole });
  console.log('[MessageCenterScreen] 🎨 UI状态Hook完成');

  // 🔧 修复：条件渲染放在所有Hook调用之后
  if (!isAuthenticated || !user) {
    console.log('[MessageCenterScreen] 🚫 用户未登录，显示未登录组件');
    return (
      <React.Suspense fallback={<View style={messageStyles.container}><Text>加载中...</Text></View>}>
        <UnauthorizedMessageCenter />
      </React.Suspense>
    );
  }

  // 已登录状态：完全恢复原始的消息中心功能
  console.log('[MessageCenterScreen] ✅ 用户已登录，显示完整消息中心');

  return (
    <View style={messageStyles.container}>
      {/* 沉浸式状态栏 */}
      <StatusBar
        barStyle="dark-content"
        backgroundColor="transparent"
        translucent
      />

      {/* 角色切换栏（沉浸式到顶，恢复原始样式） */}
      <View style={[messageStyles.roleSwitchContainerImmersive, { paddingTop: insets.top + 16 }]}>
        <View style={messageStyles.roleSwitchContent}>
          <TouchableOpacity
            style={messageStyles.roleOption}
            onPress={() => handleRoleSwitch('tenant_buyer')}
            activeOpacity={0.6}
          >
            <Text style={[
              messageStyles.roleText,
              activeRole === 'tenant_buyer' && messageStyles.activeRoleText
            ]}>
              租买消息
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={messageStyles.roleOption}
            onPress={() => handleRoleSwitch('landlord')}
            activeOpacity={0.6}
          >
            <Text style={[
              messageStyles.roleText,
              activeRole === 'landlord' && messageStyles.activeRoleText
            ]}>
              业主消息
            </Text>
          </TouchableOpacity>
        </View>

        {/* 右上角设置图标 */}
        <TouchableOpacity style={messageStyles.settingsButton} onPress={handleSettingsPress}>
          <Text style={messageStyles.settingsIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>

      {/* 主要内容区域 */}
      <ScrollView
        style={messageStyles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 欢迎区域 */}
        <View style={messageStyles.welcomeSection}>
          <Text style={messageStyles.welcomeIcon}>📨</Text>
          <Text style={messageStyles.welcomeTitle}>
            {activeRole === 'tenant_buyer' ? '租买消息中心' : '业主消息中心'}
          </Text>
          <Text style={messageStyles.welcomeSubtitle}>
            {activeRole === 'tenant_buyer'
              ? '发布求租求购信息后，消息将在这里显示'
              : '发布房源信息后，消息将在这里显示'
            }
          </Text>
        </View>

        {/* 功能预览区域 - 使用原始组件 */}
        <FeaturePreview activeRole={activeRole} />

        {/* 底部间距（与未登录时保持一致） */}
        <View style={messageStyles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

export default MessageCenterScreen;