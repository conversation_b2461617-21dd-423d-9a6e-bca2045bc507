#!/usr/bin/env node

/**
 * 修复contentFit属性错误
 * ImageBackground组件不支持contentFit属性，需要移除
 */

const fs = require('fs');

const filesToFix = [
  'src/domains/property/components/PropertyCard.tsx',
  'src/domains/property/components/PriceDropSection.tsx',
  'src/domains/property/components/PopularListings.tsx',
  'src/domains/property/components/detail/SimilarProperties.tsx',
  'src/domains/property/components/detail/ImageViewer.tsx',
  'src/domains/property/components/detail/MediaCarousel.tsx',
  'src/domains/property/components/detail/NearbyPropertiesSection.tsx',
  'src/domains/property/components/detail/VideoPlayer.tsx',
  'src/domains/property/components/SpecialOffersSection.tsx'
];

console.log('🔧 开始修复contentFit属性错误...\n');

let totalFixed = 0;

filesToFix.forEach(filePath => {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let changes = 0;

    // 移除ImageBackground中的contentFit属性
    const contentFitPattern = /(\s+)contentFit="[^"]*"\s*\n/g;
    content = content.replace(contentFitPattern, (match, indent) => {
      changes++;
      return '';
    });

    // 移除单行的contentFit属性
    const singleLinePattern = /\s+contentFit="[^"]*"/g;
    content = content.replace(singleLinePattern, () => {
      changes++;
      return '';
    });

    if (changes > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ ${filePath}: 移除了 ${changes} 个contentFit属性`);
      totalFixed++;
    } else {
      console.log(`ℹ️  ${filePath}: 无需修复`);
    }

  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
  }
});

console.log(`\n🎉 修复完成!`);
console.log(`📊 统计信息:`);
console.log(`   - 修复文件: ${totalFixed} 个`);
