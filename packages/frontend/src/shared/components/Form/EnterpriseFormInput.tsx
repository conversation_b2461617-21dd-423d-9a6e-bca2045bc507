/**
 * 企业级表单输入组件
 * 
 * 功能特性：
 * 1. 键盘响应式布局
 * 2. 实时验证与错误提示
 * 3. 统一的样式规范
 * 4. 错误状态的视觉反馈
 * 5. 支持多行文本和普通输入
 * 6. 自动滚动到错误字段
 * 
 * 基于React Native官方指导和企业级开发最佳实践
 */

import React, { useRef, useImperativeHandle, forwardRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TextInputProps,
  Animated,
  Platform,
  LayoutChangeEvent,
} from 'react-native';
import { wp, hp, fp } from '../../utils/responsiveUtils';
import { useKeyboardAwareField } from '../../hooks/useKeyboardAwareForm';
import type { UseKeyboardAwareFormReturn } from '../../hooks/useKeyboardAwareForm';

export interface EnterpriseFormInputProps extends Omit<TextInputProps, 'style'> {
  /** 字段标识符，用于表单验证和键盘管理 */
  fieldName: string;
  
  /** 输入框标签 */
  label: string;
  
  /** 是否必填 */
  required?: boolean;
  
  /** 错误信息 */
  error?: string;
  
  /** 警告信息 */
  warning?: string;
  
  /** 提示信息 */
  hint?: string;
  
  /** 是否多行输入 */
  multiline?: boolean;
  
  /** 多行输入时的行数 */
  numberOfLines?: number;
  
  /** 键盘感知表单Hook实例 */
  formHook?: UseKeyboardAwareFormReturn;
  
  /** 自定义容器样式 */
  containerStyle?: any;
  
  /** 自定义输入框样式 */
  inputStyle?: any;
  
  /** 输入值变化回调 */
  onValueChange?: (value: string) => void;
  
  /** 字段布局变化回调 */
  onLayout?: (event: LayoutChangeEvent) => void;
}

export interface EnterpriseFormInputRef {
  focus: () => void;
  blur: () => void;
  clear: () => void;
  getValue: () => string;
}

export const EnterpriseFormInput = forwardRef<EnterpriseFormInputRef, EnterpriseFormInputProps>(
  ({
    fieldName,
    label,
    required = false,
    error,
    warning,
    hint,
    multiline = false,
    numberOfLines = 1,
    formHook,
    containerStyle,
    inputStyle,
    onValueChange,
    onLayout,
    value,
    ...textInputProps
  }, ref) => {
    
    const inputRef = useRef<TextInput>(null);
    const errorOpacity = useRef(new Animated.Value(0)).current;
    
    // 使用键盘感知字段Hook
    const keyboardField = formHook ? useKeyboardAwareField(fieldName, formHook) : null;
    
    // 暴露组件方法给父组件
    useImperativeHandle(ref, () => ({
      focus: () => inputRef.current?.focus(),
      blur: () => inputRef.current?.blur(),
      clear: () => inputRef.current?.clear(),
      getValue: () => value || '',
    }));
    
    // 错误状态动画
    useEffect(() => {
      if (error) {
        Animated.timing(errorOpacity, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }).start();
      } else {
        Animated.timing(errorOpacity, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start();
      }
    }, [error, errorOpacity]);
    
    // 处理输入框焦点
    const handleFocus = (e: any) => {
      keyboardField?.handleFocus();
      textInputProps.onFocus?.(e);
    };
    
    // 处理输入框失焦
    const handleBlur = (e: any) => {
      keyboardField?.handleBlur();
      textInputProps.onBlur?.(e);
    };
    
    // 处理文本变化
    const handleChangeText = (text: string) => {
      onValueChange?.(text);
      textInputProps.onChangeText?.(text);
    };
    
    // 处理布局变化
    const handleLayout = (event: LayoutChangeEvent) => {
      keyboardField?.handleLayout(event);
      onLayout?.(event);
    };
    
    // 计算动态样式
    const containerDynamicStyle = {
      ...containerStyle,
      // 错误状态时添加红色边框动画
      ...(error && {
        borderColor: '#FF3B30',
        borderWidth: 1,
      })
    };
    
    const inputDynamicStyle = {
      ...styles.textInput,
      ...(multiline && styles.multilineInput),
      ...(error && styles.errorInput),
      ...inputStyle,
    };
    
    return (
      <View 
        style={[styles.container, containerDynamicStyle]}
        onLayout={handleLayout}
        ref={keyboardField?.inputRef}
      >
        {/* 标签区域 */}
        <View style={styles.labelContainer}>
          <Text style={styles.label}>
            {label}
            {required && <Text style={styles.required}> *</Text>}
          </Text>
        </View>
        
        {/* 输入框 */}
        <View style={[
          styles.inputContainer,
          error && styles.inputContainerError,
          warning && styles.inputContainerWarning
        ]}>
          <TextInput
            ref={inputRef}
            style={inputDynamicStyle}
            value={value}
            onChangeText={handleChangeText}
            onFocus={handleFocus}
            onBlur={handleBlur}
            multiline={multiline}
            numberOfLines={numberOfLines}
            textAlignVertical={multiline ? 'top' : 'center'}
            placeholderTextColor="#C7C7CC"
            selectionColor="#007AFF"
            {...textInputProps}
          />
        </View>
        
        {/* 错误信息 */}
        {error && (
          <Animated.View 
            style={[
              styles.messageContainer, 
              { opacity: errorOpacity }
            ]}
          >
            <Text style={styles.errorText}>
              {error}
            </Text>
          </Animated.View>
        )}
        
        {/* 警告信息 */}
        {!error && warning && (
          <View style={styles.messageContainer}>
            <Text style={styles.warningText}>
              {warning}
            </Text>
          </View>
        )}
        
        {/* 提示信息 */}
        {!error && !warning && hint && (
          <View style={styles.messageContainer}>
            <Text style={styles.hintText}>
              {hint}
            </Text>
          </View>
        )}
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(16),
  },
  labelContainer: {
    marginBottom: hp(8),
  },
  label: {
    fontSize: fp(14),
    fontWeight: '500',
    color: '#000000',
    lineHeight: fp(20),
  },
  required: {
    color: '#FF3B30',
    fontWeight: '600',
  },
  inputContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: wp(12),
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    minHeight: hp(48),
    
    // 平台特定样式
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
      },
      android: {
        elevation: 1,
      },
    }),
  },
  inputContainerError: {
    borderColor: '#FF3B30',
    borderWidth: 1.5,
    backgroundColor: '#FFF5F5',
  },
  inputContainerWarning: {
    borderColor: '#FF9500',
    backgroundColor: '#FFFBF5',
  },
  textInput: {
    fontSize: fp(16),
    color: '#000000',
    padding: 0,
    margin: 0,
    minHeight: hp(24),
    
    // 确保文本在容器中正确对齐
    ...Platform.select({
      ios: {
        paddingTop: 0,
        paddingBottom: 0,
      },
      android: {
        paddingVertical: 0,
        textAlignVertical: 'center',
      },
    }),
  },
  multilineInput: {
    minHeight: hp(80),
    textAlignVertical: 'top',
    paddingTop: hp(8),
  },
  errorInput: {
    color: '#000000', // 保持文本颜色不变，只改变边框
  },
  messageContainer: {
    marginTop: hp(6),
    paddingHorizontal: wp(4),
  },
  errorText: {
    fontSize: fp(12),
    color: '#FF3B30',
    fontWeight: '500',
    lineHeight: fp(16),
  },
  warningText: {
    fontSize: fp(12),
    color: '#FF9500',
    fontWeight: '400',
    lineHeight: fp(16),
  },
  hintText: {
    fontSize: fp(11),
    color: '#8E8E93',
    fontWeight: '400',
    lineHeight: fp(15),
    fontStyle: 'italic',
  },
});

EnterpriseFormInput.displayName = 'EnterpriseFormInput';

export default EnterpriseFormInput;