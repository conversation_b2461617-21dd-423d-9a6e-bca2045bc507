#!/usr/bin/env node

/**
 * 测试用户信息修复 - 验证前端修复效果
 */

const axios = require('axios');

// API配置
const API_CONFIG = {
  BASE_URL: 'http://8.134.250.136',
  API_VERSION: '/api/v1',
  TIMEOUT: 8000
};

// 创建API客户端
const apiClient = axios.create({
  baseURL: `${API_CONFIG.BASE_URL}${API_CONFIG.API_VERSION}`,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// 添加请求拦截器
apiClient.interceptors.request.use(
  config => {
    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
      data: config.data,
    });
    return config;
  },
  error => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
apiClient.interceptors.response.use(
  response => {
    console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
      status: response.status,
      data: response.data,
    });
    return response;
  },
  error => {
    console.error('[API Response Error]', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
    });
    return Promise.reject(error);
  }
);

// 测试完整的登录和用户信息验证流程
async function testUserInfoConsistency() {
  console.log('=== 测试用户信息一致性修复 ===');
  console.log('验证前端修复效果...\n');

  const phoneNumber = '13877193340';
  const verificationCode = '035696'; // 使用最新的验证码

  try {
    // 1. 执行登录
    console.log('1. 执行登录...');
    const loginResponse = await apiClient.post('/auth/login/sms', {
      phone_number: phoneNumber,
      verification_code: verificationCode,
      code_type: 'login'
    });
    
    const { access_token, user: loginUser } = loginResponse.data;
    console.log('✅ 登录成功');
    console.log('📱 登录响应中的用户信息:', {
      id: loginUser.id,
      phone_number: loginUser.phone_number,
      nickname: loginUser.nickname
    });

    // 2. 使用token获取用户信息
    console.log('\n2. 获取当前用户信息...');
    const userResponse = await apiClient.get('/users/me', {
      headers: {
        'Authorization': `Bearer ${access_token}`
      }
    });
    
    const currentUser = userResponse.data;
    console.log('🌐 /users/me返回的用户信息:', {
      id: currentUser.id,
      phone_number: currentUser.phone_number,
      nickname: currentUser.nickname
    });

    // 3. 验证数据一致性
    console.log('\n3. 验证数据一致性...');
    const isIdConsistent = loginUser.id === currentUser.id;
    const isPhoneConsistent = loginUser.phone_number === currentUser.phone_number;
    const isNicknameConsistent = loginUser.nickname === currentUser.nickname;
    
    console.log('📊 一致性检查结果:');
    console.log(`用户ID: ${isIdConsistent ? '✅' : '❌'} (${loginUser.id} vs ${currentUser.id})`);
    console.log(`手机号: ${isPhoneConsistent ? '✅' : '❌'} (${loginUser.phone_number} vs ${currentUser.phone_number})`);
    console.log(`昵称: ${isNicknameConsistent ? '✅' : '❌'} (${loginUser.nickname} vs ${currentUser.nickname})`);
    
    const overallConsistent = isIdConsistent && isPhoneConsistent && isNicknameConsistent;
    
    if (overallConsistent) {
      console.log('\n🎉 数据一致性检查通过！后端逻辑完全正确。');
      console.log('💡 如果前端仍显示错误信息，问题在于前端存储或显示逻辑。');
    } else {
      console.log('\n❌ 数据一致性检查失败！');
    }

    // 4. 解析JWT token验证
    console.log('\n4. JWT Token验证...');
    const tokenParts = access_token.split('.');
    const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
    console.log('🔑 JWT Payload:', {
      sub: payload.sub,
      aud: payload.aud,
      exp: new Date(payload.exp * 1000).toISOString()
    });
    
    const isTokenConsistent = payload.sub === loginUser.id && payload.sub === currentUser.id;
    console.log(`JWT一致性: ${isTokenConsistent ? '✅' : '❌'}`);

    // 5. 生成修复建议
    console.log('\n5. 修复建议:');
    if (overallConsistent && isTokenConsistent) {
      console.log('✅ 后端数据完全一致，如果前端显示错误：');
      console.log('   1. 清除前端AsyncStorage中的用户数据');
      console.log('   2. 重新登录让前端获取正确的用户信息');
      console.log('   3. 检查前端用户信息显示组件的数据来源');
      console.log('   4. 验证AuthStore中的用户信息是否正确');
    } else {
      console.log('❌ 后端数据不一致，需要检查后端逻辑');
    }

    return {
      success: true,
      consistent: overallConsistent && isTokenConsistent,
      loginUser,
      currentUser,
      token: access_token
    };

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行测试
testUserInfoConsistency().then((result) => {
  console.log('\n=== 测试完成 ===');
  if (result.success) {
    if (result.consistent) {
      console.log('🎉 所有数据一致性检查通过！');
      console.log('💡 前端问题已定位：需要清除前端存储并重新登录。');
    } else {
      console.log('⚠️ 发现数据不一致问题，需要进一步调查。');
    }
  } else {
    console.log('❌ 测试执行失败，请检查网络连接和API状态。');
  }
}).catch(error => {
  console.error('测试异常:', error);
});
