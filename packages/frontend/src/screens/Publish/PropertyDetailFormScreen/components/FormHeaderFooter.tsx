/**
 * 表单头部和底部组件
 * 
 * 包含导航栏、进度条和提交按钮
 * 从SimplePropertyForm中抽取，减少主组件复杂度
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, StatusBar } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';
import { PropertyFormProgress } from '../../components/PropertyFormProgress';

interface FormHeaderProps {
  onBack: () => void;
  paddingTop: number;
}

interface FormFooterProps {
  onSubmit: () => void;
  isSubmitting: boolean;
  paddingBottom: number;
}

interface FormHeaderFooterProps extends FormHeaderProps, FormFooterProps {
  currentStep?: number;
  totalSteps?: number;
  stepNames?: string[];
}

// 头部组件
export const FormHeader: React.FC<FormHeaderProps> = ({ onBack, paddingTop }) => {
  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      {/* 顶部导航 */}
      <View style={[styles.header, { paddingTop }]}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>发布房源</Text>
        <View style={styles.headerRight} />
      </View>
    </>
  );
};

// 底部组件
export const FormFooter: React.FC<FormFooterProps> = ({ onSubmit, isSubmitting, paddingBottom }) => {
  return (
    <View style={[styles.bottomActions, { paddingBottom }]}>
      <TouchableOpacity
        style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
        onPress={onSubmit}
        disabled={isSubmitting}
      >
        <Text style={styles.submitButtonText}>
          {isSubmitting ? '验证中...' : '发布房源'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

// 完整头部+底部组件
export const FormHeaderFooter: React.FC<FormHeaderFooterProps> = ({
  onBack,
  onSubmit,
  isSubmitting,
  paddingTop,
  paddingBottom,
  currentStep = 1,
  totalSteps = 4,
  stepNames = ['基础信息', '媒体上传', '价格设置', '完成发布']
}) => {
  return {
    Header: (
      <>
        <FormHeader onBack={onBack} paddingTop={paddingTop} />
        
        {/* 进度条 */}
        <PropertyFormProgress
          currentStep={currentStep}
          totalSteps={totalSteps}
          stepNames={stepNames}
        />
      </>
    ),
    Footer: (
      <FormFooter 
        onSubmit={onSubmit}
        isSubmitting={isSubmitting}
        paddingBottom={paddingBottom}
      />
    )
  };
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: wp(16),
    paddingBottom: hp(12),
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  backButton: {
    width: wp(44),
    height: wp(44),
    borderRadius: wp(22),
    backgroundColor: '#F8F9FA',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: fp(18),
    fontWeight: 'bold',
    color: '#333333',
  },
  headerRight: {
    width: wp(44),
  },
  bottomActions: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: wp(16),
    paddingTop: hp(16),
    borderTopWidth: 1,
    borderTopColor: '#F1F3F4',
  },
  submitButton: {
    backgroundColor: '#FF6B35',
    borderRadius: wp(12),
    paddingVertical: hp(16),
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#CED4DA',
  },
  submitButtonText: {
    fontSize: fp(16),
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});

console.log('✅ [FormHeaderFooter] 表单头部底部组件创建完成');