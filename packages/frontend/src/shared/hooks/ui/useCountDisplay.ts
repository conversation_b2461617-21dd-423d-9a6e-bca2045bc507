/**
 * 通用计数显示Hook
 * 提供计数格式化、动画、缓存等功能
 * 遵循企业级架构规范 - Hook层
 */
import { useMemo, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';

// 🏗️ 企业级架构：计数数据源类型
export type CountDataSource =
  | 'api' // API接口数据
  | 'cache' // 缓存数据
  | 'realtime' // 实时数据
  | 'static'; // 静态数据

export interface CountConfig {
  // 数据源配置
  dataSource: CountDataSource;
  apiEndpoint?: string;
  cacheKey?: string;

  // 显示配置
  maxCount?: number;
  showZero?: boolean;
  hideWhenZero?: boolean;

  // 缓存配置
  staleTime?: number;
  cacheTime?: number;

  // 更新配置
  refetchInterval?: number;
  refetchOnFocus?: boolean;
}

export interface UseCountDisplayProps {
  // 基础配置
  initialCount?: number;
  config?: Partial<CountConfig>;

  // 数据获取函数
  fetcher?: () => Promise<number>;

  // 依赖项
  dependencies?: any[];
}

export interface UseCountDisplayReturn {
  // 计数数据
  count: number;
  formattedCount: string;

  // 状态
  isLoading: boolean;
  error: Error | null;

  // 操作方法
  refresh: () => void;
  increment: (amount?: number) => void;
  decrement: (amount?: number) => void;
  reset: () => void;

  // 格式化方法
  formatCount: (num: number) => string;

  // 配置
  config: CountConfig;
}

// 🔧 默认配置
const defaultConfig: CountConfig = {
  dataSource: 'static',
  maxCount: 99,
  showZero: false,
  hideWhenZero: false,
  staleTime: 5 * 60 * 1000, // 5分钟
  cacheTime: 10 * 60 * 1000, // 10分钟
  refetchInterval: 0, // 不自动刷新
  refetchOnFocus: false,
};

export const useCountDisplay = ({
  initialCount = 0,
  config: userConfig = {},
  fetcher,
  dependencies = [],
}: UseCountDisplayProps): UseCountDisplayReturn => {
  // 🔧 合并配置
  const config = useMemo(
    () => ({
      ...defaultConfig,
      ...userConfig,
    }),
    [userConfig]
  );

  // 🔧 API数据查询（仅在需要时启用）
  const {
    data: apiCount,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: [config.cacheKey || 'count-display', ...dependencies],
    queryFn: fetcher || (() => Promise.resolve(initialCount)),
    enabled: config.dataSource === 'api' && !!fetcher,
    staleTime: config.staleTime,
    gcTime: config.cacheTime,
    refetchInterval: config.refetchInterval,
    refetchOnWindowFocus: config.refetchOnFocus,
  });

  // 🔧 计算最终计数值
  const count = useMemo(() => {
    switch (config.dataSource) {
      case 'api':
        return apiCount ?? initialCount;
      case 'cache':
        // TODO: 实现缓存数据获取
        return initialCount;
      case 'realtime':
        // TODO: 实现实时数据获取
        return initialCount;
      case 'static':
      default:
        return initialCount;
    }
  }, [config.dataSource, apiCount, initialCount]);

  // 🔧 格式化计数
  const formatCount = useCallback(
    (num: number): string => {
      if (num === 0 && !config.showZero) return '';
      if (num > config.maxCount!) return `${config.maxCount}+`;
      return num.toString();
    },
    [config.showZero, config.maxCount]
  );

  // 🔧 格式化当前计数
  const formattedCount = useMemo(
    () => formatCount(count),
    [count, formatCount]
  );

  // 🔧 操作方法
  const refresh = useCallback(() => {
    if (config.dataSource === 'api') {
      refetch();
    }
  }, [config.dataSource, refetch]);

  const increment = useCallback((amount = 1) => {
    // TODO: 实现乐观更新
    console.log(`[useCountDisplay] 增加计数: +${amount}`);
  }, []);

  const decrement = useCallback((amount = 1) => {
    // TODO: 实现乐观更新
    console.log(`[useCountDisplay] 减少计数: -${amount}`);
  }, []);

  const reset = useCallback(() => {
    // TODO: 实现重置功能
    console.log('[useCountDisplay] 重置计数');
  }, []);

  return {
    // 计数数据
    count,
    formattedCount,

    // 状态
    isLoading,
    error,

    // 操作方法
    refresh,
    increment,
    decrement,
    reset,

    // 格式化方法
    formatCount,

    // 配置
    config,
  };
};

// 🔧 预设Hook：Tab计数
export const useTabCount = (initialCount: number, dependencies: any[] = []) => {
  return useCountDisplay({
    initialCount,
    config: {
      dataSource: 'static',
      showZero: true,
      hideWhenZero: false,
    },
    dependencies,
  });
};

// 🔧 预设Hook：徽章计数
export const useBadgeCount = (
  fetcher?: () => Promise<number>,
  dependencies: any[] = []
) => {
  return useCountDisplay({
    initialCount: 0,
    config: {
      dataSource: fetcher ? 'api' : 'static',
      showZero: false,
      hideWhenZero: true,
      maxCount: 99,
      staleTime: 30 * 1000, // 30秒
      refetchInterval: 60 * 1000, // 1分钟自动刷新
    },
    fetcher,
    dependencies,
  });
};

// 🔧 预设Hook：统计卡片计数
export const useStatCount = (
  fetcher?: () => Promise<number>,
  dependencies: any[] = []
) => {
  return useCountDisplay({
    initialCount: 0,
    config: {
      dataSource: fetcher ? 'api' : 'static',
      showZero: true,
      hideWhenZero: false,
      maxCount: 9999,
      staleTime: 5 * 60 * 1000, // 5分钟
    },
    fetcher,
    dependencies,
  });
};

export default useCountDisplay;
