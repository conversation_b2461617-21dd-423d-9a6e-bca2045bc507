/**
 * 媒体上传调试脚本
 * 用于测试修复后的上传功能
 */

// 模拟视频资产数据
const testVideoAssets = [
  {
    uri: 'file://test.mp4',
    type: 'video',
    fileName: 'test_short.mp4',
    fileSize: 5 * 1024 * 1024, // 5MB
    width: 1920,
    height: 1080,
    duration: 30 * 1000, // 30秒 (毫秒)
    mimeType: 'video/mp4',
  },
  {
    uri: 'file://test_long.mp4', 
    type: 'video',
    fileName: 'test_long.mp4',
    fileSize: 50 * 1024 * 1024, // 50MB
    width: 1080,
    height: 1920,
    duration: 400 * 1000, // 400秒 = 6分40秒 (应该被拒绝)
    mimeType: 'video/mp4',
  },
  {
    uri: 'file://test_portrait.mp4',
    type: 'video', 
    fileName: 'test_portrait.mp4',
    fileSize: 10 * 1024 * 1024, // 10MB
    width: 720,
    height: 1280,
    duration: 120 * 1000, // 2分钟 (毫秒)
    mimeType: 'video/mp4',
  }
];

// 测试验证逻辑
function testValidation() {
  console.log('=== 测试视频验证逻辑 ===');
  
  testVideoAssets.forEach((asset, index) => {
    console.log(`\n测试视频 ${index + 1}: ${asset.fileName}`);
    console.log(`尺寸: ${asset.width}x${asset.height}`);
    console.log(`时长: ${asset.duration / 1000}秒`);
    console.log(`大小: ${(asset.fileSize / 1024 / 1024).toFixed(1)}MB`);
    
    // 模拟验证逻辑
    const result = validateVideoAsset(asset);
    console.log(`验证结果: ${result.valid ? '✅ 通过' : '❌ 失败'}`);
    if (result.error) {
      console.log(`错误信息: ${result.error}`);
    }
    if (result.info) {
      console.log(`提示信息: ${result.info}`);
    }
  });
}

function validateVideoAsset(asset) {
  // 检查视频时长 (duration是毫秒，需要转换为秒)
  if (asset.duration) {
    const durationInSeconds = asset.duration / 1000;
    if (durationInSeconds > 300) { // 5分钟 = 300秒
      return { 
        valid: false, 
        error: `视频时长${Math.round(durationInSeconds)}秒，不能超过5分钟(300秒)` 
      };
    }
  }

  // 智能识别视频方向并给出友好提示
  if (asset.width && asset.height) {
    const isPortrait = asset.height > asset.width;

    if (isPortrait) {
      return {
        valid: true,
        info: `竖屏视频 ${asset.width}×${asset.height} - 将通过阿里云自动优化显示效果`
      };
    } else {
      return {
        valid: true,
        info: `横屏视频 ${asset.width}×${asset.height} - 推荐的房产展示格式`
      };
    }
  }

  return { valid: true };
}

// 测试API URL配置
function testAPIConfig() {
  console.log('\n=== 测试API配置 ===');
  
  const baseURL = process.env.EXPO_PUBLIC_FRONTEND_API_URL || 'http://8.134.250.136:8081';
  console.log(`API基础URL: ${baseURL}`);
  console.log(`上传URL端点: ${baseURL}/api/v1/media/upload-url`);
  
  // 模拟请求数据
  const requestData = {
    property_id: 1,
    media_type: 'VIDEO',
    file_name: 'test.mp4',
    file_size: 5242880,
  };
  
  console.log('请求数据:', JSON.stringify(requestData, null, 2));
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testValidation, testAPIConfig, validateVideoAsset };
} else {
  // 在浏览器环境中运行
  testValidation();
  testAPIConfig();
}
