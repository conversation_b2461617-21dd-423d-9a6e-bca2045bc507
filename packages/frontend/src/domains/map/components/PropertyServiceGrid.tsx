import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../../shared/theme/designSystem';

interface ServiceItem {
  id: string;
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  onPress?: () => void;
}

interface PropertyServiceGridProps {
  onServicePress?: (serviceId: string) => void;
}

const SERVICES: ServiceItem[] = [
  { id: 'rent', title: '租房网', icon: 'home', color: Colors.primary[500] },
  { id: 'buy', title: '买房网', icon: 'business', color: Colors.semantic.success },
  { id: 'new', title: '租写字楼', icon: 'star', color: Colors.semantic.warning },
  { id: 'find', title: '买写字楼', icon: 'search', color: Colors.semantic.info },
  { id: 'map', title: '地图找房', icon: 'map', color: Colors.primary[500] },
  { id: 'new-property', title: '上新房源', icon: 'add-circle', color: Colors.semantic.success },
  { id: 'single', title: '娱乐单间', icon: 'musical-notes', color: Colors.semantic.warning },
  { id: 'commercial', title: '商业新盘', icon: 'storefront', color: Colors.semantic.info },
  { id: 'ai', title: 'AI匹配', icon: 'flash', color: Colors.primary[500] },
  { id: 'data', title: '求租适配', icon: 'bar-chart', color: Colors.semantic.success },
  { id: 'transfer', title: '极速转租', icon: 'swap-horizontal', color: Colors.semantic.warning },
  { id: 'price', title: '我要估价', icon: 'calculator', color: Colors.semantic.error },
];

export const PropertyServiceGrid: React.FC<PropertyServiceGridProps> = ({
  onServicePress,
}) => {
  const handleServicePress = (serviceId: string) => {
    onServicePress?.(serviceId);
  };

  const renderServiceItem = (service: ServiceItem) => (
    <TouchableOpacity
      key={service.id}
      style={styles.serviceItem}
      onPress={() => handleServicePress(service.id)}
    >
      <View style={[styles.iconContainer, { backgroundColor: service.color }]}>
        <Ionicons 
          name={service.icon} 
          size={24} 
          color={Colors.background.primary} 
        />
      </View>
      <Text style={styles.serviceTitle}>{service.title}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.grid}>
          <View style={styles.row}>
            {SERVICES.slice(0, 4).map(renderServiceItem)}
          </View>
          <View style={styles.row}>
            {SERVICES.slice(4, 8).map(renderServiceItem)}
          </View>
          <View style={styles.row}>
            {SERVICES.slice(8, 12).map(renderServiceItem)}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background.primary,
    paddingVertical: 16,
  },
  scrollContent: {
    paddingHorizontal: 16,
  },
  grid: {
    gap: 16,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  serviceItem: {
    flex: 1,
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  serviceTitle: {
    fontSize: 12,
    color: Colors.neutral[700],
    textAlign: 'center',
    fontWeight: '500',
  },
});
