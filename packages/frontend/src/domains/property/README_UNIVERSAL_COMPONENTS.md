# 🏗️ 房源通用组件使用指南

## 📋 概述

基于你的求租求购通用组件设计模式，我们为房源功能创建了一套完整的通用组件架构。这些组件可以在保持现有功能的基础上，提供更好的代码复用性和一致性。

## 🔧 组件架构

### 1. PropertyListItem (基础通用组件)
**文件位置**: `src/domains/property/components/PropertyListItem.tsx`

**功能特点**:
- 基础房源列表项展示
- 支持状态管理和操作按钮
- 适用于简单的房源列表场景
- 完全配置化，支持自定义布局

**使用示例**:
```tsx
<PropertyListItem
  item={propertyData}
  onPress={handleItemPress}
  onEdit={handleEdit}
  onDelete={handleDelete}
  onStatusChange={handleStatusChange}
  onRefresh={handleRefresh}
/>
```

### 2. EnhancedPropertyListItem (增强组件)
**文件位置**: `src/domains/property/components/EnhancedPropertyListItem.tsx`

**功能特点**:
- 兼容现有MyPropertiesScreen的复杂功能
- 支持草稿模式、统计信息展示
- 保持原有的UI设计和交互逻辑
- 类型安全，支持多种Property接口

**使用示例**:
```tsx
<EnhancedPropertyListItem
  item={property}
  onPress={handleItemPress}
  onEdit={handleDraftEdit}
  onDelete={handleDraftDelete}
  formatDateTime={formatDateTime}
  showActions={true}
  showStats={true}
  customLayout="enhanced"
/>
```

### 3. UniversalMyPropertiesScreen (完整页面组件)
**文件位置**: `src/domains/property/screens/UniversalMyPropertiesScreen.tsx`

**功能特点**:
- 完整的房源列表页面
- 内置筛选、刷新、分页功能
- 支持空状态自定义
- 基于FlashList优化性能

**使用示例**:
```tsx
<UniversalMyPropertiesScreen
  screenTitle="我的房源"
  showFilter={true}
  initialStatus="published"
  emptyStateConfig={{
    title: '暂无房源',
    description: '您还没有发布任何房源',
    actionText: '发布房源',
    onAction: () => navigation.navigate('PropertyForm'),
  }}
/>
```

## 🔄 现有页面集成方案

### 方案1: 渐进式集成 (推荐)

在现有的`MyPropertiesScreen.tsx`中添加组件切换功能:

```tsx
// 在MyPropertiesScreen.tsx中
import { EnhancedPropertyListItem } from '../../property/components/EnhancedPropertyListItem';

const MyPropertiesScreen = () => {
  // 配置开关，可以通过设置或用户偏好控制
  const useEnhancedComponent = true; // 设置为true使用新组件

  const renderPropertyItem = useCallback(({ item }) => {
    if (useEnhancedComponent) {
      return (
        <EnhancedPropertyListItem
          item={item}
          onPress={handleItemPress}
          onEdit={handleDraftEdit}
          onDelete={handleDraftDelete}
          formatDateTime={formatDateTime}
          showActions={true}
          showStats={true}
        />
      );
    }
    
    // 保持原有渲染逻辑
    return (/* 原有的JSX */);
  }, [useEnhancedComponent, /* 其他依赖 */]);
};
```

### 方案2: 完全替换

创建新的房源页面使用通用组件:

```tsx
// 新建 SimpleMyPropertiesScreen.tsx
import { UniversalMyPropertiesScreen } from '../../property/screens/UniversalMyPropertiesScreen';

export const SimpleMyPropertiesScreen = () => {
  return (
    <UniversalMyPropertiesScreen
      screenTitle="我的房源"
      showFilter={true}
      emptyStateConfig={{
        title: '暂无房源',
        description: '您还没有发布任何房源\n点击右上角"+"开始发布房源',
        actionText: '发布房源',
        onAction: () => navigation.navigate('PropertyForm'),
      }}
    />
  );
};
```

## 🎯 配置选项

### PropertyListItem 配置
```tsx
interface PropertyListItemProps {
  item: any;                    // 房源数据
  onPress: (item: any) => void; // 点击事件
  onEdit: (item: any) => void;  // 编辑事件
  onDelete: (item: any) => void; // 删除事件
  onStatusChange?: (item: any, newStatus: PropertyStatus) => void; // 状态变更
  onRefresh?: () => void;       // 刷新回调
  
  // UI配置
  isDraft?: boolean;            // 是否为草稿
  showActions?: boolean;        // 显示操作按钮
  showStats?: boolean;          // 显示统计信息
  customLayout?: 'default' | 'draft' | 'published'; // 布局类型
}
```

### UniversalMyPropertiesScreen 配置
```tsx
interface UniversalMyPropertiesScreenProps {
  initialStatus?: PropertyStatus;     // 初始筛选状态
  screenTitle?: string;              // 页面标题
  showFilter?: boolean;              // 显示筛选栏
  emptyStateConfig?: {               // 空状态配置
    title: string;
    description: string;
    actionText?: string;
    onAction?: () => void;
  };
}
```

## 🔧 API集成

### PropertyAPI 扩展
已为通用组件添加了必要的API方法:

```tsx
// 更新房源状态
PropertyAPI.updatePropertyStatus(propertyId, status)

// 删除房源
PropertyAPI.deleteProperty(propertyId)

// 获取我的房源列表
PropertyAPI.getMyProperties(params)
```

## 📊 类型定义

### 统一的PropertyStatus类型
```tsx
export type PropertyStatus = 
  | 'draft'      // 草稿
  | 'published'  // 已发布
  | 'inactive'   // 已下架
  | 'sold'       // 已售出
  | 'rented'     // 已出租
  | 'expired';   // 已过期
```

## 🧪 测试和演示

### 演示页面
**文件位置**: `src/domains/property/screens/PropertyListDemo.tsx`

这个演示页面展示了:
- 基础组件 vs 增强组件的对比
- 不同配置选项的效果
- 完整页面组件的使用方式

### 使用演示页面
```tsx
// 在导航中添加演示页面
navigation.navigate('PropertyListDemo');
```

## 🚀 迁移建议

### 阶段1: 测试验证
1. 使用演示页面验证组件功能
2. 在开发环境中启用组件切换
3. 对比新旧组件的表现

### 阶段2: 渐进迁移
1. 在现有页面中添加组件切换开关
2. 逐步启用新组件功能
3. 收集用户反馈

### 阶段3: 完全迁移
1. 移除旧的渲染逻辑
2. 统一使用通用组件
3. 清理冗余代码

## 💡 最佳实践

1. **保持向后兼容**: 新组件完全兼容现有数据结构
2. **配置化设计**: 通过props控制组件行为和外观
3. **类型安全**: 使用TypeScript确保类型安全
4. **性能优化**: 使用FlashList和React.memo优化性能
5. **错误处理**: 完善的错误处理和用户反馈

## 🔍 注意事项

1. **数据兼容性**: 确保API返回的数据结构与组件期望的格式匹配
2. **样式一致性**: 新组件保持与现有设计系统的一致性
3. **功能完整性**: 所有现有功能都在新组件中得到保留
4. **测试覆盖**: 对新组件进行充分的测试

通过这套通用组件架构，你可以在保持现有功能完整性的同时，获得更好的代码复用性和维护性。
