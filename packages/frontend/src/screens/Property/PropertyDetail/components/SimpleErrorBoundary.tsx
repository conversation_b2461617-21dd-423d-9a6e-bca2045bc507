/**
 * 房源详情页简化错误边界组件
 * 
 * 核心功能：
 * 1. 基础错误捕获和用户友好提示
 * 2. 简单的重试机制
 * 3. 开发环境错误信息展示
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { hp, wp, fp } from '@shared/utils/responsive';

/**
 * 简化的错误边界属性接口
 */
interface SimpleErrorBoundaryProps {
  children: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  maxRetryCount?: number;
}

/**
 * 简化的错误状态接口
 */
interface SimpleErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  retryCount: number;
  isRetrying: boolean;
}

/**
 * 简化的错误边界组件
 */
class SimpleErrorBoundary extends Component<
  SimpleErrorBoundaryProps,
  SimpleErrorBoundaryState
> {
  private retryTimeoutId: NodeJS.Timeout | null = null;
  
  constructor(props: SimpleErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0,
      isRetrying: false,
    };
  }
  
  static getDerivedStateFromError(error: Error): Partial<SimpleErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 调用外部错误处理函数
    this.props.onError?.(error, errorInfo);
    
    // 详细错误日志收集（简化实现）
    const errorDetails = {
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: Platform.OS,
      retryCount: this.state.retryCount,
    };
    
    // 开发环境详细日志
    if (__DEV__) {
      console.group('🚨 [PropertyDetail] Error Boundary Triggered');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Error Details:', errorDetails);
      console.groupEnd();
    } else {
      // 生产环境简化日志（可以发送到日志服务）
      console.error('[PropertyDetail] Error:', {
        message: error.message,
        timestamp: errorDetails.timestamp,
      });
      
      // 这里可以集成错误上报服务，如 Sentry, Bugsnag 等
      // ErrorReporting.captureException(error, errorDetails);
    }
  }
  
  /**
   * 重试逻辑
   */
  private handleRetry = () => {
    const { maxRetryCount = 2 } = this.props;
    
    if (this.state.retryCount >= maxRetryCount) {
      return;
    }
    
    this.setState({ isRetrying: true });
    
    this.retryTimeoutId = setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        retryCount: this.state.retryCount + 1,
        isRetrying: false,
      });
    }, 1000);
  };
  
  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }
  
  render() {
    const { hasError, error, retryCount, isRetrying } = this.state;
    const { children, maxRetryCount = 2 } = this.props;
    
    if (hasError) {
      return (
        <View style={styles.errorContainer}>
          {/* 错误图标和标题 */}
          <View style={styles.errorHeader}>
            <Ionicons name="warning-outline" size={wp(50)} color="#EF4444" />
            <Text style={styles.errorTitle}>页面加载出错</Text>
            <Text style={styles.errorSubtitle}>
              房源详情页面遇到了问题
            </Text>
          </View>
          
          {/* 开发环境显示错误信息 */}
          {__DEV__ && error && (
            <View style={styles.devErrorContainer}>
              <Text style={styles.devErrorText}>
                {error.name}: {error.message}
              </Text>
            </View>
          )}
          
          {/* 重试按钮 */}
          {retryCount < maxRetryCount && (
            <TouchableOpacity
              style={[styles.retryButton, isRetrying && styles.retryButtonDisabled]}
              onPress={this.handleRetry}
              disabled={isRetrying}
              activeOpacity={0.8}
            >
              <Ionicons 
                name="refresh-outline" 
                size={wp(18)} 
                color="#FFFFFF" 
                style={styles.buttonIcon}
              />
              <Text style={styles.retryButtonText}>
                {isRetrying ? '重试中...' : '重试'}
              </Text>
            </TouchableOpacity>
          )}
          
          {/* 建议信息 */}
          <View style={styles.suggestionContainer}>
            <Text style={styles.suggestionText}>• 检查网络连接</Text>
            <Text style={styles.suggestionText}>• 返回上一页面</Text>
          </View>
        </View>
      );
    }
    
    return children;
  }
}

/**
 * 简化的样式定义
 */
const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: wp(20),
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  errorHeader: {
    alignItems: 'center',
    marginBottom: hp(30),
  },
  
  errorTitle: {
    fontSize: fp(20),
    fontWeight: 'bold',
    color: '#1F2937',
    marginTop: hp(12),
    textAlign: 'center',
  },
  
  errorSubtitle: {
    fontSize: fp(14),
    color: '#6B7280',
    marginTop: hp(8),
    textAlign: 'center',
  },
  
  devErrorContainer: {
    backgroundColor: '#FEF2F2',
    padding: wp(12),
    borderRadius: 8,
    marginBottom: hp(20),
    width: '100%',
  },
  
  devErrorText: {
    fontSize: fp(12),
    color: '#DC2626',
    textAlign: 'center',
  },
  
  retryButton: {
    backgroundColor: '#EF4444',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(12),
    paddingHorizontal: wp(24),
    borderRadius: 8,
    marginBottom: hp(20),
  },
  
  retryButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  
  retryButtonText: {
    fontSize: fp(14),
    fontWeight: '600',
    color: '#FFFFFF',
  },
  
  buttonIcon: {
    marginRight: wp(6),
  },
  
  suggestionContainer: {
    alignItems: 'center',
  },
  
  suggestionText: {
    fontSize: fp(13),
    color: '#6B7280',
    marginBottom: hp(4),
  },
});

export default SimpleErrorBoundary;