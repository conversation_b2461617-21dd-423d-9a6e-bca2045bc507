/**
 * 企业级统一数据转换层
 * 
 * 架构设计：
 * - 共享核心转换引擎
 * - 模块化业务转换器
 * - 统一接口和错误处理
 * - 类型安全和运行时验证
 * 
 * @fileoverview 数据转换层统一入口
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */

// ==================== 核心转换引擎 ====================
export { BaseTransformer } from './core/BaseTransformer';
export { TransformRegistry } from './core/TransformRegistry';
export { ValidationEngine, defaultValidationEngine } from './core/ValidationEngine';

// ==================== 业务转换器 ====================
export { DemandTransformer } from './transformers/DemandTransformer';
export { PropertyTransformer } from './transformers/PropertyTransformer';
export { UserTransformer } from './transformers/UserTransformer';
export { CommonTransformer } from './transformers/CommonTransformer';
export { MediaTransformer } from './transformers/MediaTransformer';
export { InquiryTransformer } from './transformers/InquiryTransformer';
export { MapTransformer } from './transformers/MapTransformer';

// ==================== 服务适配器 ====================
export * from './adapters';

// ==================== 工具模块 ====================
export * from './utils';

// ==================== 类型定义 ====================
export type * from './types/TransformTypes';
export type { ValidationResult, ValidationContext, ValidationStats, ValidationHealthCheck } from './types/ValidationTypes';

// ==================== 便捷导出 ====================
export { registerTransformer } from './core/TransformRegistry';

// ==================== 统一转换服务 ====================
import { TransformRegistry } from './core/TransformRegistry';
import { DemandTransformer } from './transformers/DemandTransformer';
import { PropertyTransformer } from './transformers/PropertyTransformer';
import { UserTransformer } from './transformers/UserTransformer';
import { CommonTransformer } from './transformers/CommonTransformer';
import { MediaTransformer } from './transformers/MediaTransformer';
import { InquiryTransformer } from './transformers/InquiryTransformer';
import { MapTransformer } from './transformers/MapTransformer';

/**
 * 企业级数据转换服务
 * 提供统一的数据转换接口
 */
export class DataTransformService {
  private static instance: DataTransformService;
  private registry: TransformRegistry;

  private constructor() {
    this.registry = new TransformRegistry();
    this.registerDefaultTransformers();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): DataTransformService {
    if (!DataTransformService.instance) {
      DataTransformService.instance = new DataTransformService();
    }
    return DataTransformService.instance;
  }

  /**
   * 注册默认转换器
   */
  private registerDefaultTransformers() {
    this.registry.register('demand', new DemandTransformer(), '需求数据转换器');
    this.registry.register('property', new PropertyTransformer(), '房源数据转换器');
    this.registry.register('user', new UserTransformer(), '用户数据转换器');
    this.registry.register('common', new CommonTransformer(), '通用数据转换器');
    this.registry.register('media', new MediaTransformer(), '媒体文件转换器');
    this.registry.register('inquiry', new InquiryTransformer(), '房源咨询转换器');
    this.registry.register('map', new MapTransformer(), '地图数据转换器');
  }

  /**
   * 获取转换器
   */
  getTransformer<T>(name: string): T | null {
    const transformer = this.registry.get(name);
    return transformer as T | null;
  }

  /**
   * 注册新转换器
   */
  registerTransformer(name: string, transformer: any, description?: string) {
    this.registry.register(name, transformer, description);
  }
}

// ==================== 全局实例 ====================
export const dataTransformService = DataTransformService.getInstance();

// ==================== 便捷访问器 ====================
export const Transformers = {
  demand: dataTransformService.getTransformer<DemandTransformer>('demand')!,
  property: dataTransformService.getTransformer<PropertyTransformer>('property')!,
  user: dataTransformService.getTransformer<UserTransformer>('user')!,
  common: dataTransformService.getTransformer<CommonTransformer>('common')!,
  media: dataTransformService.getTransformer<MediaTransformer>('media')!,
  inquiry: dataTransformService.getTransformer<InquiryTransformer>('inquiry')!,
  map: dataTransformService.getTransformer<MapTransformer>('map')! // 修复：添加地图转换器
};

// ==================== 向后兼容 ====================
// 保持与现有代码的兼容性
// 这些类型已经迁移到统一转换层，不再需要从旧服务导入
export const PropertyTypeTransformer = {
  uiToApi: (uiType: any) => Transformers.property.toAPI(uiType, { context: 'propertyType' }),
  apiToUi: (apiType: any) => Transformers.property.fromAPI(apiType, { context: 'propertyType' }),
  isValidUI: (type: string) => {
    try {
      const result = Transformers.property.toAPI(type, { context: 'propertyType' });
      return result.success;
    } catch {
      return false;
    }
  },
  isValidAPI: (type: string) => {
    try {
      const result = Transformers.property.fromAPI(type, { context: 'propertyType' });
      return result.success;
    } catch {
      return false;
    }
  }
};

export const SortFieldTransformer = {
  uiToApi: (uiSort: string) => Transformers.property.toAPI(uiSort, { context: 'sortField' })
};

export const StoreParamsTransformer = {
  toApi: (storeParams: any) => Transformers.property.toAPI(storeParams, { context: 'storeParams' })
};

export const ResponseTransformer = {
  propertyList: (apiResponse: any) => Transformers.property.fromAPI(apiResponse, { context: 'propertyList' })
};

// ==================== 新增：媒体处理抽象层 ====================
// 核心抽象层组件
export { UnifiedMediaProcessor } from './processors/UnifiedMediaProcessor';
export { ImageProcessor } from './processors/ImageProcessor';
export { VideoProcessor } from './processors/VideoProcessor';
export { MemoryMetadataCache } from './cache/MemoryMetadataCache';

// 抽象层类型定义
export type {
  IMediaProcessor,
  IImageProcessor,
  IVideoProcessor,
  IMetadataCache,
  MediaUploadResult,
  MediaStatus,
  MediaSize,
  VideoQuality,
  MediaFileType,
  MediaMetadata,
  VideoInfo,
  VideoUploadResult,
  TranscodeTemplate,
  WatermarkConfig
} from './types/MediaTypes';

// 媒体分类类型（从MediaTransformer导出）
export {
  SecurityLevel,
  AccessLevel,
  MediaType,
  MediaCategory
} from './transformers/MediaTransformer';

// ==================== 便捷的媒体操作接口 ====================
import { MediaServiceAdapter } from './adapters/MediaServiceAdapter';

/**
 * 默认媒体服务适配器实例
 */
export const mediaServiceAdapter = new MediaServiceAdapter();

/**
 * 便捷的媒体操作函数
 */
export const mediaOperations = {
  /**
   * 上传媒体文件
   */
  async uploadFile(
    file: { uri: string; name: string; type: string; size: number },
    businessType: any // MediaBusinessType
  ) {
    return await mediaServiceAdapter.uploadFile(file, { businessType });
  },

  /**
   * 获取媒体URL
   */
  async getMediaUrl(mediaId: string, size: any = 'medium') {
    return await mediaServiceAdapter.getMediaUrl(mediaId, size);
  },

  /**
   * 获取媒体状态
   */
  async getMediaStatus(mediaId: string) {
    return await mediaServiceAdapter.getMediaStatus(mediaId);
  },

  /**
   * 删除媒体文件
   */
  async deleteMedia(mediaId: string) {
    return await mediaServiceAdapter.deleteFile(mediaId);
  }
};

/**
 * 使用指南：
 *
 * 1. 基础使用：
 *    import { Transformers } from '@shared/services/dataTransform';
 *    const result = Transformers.property.toAPI(data);
 *
 * 2. 高级使用：
 *    import { dataTransformService } from '@shared/services/dataTransform';
 *    const customTransformer = dataTransformService.getTransformer('custom');
 *
 * 3. 扩展使用：
 *    import { BaseTransformer, dataTransformService } from '@shared/services/dataTransform';
 *    class MyTransformer extends BaseTransformer { ... }
 *    dataTransformService.registerTransformer('my', new MyTransformer());
 *
 * 4. 媒体处理使用：
 *    import { mediaOperations, MediaBusinessType } from '@shared/services/dataTransform';
 *    const result = await mediaOperations.uploadFile(file, MediaBusinessType.PROPERTY_IMAGE);
 */
