/**
 * 用户中心Store选择器Hook - 企业级重新设计
 * 架构师重新设计：遵循Zustand最佳实践，最小化选择器使用
 * 核心原则：只为性能关键点提供选择器，避免过度抽象
 * 目标：简化到30行，保持功能完整性
 */
import { useProfileStore } from './ProfileStore';

// 企业级架构师设计：只保留真正需要的选择器
// 原则1：最小化选择器数量
// 原则2：避免返回新对象
// 原则3：只在性能关键点使用

// 企业级架构师重新设计：只保留真正必要的选择器
// 根据Zustand最佳实践，大部分情况下应该直接使用 useProfileStore

/**
 * UI状态选择器 - 仅用于模态框状态（性能关键点）
 * 这是唯一真正需要的选择器，因为模态框状态变化频繁
 */
export const useProfileUIState = () => {
  return useProfileStore((state) => ({
    showLoginModal: state.showLoginModal,
    showLogoutModal: state.showLogoutModal,
    refreshing: state.refreshing,
  }));
};

// 企业级架构师建议：
// 1. 其他状态直接使用 useProfileStore((state) => state.specificField)
// 2. 避免创建返回对象的选择器，除非真正需要性能优化
// 3. 保持代码简单，避免过度抽象
// 4. 功能完整性：所有原有功能通过直接访问Store实现
