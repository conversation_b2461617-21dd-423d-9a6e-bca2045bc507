import asyncio
from app.core.db import get_async_session
from sqlalchemy import text

async def check_and_update_user():
    async for session in get_async_session():
        # 查看18377100296用户的信息
        result = await session.execute(
            text('SELECT id, email, phone_number, "current_role", is_landlord_certified FROM users WHERE phone_number = :phone'),
            {'phone': '18377100296'}
        )
        
        user = result.fetchone()
        if user:
            print(f'现有用户: ID={user.id}, Email={user.email}, Phone={user.phone_number}, Role={user.current_role}, 房东认证={user.is_landlord_certified}')
            
            # 如果不是房东，更新为房东
            if user.current_role != 'LANDLORD' or not user.is_landlord_certified:
                update_result = await session.execute(
                    text('''
                        UPDATE users 
                        SET email = :email,
                            "current_role" = :role,
                            is_landlord_certified = true,
                            hashed_password = :password
                        WHERE phone_number = :phone
                        RETURNING id, email, phone_number, "current_role"
                    '''),
                    {
                        'email': '<EMAIL>',
                        'role': 'LANDLORD', 
                        'password': '$2b$12$XVXjV9/8nKb7k6o4JzF4D.z3z.Bv2ZZ4mKQCJ5nF2vr2.XwfnJ1qm',
                        'phone': '18377100296'
                    }
                )
                updated_user = update_result.fetchone()
                await session.commit()
                print(f'✅ 用户已更新为房东: {updated_user.email} ({updated_user.current_role})')
            else:
                print('✅ 用户已经是房东，无需更新')
        else:
            print('❌ 未找到用户')
        break

asyncio.run(check_and_update_user())