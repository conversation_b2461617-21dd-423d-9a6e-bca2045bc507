#!/bin/bash
# 容器架构标准化脚本
# 用途：清理非标准容器，启动标准化开发环境，验证配置正确性

set -e

echo "🚀 容器架构标准化开始..."
echo "======================================"

# 第一步：清理非标准容器
echo "🧹 第一步：清理非标准容器..."
# 强制移除临时容器
docker rm -f backend-mapped backend-test 2>/dev/null || true
echo "✅ 非标准容器清理完成"

# 第二步：标准化启动
echo "🏗️ 第二步：标准化启动..."
# 确保在正确的目录
cd /data/my-real-estate-app

# 停止现有容器（如果有）
docker compose down 2>/dev/null || docker-compose down 2>/dev/null || true

# 使用标准配置启动
echo "启动标准化容器环境..."
docker compose -f docker-compose.yml -f docker-compose.override.yml up -d || \
docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d

echo "✅ 标准化环境启动完成"

# 第三步：验证标准化结果
echo "✅ 第三步：验证标准化结果..."
echo "当前容器状态："
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 第四步：健康检查
echo "🎯 第四步：健康检查..."
echo "等待服务启动..."
sleep 15

# 检查后端健康状态
echo "检查后端服务健康状态..."
if curl -f http://localhost:8082/health 2>/dev/null; then
    echo "✅ 后端服务健康正常"
else
    echo "⚠️ 后端服务需要检查（可能还在启动中）"
    echo "建议等待1-2分钟后手动检查：curl http://localhost:8082/health"
fi

# 检查数据库和Redis
echo "检查数据库和Redis状态..."
if docker exec my-real-estate-app-db-1 pg_isready -U postgres 2>/dev/null; then
    echo "✅ 数据库连接正常"
else
    echo "⚠️ 数据库需要检查"
fi

if docker exec my-real-estate-app-redis-1 redis-cli ping 2>/dev/null | grep -q "PONG"; then
    echo "✅ Redis连接正常"
else
    echo "⚠️ Redis需要检查"
fi

echo "======================================"
echo "✅ 标准化完成！"
echo ""
echo "📋 快速命令："
echo "  查看日志: docker-compose logs -f backend"
echo "  检查健康: curl http://localhost:8082/health"
echo "  容器状态: docker ps"
echo "  停止环境: docker-compose down"
echo ""
echo "🎯 开发环境已就绪：http://localhost:8082"