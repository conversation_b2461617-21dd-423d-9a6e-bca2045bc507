"""merge_clean_validation_and_media_uuid

Revision ID: fc0032c090c6
Revises: 20250722_clean_validation, fix_property_media_uuid
Create Date: 2025-07-22 17:06:18.783310

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'fc0032c090c6'
down_revision = ('20250722_clean_validation', 'fix_property_media_uuid')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
