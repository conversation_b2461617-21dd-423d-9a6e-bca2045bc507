/**
 * FormHeader 组件
 * @fileoverview 需求发布表单的头部组件
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🔒 功能保证：100%保留原有DemandFormScreen.tsx中的头部UI和交互
 */

import React from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { wp, hp, fp } from '../../../../../../shared/utils/responsiveUtils';

// 样式
import { styles } from './formHeader.styles';

interface FormHeaderProps {
  title: string;
  subtitle: string;
  onBack: () => void;
  loading?: boolean;
  completeness?: number;
}

/**
 * 表单头部组件 - 完整保留原有头部设计
 */
export const FormHeader: React.FC<FormHeaderProps> = React.memo(
  ({ title, subtitle, onBack, loading = false, completeness = 0 }) => {
    return (
      <>
        {/* 🔒 保留原有的顶部导航带标题 */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={onBack}
            disabled={loading}
          >
            <Ionicons name="chevron-back" size={wp(24)} color="#FFFFFF" />
          </TouchableOpacity>

          <Text style={styles.headerTitle}>{title}</Text>

          <View style={styles.headerRight}>
            {loading && <ActivityIndicator size="small" color="#FFFFFF" />}
          </View>
        </View>

        {/* 🔒 保留原有的页面副标题 */}
        <View style={styles.titleSection}>
          <Text style={styles.subtitle}>{subtitle}</Text>

          {/* 完成度指示器（可选） */}
          {completeness > 0 && (
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${Math.round(completeness * 100)}%` },
                  ]}
                />
              </View>
              <Text style={styles.progressText}>
                {Math.round(completeness * 100)}% 完成
              </Text>
            </View>
          )}
        </View>
      </>
    );
  }
);

FormHeader.displayName = 'FormHeader';
