/**
 * 🧪 Phase 2.3验证：地址搜索功能深度集成验证
 * 验证地址搜索、Store集成、Hook业务逻辑、API调用的完整链路
 */

const fs = require('fs');
const path = require('path');

console.log(
  '🚀 [AddressIntegrationTest] 开始Phase 2.3地址搜索功能深度集成验证...'
);

// 测试文件路径
const BASE_PATH =
  '/data/my-real-estate-app/packages/frontend/src/domains/property/components/detail';
const HOOK_FILE = path.join(BASE_PATH, 'hooks/usePropertyNavigation.ts');
const STORE_FILE = path.join(BASE_PATH, 'stores/MapNavigationStore.ts');
const MAIN_COMPONENT = path.join(
  BASE_PATH,
  'PropertyNavigationMapRefactored.tsx'
);
const TYPE_FILE = path.join(BASE_PATH, 'types/navigation.types.ts');

// API服务路径
const API_SERVICE_PATH =
  '/data/my-real-estate-app/packages/frontend/src/domains/property/services/amapRouteService.ts';

// 统一转换层路径
const TRANSFORMER_PATH =
  '/data/my-real-estate-app/packages/frontend/src/shared/services/dataTransform/transformers/MapTransformer.ts';

// 导航屏幕路径
const NAVIGATION_SCREEN_PATH =
  '/data/my-real-estate-app/packages/frontend/src/screens/Property/PropertyNavigationScreen.tsx';

const testResults = {
  addressSearchIntegration: [],
  storeIntegration: [],
  hookBusinessLogic: [],
  apiIntegration: [],
  transformerIntegration: [],
  fullChainIntegration: [],
};

// 1. 地址搜索集成验证
console.log('\n📍 [Test 1] 地址搜索集成验证...');

if (fs.existsSync(HOOK_FILE)) {
  const hookContent = fs.readFileSync(HOOK_FILE, 'utf8');

  const addressSearchChecks = [
    {
      name: 'handleAddressSearch方法存在',
      pattern: /const handleAddressSearch = useCallback/,
      required: true,
    },
    {
      name: 'navigation.navigate调用正确',
      pattern: /navigation\.navigate\('AddressSearchScreen'/,
      required: true,
    },
    {
      name: 'AddressReturnKey类型使用',
      pattern: /AddressReturnKey\.(START_LOCATION|END_LOCATION)/,
      required: true,
    },
    {
      name: '地址搜索回调处理',
      pattern:
        /useFocusEffect[\s\S]*params\.selectedAddress.*params\.returnKey/,
      required: true,
    },
    {
      name: '统一转换层集成',
      pattern: /Transformers\.map\?\.(fromAPI|transformFromAPI)/,
      required: true,
    },
    {
      name: '类型守卫验证',
      pattern: /isValidAddressData\(addressData\)/,
      required: true,
    },
    {
      name: 'Store状态更新',
      pattern: /setStartLocationText|setCustomStartLocation/,
      required: true,
    },
    {
      name: '自动路线重计算',
      pattern: /自动路线重计算[\s\S]*setTimeout[\s\S]*calculateRoute/,
      required: true,
    },
  ];

  addressSearchChecks.forEach(({ name, pattern, required }) => {
    const found = pattern.test(hookContent);
    testResults.addressSearchIntegration.push({
      name,
      status: found ? 'PASS' : 'FAIL',
      required,
    });

    console.log(
      `${found ? '✅' : '❌'} ${name}: ${found ? 'INTEGRATED' : 'MISSING'}`
    );
  });
} else {
  console.log('❌ usePropertyNavigation Hook: FILE_NOT_FOUND');
}

// 2. Store集成验证
console.log('\n🏪 [Test 2] Store集成验证...');

if (fs.existsSync(STORE_FILE)) {
  const storeContent = fs.readFileSync(STORE_FILE, 'utf8');

  const storeIntegrationChecks = [
    {
      name: 'Zustand三中间件配置',
      pattern: /devtools.*persist.*subscribeWithSelector/,
      required: true,
    },
    {
      name: '地址相关状态管理',
      pattern:
        /startLocationText|endLocationText|customStartLocation|customEndLocation/,
      required: true,
    },
    {
      name: 'swapLocations复合操作',
      pattern: /swapLocations.*=>.*set/,
      required: true,
    },
    {
      name: '选择器Hook导出',
      pattern: /export const useStartLocationText/,
      required: true,
    },
    {
      name: '计算属性Hook',
      pattern: /useCurrentStartLocation|useCurrentEndLocation/,
      required: true,
    },
    {
      name: '选择性持久化',
      pattern: /不持久化UI状态.*mapReady.*isLocationSwapped/,
      required: true,
    },
    {
      name: 'DevTools命名',
      pattern: /name.*MapNavigationStore/,
      required: true,
    },
  ];

  storeIntegrationChecks.forEach(({ name, pattern, required }) => {
    const found = pattern.test(storeContent);
    testResults.storeIntegration.push({
      name,
      status: found ? 'PASS' : 'FAIL',
      required,
    });

    console.log(
      `${found ? '✅' : '❌'} ${name}: ${found ? 'INTEGRATED' : 'MISSING'}`
    );
  });
} else {
  console.log('❌ MapNavigationStore: FILE_NOT_FOUND');
}

// 3. Hook业务逻辑验证
console.log('\n🔧 [Test 3] Hook业务逻辑验证...');

if (fs.existsSync(HOOK_FILE)) {
  const hookContent = fs.readFileSync(HOOK_FILE, 'utf8');

  const businessLogicChecks = [
    {
      name: 'Store选择器集成',
      pattern: /const nativeLocation = useNativeLocation/,
      required: true,
    },
    {
      name: 'Store操作方法集成',
      pattern: /useMapNavigationActions/,
      required: true,
    },
    {
      name: 'useState完全移除',
      pattern: /useState/,
      required: false,
      expectNotFound: true,
    },
    {
      name: '初始化逻辑优化',
      pattern: /企业级架构[\s\S]*setInitialized/,
      required: true,
    },
    {
      name: '路线计算业务逻辑',
      pattern: /calculateRoute.*useCallback/,
      required: true,
    },
    {
      name: '错误处理完善',
      pattern: /updateRouteError/,
      required: true,
    },
    {
      name: '向后兼容接口',
      pattern: /UsePropertyNavigationReturn/,
      required: true,
    },
  ];

  businessLogicChecks.forEach(({ name, pattern, required, expectNotFound }) => {
    const found = pattern.test(hookContent);
    const passed = expectNotFound ? !found : found;

    testResults.hookBusinessLogic.push({
      name,
      status: passed ? 'PASS' : 'FAIL',
      required,
      expectNotFound,
    });

    if (expectNotFound) {
      console.log(
        `${passed ? '✅' : '❌'} ${name}: ${passed ? 'REMOVED' : 'STILL_EXISTS'}`
      );
    } else {
      console.log(
        `${passed ? '✅' : '❌'} ${name}: ${passed ? 'IMPLEMENTED' : 'MISSING'}`
      );
    }
  });
} else {
  console.log('❌ usePropertyNavigation Hook: FILE_NOT_FOUND');
}

// 4. API集成验证
console.log('\n🌐 [Test 4] API集成验证...');

if (fs.existsSync(API_SERVICE_PATH)) {
  const apiContent = fs.readFileSync(API_SERVICE_PATH, 'utf8');

  const apiIntegrationChecks = [
    {
      name: 'getRouteByMode新接口',
      pattern: /export.*getRouteByMode/,
      required: true,
    },
    {
      name: 'RouteRequest类型定义',
      pattern: /interface RouteRequest/,
      required: true,
    },
    {
      name: '异步API调用',
      pattern: /export const getRouteByMode = async/,
      required: true,
    },
  ];

  apiIntegrationChecks.forEach(({ name, pattern, required }) => {
    const found = pattern.test(apiContent);
    testResults.apiIntegration.push({
      name,
      status: found ? 'PASS' : 'FAIL',
      required,
    });

    console.log(
      `${found ? '✅' : '❌'} ${name}: ${found ? 'AVAILABLE' : 'MISSING'}`
    );
  });
} else {
  console.log('❌ amapRouteService: FILE_NOT_FOUND');
}

// 5. 统一转换层集成验证
console.log('\n🔄 [Test 5] 统一转换层集成验证...');

if (fs.existsSync(TRANSFORMER_PATH)) {
  const transformerContent = fs.readFileSync(TRANSFORMER_PATH, 'utf8');

  const transformerIntegrationChecks = [
    {
      name: 'MapTransformer企业级改造',
      pattern: /transformToAPI.*transformFromAPI/,
      required: true,
    },
    {
      name: '地址搜索转换支持',
      pattern: /addressSelection/,
      required: true,
    },
    {
      name: '坐标验证功能',
      pattern: /坐标验证|coordinate.*validation/,
      required: true,
    },
  ];

  transformerIntegrationChecks.forEach(({ name, pattern, required }) => {
    const found = pattern.test(transformerContent);
    testResults.transformerIntegration.push({
      name,
      status: found ? 'PASS' : 'FAIL',
      required,
    });

    console.log(
      `${found ? '✅' : '❌'} ${name}: ${found ? 'AVAILABLE' : 'MISSING'}`
    );
  });
} else {
  console.log('❌ MapTransformer: FILE_NOT_FOUND');
}

// 6. 全链路集成验证
console.log('\n🔗 [Test 6] 全链路集成验证...');

// 检查导航屏幕是否使用新组件
if (fs.existsSync(NAVIGATION_SCREEN_PATH)) {
  const screenContent = fs.readFileSync(NAVIGATION_SCREEN_PATH, 'utf8');

  const fullChainChecks = [
    {
      name: '使用PropertyNavigationMapRefactored',
      pattern: /PropertyNavigationMapRefactored/,
      required: true,
    },
    {
      name: '正确的导入路径',
      pattern: /from.*PropertyNavigationMapRefactored/,
      required: true,
    },
  ];

  fullChainChecks.forEach(({ name, pattern, required }) => {
    const found = pattern.test(screenContent);
    testResults.fullChainIntegration.push({
      name,
      status: found ? 'PASS' : 'FAIL',
      required,
    });

    console.log(
      `${found ? '✅' : '❌'} ${name}: ${found ? 'CONNECTED' : 'DISCONNECTED'}`
    );
  });
} else {
  console.log('❌ PropertyNavigationScreen: FILE_NOT_FOUND');
}

// 检查组件间数据流
if (fs.existsSync(MAIN_COMPONENT)) {
  const componentContent = fs.readFileSync(MAIN_COMPONENT, 'utf8');

  const dataFlowChecks = [
    {
      name: 'Hook数据流集成',
      pattern: /企业级Hook集成.*地址搜索深度集成验证/,
      required: true,
    },
    {
      name: '子组件数据传递',
      pattern: /NavigationControls[\s\S]*onAddressSearch={handleAddressSearch}/,
      required: true,
    },
    {
      name: '统一类型使用',
      pattern: /PropertyNavigationParams.*AddressReturnKey/,
      required: true,
    },
  ];

  dataFlowChecks.forEach(({ name, pattern, required }) => {
    const found = pattern.test(componentContent);
    testResults.fullChainIntegration.push({
      name,
      status: found ? 'PASS' : 'FAIL',
      required,
    });

    console.log(
      `${found ? '✅' : '❌'} ${name}: ${found ? 'CONNECTED' : 'DISCONNECTED'}`
    );
  });
}

// 7. 生成深度集成报告
console.log('\n📊 [Deep Integration Report] 地址搜索功能深度集成汇总:');

const generateCategoryReport = (tests, categoryName) => {
  const total = tests.length;
  const passed = tests.filter(t => t.status === 'PASS').length;
  const required = tests.filter(t => t.required).length;
  const requiredPassed = tests.filter(
    t => t.required && t.status === 'PASS'
  ).length;
  const percentage = Math.round((passed / total) * 100);

  console.log(`\n${categoryName}:`);
  console.log(`  ✅ 通过: ${passed}/${total} (${percentage}%)`);

  if (required > 0) {
    console.log(
      `  🔥 必需项: ${requiredPassed}/${required} (${Math.round((requiredPassed / required) * 100)}%)`
    );
  }

  if (passed < total) {
    console.log(`  ❌ 失败项目:`);
    tests
      .filter(t => t.status === 'FAIL')
      .forEach(test => {
        console.log(`    - ${test.name}${test.required ? ' (必需)' : ''}`);
      });
  }

  return percentage;
};

const addressSearchScore = generateCategoryReport(
  testResults.addressSearchIntegration,
  '📍 地址搜索集成'
);
const storeIntegrationScore = generateCategoryReport(
  testResults.storeIntegration,
  '🏪 Store集成'
);
const hookBusinessLogicScore = generateCategoryReport(
  testResults.hookBusinessLogic,
  '🔧 Hook业务逻辑'
);
const apiIntegrationScore = generateCategoryReport(
  testResults.apiIntegration,
  '🌐 API集成'
);
const transformerScore = generateCategoryReport(
  testResults.transformerIntegration,
  '🔄 统一转换层集成'
);
const fullChainScore = generateCategoryReport(
  testResults.fullChainIntegration,
  '🔗 全链路集成'
);

const overallIntegrationScore = Math.round(
  (addressSearchScore +
    storeIntegrationScore +
    hookBusinessLogicScore +
    apiIntegrationScore +
    transformerScore +
    fullChainScore) /
    6
);

console.log(
  `\n🎯 [OVERALL INTEGRATION SCORE] 地址搜索深度集成评分: ${overallIntegrationScore}% 🎯`
);

// 8. 集成质量等级评估
if (overallIntegrationScore >= 95) {
  console.log('🚀 [STATUS] 地址搜索A级集成！完美的企业级架构集成！');
} else if (overallIntegrationScore >= 90) {
  console.log('⭐ [STATUS] 地址搜索A-级集成！优秀的架构集成质量！');
} else if (overallIntegrationScore >= 80) {
  console.log('✅ [STATUS] 地址搜索B+级集成！良好的集成质量，需少量优化！');
} else if (overallIntegrationScore >= 70) {
  console.log('⚠️ [STATUS] 地址搜索B级集成！基本集成完成，需要改进！');
} else {
  console.log('❌ [STATUS] 地址搜索集成不达标！需要大幅改进集成质量！');
}

// 9. 功能验证建议
console.log('\n📋 [Function Verification] 功能验证建议:');

if (overallIntegrationScore >= 90) {
  console.log('1. 🎯 运行应用验证地址搜索完整流程');
  console.log('2. 📱 测试起点/终点地址选择功能');
  console.log('3. 🔄 验证位置交换功能正常');
  console.log('4. 🗺️ 确认路线计算和显示正常');
  console.log('5. 💾 测试地址数据持久化');
  console.log('6. 🎉 标记Phase 2.3完成');
} else {
  console.log('1. 🔧 修复缺失的集成点');
  console.log('2. 🏪 完善Store状态管理');
  console.log('3. 🔗 确保数据流完整性');
  console.log('4. 📝 更新组件集成');
}

// 10. 架构质量总结
const totalTestsRun = Object.values(testResults).reduce(
  (sum, tests) => sum + tests.length,
  0
);
const totalTestsPassed = Object.values(testResults).reduce(
  (sum, tests) => sum + tests.filter(t => t.status === 'PASS').length,
  0
);
const integrationCoverageRate = Math.round(
  (totalTestsPassed / totalTestsRun) * 100
);

console.log(`\n📈 [Integration Coverage] 集成覆盖统计:`);
console.log(`  🧪 测试项目: ${totalTestsRun}`);
console.log(`  ✅ 通过测试: ${totalTestsPassed}`);
console.log(`  📊 集成覆盖率: ${integrationCoverageRate}%`);

console.log(
  '\n🔚 [AddressIntegrationTest] Phase 2.3地址搜索功能深度集成验证完成！'
);
