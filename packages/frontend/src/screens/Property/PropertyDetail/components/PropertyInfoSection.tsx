/**
 * 房源信息区域组件
 * 
 * 功能：
 * - 房源标题展示
 * - SimplifiedSDUI 关键指标区域
 * - 降级展示处理
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { PropertyDetailDesignSystem } from '@property/styles/propertyDetailDesignSystem';
import { SimplifiedSDUIRenderer } from '@utils/sdui/SimplifiedSDUI';
import { fp } from '@shared/utils/responsive';
import type { PropertyDetailData } from '@property/types';

const { colors, typography, spacing, borderRadius, shadows } = PropertyDetailDesignSystem;

interface PropertyInfoSectionProps {
  propertyData: PropertyDetailData;
  sduiConfig: any;
  isSDUIReady: boolean;
}

export const PropertyInfoSection: React.FC<PropertyInfoSectionProps> = ({
  propertyData,
  sduiConfig,
  isSDUIReady,
}) => {
  return (
    <View style={styles.propertyInfoCard}>
      {/* 房源标题和基本信息 */}
      <View style={styles.propertyTitleSection}>
        <Text style={styles.propertyTitle}>{propertyData.title}</Text>
      </View>

      {/* SimplifiedSDUI 关键指标区域 */}
      {isSDUIReady && sduiConfig ? (
        <SimplifiedSDUIRenderer
          config={sduiConfig}
          fallback={
            <Text style={styles.errorText}>关键指标加载失败</Text>
          }
        />
      ) : (
        <View style={styles.fallbackMetrics}>
          <Text style={styles.fallbackText}>正在加载关键指标...</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  // 房源信息卡片
  propertyInfoCard: {
    backgroundColor: colors.background.primary,
    marginHorizontal: spacing.sm,
    marginTop: spacing.sm,
    marginBottom: spacing.sm,
    borderRadius: borderRadius.md,
    ...shadows.card,
    padding: spacing.lg,
  },

  // 房源标题区域
  propertyTitleSection: {
    marginBottom: spacing.xs,
  },

  // 房源标题
  propertyTitle: {
    ...typography.title,
    color: colors.text.primary,
    marginBottom: spacing.xs,
    fontSize: fp(20),
    fontWeight: '600',
  },

  // 错误文字
  errorText: {
    ...typography.body,
    color: colors.status.error,
    marginBottom: spacing.md,
  },

  // 降级指标显示
  fallbackMetrics: {
    padding: spacing.md,
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.sm,
    marginVertical: spacing.sm,
  },

  // 降级文字
  fallbackText: {
    ...typography.body,
    color: colors.text.secondary,
  },
});