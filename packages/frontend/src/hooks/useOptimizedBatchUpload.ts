/**
 * 优化批量上传Hook
 * 
 * 功能特性：
 * - 集成OptimizedBatchUploader服务
 * - 状态管理和进度跟踪
 * - 错误处理和重试机制
 * - 性能监控和统计
 * - 用户体验优化
 * 
 * 设计原则：
 * - 简化组件使用：一个Hook解决所有上传需求
 * - 企业级架构：可扩展、可维护、可监控
 * - 移动端优化：适配网络波动和设备性能
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月16日
 */

import { useState, useCallback, useRef } from 'react';
import { Alert } from 'react-native';
import FeedbackService from '../shared/services/FeedbackService';
import {
  OptimizedBatchUploader,
  BatchProgress,
  BatchUploadResult,
  ProcessedFile,
  FileProcessingStatus,
  BatchUploadConfig


} from '../shared/services/media/OptimizedBatchUploader';
// import { mediaPickerService, MediaPickerOptions } from '../services/media/MediaPickerService';
import * as ImagePicker from 'expo-image-picker';

interface MediaPickerOptions {
  mediaTypes?: 'all' | 'images' | 'videos';
  allowsMultipleSelection?: boolean;
  selectionLimit?: number;
  quality?: number;
}

export interface OptimizedBatchUploadState {
  isUploading: boolean;
  isPreprocessing: boolean;
  progress: BatchProgress | null;
  files: ProcessedFile[];
  result: BatchUploadResult | null;
  error: string | null;
  showProgress: boolean;
}

export interface OptimizedBatchUploadOptions {
  maxFiles?: number;
  propertyId?: string;
  config?: Partial<BatchUploadConfig>;
  autoStartUpload?: boolean;
  showProgressModal?: boolean;
}

export interface OptimizedBatchUploadActions {
  selectAndUploadFiles: (pickerOptions?: MediaPickerOptions) => Promise<void>;
  uploadFiles: (files: Array<{ uri: string; name: string; type: string; size: number }>) => Promise<BatchUploadResult | null>;
  cancelUpload: () => void;
  retryFile: (fileId: string) => Promise<void>;
  clearState: () => void;
  hideProgress: () => void;
  showProgress: () => void;
}

export interface OptimizedBatchUploadReturn {
  state: OptimizedBatchUploadState;
  actions: OptimizedBatchUploadActions;
}

/**
 * 优化批量上传Hook
 */
export const useOptimizedBatchUpload = (
  options: OptimizedBatchUploadOptions = {}
): OptimizedBatchUploadReturn => {
  const {
    maxFiles = 10,
    propertyId,
    config = {},
    autoStartUpload = true,
    showProgressModal = true
  } = options;

  // 状态管理
  const [state, setState] = useState<OptimizedBatchUploadState>({
    isUploading: false,
    isPreprocessing: false,
    progress: null,
    files: [],
    result: null,
    error: null,
    showProgress: false
  });

  // 上传器实例
  const uploaderRef = useRef<OptimizedBatchUploader | null>(null);
  const cancelTokenRef = useRef<boolean>(false);

  // 获取上传器实例
  const getUploader = useCallback(() => {
    if (!uploaderRef.current) {
      uploaderRef.current = new OptimizedBatchUploader({
        propertyId,
        ...config
      });
    }
    return uploaderRef.current;
  }, [propertyId, config]);

  // 更新状态
  const updateState = useCallback((updates: Partial<OptimizedBatchUploadState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // 清除错误
  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  // 进度回调
  const handleProgress = useCallback((progress: BatchProgress) => {
    updateState({ progress });
  }, [updateState]);

  // 选择并上传文件
  const selectAndUploadFiles = useCallback(async (pickerOptions?: MediaPickerOptions) => {
    try {
      clearError();

      // 选择文件
      const pickerResult = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsMultipleSelection: true,
        selectionLimit: maxFiles,
        quality: 0.8,
      });

      if (pickerResult.canceled || !pickerResult.assets) {
        return;
      }

      // 转换文件格式
      const files = pickerResult.assets.map(asset => ({
        uri: asset.uri,
        name: asset.fileName || `file_${Date.now()}.${asset.type === 'image' ? 'jpg' : 'mp4'}`,
        type: asset.type === 'image' ? 'image/jpeg' : 'video/mp4',
        size: asset.fileSize || 0
      }));

      console.log(`[useOptimizedBatchUpload] 选择了 ${files.length} 个文件`);

      // 自动开始上传
      if (autoStartUpload) {
        await uploadFiles(files);
      }

    } catch (error) {
      console.error('[useOptimizedBatchUpload] 选择文件失败:', error);
      updateState({ 
        error: error instanceof Error ? error.message : '选择文件失败' 
      });
    }
  }, [maxFiles, autoStartUpload, clearError, updateState]);

  // 上传文件
  const uploadFiles = useCallback(async (
    files: Array<{ uri: string; name: string; type: string; size: number }>
  ): Promise<BatchUploadResult | null> => {
    try {
      clearError();
      cancelTokenRef.current = false;

      if (files.length === 0) {
        updateState({ error: '没有选择文件' });
        return null;
      }

      console.log(`[useOptimizedBatchUpload] 开始批量上传 ${files.length} 个文件`);

      // 显示进度
      if (showProgressModal) {
        updateState({ showProgress: true });
      }

      // 预处理阶段
      updateState({ isPreprocessing: true });
      const uploader = getUploader();
      const processedFiles = await uploader.preprocessFiles(files);
      
      updateState({ 
        isPreprocessing: false,
        isUploading: true,
        files: processedFiles 
      });

      // 上传阶段
      const result = await uploader.uploadWithStrategy(processedFiles, (progress) => {
        // 检查是否被取消
        if (cancelTokenRef.current) {
          throw new Error('上传已取消');
        }
        handleProgress(progress);
      });

      updateState({
        isUploading: false,
        result,
        files: result.results
      });

      // 显示结果
      if (result.success) {
        console.log(`[useOptimizedBatchUpload] 批量上传成功: ${result.successCount}/${result.totalFiles}`);
        if (result.failedCount > 0) {
          FeedbackService.showInfo(`成功: ${result.successCount}个，失败: ${result.failedCount}个`);
        }
      } else {
        console.error(`[useOptimizedBatchUpload] 批量上传失败:`, result.errorSummary);
        FeedbackService.showUploadFailed();
      }

      return result;

    } catch (error) {
      console.error('[useOptimizedBatchUpload] 上传失败:', error);
      const errorMessage = error instanceof Error ? error.message : '上传失败';
      
      updateState({
        isUploading: false,
        isPreprocessing: false,
        error: errorMessage
      });

      if (errorMessage !== '上传已取消') {
        FeedbackService.showInfo(errorMessage);
      }

      return null;
    }
  }, [clearError, updateState, showProgressModal, getUploader, handleProgress]);

  // 取消上传
  const cancelUpload = useCallback(() => {
    console.log('[useOptimizedBatchUpload] 取消上传');
    cancelTokenRef.current = true;
    
    updateState({
      isUploading: false,
      isPreprocessing: false,
      showProgress: false
    });

    FeedbackService.showInfo('文件上传已取消');
  }, [updateState]);

  // 重试单个文件
  const retryFile = useCallback(async (fileId: string) => {
    try {
      console.log(`[useOptimizedBatchUpload] 重试文件: ${fileId}`);
      
      const file = state.files.find(f => f.id === fileId);
      if (!file) {
        throw new Error('文件不存在');
      }

      // 重置文件状态
      const updatedFiles = state.files.map(f => 
        f.id === fileId 
          ? { ...f, status: FileProcessingStatus.PENDING, error: undefined, retryCount: 0 }
          : f
      );

      updateState({ files: updatedFiles });

      // 重新上传单个文件
      const uploader = getUploader();
      const result = await uploader.uploadWithStrategy([file], handleProgress);
      
      // 更新文件状态
      const finalFiles = state.files.map(f => 
        f.id === fileId 
          ? result.results.find(r => r.id === fileId) || f
          : f
      );

      updateState({ files: finalFiles });

      if (result.success) {
        FeedbackService.showInfo('文件上传成功');
      } else {
        FeedbackService.showInfo(result.errorSummary[0] || '重试失败');
      }

    } catch (error) {
      console.error('[useOptimizedBatchUpload] 重试失败:', error);
      FeedbackService.showInfo(error instanceof Error ? error.message : '重试失败');
    }
  }, [state.files, updateState, getUploader, handleProgress]);

  // 清除状态
  const clearState = useCallback(() => {
    setState({
      isUploading: false,
      isPreprocessing: false,
      progress: null,
      files: [],
      result: null,
      error: null,
      showProgress: false
    });
    cancelTokenRef.current = false;
  }, []);

  // 隐藏进度
  const hideProgress = useCallback(() => {
    updateState({ showProgress: false });
  }, [updateState]);

  // 显示进度
  const showProgress = useCallback(() => {
    updateState({ showProgress: true });
  }, [updateState]);

  return {
    state,
    actions: {
      selectAndUploadFiles,
      uploadFiles,
      cancelUpload,
      retryFile,
      clearState,
      hideProgress,
      showProgress
    }
  };
};
