/**
 * DemandFormScreen 样式定义
 * @fileoverview 需求发布表单主屏幕的样式
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🔒 功能保证：100%保留原有DemandFormScreen.tsx中的样式定义
 */

import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../../shared/utils/responsiveUtils';

export const styles = StyleSheet.create({
  // === 主容器样式 ===
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },

  // === 背景样式 ===
  gradientBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: hp(200),
    backgroundColor: '#FF6B35', // 橙色背景，保留原有设计
  },

  // === 内容区域样式 ===
  content: {
    flex: 1,
    backgroundColor: 'transparent',
  },

  scrollContent: {
    paddingBottom: hp(100), // 为底部按钮留出空间
  },

  // === 表单卡片样式 ===
  formCard: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: wp(20),
    borderTopRightRadius: wp(20),
    marginTop: hp(20),
    paddingHorizontal: wp(20),
    paddingTop: hp(30),
    paddingBottom: hp(40),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },

  // === 标题区域样式 ===
  titleSection: {
    paddingHorizontal: wp(20),
    paddingVertical: hp(20),
  },

  subtitle: {
    fontSize: fp(16),
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: '500',
  },

  // === 区块标题样式 ===
  sectionTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#333333',
    marginBottom: hp(15),
    marginTop: hp(20),
  },

  // === 需求类型选择样式 ===
  currentDemandTypeContainer: {
    flexDirection: 'row',
    marginBottom: hp(20),
    backgroundColor: '#F8F9FA',
    borderRadius: wp(12),
    padding: wp(4),
  },

  currentDemandTypeButton: {
    flex: 1,
    paddingVertical: hp(12),
    paddingHorizontal: wp(20),
    borderRadius: wp(8),
    alignItems: 'center',
    justifyContent: 'center',
  },

  currentDemandTypeButtonSelected: {
    backgroundColor: '#FF6B35',
    shadowColor: '#FF6B35',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },

  currentDemandTypeText: {
    fontSize: fp(16),
    fontWeight: '500',
    color: '#666666',
  },

  currentDemandTypeTextSelected: {
    color: '#FFFFFF',
    fontWeight: '600',
  },

  // === 表单字段样式 ===
  fieldContainer: {
    marginBottom: hp(20),
  },

  fieldLabel: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#333333',
    marginBottom: hp(8),
  },

  requiredMark: {
    color: '#FF4444',
  },

  // === 错误提示样式 ===
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(5),
    paddingHorizontal: wp(12),
  },

  errorText: {
    fontSize: fp(14),
    color: '#FF4444',
    marginLeft: wp(5),
    flex: 1,
  },

  // === 提示信息样式 ===
  tipContainer: {
    backgroundColor: '#F0F8FF',
    borderRadius: wp(8),
    padding: wp(12),
    marginTop: hp(10),
    borderLeftWidth: 3,
    borderLeftColor: '#4A90E2',
  },

  tipText: {
    fontSize: fp(14),
    color: '#4A90E2',
    lineHeight: fp(20),
  },

  // === 标签区域样式 ===
  tagsSection: {
    marginTop: hp(30),
  },

  tagsSectionTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#333333',
    marginBottom: hp(15),
  },

  selectedTagsContainer: {
    marginBottom: hp(20),
  },

  selectedTagsTitle: {
    fontSize: fp(16),
    fontWeight: '500',
    color: '#333333',
    marginBottom: hp(10),
  },

  selectedTagsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: wp(8),
  },

  selectedTag: {
    backgroundColor: '#FF6B35',
    borderRadius: wp(20),
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    flexDirection: 'row',
    alignItems: 'center',
  },

  selectedTagText: {
    fontSize: fp(14),
    color: '#FFFFFF',
    marginRight: wp(5),
  },

  removeTagButton: {
    width: wp(16),
    height: wp(16),
    borderRadius: wp(8),
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  availableTagsContainer: {
    marginTop: hp(20),
  },

  availableTagsTitle: {
    fontSize: fp(16),
    fontWeight: '500',
    color: '#333333',
    marginBottom: hp(10),
  },

  availableTagsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: wp(8),
  },

  availableTag: {
    backgroundColor: '#F8F9FA',
    borderRadius: wp(20),
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },

  availableTagDisabled: {
    opacity: 0.5,
  },

  availableTagText: {
    fontSize: fp(14),
    color: '#666666',
  },

  // === 最大标签警告样式 ===
  maxTagsWarning: {
    backgroundColor: '#FFF3CD',
    borderColor: '#FFEAA7',
    borderWidth: 1,
    borderRadius: wp(8),
    padding: wp(12),
    marginTop: hp(10),
    flexDirection: 'row',
    alignItems: 'center',
  },

  maxTagsWarningText: {
    fontSize: fp(14),
    color: '#856404',
    marginLeft: wp(8),
    flex: 1,
  },

  // === 协议区域样式 ===
  agreementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(30),
    paddingHorizontal: wp(20),
  },

  checkbox: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(4),
    borderWidth: 2,
    borderColor: '#DDD',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: wp(10),
  },

  checkboxChecked: {
    backgroundColor: '#FF6B35',
    borderColor: '#FF6B35',
  },

  agreementText: {
    fontSize: fp(14),
    color: '#666666',
    flex: 1,
    lineHeight: fp(20),
  },

  agreementLink: {
    color: '#FF6B35',
    textDecorationLine: 'underline',
  },
});
