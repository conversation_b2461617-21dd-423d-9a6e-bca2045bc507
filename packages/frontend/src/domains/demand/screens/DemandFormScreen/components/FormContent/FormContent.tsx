/**
 * FormContent 组件
 * @fileoverview 需求发布表单的内容区域组件
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🔒 功能保证：100%保留原有DemandFormScreen.tsx中的所有表单内容
 */

import React, { useRef } from 'react';
import { View, ScrollView } from 'react-native';

// 导入表单区块组件
import { BasicInfoSection } from '../FormSections/BasicInfoSection';
import { LocationSection } from '../FormSections/LocationSection';
import { BudgetSection } from '../FormSections/BudgetSection';
import { RequirementsSection } from '../FormSections/RequirementsSection';
import { ContactSection } from '../FormSections/ContactSection';
import { TagSection } from '../FormSections/TagSection';
import { AgreementSection } from '../FormSections/AgreementSection';

// 导入类型
import type { DemandFormData, FormErrors } from '../../types/demandForm.types';

// 样式
import { styles } from './formContent.styles';

interface FormContentProps {
  // === 表单数据 ===
  formData: DemandFormData;
  formErrors: FormErrors;
  validationErrors: Record<string, string>; // 兼容原有命名

  // === 标签状态 ===
  selectedFeatureTags: string[];
  availableTags: string[];
  removedTags: string[];
  showTags: boolean;
  showMaxTagsWarning: boolean;
  hasInitialTagsGenerated: boolean;

  // === 编辑模式 ===
  isEditMode: boolean;

  // === 事件处理 ===
  onFieldChange: (field: string, value: any) => void;
  onNestedFieldChange: (path: string, value: any) => void;
  onMoveToSelected: (tag: string) => void;
  onMoveToAvailable: (tag: string) => void;
  onValidationError: (field: string, value: any) => void;

  // === 滚动引用 ===
  scrollViewRef?: React.RefObject<ScrollView>;
}

/**
 * 表单内容组件 - 完整保留原有表单结构和字段
 */
export const FormContent: React.FC<FormContentProps> = React.memo(
  ({
    formData,
    formErrors,
    validationErrors,
    selectedFeatureTags,
    availableTags,
    removedTags,
    showTags,
    showMaxTagsWarning,
    hasInitialTagsGenerated,
    isEditMode,
    onFieldChange,
    onNestedFieldChange,
    onMoveToSelected,
    onMoveToAvailable,
    onValidationError,
    scrollViewRef,
  }) => {
    // 内部引用，用于字段定位
    const sectionRefs = {
      basicInfo: useRef<View>(null),
      location: useRef<View>(null),
      budget: useRef<View>(null),
      requirements: useRef<View>(null),
      contact: useRef<View>(null),
      tags: useRef<View>(null),
      agreement: useRef<View>(null),
    };

    return (
      <View style={styles.container}>
        {/* 🔒 保留原有的白色表单卡片容器 */}
        <View style={styles.formCard}>
          {/* 🔒 基础信息区块 - 需求类型、房源类型 */}
          <View ref={sectionRefs.basicInfo}>
            <BasicInfoSection
              formData={formData}
              formErrors={formErrors}
              validationErrors={validationErrors}
              onFieldChange={onFieldChange}
              onNestedFieldChange={onNestedFieldChange}
              onValidationError={onValidationError}
            />
          </View>

          {/* 🔒 位置需求区块 - 区域选择 */}
          <View ref={sectionRefs.location}>
            <LocationSection
              formData={formData}
              formErrors={formErrors}
              validationErrors={validationErrors}
              onFieldChange={onFieldChange}
              onNestedFieldChange={onNestedFieldChange}
              onValidationError={onValidationError}
            />
          </View>

          {/* 🔒 面积预算区块 - 面积范围、预算范围 */}
          <View ref={sectionRefs.budget}>
            <BudgetSection
              formData={formData}
              formErrors={formErrors}
              validationErrors={validationErrors}
              onFieldChange={onFieldChange}
              onNestedFieldChange={onNestedFieldChange}
              onValidationError={onValidationError}
            />
          </View>

          {/* 🔒 详细要求区块 - 行业类型、楼层偏好等 */}
          <View ref={sectionRefs.requirements}>
            <RequirementsSection
              formData={formData}
              formErrors={formErrors}
              validationErrors={validationErrors}
              onFieldChange={onFieldChange}
              onNestedFieldChange={onNestedFieldChange}
              onValidationError={onValidationError}
            />
          </View>

          {/* 🔒 联系信息区块 - 手机号、姓氏、性别等 */}
          <View ref={sectionRefs.contact}>
            <ContactSection
              formData={formData}
              formErrors={formErrors}
              validationErrors={validationErrors}
              isEditMode={isEditMode}
              onFieldChange={onFieldChange}
              onNestedFieldChange={onNestedFieldChange}
              onValidationError={onValidationError}
            />
          </View>

          {/* 🔒 标签选择区块 - AI推荐标签、手动管理 */}
          <View ref={sectionRefs.tags}>
            <TagSection
              formData={formData}
              selectedFeatureTags={selectedFeatureTags}
              availableTags={availableTags}
              removedTags={removedTags}
              showTags={showTags}
              showMaxTagsWarning={showMaxTagsWarning}
              hasInitialTagsGenerated={hasInitialTagsGenerated}
              onMoveToSelected={onMoveToSelected}
              onMoveToAvailable={onMoveToAvailable}
            />
          </View>

          {/* 🔒 协议确认区块 - 同意发布规则 */}
          <View ref={sectionRefs.agreement}>
            <AgreementSection
              formData={formData}
              formErrors={formErrors}
              validationErrors={validationErrors}
              onFieldChange={onFieldChange}
            />
          </View>
        </View>
      </View>
    );
  }
);

FormContent.displayName = 'FormContent';
