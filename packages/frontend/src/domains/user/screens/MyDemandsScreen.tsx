/**
 * 我的求租/求购详细页面
 * 参考闲鱼设计，显示详细统计和需求列表
 */

import React, { useState, useCallback, useRef, memo } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import PagerView from 'react-native-pager-view';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';

// API服务
import { DemandAPI } from '../../demand/services/demandAPI';

// 🏗️ 企业级架构：导入企业级Hook系统
import { useDemandStatusCounts } from '../../../shared/hooks/entity/useEntityStatusCounts';
import { useDemandStatistics } from '../../../shared/hooks/entity/useEntityStatistics';

// 🌐 其他服务
import FeedbackService from '../../../shared/services/FeedbackService';
import { formatSmartTime } from '../../../shared/utils/timeFormat';

// 🌐 转换工具：中文显示
import { getPropertyTypeDisplayName } from '../../property/config/propertyTypeConfigs';
import { getRegionsDisplayString } from '../../demand/utils/regionUtils';

// 导入企业级响应式工具函数
import {
  wp,
  hp,
  fp,
  spacing,
  fontSize,
  borderRadius,
} from '../../../shared/utils/responsiveUtils';

// 需求状态类型
type DemandStatus = 'active' | 'draft' | 'inactive';
type DemandType = 'rental' | 'purchase';

// 需求数据接口
interface Demand {
  id: string;
  type: DemandType;
  title: string;
  description: string;
  budget: string;
  location: string;
  status: DemandStatus;
  exposureCount: number;
  matchCount: number;
  interestedCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// 详细统计数据接口
interface DetailedStats {
  rentalDemands: number;
  purchaseDemands: number;
  totalExposure: number;
  totalMatches: number;
  totalInterested: number;
  contactSuccessRate: number;
  avgResponseTime: number;
}

const MyDemandsScreen: React.FC = memo(() => {
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState<'active' | 'draft' | 'inactive'>('active');

  // 🏗️ 企业级架构：PagerView状态管理
  const pagerRef = useRef<PagerView>(null);
  const [currentPage, setCurrentPage] = useState(0);

  // Tab索引映射 - 修复循环依赖问题
  const tabIndexMap = { 'active': 0, 'draft': 1, 'inactive': 2 };
  const indexTabMap = ['active', 'draft', 'inactive'] as const;

  // 🔧 企业级事件处理：Tab点击（遵循Hook层规范）
  const handleTabPress = useCallback((tab: 'active' | 'draft' | 'inactive') => {
    const targetIndex = tabIndexMap[tab];
    setActiveTab(tab);
    setCurrentPage(targetIndex);
    pagerRef.current?.setPage(targetIndex);
  }, []);

  // 🔧 企业级事件处理：页面滑动
  const handlePageSelected = useCallback((event: any) => {
    const { position } = event.nativeEvent;
    const newTab = indexTabMap[position];
    setCurrentPage(position);
    setActiveTab(newTab);
  }, []);

  // 🏗️ 企业级架构：使用修复后的企业级Hook系统
  const {
    statusCounts,
    statusCountsData, // 🔧 获取包含数据的对象
    isLoading: statusCountsLoading,
    error: statusCountsError,
  } = useDemandStatusCounts({
    staleTime: 5 * 1000, // 🔧 减少缓存时间到5秒，确保状态变更后快速刷新
  });

  // 🔧 需求操作处理函数 - 基于正确的导航参数
  const handleViewDemand = useCallback((demand: Demand) => {
    console.log('🔍 查看需求详情:', demand.id);

    try {
      // 🚀 导航到正确的需求详情页 - DemandDetailScreen
      (navigation as any).navigate('DemandDetail', {
        demandId: demand.id
      });
    } catch (error) {
      console.error('导航到详情页面失败:', error);
      FeedbackService.showError('页面跳转失败，请稍后重试');
    }
  }, [navigation]);

  const handleEditDemand = useCallback((demand: Demand) => {
    console.log('✏️ 编辑需求:', demand.id, demand);
    try {
      // 🔧 从所有状态中查找原始数据
      let originalData = null;

      if (statusCountsData) {
        // 合并所有状态的数据进行查找
        const allItems = [
          ...(statusCountsData.activeItems || []),
          ...(statusCountsData.draftItems || []),
          ...(statusCountsData.inactiveItems || [])
        ];

        originalData = allItems.find((item: any) => item.id === demand.id);
      }

      console.log('🔍 找到原始编辑数据:', originalData);

      // 🚀 修复导航参数 - 使用正确的字段名
      const demandType = originalData?.demandType || (demand as any).demandType || (demand as any).demand_type || 'RENTAL';
      console.log('🔍 编辑需求类型:', demandType);

      // 导航到需求编辑页面，使用原始数据格式
      (navigation as any).navigate('DemandForm', {
        demandType: demandType.toUpperCase(),
        editMode: true,
        demandId: demand.id,
        demandData: originalData || demand
      });
    } catch (error) {
      console.error('导航到编辑页面失败:', error);
      FeedbackService.showError('页面跳转失败，请稍后重试');
    }
  }, [navigation, statusCountsData]);

  const handleDemandSettings = useCallback((demand: Demand) => {
    console.log('⚙️ 需求设置:', demand.id, demand.status);

    // 🔧 修复状态判断逻辑 - 统一状态值处理
    const currentStatus = demand.status.toLowerCase();

    // 根据状态显示不同的操作选项
    const options = currentStatus === 'active'
      ? ['下架', '删除', '取消']
      : currentStatus === 'offline' || currentStatus === 'inactive'
      ? ['上架', '删除', '取消']
      : ['发布', '删除', '取消']; // 草稿状态

    // 显示操作选择弹窗
    import('react-native').then(({ Alert }) => {
      Alert.alert(
        '需求设置',
        `当前状态: ${demand.status}\n请选择操作`,
        options.map(option => ({
          text: option,
          onPress: () => {
            if (option === '删除') {
              handleDeleteDemand(demand);
            } else if (option === '上架' || option === '发布') {
              handlePublishDemand(demand);
            } else if (option === '下架') {
              handleUnpublishDemand(demand);
            }
          },
          style: option === '删除' ? 'destructive' : 'default'
        }))
      );
    });
  }, []);

  // 🔧 需求状态操作函数 - 基于现有API实现，添加数据刷新
  const handleDeleteDemand = useCallback(async (demand: Demand) => {
    console.log('🗑️ 删除需求:', demand.id);

    try {
      const result = await DemandAPI.deleteDemand(demand.id);
      if (!result.success) {
        FeedbackService.showError(result.message || '删除失败，请稍后重试');
        return;
      }

      FeedbackService.showSuccess('需求已删除');
      // 🔄 刷新数据：重新获取状态计数和列表
      // React Native使用导航刷新而不是window.reload
      navigation.reset({
        index: 0,
        routes: [{ name: 'MyDemands' }],
      });
    } catch (error) {
      console.error('删除需求失败:', error);
      FeedbackService.showError('删除失败，请稍后重试');
    }
  }, []);

  const handlePublishDemand = useCallback(async (demand: Demand) => {
    console.log('📢 上架需求:', demand.id);

    try {
      const result = await DemandAPI.updateDemandStatus(demand.id, 'ACTIVE');
      if (!result.success) {
        FeedbackService.showError(result.message || '上架失败，请稍后重试');
        return;
      }

      FeedbackService.showSuccess('需求已上架');

      // 🔄 刷新页面数据（缓存时间已减少到5秒，会自动刷新）
      setTimeout(() => {
        navigation.reset({
          index: 0,
          routes: [{ name: 'MyDemands' }],
        });
      }, 500); // 延迟500ms确保API响应完成
    } catch (error) {
      console.error('上架需求失败:', error);
      FeedbackService.showError('上架失败，请稍后重试');
    }
  }, [navigation]);

  const handleUnpublishDemand = useCallback(async (demand: Demand) => {
    console.log('📦 下架需求:', demand.id);

    try {
      const result = await DemandAPI.updateDemandStatus(demand.id, 'OFFLINE');
      if (!result.success) {
        FeedbackService.showError(result.message || '下架失败，请稍后重试');
        return;
      }

      FeedbackService.showSuccess('需求已下架');

      // 🔄 刷新页面数据（缓存时间已减少到5秒，会自动刷新）
      setTimeout(() => {
        navigation.reset({
          index: 0,
          routes: [{ name: 'MyDemands' }],
        });
      }, 500); // 延迟500ms确保API响应完成
    } catch (error) {
      console.error('下架需求失败:', error);
      FeedbackService.showError('下架失败，请稍后重试');
    }
  }, [navigation]);

  // 🏗️ 企业级架构：需求列表场景组件（解决VirtualizedLists嵌套问题）
  const DemandListScene = React.memo(({ status }: { status: 'active' | 'draft' | 'inactive' }) => {
    // 🚀 企业级架构：使用React状态确保数据同步
    const [demands, setDemands] = React.useState<Demand[]>([]);

    // 🔧 监听数据变化，确保时序正确
    React.useEffect(() => {
      if (!statusCountsData) return;

      // 从statusCountsData中获取对应状态的数据
      let sourceItems: any[] = [];

      switch (status) {
        case 'active':
          sourceItems = statusCountsData.activeItems || [];
          break;
        case 'draft':
          sourceItems = statusCountsData.draftItems || [];
          break;
        case 'inactive':
          sourceItems = statusCountsData.inactiveItems || [];
          break;
        default:
          sourceItems = [];
      }

      // 🔍 调试：查看实际数据结构
      console.log(`[MyDemandsScreen] ${status}状态数据:`, {
        count: sourceItems.length,
        firstItem: sourceItems[0],
        hasId: sourceItems[0]?.id,
        hasUuid: sourceItems[0]?.uuid,
        keys: sourceItems[0] ? Object.keys(sourceItems[0]) : []
      });

      // 🏗️ 企业级转换：DTO转换后数据 → Demand
      const transformedDemands = sourceItems.map((demandForm: any, index: number) => {
        try {
          // 🔧 处理DTO转换后的数据结构
          const demandType = demandForm.demandType || 'RENTAL';
          const propertyTypes = demandForm.propertyType || [];
          const areaRange = demandForm.areaRange || {};
          const budgetRange = demandForm.budgetRange || {};
          const location = demandForm.location || {};

          return {
            id: demandForm.id || `temp-${index}`, // 🔧 使用DTO转换后的id字段
            type: (demandType.toLowerCase() || 'rental') as DemandType,
            title: `${demandType === 'RENTAL' ? '求租' : '求购'} - ${getPropertyTypeDisplayName(propertyTypes[0]) || '未知类型'}`,
            description: `面积: ${areaRange.min || 0}-${areaRange.max || 0}${areaRange.unit || '㎡'}, 预算: ${budgetRange.min || 0}-${budgetRange.max || 0}${budgetRange.unit || '元/月'}`,
            budget: `${budgetRange.min || 0}-${budgetRange.max || 0}${budgetRange.unit || '元/月'}`,
            location: getRegionsDisplayString(location.districts) || '区域不限',
            status: status as DemandStatus,
            exposureCount: 0,
            matchCount: 0,
            interestedCount: 0,
            createdAt: demandForm.created_at ? new Date(demandForm.created_at) : new Date(),
            updatedAt: demandForm.updated_at ? new Date(demandForm.updated_at) : new Date(),
          };
        } catch (itemError) {
          return {
            id: `error-${index}`,
            type: 'rental' as DemandType,
            title: '数据解析错误',
            description: '无法解析需求数据',
            budget: '未知',
            location: '未知',
            status: status as DemandStatus,
            exposureCount: 0,
            matchCount: 0,
            interestedCount: 0,
            createdAt: new Date(),
            updatedAt: new Date(),
          };
        }
      });

      setDemands(transformedDemands);
    }, [statusCountsData, status]);

    const isLoading = statusCountsLoading;

    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B35" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      );
    }

    return (
      <FlatList
        data={demands || []}
        keyExtractor={(item) => item.id}
        renderItem={renderDemandItem}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>📋</Text>
            <Text style={styles.emptyTitle}>暂无{status === 'active' ? '已发布' : status === 'draft' ? '草稿' : '已下架'}需求</Text>
            <Text style={styles.emptyDescription}>
              {status === 'draft' ? '您还没有保存任何草稿' : '暂时没有相关需求记录'}
            </Text>
          </View>
        )}
      />
    );
  });



  // 🏗️ 企业级架构：使用企业级统计Hook
  const {
    statistics: detailedStats,
    isLoading: statsLoading,
    error: statsError,
  } = useDemandStatistics({
    enabled: true, // 启用统计查询
  });

  // 🔧 企业级架构：移除重复的需求列表查询，现在每个场景组件都有自己的查询

  // 渲染简化的AI推荐区域 (保留两行完整内容) - 使用企业级Hook数据
  const renderAISection = useCallback(() => {
    // 使用企业级Hook的统计数据，带降级处理
    const aiStats = {
      totalMatches: detailedStats?.totalMatches || (statusCounts?.active || 0) + (statusCounts?.draft || 0),
      totalInterested: detailedStats?.totalInquiries || 0,
      contactSuccessRate: detailedStats?.contactSuccessRate || 85,
    };

    return (
      <View style={styles.aiSection}>
        {/* 第一行：AI推荐统计 (3项) */}
        <View style={styles.aiStatsContainer}>
          <View style={styles.aiStatItem}>
            <Text style={styles.aiStatNumber}>{aiStats.totalMatches}</Text>
            <Text style={styles.aiStatLabel}>AI推荐房源</Text>
          </View>
          <View style={styles.aiStatItem}>
            <Text style={styles.aiStatNumber}>{aiStats.totalInterested}</Text>
            <Text style={styles.aiStatLabel}>业主感兴趣</Text>
          </View>
          <View style={styles.aiStatItem}>
            <Text style={styles.aiStatNumber}>{aiStats.contactSuccessRate}%</Text>
            <Text style={styles.aiStatLabel}>匹配准确率</Text>
          </View>
        </View>

        {/* 第二行：智能分析状态 (3项) */}
        <View style={styles.aiStatusSection}>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>智能分析</Text>
            <Text style={styles.statusValue}>已完成</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>房源匹配</Text>
            <Text style={styles.statusValue}>进行中</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>推送通知</Text>
            <Text style={styles.statusValue}>已开启</Text>
          </View>
        </View>
      </View>
    );
  }, [detailedStats, statusCounts]);

  // 渲染需求项 (参考主流APP紧凑设计)
  const renderDemandItem = ({ item }: { item: Demand }) => {
    return (
      <View style={styles.demandItem}>
        {/* 第一行：类型标签 + 标题 */}
        <View style={styles.demandHeader}>
          <View style={styles.demandTypeTag}>
            <Text style={styles.demandTypeText}>
              {item.type === 'rental' ? '求租' : '求购'}
            </Text>
          </View>
          <Text style={styles.demandTitle} numberOfLines={1}>{item.title}</Text>
        </View>

        {/* 第二行：面积 + 预算 (同行显示) */}
        <View style={styles.demandInfoRow}>
          <Text style={styles.demandArea}>面积: 100-200㎡</Text>
          <Text style={styles.demandBudget}>{item.budget}</Text>
        </View>

        {/* 第三行：位置 */}
        <Text style={styles.demandLocation}>📍 {item.location}</Text>

        {/* 第四行：统计数据 + 智能时间显示 */}
        <View style={styles.demandStats}>
          <View style={styles.statsLeft}>
            <Text style={styles.demandStat}>曝光{item.exposureCount}</Text>
            <Text style={styles.demandStat}>匹配{item.matchCount}</Text>
            <Text style={styles.demandStat}>感兴趣{item.interestedCount}</Text>
          </View>
          <Text style={styles.smartTime}>
            {formatSmartTime(item.updatedAt)}
          </Text>
        </View>

        {/* 第五行：操作按钮 - 草稿状态隐藏查看按钮 */}
        <View style={styles.demandActions}>
          {/* 🚀 只有已发布和已下架状态才显示查看按钮 */}
          {item.status !== 'draft' && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleViewDemand(item)}
            >
              <Text style={styles.actionButtonText}>查看</Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEditDemand(item)}
          >
            <Text style={styles.actionButtonText}>编辑</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDemandSettings(item)}
          >
            <Text style={styles.actionButtonText}>设置</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const insets = useSafeAreaInsets();

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      {/* 头部导航 */}
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>我的需求</Text>
        <TouchableOpacity style={styles.searchButton}>
          <Ionicons name="search" size={24} color="#333333" />
        </TouchableOpacity>
      </View>

      <View style={styles.scrollView}>
        {/* AI智能推荐区域 */}
        {renderAISection()}

        {/* Tab导航 */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'active' && styles.activeTab]}
            onPress={() => handleTabPress('active')}
          >
            <Text style={[styles.tabText, activeTab === 'active' && styles.activeTabText]}>
              已发布
            </Text>
            <Text style={[
              styles.tabCount,
              activeTab === 'active' && styles.activeTabCount
            ]}>
              {statusCounts?.active || 0}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'draft' && styles.activeTab]}
            onPress={() => handleTabPress('draft')}
          >
            <Text style={[styles.tabText, activeTab === 'draft' && styles.activeTabText]}>
              草稿
            </Text>
            <Text style={[
              styles.tabCount,
              activeTab === 'draft' && styles.activeTabCount
            ]}>
              {statusCounts?.draft || 0}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'inactive' && styles.activeTab]}
            onPress={() => handleTabPress('inactive')}
          >
            <Text style={[styles.tabText, activeTab === 'inactive' && styles.activeTabText]}>
              已下架
            </Text>
            <Text style={[
              styles.tabCount,
              activeTab === 'inactive' && styles.activeTabCount
            ]}>
              {statusCounts?.inactive || 0}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.searchButton}>
            <Ionicons name="search" size={20} color="#666666" />
          </TouchableOpacity>
        </View>

        {/* 🏗️ 企业级架构：可滑动的需求列表（修复VirtualizedLists嵌套问题） */}
        <PagerView
          ref={pagerRef}
          style={styles.pagerView}
          initialPage={0}
          onPageSelected={handlePageSelected}
        >
          {/* 已发布页面 */}
          <View key="active" style={styles.pageContainer}>
            <DemandListScene status="active" />
          </View>

          {/* 草稿页面 */}
          <View key="draft" style={styles.pageContainer}>
            <DemandListScene status="draft" />
          </View>

          {/* 已下架页面 */}
          <View key="inactive" style={styles.pageContainer}>
            <DemandListScene status="inactive" />
          </View>
        </PagerView>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF', // 整个页面白色背景
  },
  
  // 头部导航
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 0.5,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: fontSize.lg,
    fontWeight: '600',
    color: '#333333',
  },
  
  scrollView: {
    flex: 1,
  },

  // AI智能推荐区域 (保留两行完整内容)
  aiSection: {
    backgroundColor: '#FFFFFF',
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },

  // AI统计数据 (第一行)
  aiStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#F8FBFF',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    marginBottom: spacing.lg,
  },
  aiStatItem: {
    alignItems: 'center',
  },
  aiStatNumber: {
    fontSize: fontSize.xl,
    fontWeight: '700',
    color: '#4A90E2',
    marginBottom: spacing.xs / 2,
  },
  aiStatLabel: {
    fontSize: fontSize.sm,
    color: '#666666',
    textAlign: 'center',
  },

  // 智能分析状态 (第二行)
  aiStatusSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: spacing.sm,
  },
  statusItem: {
    alignItems: 'center',
    flex: 1,
  },
  statusLabel: {
    fontSize: fontSize.sm,
    color: '#666666',
    marginBottom: spacing.xs / 2,
  },
  statusValue: {
    fontSize: fontSize.sm,
    fontWeight: '600',
    color: '#4CAF50',
  },



  // Tab导航
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginTop: spacing.sm,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#FF6B35',
  },
  tabText: {
    fontSize: fontSize.base,
    color: '#666666',
  },
  activeTabText: {
    color: '#FF6B35',
    fontWeight: '600',
  },
  tabCount: {
    fontSize: fontSize.sm,
    color: '#999999',
  },
  activeTabCount: {
    color: '#FF6B35',
    fontWeight: '600',
  },
  searchButton: {
    marginLeft: 'auto',
    padding: spacing.sm,
    borderRadius: borderRadius.md,
    backgroundColor: '#F5F5F5',
  },

  // 需求项 (参考主流APP紧凑设计)
  demandItem: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs, // 进一步减少垂直内边距
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0', // 只保留底部分割线
  },
  demandHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs, // 减少间距
  },

  // 面积和预算同行显示
  demandInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  demandArea: {
    fontSize: fontSize.sm,
    color: '#666666',
  },
  demandTypeTag: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    borderRadius: borderRadius.sm,
    marginRight: spacing.sm,
  },
  demandTypeText: {
    color: '#FFFFFF',
    fontSize: fontSize.xs,
    fontWeight: '600',
  },
  demandTitle: {
    fontSize: fontSize.base,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  demandDescription: {
    fontSize: fontSize.sm,
    color: '#666666',
    marginBottom: spacing.sm,
    lineHeight: fontSize.sm * 1.4,
  },
  demandBudget: {
    fontSize: fontSize.sm,
    color: '#FF6B35', // 橙色预算
    fontWeight: '600',
  },
  demandLocation: {
    fontSize: fontSize.sm,
    color: '#666666',
    marginBottom: spacing.xs, // 减少间距
  },
  demandStats: {
    flexDirection: 'row',
    justifyContent: 'space-between', // 左右分布
    alignItems: 'center',
    marginBottom: spacing.xs, // 减少间距
  },
  statsLeft: {
    flexDirection: 'row',
  },
  demandStat: {
    fontSize: fontSize.xs, // 缩小字体
    color: '#999999', // 更浅的颜色
    marginRight: spacing.md, // 减少间距
  },
  smartTime: {
    fontSize: fontSize.xs, // 小字体
    color: '#CCCCCC', // 浅灰色
    fontWeight: '400',
  },
  demandActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: spacing.xs, // 减少顶部间距
    // 移除上方分割线，只保留卡片底部分割线
  },
  actionButton: {
    paddingHorizontal: spacing.sm, // 减少水平内边距
    paddingVertical: spacing.xs, // 减少垂直内边距
  },
  actionButtonText: {
    fontSize: fontSize.xs, // 缩小字体
    color: '#666666',
  },

  // 🏗️ 企业级架构：PagerView样式
  pagerView: {
    flex: 1,
    backgroundColor: '#FFFFFF', // PagerView白色背景
  },
  pageContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF', // 页面容器白色背景
  },
  listContainer: {
    paddingBottom: spacing.lg, // 只保留底部间距，移除水平边距
  },

  // 🏗️ 企业级架构：加载和空状态样式
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  loadingText: {
    fontSize: fontSize.sm,
    color: '#666666',
    marginTop: spacing.sm,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl * 2,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: spacing.lg,
  },
  emptyTitle: {
    fontSize: fontSize.lg,
    fontWeight: '600',
    color: '#333333',
    marginBottom: spacing.sm,
  },
  emptyDescription: {
    fontSize: fontSize.sm,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default MyDemandsScreen;