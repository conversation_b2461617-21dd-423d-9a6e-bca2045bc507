/**
 * 用户中心数据转换器
 * 继承BaseTransformer，实现用户中心的数据转换逻辑
 * 符合企业级架构规范，目标：120行
 */
import { BaseTransformer } from '../../../../../shared/services/dataTransform/core/BaseTransformer';
import { z } from 'zod';
import type {
  UserInfo,
  UserInfoAPI,
  TenantBuyerStats,
  TenantBuyerStatsAPI,
  LandlordStats,
  LandlordStatsAPI,
  OrderCounts,
  OrderCountsAPI,
  TransformResult,
  TransformOptions,
  UserInfoSchema,
  TenantBuyerStatsSchema,
  LandlordStatsSchema,
  OrderCountsSchema,
} from '../types/profile.types';

/**
 * 用户信息转换器
 */
export class UserInfoTransformer extends BaseTransformer<UserInfo, UserInfoAPI> {
  constructor() {
    super('UserInfoTransformer', '1.0.0');
  }

  protected initializeValidationRules(): void {
    this.addValidationRule('userInfo', {
      name: 'userInfo',
      schema: UserInfoSchema,
      required: true
    });
  }

  toAPI(input: UserInfo, options?: TransformOptions): TransformResult<UserInfoAPI> {
    return this.safeTransform(() => {
      // 输入验证
      if (options?.validateInput !== false) {
        const validationResult = this.validateInput(input, 'userInfo');
        if (!validationResult.success) {
          throw new Error(`输入验证失败: ${validationResult.error}`);
        }
      }

      const output: UserInfoAPI = {
        user_id: input.id,
        phone_number: input.phoneNumber,
        user_name: input.userName,
        user_avatar: input.avatar,
        is_verified: input.isVerified,
        user_type: input.userType,
        created_at: input.createdAt.toISOString(),
        updated_at: input.updatedAt.toISOString(),
      };

      return output;
    }, 'UserInfoTransformer.toAPI', options);
  }

  fromAPI(apiData: UserInfoAPI, options?: TransformOptions): TransformResult<UserInfo> {
    return this.safeTransform(() => {
      const input: UserInfo = {
        id: apiData.user_id,
        phoneNumber: apiData.phone_number,
        userName: apiData.user_name,
        avatar: apiData.user_avatar,
        isVerified: apiData.is_verified,
        userType: apiData.user_type,
        createdAt: new Date(apiData.created_at),
        updatedAt: new Date(apiData.updated_at),
      };

      return input;
    }, 'UserInfoTransformer.fromAPI', options);
  }
}

/**
 * 租买统计转换器
 */
export class TenantBuyerStatsTransformer extends BaseTransformer<TenantBuyerStats, TenantBuyerStatsAPI> {
  constructor() {
    super('TenantBuyerStatsTransformer', '1.0.0');
  }

  protected initializeValidationRules(): void {
    this.addValidationRule('tenantBuyerStats', {
      name: 'tenantBuyerStats',
      schema: TenantBuyerStatsSchema,
      required: true
    });
  }

  toAPI(input: TenantBuyerStats, options?: TransformOptions): TransformResult<TenantBuyerStatsAPI> {
    return this.safeTransform(() => {
      const output: TenantBuyerStatsAPI = {
        rental_demands: input.rentalDemands,
        purchase_demands: input.purchaseDemands,
        interested_properties: input.interestedProperties,
        total_exposure: input.totalExposure,
        total_matches: input.totalMatches,
      };

      return output;
    }, 'TenantBuyerStatsTransformer.toAPI', options);
  }

  fromAPI(apiData: TenantBuyerStatsAPI, options?: TransformOptions): TransformResult<TenantBuyerStats> {
    return this.safeTransform(() => {
      const input: TenantBuyerStats = {
        rentalDemands: apiData.rental_demands,
        purchaseDemands: apiData.purchase_demands,
        interestedProperties: apiData.interested_properties,
        totalExposure: apiData.total_exposure,
        totalMatches: apiData.total_matches,
      };

      return input;
    }, 'TenantBuyerStatsTransformer.fromAPI', options);
  }
}

/**
 * 业主统计转换器
 */
export class LandlordStatsTransformer extends BaseTransformer<LandlordStats, LandlordStatsAPI> {
  constructor() {
    super('LandlordStatsTransformer', '1.0.0');
  }

  protected initializeValidationRules(): void {
    this.addValidationRule('landlordStats', {
      name: 'landlordStats',
      schema: LandlordStatsSchema,
      required: true
    });
  }

  toAPI(input: LandlordStats, options?: TransformOptions): TransformResult<LandlordStatsAPI> {
    return this.safeTransform(() => {
      const output: LandlordStatsAPI = {
        rental_properties: input.rentalProperties,
        sale_properties: input.saleProperties,
        total_favorites: input.totalFavorites,
        total_inquiries: input.totalInquiries,
        total_views: input.totalViews,
        average_rating: input.averageRating,
      };

      return output;
    }, 'LandlordStatsTransformer.toAPI', options);
  }

  fromAPI(apiData: LandlordStatsAPI, options?: TransformOptions): TransformResult<LandlordStats> {
    return this.safeTransform(() => {
      const input: LandlordStats = {
        rentalProperties: apiData.rental_properties,
        saleProperties: apiData.sale_properties,
        totalFavorites: apiData.total_favorites,
        totalInquiries: apiData.total_inquiries,
        totalViews: apiData.total_views,
        averageRating: apiData.average_rating,
      };

      return input;
    }, 'LandlordStatsTransformer.fromAPI', options);
  }
}

/**
 * 订单统计转换器
 */
export class OrderCountsTransformer extends BaseTransformer<OrderCounts, OrderCountsAPI> {
  constructor() {
    super('OrderCountsTransformer', '1.0.0');
  }

  protected initializeValidationRules(): void {
    this.addValidationRule('orderCounts', {
      name: 'orderCounts',
      schema: OrderCountsSchema,
      required: true
    });
  }

  toAPI(input: OrderCounts, options?: TransformOptions): TransformResult<OrderCountsAPI> {
    return this.safeTransform(() => {
      const output: OrderCountsAPI = {
        favorite_count: input.favoriteCount,
        appointment_count: input.appointmentCount,
        order_count: input.orderCount,
        tenant_resource_count: input.tenantResourceCount,
        completed_orders: input.completedOrders,
        pending_orders: input.pendingOrders,
      };

      return output;
    }, 'OrderCountsTransformer.toAPI', options);
  }

  fromAPI(apiData: OrderCountsAPI, options?: TransformOptions): TransformResult<OrderCounts> {
    return this.safeTransform(() => {
      const input: OrderCounts = {
        favoriteCount: apiData.favorite_count,
        appointmentCount: apiData.appointment_count,
        orderCount: apiData.order_count,
        tenantResourceCount: apiData.tenant_resource_count,
        completedOrders: apiData.completed_orders,
        pendingOrders: apiData.pending_orders,
      };

      return input;
    }, 'OrderCountsTransformer.fromAPI', options);
  }
}
