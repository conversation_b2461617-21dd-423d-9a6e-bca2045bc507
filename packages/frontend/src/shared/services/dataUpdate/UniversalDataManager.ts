/**
 * UniversalDataManager - 通用数据管理器
 * @fileoverview 统一管理所有数据更新操作
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🎯 功能：
 * - 统一的数据更新入口
 * - 自动处理乐观更新、API调用、缓存失效
 * - 支持错误回滚和重试
 */

import { EventBusService } from './EventBusService';
import { CacheInvalidationService } from './CacheInvalidationService';
import { OptimisticUpdateService } from './OptimisticUpdateService';

// API服务导入
import { DemandAPI } from '../../../domains/demand/services/demandAPI';
import { PropertyAPI } from '../../../domains/property/services/propertyAPI';

// 类型定义
export type EntityType = 'demand' | 'property' | 'user';

export interface UpdateEntityConfig {
  entityType: EntityType;
  entityId: string;
  updates: any;
  optimistic?: boolean;
  skipCache?: boolean;
  source?: string;
}

export interface StatusUpdateConfig {
  entityType: EntityType;
  entityId: string;
  newStatus: string;
  oldStatus?: string;
  reason?: string;
  optimistic?: boolean;
  source?: string;
}

export interface UpdateResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  rollbackId?: string;
}

/**
 * 通用数据管理器类 - 单例模式
 */
class UniversalDataManagerClass {
  private static instance: UniversalDataManagerClass | null = null;
  private isInitialized = false;

  private constructor() {
    // 私有构造函数，防止外部实例化
  }

  /**
   * 获取单例实例（按需初始化）
   */
  public static getInstance(): UniversalDataManagerClass {
    if (!UniversalDataManagerClass.instance) {
      UniversalDataManagerClass.instance = new UniversalDataManagerClass();
      UniversalDataManagerClass.instance.initialize();
    }
    return UniversalDataManagerClass.instance;
  }

  /**
   * 初始化服务
   */
  private initialize(): void {
    if (this.isInitialized) return;

    console.log('🌐 [UniversalDataManager] 按需初始化通用数据管理器');
    this.isInitialized = true;
  }

  /**
   * 通用实体更新方法
   */
  async updateEntity<T = any>(
    config: UpdateEntityConfig
  ): Promise<UpdateResult<T>> {
    const {
      entityType,
      entityId,
      updates,
      optimistic = true,
      skipCache = false,
      source,
    } = config;

    console.log(
      `🌐 [UniversalDataManager] 更新实体: ${entityType}/${entityId}`,
      updates
    );

    let rollbackId: string | undefined;

    try {
      // 1. 乐观更新（如果启用）
      if (optimistic) {
        try {
          // 需要从Hook中获取QueryClient，暂时跳过乐观更新
          console.log(
            `🌐 [UniversalDataManager] 跳过乐观更新（需要QueryClient）`
          );
          // const optimisticService = OptimisticUpdateService.getInstance();
          // rollbackId = await optimisticService.updateStatus(
          //   entityType,
          //   entityId,
          //   updates.status || updates.newStatus,
          //   updates
          // );
        } catch (error) {
          console.warn(`🌐 [UniversalDataManager] 乐观更新失败:`, error);
          // 乐观更新失败不影响后续流程
        }
      }

      // 2. API调用
      const apiResult = await this.callEntityAPI(entityType, entityId, updates);

      if (!apiResult.success) {
        // API调用失败，回滚乐观更新
        if (rollbackId) {
          const optimisticService = OptimisticUpdateService.getInstance();
          await optimisticService.rollbackUpdate(rollbackId);
        }
        return {
          success: false,
          error: apiResult.error || 'API调用失败',
        };
      }

      // 3. 确认乐观更新（用真实数据）
      if (rollbackId) {
        const optimisticService = OptimisticUpdateService.getInstance();
        optimisticService.confirmUpdate(rollbackId, apiResult.data);
      }

      // 4. 缓存失效（如果未跳过）
      if (!skipCache) {
        try {
          // 需要从Hook中获取QueryClient，暂时跳过缓存失效
          console.log(
            `🌐 [UniversalDataManager] 跳过缓存失效（需要QueryClient）`
          );
          // const cacheService = CacheInvalidationService.getInstance();
          // await cacheService.invalidateByEntity(
          //   entityType,
          //   entityId,
          //   updates.status ? 'statusChange' : 'update'
          // );
        } catch (error) {
          console.warn(`🌐 [UniversalDataManager] 缓存失效失败:`, error);
        }
      }

      // 5. 发布事件
      const eventBusService = EventBusService.getInstance();
      await eventBusService.emit('entityUpdated', {
        entityType,
        entityId,
        action: updates.status ? 'statusChange' : 'update',
        data: apiResult.data,
        source,
      });

      console.log(
        `🌐 [UniversalDataManager] 实体更新成功: ${entityType}/${entityId}`
      );

      return {
        success: true,
        data: apiResult.data,
        rollbackId,
      };
    } catch (error) {
      console.error(`🌐 [UniversalDataManager] 实体更新失败:`, error);

      // 回滚乐观更新
      if (rollbackId) {
        const optimisticService = OptimisticUpdateService.getInstance();
        await optimisticService.rollbackUpdate(rollbackId);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  /**
   * 状态更新专用方法（上架/下架）
   */
  async updateStatus(config: StatusUpdateConfig): Promise<UpdateResult> {
    const {
      entityType,
      entityId,
      newStatus,
      oldStatus,
      reason,
      optimistic = true,
      source,
    } = config;

    console.log(
      `🌐 [UniversalDataManager] 状态更新: ${entityType}/${entityId} ${oldStatus} → ${newStatus}`
    );

    // 构建更新数据
    const updates = {
      status: newStatus,
      ...(reason && { reason }),
      updated_at: new Date().toISOString(),
    };

    // 调用通用更新方法
    const result = await this.updateEntity({
      entityType,
      entityId,
      updates,
      optimistic,
      source,
    });

    // 如果成功，发布状态更新事件
    if (result.success && oldStatus) {
      const eventBusService = EventBusService.getInstance();
      await eventBusService.emitStatusUpdate(
        entityType,
        entityId,
        oldStatus,
        newStatus,
        result.data
      );
    }

    return result;
  }

  /**
   * 批量状态更新
   */
  async batchUpdateStatus(
    configs: StatusUpdateConfig[]
  ): Promise<UpdateResult[]> {
    console.log(
      `🌐 [UniversalDataManager] 批量状态更新: ${configs.length}个实体`
    );

    const results = await Promise.allSettled(
      configs.map(config => this.updateStatus(config))
    );

    return results.map(result =>
      result.status === 'fulfilled'
        ? result.value
        : { success: false, error: result.reason?.message || '批量更新失败' }
    );
  }

  /**
   * 上架操作
   */
  async publishEntity(
    entityType: EntityType,
    entityId: string,
    source?: string
  ): Promise<UpdateResult> {
    // 🔧 修复：根据实体类型使用不同的状态枚举
    const inactiveStatus = entityType === 'demand' ? 'OFFLINE' : 'INACTIVE';

    return this.updateStatus({
      entityType,
      entityId,
      oldStatus: inactiveStatus,
      newStatus: 'ACTIVE',
      source,
    });
  }

  /**
   * 下架操作
   */
  async unpublishEntity(
    entityType: EntityType,
    entityId: string,
    reason?: string,
    source?: string
  ): Promise<UpdateResult> {
    // 🔧 修复：根据实体类型使用不同的状态枚举
    const inactiveStatus = entityType === 'demand' ? 'OFFLINE' : 'INACTIVE';

    return this.updateStatus({
      entityType,
      entityId,
      oldStatus: 'ACTIVE',
      newStatus: inactiveStatus,
      reason,
      source,
    });
  }

  /**
   * 获取服务状态
   */
  getServiceStatus(): {
    initialized: boolean;
    cacheStats: any;
    rollbackStats: any;
    eventStats: any;
  } {
    return {
      initialized: this.isInitialized,
      cacheStats: CacheInvalidationService.getInstance().getCacheStats(),
      rollbackStats: OptimisticUpdateService.getInstance().getRollbackStats(),
      eventStats: EventBusService.getInstance().getListenerStats(),
    };
  }

  // === 私有方法 ===

  /**
   * 调用对应的API
   */
  private async callEntityAPI(
    entityType: EntityType,
    entityId: string,
    updates: any
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      switch (entityType) {
        case 'demand':
          if (updates.status) {
            // 状态更新
            const result = await DemandAPI.updateDemandStatus(
              entityId,
              updates.status
            );
            return {
              success: result.success,
              data: result.data,
              error: result.message,
            };
          } else {
            // 通用更新
            const result = await DemandAPI.updateDemand(entityId, updates);
            return {
              success: result.success,
              data: result.data,
              error: result.message,
            };
          }

        case 'property':
          if (updates.status) {
            // 状态更新
            const result = await PropertyAPI.updatePropertyStatus(
              entityId,
              updates.status
            );
            return {
              success: result.success,
              data: result.data,
              error: result.message,
            };
          } else {
            // 通用更新
            const result = await PropertyAPI.updateProperty(entityId, updates);
            return {
              success: result.success,
              data: result.data,
              error: result.message,
            };
          }

        default:
          return { success: false, error: `不支持的实体类型: ${entityType}` };
      }
    } catch (error) {
      console.error(`🌐 [UniversalDataManager] API调用失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'API调用失败',
      };
    }
  }
}

// 导出单例访问器
export const UniversalDataManager = {
  getInstance: () => UniversalDataManagerClass.getInstance(),
  // 兼容性方法，直接代理到实例
  updateEntity: (...args: any[]) =>
    UniversalDataManagerClass.getInstance().updateEntity(...args),
  updateStatus: (...args: any[]) =>
    UniversalDataManagerClass.getInstance().updateStatus(...args),
  batchUpdateStatus: (...args: any[]) =>
    UniversalDataManagerClass.getInstance().batchUpdateStatus(...args),
  publishEntity: (...args: any[]) =>
    UniversalDataManagerClass.getInstance().publishEntity(...args),
  unpublishEntity: (...args: any[]) =>
    UniversalDataManagerClass.getInstance().unpublishEntity(...args),
  getServiceStatus: () =>
    UniversalDataManagerClass.getInstance().getServiceStatus(),
};

// React Hook 用于在组件中使用通用数据管理器
import { useCallback, useMemo } from 'react';

/**
 * React Hook：使用通用数据管理器
 */
export const useUniversalDataManager = () => {
  // 按需获取服务实例
  const serviceInstance = useMemo(() => {
    return UniversalDataManagerClass.getInstance();
  }, []);

  const updateEntity = useCallback(
    (config: UpdateEntityConfig) => {
      return serviceInstance.updateEntity(config);
    },
    [serviceInstance]
  );

  const updateStatus = useCallback(
    (config: StatusUpdateConfig) => {
      return serviceInstance.updateStatus(config);
    },
    [serviceInstance]
  );

  const publishEntity = useCallback(
    (entityType: EntityType, entityId: string, source?: string) => {
      return serviceInstance.publishEntity(entityType, entityId, source);
    },
    [serviceInstance]
  );

  const unpublishEntity = useCallback(
    (
      entityType: EntityType,
      entityId: string,
      reason?: string,
      source?: string
    ) => {
      return serviceInstance.unpublishEntity(
        entityType,
        entityId,
        reason,
        source
      );
    },
    [serviceInstance]
  );

  return {
    updateEntity,
    updateStatus,
    publishEntity,
    unpublishEntity,
    batchUpdateStatus: serviceInstance.batchUpdateStatus.bind(serviceInstance),
    getServiceStatus: serviceInstance.getServiceStatus.bind(serviceInstance),
  };
};
