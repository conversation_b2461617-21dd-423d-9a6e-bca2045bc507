/**
 * 企业级认证服务 - 业务逻辑层
 * 
 * 参考Netflix、Spotify等的服务层设计：
 * 1. 单一职责原则 - 只处理认证相关的业务逻辑
 * 2. 错误处理标准化 - 统一的错误处理和重试机制
 * 3. 类型安全 - 完整的TypeScript支持
 * 4. 可测试性 - 易于单元测试
 * 5. 可扩展性 - 支持多种认证方式
 */

import { User, LoginResponse, RefreshTokenResponse, SendSMSResponse } from '../types/auth.types';
import apiClient from './client';

// 🏢 企业级接口定义
export interface LoginWithSMSRequest {
  phone_number: string;
  verification_code: string;
  code_type?: string;
}

export interface LoginWithPasswordRequest {
  phone_number: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  tokenExpiry: number;
  refreshToken?: string;
}

export interface SendSMSRequest {
  phone_number: string;
  code_type: 'login' | 'register' | 'reset_password';
}

/**
 * 🏭 企业级认证错误类
 * 提供结构化的错误信息，便于UI层处理
 */
export class AuthError extends Error {
  constructor(
    public code: string,
    message: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = 'AuthError';
  }
  
  static fromApiError(error: any): AuthError {
    if (error.response?.status === 429) {
      return new AuthError('RATE_LIMITED', '请求过于频繁，请稍后再试', 429, error);
    }
    
    if (error.response?.status === 401) {
      return new AuthError('UNAUTHORIZED', '用户名或密码错误', 401, error);
    }
    
    if (error.response?.status === 403) {
      return new AuthError('FORBIDDEN', '账户已被禁用', 403, error);
    }
    
    if (error.code === 'NETWORK_ERROR') {
      return new AuthError('NETWORK_ERROR', '网络连接失败，请检查网络设置', 0, error);
    }
    
    return new AuthError(
      'UNKNOWN_ERROR', 
      error.message || '未知错误', 
      error.response?.status,
      error
    );
  }
}

/**
 * 🏢 企业级认证服务类
 * 
 * 设计原则：
 * - 所有方法都返回Promise，支持async/await
 * - 统一的错误处理和日志记录
 * - 自动重试机制（针对网络错误）
 * - 请求去重（防止重复提交）
 */
class AuthService {
  private pendingRequests = new Map<string, Promise<any>>();
  
  /**
   * 📱 发送短信验证码
   * 包含自动重试和去重逻辑
   */
  async sendSMSCode(request: SendSMSRequest): Promise<{ message: string }> {
    const requestKey = `sms_${request.phone_number}_${request.code_type}`;
    
    // 防止重复请求
    if (this.pendingRequests.has(requestKey)) {
      console.log('[AuthService] 📱 短信请求已在进行中，等待结果');
      return this.pendingRequests.get(requestKey);
    }
    
    const requestPromise = this._sendSMSCodeInternal(request);
    this.pendingRequests.set(requestKey, requestPromise);
    
    try {
      const result = await requestPromise;
      return result;
    } finally {
      // 请求完成后清理
      this.pendingRequests.delete(requestKey);
    }
  }
  
  private async _sendSMSCodeInternal(request: SendSMSRequest, retryCount = 0): Promise<{ message: string }> {
    try {
      console.log('[AuthService] 📱 发送短信验证码:', { 
        phone: request.phone_number, 
        type: request.code_type 
      });
      
      const response = await apiClient.post('/auth/request-code', {
        phone_number: request.phone_number,
        code_type: request.code_type || 'login',
      });
      
      console.log('[AuthService] ✅ 短信发送成功');
      return { message: response.data.message || '验证码已发送' };
      
    } catch (error: any) {
      console.error('[AuthService] ❌ 短信发送失败:', error);
      
      // 网络错误自动重试（最多3次）
      if (retryCount < 3 && this._isNetworkError(error)) {
        console.log(`[AuthService] 🔄 网络错误，第${retryCount + 1}次重试`);
        await this._delay(1000 * (retryCount + 1)); // 递增延迟
        return this._sendSMSCodeInternal(request, retryCount + 1);
      }
      
      throw AuthError.fromApiError(error);
    }
  }
  
  /**
   * 🔐 短信验证码登录
   */
  async loginWithSMS(request: LoginWithSMSRequest): Promise<AuthResponse> {
    try {
      console.log('[AuthService] 🔐 短信验证码登录:', { phone: request.phone_number });
      
      const response = await apiClient.post('/auth/login/sms', {
        phone_number: request.phone_number,
        verification_code: request.verification_code,
        code_type: request.code_type || 'login',
      });
      
      const authResponse = this._transformAuthResponse(response.data);
      console.log('[AuthService] ✅ 短信登录成功');
      
      return authResponse;
      
    } catch (error: any) {
      console.error('[AuthService] ❌ 短信登录失败:', error);
      throw AuthError.fromApiError(error);
    }
  }
  
  /**
   * 🔑 密码登录
   */
  async loginWithPassword(request: LoginWithPasswordRequest): Promise<AuthResponse> {
    try {
      console.log('[AuthService] 🔑 密码登录:', { phone: request.phone_number });
      
      const response = await apiClient.post('/auth/login/phone', {
        phone_number: request.phone_number,
        password: request.password,
      });
      
      const authResponse = this._transformAuthResponse(response.data);
      console.log('[AuthService] ✅ 密码登录成功');
      
      return authResponse;
      
    } catch (error: any) {
      console.error('[AuthService] ❌ 密码登录失败:', error);
      throw AuthError.fromApiError(error);
    }
  }
  
  /**
   * 🔄 刷新访问令牌
   */
  async refreshToken(currentToken: string): Promise<AuthResponse> {
    try {
      console.log('[AuthService] 🔄 刷新访问令牌');
      
      // 获取当前用户信息（包含最新的token）
      const response = await apiClient.get('/users/me', {
        headers: {
          Authorization: `Bearer ${currentToken}`,
        },
      });
      
      // 注意：这里假设服务端在/users/me响应中包含了新的token
      // 如果服务端有专门的refresh endpoint，请相应调整
      const authResponse: AuthResponse = {
        user: response.data,
        accessToken: currentToken, // 如果服务端返回新token，使用新token
        tokenExpiry: Date.now() + (24 * 60 * 60 * 1000), // 24小时后过期
      };
      
      console.log('[AuthService] ✅ Token刷新成功');
      return authResponse;
      
    } catch (error: any) {
      console.error('[AuthService] ❌ Token刷新失败:', error);
      throw AuthError.fromApiError(error);
    }
  }
  
  /**
   * 🚪 登出
   */
  async logout(): Promise<void> {
    try {
      console.log('[AuthService] 🚪 用户登出');
      
      // 如果服务端有登出接口，调用它
      // await apiClient.post('/auth/logout');
      
      console.log('[AuthService] ✅ 登出成功');
      
    } catch (error: any) {
      console.error('[AuthService] ❌ 登出失败:', error);
      // 登出失败不应该阻止客户端清理状态
      console.warn('[AuthService] 服务端登出失败，继续客户端清理');
    }
  }
  
  /**
   * 👤 获取用户信息
   */
  async getCurrentUser(token: string): Promise<User> {
    try {
      console.log('[AuthService] 👤 获取用户信息');
      
      const response = await apiClient.get('/users/me', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      
      console.log('[AuthService] ✅ 用户信息获取成功');
      return response.data;
      
    } catch (error: any) {
      console.error('[AuthService] ❌ 用户信息获取失败:', error);
      throw AuthError.fromApiError(error);
    }
  }
  
  /**
   * 🔧 内部工具方法
   */
  
  private _transformAuthResponse(apiResponse: any): AuthResponse {
    // 计算token过期时间（如果服务端没有提供）
    const tokenExpiry = apiResponse.expires_at 
      ? new Date(apiResponse.expires_at).getTime()
      : Date.now() + (24 * 60 * 60 * 1000); // 默认24小时
    
    return {
      user: apiResponse.user || apiResponse, // 兼容不同的API响应格式
      accessToken: apiResponse.access_token,
      tokenExpiry,
      refreshToken: apiResponse.refresh_token,
    };
  }
  
  private _isNetworkError(error: any): boolean {
    return !error.response || error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED';
  }
  
  private _delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 🏭 导出单例实例
 */
export const authService = new AuthService();

/**
 * 🧪 用于测试的工厂方法
 */
export const createAuthService = () => new AuthService();

export default authService;