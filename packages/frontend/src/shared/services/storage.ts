/**
 * 企业级存储服务 - 数据持久化层
 * 
 * 参考主流APP的存储策略：
 * 1. 安全存储 - 敏感数据加密存储
 * 2. 降级策略 - SecureStore不可用时自动降级
 * 3. 数据验证 - 存储和读取时的数据完整性检查
 * 4. 错误恢复 - 存储失败时的自动重试和恢复
 * 5. 性能优化 - 内存缓存减少I/O操作
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { User } from '../types/auth.types';

// 🏢 企业级类型定义
export interface StoredAuthData {
  user: User;
  accessToken: string;
  tokenExpiry: number;
  refreshToken?: string;
  storedAt: number; // 存储时间戳，用于数据有效性检查
}

export interface StorageOptions {
  secure?: boolean; // 是否使用安全存储
  ttl?: number; // 数据过期时间（毫秒）
  retryCount?: number; // 失败重试次数
}

/**
 * 🔐 存储服务类
 * 
 * 设计原则：
 * - 自动选择最安全的存储方式
 * - 内存缓存提升性能
 * - 数据完整性验证
 * - 优雅的错误处理
 */
class StorageService {
  private memoryCache = new Map<string, any>();
  private secureStoreAvailable: boolean | null = null;
  
  // 🔑 存储键名常量
  private readonly KEYS = {
    AUTH_DATA: 'auth_data',
    USER_PREFERENCES: 'user_preferences',
    APP_CONFIG: 'app_config',
    FLOATING_LOGIN_STATE: 'floating_login_state',
  } as const;
  
  /**
   * 🔐 存储认证数据
   * 自动选择最安全的存储方式
   */
  async storeAuthData(authData: Omit<StoredAuthData, 'storedAt'>): Promise<void> {
    try {
      console.log('[Storage] 🔐 存储认证数据');
      
      const dataToStore: StoredAuthData = {
        ...authData,
        storedAt: Date.now(),
      };
      
      // 验证数据完整性
      this._validateAuthData(dataToStore);
      
      const serializedData = JSON.stringify(dataToStore);
      
      // 优先使用SecureStore存储敏感信息
      if (await this._isSecureStoreAvailable()) {
        await this._secureStoreWithRetry(this.KEYS.AUTH_DATA, serializedData);
        console.log('[Storage] ✅ 认证数据已安全存储');
      } else {
        // 降级到AsyncStorage
        console.warn('[Storage] ⚠️ SecureStore不可用，降级到AsyncStorage');
        await AsyncStorage.setItem(this.KEYS.AUTH_DATA, serializedData);
      }
      
      // 更新内存缓存
      this.memoryCache.set(this.KEYS.AUTH_DATA, dataToStore);
      
    } catch (error) {
      console.error('[Storage] ❌ 认证数据存储失败:', error);
      throw new Error(`认证数据存储失败: ${error.message}`);
    }
  }
  
  /**
   * 📖 获取存储的认证数据
   * 优先从内存缓存读取，提升性能
   */
  async getStoredAuthData(): Promise<StoredAuthData | null> {
    try {
      console.log('[Storage] 📖 读取认证数据');
      
      // 优先从内存缓存读取
      const cachedData = this.memoryCache.get(this.KEYS.AUTH_DATA);
      if (cachedData && this._isDataValid(cachedData)) {
        console.log('[Storage] 📝 内存缓存命中 - auth_data');
        return cachedData;
      }
      
      // 从持久化存储读取
      let serializedData: string | null = null;
      
      if (await this._isSecureStoreAvailable()) {
        serializedData = await SecureStore.getItemAsync(this.KEYS.AUTH_DATA);
      } else {
        serializedData = await AsyncStorage.getItem(this.KEYS.AUTH_DATA);
      }
      
      if (!serializedData) {
        console.log('[Storage] 📭 没有找到存储的认证数据');
        return null;
      }
      
      const authData: StoredAuthData = JSON.parse(serializedData);
      
      // 验证数据完整性和有效性
      if (!this._isDataValid(authData)) {
        console.warn('[Storage] ⚠️ 存储的认证数据已过期或无效');
        await this.clearAuthData();
        return null;
      }
      
      // 更新内存缓存
      this.memoryCache.set(this.KEYS.AUTH_DATA, authData);
      
      console.log('[Storage] ✅ 认证数据读取成功');
      return authData;
      
    } catch (error) {
      console.error('[Storage] ❌ 认证数据读取失败:', error);
      
      // 数据损坏时自动清理
      await this.clearAuthData();
      return null;
    }
  }
  
  /**
   * 🗑️ 清理认证数据
   * 同时清理所有存储位置
   */
  async clearAuthData(): Promise<void> {
    try {
      console.log('[Storage] 🗑️ 清理认证数据');
      
      // 清理SecureStore
      if (await this._isSecureStoreAvailable()) {
        await SecureStore.deleteItemAsync(this.KEYS.AUTH_DATA);
      }
      
      // 清理AsyncStorage
      await AsyncStorage.removeItem(this.KEYS.AUTH_DATA);
      
      // 清理内存缓存
      this.memoryCache.delete(this.KEYS.AUTH_DATA);
      
      console.log('[Storage] ✅ 认证数据清理完成');
      
    } catch (error) {
      console.error('[Storage] ❌ 认证数据清理失败:', error);
      // 清理失败不应该阻止应用继续运行
    }
  }
  
  /**
   * 🎨 存储UI状态数据
   * 非敏感数据使用AsyncStorage
   */
  async storeUIState(key: string, data: any): Promise<void> {
    try {
      const serializedData = JSON.stringify({
        data,
        storedAt: Date.now(),
      });
      
      await AsyncStorage.setItem(`ui_${key}`, serializedData);
      this.memoryCache.set(`ui_${key}`, data);
      
      console.log(`[Storage] 🎨 UI状态已存储: ${key}`);
      
    } catch (error) {
      console.error(`[Storage] ❌ UI状态存储失败: ${key}`, error);
    }
  }
  
  /**
   * 🎭 获取UI状态数据
   */
  async getUIState<T>(key: string, defaultValue?: T): Promise<T | null> {
    try {
      // 优先从内存读取
      const cachedData = this.memoryCache.get(`ui_${key}`);
      if (cachedData !== undefined) {
        return cachedData;
      }
      
      const serializedData = await AsyncStorage.getItem(`ui_${key}`);
      if (!serializedData) {
        return defaultValue || null;
      }
      
      const { data, storedAt } = JSON.parse(serializedData);
      
      // 检查数据是否过期（默认30天）
      const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
      if (Date.now() - storedAt > maxAge) {
        await AsyncStorage.removeItem(`ui_${key}`);
        return defaultValue || null;
      }
      
      this.memoryCache.set(`ui_${key}`, data);
      return data;
      
    } catch (error) {
      console.error(`[Storage] ❌ UI状态读取失败: ${key}`, error);
      return defaultValue || null;
    }
  }
  
  /**
   * 🧹 清理所有存储数据
   * 用于用户登出或重置应用状态
   */
  async clearAllData(): Promise<void> {
    try {
      console.log('[Storage] 🧹 清理所有存储数据');
      
      // 清理认证数据
      await this.clearAuthData();
      
      // 清理AsyncStorage中的应用数据
      const keys = await AsyncStorage.getAllKeys();
      const appKeys = keys.filter(key => 
        key.startsWith('ui_') || 
        key.startsWith('app_') || 
        key === this.KEYS.USER_PREFERENCES
      );
      
      if (appKeys.length > 0) {
        await AsyncStorage.multiRemove(appKeys);
      }
      
      // 清理内存缓存
      this.memoryCache.clear();
      
      console.log('[Storage] ✅ 所有数据清理完成');
      
    } catch (error) {
      console.error('[Storage] ❌ 数据清理失败:', error);
    }
  }
  
  /**
   * 🔧 内部工具方法
   */
  
  private async _isSecureStoreAvailable(): Promise<boolean> {
    if (this.secureStoreAvailable !== null) {
      return this.secureStoreAvailable;
    }
    
    try {
      // 测试SecureStore是否可用
      await SecureStore.getItemAsync('__test__');
      this.secureStoreAvailable = true;
      console.log('[Storage] ✅ SecureStore可用');
    } catch (error) {
      this.secureStoreAvailable = false;
      console.warn('[Storage] ⚠️ SecureStore不可用:', error.message);
    }
    
    return this.secureStoreAvailable;
  }
  
  private async _secureStoreWithRetry(key: string, value: string, retryCount = 0): Promise<void> {
    try {
      await SecureStore.setItemAsync(key, value);
    } catch (error) {
      if (retryCount < 3) {
        console.warn(`[Storage] SecureStore存储失败，第${retryCount + 1}次重试`);
        await this._delay(1000 * (retryCount + 1));
        return this._secureStoreWithRetry(key, value, retryCount + 1);
      }
      throw error;
    }
  }
  
  private _validateAuthData(data: StoredAuthData): void {
    if (!data.user || !data.accessToken || !data.tokenExpiry) {
      throw new Error('认证数据格式无效');
    }
    
    if (!data.user.id || !data.user.phone_number) {
      throw new Error('用户数据格式无效');
    }
  }
  
  private _isDataValid(data: StoredAuthData): boolean {
    try {
      // 检查数据结构
      this._validateAuthData(data);
      
      // 检查存储时间（数据不能超过7天）
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
      if (Date.now() - data.storedAt > maxAge) {
        console.warn('[Storage] 数据已超过最大存储时间');
        return false;
      }
      
      return true;
    } catch (error) {
      console.warn('[Storage] 数据验证失败:', error);
      return false;
    }
  }
  
  private _delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 🏭 导出单例实例
 */
export const storageService = new StorageService();

/**
 * 🎯 便捷方法导出（保持向后兼容）
 */
export const getStoredAuthData = () => storageService.getStoredAuthData();
export const storeAuthData = (data: Omit<StoredAuthData, 'storedAt'>) => storageService.storeAuthData(data);
export const clearAuthData = () => storageService.clearAuthData();

export default storageService;