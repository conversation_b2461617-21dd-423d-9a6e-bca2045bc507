/**
 * 企业级表单容器组件
 * 
 * 功能特性：
 * 1. 键盘响应式布局管理
 * 2. 自动滚动到错误字段
 * 3. 表单验证状态管理
 * 4. 统一的提交处理流程
 * 5. 防止重复提交
 * 6. 错误聚合和展示
 * 
 * 基于React Native官方指导和企业级开发最佳实践
 */

import React, { useRef, useCallback, useEffect } from 'react';
import {
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Alert,
  StatusBar,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { wp, hp } from '../../utils/responsiveUtils';
import { 
  useKeyboardAwareForm,
  UseKeyboardAwareFormOptions,
  FormFieldError 
} from '../../hooks/useKeyboardAwareForm';
import FeedbackService from '../../services/FeedbackService';

export interface EnterpriseFormContainerProps {
  /** 表单内容 */
  children: React.ReactNode;
  
  /** 键盘响应配置 */
  keyboardOptions?: UseKeyboardAwareFormOptions;
  
  /** 表单验证回调 - 返回错误数组 */
  onValidate?: () => FormFieldError[];
  
  /** 表单提交回调 */
  onSubmit?: () => Promise<void> | void;
  
  /** 是否正在提交 */
  isSubmitting?: boolean;
  
  /** 是否显示状态栏 */
  showStatusBar?: boolean;
  
  /** 状态栏样式 */
  statusBarStyle?: 'default' | 'light-content' | 'dark-content';
  
  /** 自定义容器样式 */
  containerStyle?: any;
  
  /** 自定义滚动区域样式 */
  contentStyle?: any;
  
  /** 是否启用自动验证和滚动 */
  enableAutoValidation?: boolean;
  
  /** 验证失败时的回调 */
  onValidationError?: (errors: FormFieldError[]) => void;
  
  /** 表单字段变化时的回调 */
  onFieldChange?: (fieldName: string, value: any) => void;
}

export interface EnterpriseFormContainerRef {
  /** 滚动到顶部 */
  scrollToTop: () => void;
  
  /** 滚动到底部 */
  scrollToBottom: () => void;
  
  /** 滚动到指定字段 */
  scrollToField: (fieldName: string) => void;
  
  /** 触发表单验证 */
  validateForm: () => boolean;
  
  /** 提交表单 */
  submitForm: () => Promise<void>;
  
  /** 清除所有错误 */
  clearErrors: () => void;
}

export const EnterpriseFormContainer = React.forwardRef<
  EnterpriseFormContainerRef,
  EnterpriseFormContainerProps
>(({
  children,
  keyboardOptions = {},
  onValidate,
  onSubmit,
  isSubmitting = false,
  showStatusBar = true,
  statusBarStyle = 'dark-content',
  containerStyle,
  contentStyle,
  enableAutoValidation = true,
  onValidationError,
  onFieldChange,
}, ref) => {
  
  const insets = useSafeAreaInsets();
  const submitTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // 键盘响应表单Hook
  const formHook = useKeyboardAwareForm({
    extraOffset: 20,
    enableAutoScroll: true,
    scrollAnimationDuration: 250,
    resetScrollOnKeyboardHide: false,
    ...keyboardOptions,
  });
  
  // 清理定时器
  useEffect(() => {
    return () => {
      if (submitTimeoutRef.current) {
        clearTimeout(submitTimeoutRef.current);
      }
    };
  }, []);
  
  // 表单验证
  const validateForm = useCallback((): boolean => {
    if (!onValidate) return true;
    
    try {
      const errors = onValidate();
      
      if (errors.length === 0) {
        formHook.clearFieldErrors();
        return true;
      }
      
      // 显示错误状态
      formHook.showFieldErrors(errors);
      
      // 滚动到第一个错误字段
      if (enableAutoValidation) {
        formHook.scrollToFirstError(errors);
      }
      
      // 触发验证错误回调
      onValidationError?.(errors);
      
      // 显示错误提示
      const firstError = errors[0];
      if (firstError) {
        FeedbackService.showError(firstError.message);
      }
      
      return false;
    } catch (error) {
      console.error('[EnterpriseFormContainer] 表单验证失败:', error);
      FeedbackService.showError('表单验证出现错误，请稍后重试');
      return false;
    }
  }, [onValidate, formHook, enableAutoValidation, onValidationError]);
  
  // 提交表单
  const submitForm = useCallback(async (): Promise<void> => {
    if (!onSubmit || isSubmitting) {
      console.log('[EnterpriseFormContainer] 提交已禁用或正在提交中');
      return;
    }
    
    try {
      // 先进行表单验证
      const isValid = validateForm();
      if (!isValid) {
        console.log('[EnterpriseFormContainer] 表单验证失败，终止提交');
        return;
      }
      
      console.log('[EnterpriseFormContainer] 表单验证通过，开始提交');
      
      // 防抖处理 - 防止快速重复点击
      if (submitTimeoutRef.current) {
        clearTimeout(submitTimeoutRef.current);
      }
      
      submitTimeoutRef.current = setTimeout(async () => {
        try {
          await onSubmit();
          console.log('[EnterpriseFormContainer] 表单提交成功');
        } catch (error) {
          console.error('[EnterpriseFormContainer] 表单提交失败:', error);
          
          // 如果是网络错误，显示重试选项
          if (error instanceof Error && error.message.includes('网络')) {
            Alert.alert(
              '提交失败',
              '网络连接异常，是否重试？',
              [
                { text: '取消', style: 'cancel' },
                { text: '重试', onPress: () => submitForm() }
              ]
            );
          } else {
            FeedbackService.showError(
              error instanceof Error ? error.message : '提交失败，请稍后重试'
            );
          }
        }
      }, 300);
      
    } catch (error) {
      console.error('[EnterpriseFormContainer] 提交处理出错:', error);
      FeedbackService.showError('提交处理出现错误，请稍后重试');
    }
  }, [onSubmit, isSubmitting, validateForm]);
  
  // 暴露组件方法
  React.useImperativeHandle(ref, () => ({
    scrollToTop: () => {
      formHook.scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    },
    scrollToBottom: () => {
      formHook.scrollViewRef.current?.scrollToEnd({ animated: true });
    },
    scrollToField: (fieldName: string) => {
      formHook.scrollToField(fieldName);
    },
    validateForm,
    submitForm,
    clearErrors: () => {
      formHook.clearFieldErrors();
    },
  }));
  
  // 字段变化处理
  const handleFieldChange = useCallback((fieldName: string, value: any) => {
    onFieldChange?.(fieldName, value);
    
    // 如果启用自动验证且字段有错误，则清除该字段的错误
    if (enableAutoValidation) {
      const currentErrors = formHook.getFieldErrors();
      if (currentErrors[fieldName]) {
        formHook.clearFieldErrors([fieldName]);
      }
    }
  }, [formHook, onFieldChange, enableAutoValidation]);
  
  // 容器样式
  const containerDynamicStyle = {
    ...styles.container,
    paddingTop: insets.top,
    ...containerStyle,
    ...formHook.getContainerStyle(),
  };
  
  const contentDynamicStyle = {
    ...styles.content,
    paddingBottom: Math.max(insets.bottom, hp(20)),
    ...contentStyle,
    ...formHook.getContentStyle(),
  };
  
  return (
    <KeyboardAvoidingView
      style={styles.keyboardAvoidingView}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -insets.bottom}
    >
      {/* 状态栏 */}
      {showStatusBar && (
        <StatusBar 
          barStyle={statusBarStyle} 
          backgroundColor="transparent"
          translucent
        />
      )}
      
      {/* 主容器 */}
      <View style={containerDynamicStyle}>
        {/* 滚动视图 */}
        <ScrollView
          ref={formHook.scrollViewRef}
          style={styles.scrollView}
          contentContainerStyle={contentDynamicStyle}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          scrollEventThrottle={16}
          bounces={true}
          overScrollMode="auto"
        >
          {/* 表单内容 - 通过Context传递formHook和字段变化处理函数 */}
          <FormContext.Provider value={{ 
            formHook, 
            onFieldChange: handleFieldChange,
            isSubmitting 
          }}>
            {children}
          </FormContext.Provider>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
});

// 表单上下文 - 用于传递formHook给子组件
export const FormContext = React.createContext<{
  formHook: ReturnType<typeof useKeyboardAwareForm>;
  onFieldChange: (fieldName: string, value: any) => void;
  isSubmitting: boolean;
} | null>(null);

// Hook - 获取表单上下文
export const useFormContext = () => {
  const context = React.useContext(FormContext);
  if (!context) {
    throw new Error('useFormContext must be used within EnterpriseFormContainer');
  }
  return context;
};

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: wp(16),
    paddingVertical: hp(16),
  },
});

EnterpriseFormContainer.displayName = 'EnterpriseFormContainer';

export default EnterpriseFormContainer;