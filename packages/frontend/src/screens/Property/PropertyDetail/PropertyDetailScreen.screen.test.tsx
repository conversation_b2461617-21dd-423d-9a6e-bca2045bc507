/**
 * PropertyDetailScreen Screen Test - 端到端测试
 * 
 * 测试重构后的房源详情页完整功能
 * 遵循React Native Testing Library "Screen Tests"最佳实践
 */

import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import PropertyDetailScreen from './PropertyDetailScreen';

// Mock所需的依赖
jest.mock('./hooks/usePropertyDetailLogic', () => ({
  usePropertyDetailLogic: () => ({
    // 模拟数据
    propertyId: 'test-property-123',
    currentData: {
      id: 'test-property-123',
      title: '万象城核心商铺 人流量大 可餐饮',
      media: {
        images: [
          { id: '1', url: 'https://example.com/image1.jpg', title: '房源图片1' },
          { id: '2', url: 'https://example.com/image2.jpg', title: '房源图片2' }
        ],
        videos: [],
        totalCount: 2
      },
      keyInfo: {
        area: '100',
        rent: { price: '5000', term: '月' },
        sale: { price: '200', unit: '万' }
      },
      propertyDetails: {
        location: '万象城商业中心',
        tags: ['甲级写字楼', 'CBD', '地铁口']
      }
    },
    publishedData: {
      id: 'published-123',
      title: '万象城核心商铺 人流量大 可餐饮',
      address: '万象城商业中心A座',
      images: ['https://example.com/published1.jpg'],
      tags: ['新发布', '热门']
    },
    sduiConfig: {},
    isSDUIReady: true,
    
    // 状态
    isLoading: false,
    error: null,
    isImageViewerVisible: false,
    imageViewerIndex: 0,
    isFavorited: false,
    
    // 操作方法
    handleMediaChange: jest.fn(),
    handleTabChange: jest.fn(),
    handleImagePress: jest.fn(),
    handleFavorite: jest.fn(),
    handleMessageView: jest.fn(),
    handleShare: jest.fn(),
    handleGoBack: jest.fn(),
    handleContact: jest.fn(),
    handleReserve: jest.fn(),
    refetch: jest.fn(),
    setIsImageViewerVisible: jest.fn(),
  })
}));

// Mock所需的组件
jest.mock('./components/SimpleErrorBoundary', () => {
  return function MockSimpleErrorBoundary({ children }: any) {
    return children;
  };
});

jest.mock('./components/PropertyHeader', () => ({
  PropertyHeader: ({ onGoBack, onShare, onMessageView }: any) => (
    <>
      <button testID="go-back-button" onPress={onGoBack}>返回</button>
      <button testID="share-button" onPress={onShare}>分享</button>
      <button testID="message-view-button" onPress={onMessageView}>消息</button>
    </>
  )
}));

jest.mock('./components/ReportBar', () => ({
  ReportBar: () => <div testID="report-bar">举报纠错</div>
}));

jest.mock('./components/PropertyInfoSection', () => ({
  PropertyInfoSection: ({ propertyData }: any) => (
    <div testID="property-info-section">
      <span testID="property-title">{propertyData.title}</span>
      <span testID="property-area">{propertyData.keyInfo?.area}m²</span>
    </div>
  )
}));

jest.mock('@property/components/detail', () => ({
  MediaCarousel: ({ onImagePress, onMediaChange }: any) => (
    <div testID="media-carousel">
      <button testID="image-1" onPress={() => onImagePress(0)}>图片1</button>
      <button testID="media-change" onPress={() => onMediaChange('image')}>切换媒体</button>
    </div>
  ),
  ImageViewer: ({ visible, onRequestClose }: any) => (
    visible ? (
      <div testID="image-viewer">
        <button testID="close-image-viewer" onPress={onRequestClose}>关闭</button>
      </div>
    ) : null
  ),
  DetailedInfoSection: ({ propertyData }: any) => (
    <div testID="detailed-info-section">详细信息</div>
  ),
  PropertyDetailMapSection: ({ propertyData, publishedLocation }: any) => (
    <div testID="map-section">
      <span testID="location">{publishedLocation?.address || propertyData.propertyDetails?.location}</span>
    </div>
  ),
  BottomActionBar: ({ isFavorited, onFavorite, onContact, onReserve }: any) => (
    <div testID="bottom-action-bar">
      <button testID="favorite-button" onPress={onFavorite}>
        {isFavorited ? '已收藏' : '收藏'}
      </button>
      <button testID="contact-button" onPress={onContact}>联系</button>
      <button testID="reserve-button" onPress={onReserve}>预约</button>
    </div>
  ),
  HotQuestionsSection: ({ onQuestionPress }: any) => (
    <div testID="hot-questions-section">
      <button testID="question-1" onPress={() => onQuestionPress('这个房源可以做餐饮吗?')}>
        这个房源可以做餐饮吗?
      </button>
    </div>
  )
}));

jest.mock('@components/common/LazyLoadWrapper', () => {
  return function MockLazyLoadWrapper({ children }: any) {
    return children;
  };
});

jest.mock('@components/SimilarPropertiesWithAPI', () => {
  return function MockSimilarPropertiesWithAPI({ onPropertyPress }: any) {
    return (
      <div testID="similar-properties">
        <button testID="similar-property-1" onPress={() => onPropertyPress('similar-123')}>
          相似房源1
        </button>
      </div>
    );
  };
});

describe('PropertyDetailScreen - Screen Tests', () => {
  const renderWithNavigation = (component: React.ReactElement) => {
    return render(
      <NavigationContainer>
        {component}
      </NavigationContainer>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('页面基础渲染测试', () => {
    test('应该正确渲染房源详情页的所有关键组件', async () => {
      const { getByTestId, getByText } = renderWithNavigation(<PropertyDetailScreen />);

      // 验证页面标题
      await waitFor(() => {
        expect(getByText('万象城核心商铺 人流量大 可餐饮')).toBeTruthy();
      });

      // 验证关键UI组件存在
      expect(getByTestId('property-info-section')).toBeTruthy();
      expect(getByTestId('media-carousel')).toBeTruthy();
      expect(getByTestId('report-bar')).toBeTruthy();
      expect(getByTestId('map-section')).toBeTruthy();
      expect(getByTestId('detailed-info-section')).toBeTruthy();
      expect(getByTestId('hot-questions-section')).toBeTruthy();
      expect(getByTestId('similar-properties')).toBeTruthy();
      expect(getByTestId('bottom-action-bar')).toBeTruthy();
    });

    test('应该正确显示发布数据合并后的信息', async () => {
      const { getByTestId, getByText } = renderWithNavigation(<PropertyDetailScreen />);

      await waitFor(() => {
        // 验证发布数据优先显示
        expect(getByText('万象城核心商铺 人流量大 可餐饮')).toBeTruthy();
        
        // 验证地址信息使用发布数据
        const locationElement = getByTestId('location');
        expect(locationElement.children[0]).toBe('万象城商业中心A座');
      });
    });
  });

  describe('用户交互功能测试', () => {
    test('收藏功能应该正常工作', async () => {
      const { getByTestId } = renderWithNavigation(<PropertyDetailScreen />);

      const favoriteButton = getByTestId('favorite-button');
      
      // 初始状态应该是未收藏
      expect(favoriteButton.children[0]).toBe('收藏');

      // 模拟点击收藏
      fireEvent.press(favoriteButton);

      // 验证收藏状态 (注意：实际状态变化由mock的Hook控制)
      expect(favoriteButton).toBeTruthy();
    });

    test('图片查看器应该正常工作', async () => {
      const { getByTestId, queryByTestId } = renderWithNavigation(<PropertyDetailScreen />);

      // 初始状态图片查看器应该不可见
      expect(queryByTestId('image-viewer')).toBeFalsy();

      // 点击图片
      const imageButton = getByTestId('image-1');
      fireEvent.press(imageButton);

      // 由于isImageViewerVisible由mock Hook控制，这里主要测试事件触发
      expect(imageButton).toBeTruthy();
    });

    test('底部操作栏所有按钮应该可点击', async () => {
      const { getByTestId } = renderWithNavigation(<PropertyDetailScreen />);

      const contactButton = getByTestId('contact-button');
      const reserveButton = getByTestId('reserve-button');
      const favoriteButton = getByTestId('favorite-button');

      // 测试所有按钮都可以点击
      fireEvent.press(contactButton);
      fireEvent.press(reserveButton);
      fireEvent.press(favoriteButton);

      // 验证按钮存在且可交互
      expect(contactButton).toBeTruthy();
      expect(reserveButton).toBeTruthy();
      expect(favoriteButton).toBeTruthy();
    });

    test('顶部导航按钮应该正常工作', async () => {
      const { getByTestId } = renderWithNavigation(<PropertyDetailScreen />);

      const goBackButton = getByTestId('go-back-button');
      const shareButton = getByTestId('share-button');
      const messageViewButton = getByTestId('message-view-button');

      // 测试导航按钮点击
      fireEvent.press(goBackButton);
      fireEvent.press(shareButton);
      fireEvent.press(messageViewButton);

      // 验证按钮存在
      expect(goBackButton).toBeTruthy();
      expect(shareButton).toBeTruthy();
      expect(messageViewButton).toBeTruthy();
    });
  });

  describe('数据展示和转换测试', () => {
    test('应该正确显示房源面积信息', async () => {
      const { getByTestId } = renderWithNavigation(<PropertyDetailScreen />);

      await waitFor(() => {
        const areaElement = getByTestId('property-area');
        expect(areaElement.children[0]).toBe('100m²');
      });
    });

    test('热门问题应该可以交互', async () => {
      const { getByTestId } = renderWithNavigation(<PropertyDetailScreen />);

      const questionButton = getByTestId('question-1');
      fireEvent.press(questionButton);

      expect(questionButton).toBeTruthy();
    });

    test('相似房源应该可以点击跳转', async () => {
      const { getByTestId } = renderWithNavigation(<PropertyDetailScreen />);

      const similarPropertyButton = getByTestId('similar-property-1');
      fireEvent.press(similarPropertyButton);

      expect(similarPropertyButton).toBeTruthy();
    });
  });

  describe('企业级架构验证', () => {
    test('应该使用Hook层封装所有业务逻辑', () => {
      // 这个测试验证组件只关注UI渲染，业务逻辑在Hook中
      const { container } = renderWithNavigation(<PropertyDetailScreen />);
      
      // 验证组件渲染成功，说明Hook架构工作正常
      expect(container).toBeTruthy();
    });

    test('应该正确处理发布数据与API数据的合并', async () => {
      const { getByTestId } = renderWithNavigation(<PropertyDetailScreen />);

      await waitFor(() => {
        // 验证地址显示优先使用发布数据
        const locationElement = getByTestId('location');
        expect(locationElement.children[0]).toBe('万象城商业中心A座');
      });
    });

    test('错误边界应该正确包装组件', () => {
      // 测试SimpleErrorBoundary正确包装了组件
      const { container } = renderWithNavigation(<PropertyDetailScreen />);
      expect(container).toBeTruthy();
    });
  });
});