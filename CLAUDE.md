# AI编码助手指导文件
**项目**: 商业地产APP (房源管理平台)  
**更新日期**: 2025年7月8日  
**用途**: 为AI编码工具提供项目特定的开发指导和约束
## 🏗️ **项目架构概览**
### **技术栈**
- **后端**: FastAPI + SQLModel + PostgreSQL + Redis + Alembic
- **前端**: React Native + Expo + Zustand + TypeScript
- **部署**: Docker + Docker Compose
- **架构**: Monorepo + 企业级分层架构
### **核心原则**
1. **SQLModel优先**: 严禁混用SQLAlchemy模式
2. **容器内运行**: 所有逻辑必须在Docker容器内执行
3. **企业级标准**: 遵循分层架构和最佳实践，遇到技术问题时，必须先上网查询官方文档和最新案例，确定正确的版本和工具选择，不能盲目自己判断或随意卸载重装，要基于官方指导做决策。
4. **时区统一**: 使用统一的时间管理工具
5. **谨慎决策**: 遵循标准技术问题处理流程
6. **统一转换层**: 所有数据转换必须使用统一转换层，禁止编写分散的转换逻辑
7. **依赖关系管理**: 严禁循环依赖，必须使用依赖分析工具验证代码质量
8. **🐳 标准容器架构**: 严格遵循企业级三容器架构，使用Makefile命令管理
### **🔄 统一转换层架构 (重要)**
项目已实现企业级统一转换层，用于处理前端UI数据与后端API数据之间的转换：
```
src/shared/services/dataTransform/
├── core/                    # 核心引擎
│   ├── BaseTransformer.ts   # 基础转换器
│   ├── ValidationEngine.ts # 验证引擎
│   └── TransformRegistry.ts # 转换器注册中心
├── transformers/            # 具体转换器
│   ├── PropertyTransformer.ts  # 房源转换器 (支持4种场景)
│   ├── DemandTransformer.ts    # 需求转换器
│   ├── UserTransformer.ts      # 用户转换器
│   └── CommonTransformer.ts    # 通用转换器
├── types/                   # 类型定义
│   ├── TransformTypes.ts    # 转换相关类型
│   └── ValidationTypes.ts   # 验证相关类型
└── index.ts                 # 统一导出
```

**使用方式**:
```typescript
import { Transformers } from '@/shared/services/dataTransform';

// 房源数据转换 (支持4种场景)
const result = Transformers.property.toAPI(storeParams, { context: 'storeParams' });
if (result.success) {
  const apiParams = result.data;
}

// 需求数据转换
const demandResult = Transformers.demand.toAPI(formData, { selectedTags: tags });
```
**转换器功能**:
- **PropertyTransformer**: 房源列表查询、用户房源管理、房源发布、房源详情
- **DemandTransformer**: 需求发布表单转换 (求租/求购)
- **UserTransformer**: 用户相关数据转换
- **CommonTransformer**: 通用数据转换
**特性**:
- ✅ 类型安全 (TypeScript + zod运行时验证)
- ✅ 统一错误处理和日志记录
- ✅ 完整测试覆盖 (42个测试)
- ✅ 健康检查和监控机制

### **🔄 依赖分析工具 (重要)**
项目使用madge工具进行依赖关系分析，确保代码质量和性能：
**核心命令**:
```bash
# 检测循环依赖
npm run analyze:deps
# 生成依赖关系图
npm run analyze:deps:graph
# 完整质量检查（包含依赖分析）
npm run quality:check
```
**开发要求**:
- 🚨 **强制检查**: 所有新代码必须通过依赖分析
- 📊 **定期监控**: 每次提交前运行依赖检查
- 🏗️ **架构原则**: 遵循单向依赖，避免循环引用
- 📈 **持续优化**: 定期查看依赖图，优化模块结构

## 🔍 **技术问题处理标准流程**

**重要**: 遇到任何技术问题时，必须严格按照以下四阶段流程执行，不得跳过任何步骤。

### **第一阶段：📋 全面调研**
- **查看相关文档**: 检查 `ALL_docs/` 目录下的开发日志、测试记录
- **分析依赖链**: 理解功能的完整依赖关系和影响范围
- **了解历史背景**: 查阅相关的设计意图和历史决策记录

### **第二阶段：🔍 深度分析**
- **业务逻辑层面**: 分析对业务流程的影响
- **技术架构层面**: 评估对系统架构的牵连
- **前后端关联**: 考虑前后端的数据流和接口影响
- **数据库约束**: 检查数据库关系映射和约束条件

### **第三阶段：⚖️ 谨慎决策**
- **方案对比**: 评估多种解决方案的利弊
- **时间权衡**: 考虑短期修复 vs 长期架构的平衡
- **风险控制**: 制定详细的回滚计划和风险控制措施
- **影响评估**: 全面评估修改对系统稳定性的影响

### **第四阶段：✅ 验证测试**
- **功能测试**: 全面测试相关功能的正确性
- **回归测试**: 验证修复效果，确保原有功能不受影响
- **集成测试**: 测试与其他模块的集成情况
- **问题排查**: 确保没有引入新的问题或副作用

### **流程执行要求**
1. **不得跳过**: 每个阶段都必须完整执行，不得为了速度而跳过
2. **文档记录**: 重要的分析和决策过程要记录到开发日志中
3. **团队沟通**: 涉及架构变更的决策要进行充分沟通
4. **风险评估**: 高风险操作必须有完整的回滚方案
---
## 🚨 **关键约束和禁止事项*
### **绝对禁止**
1. **❌ 混用SQLAlchemy**: 项目完全使用SQLModel，禁止引入SQLAlchemy的Session、declarative_base等
2. **❌ 删除Docker配置**: 不得删除或修改 `docker-compose.yml`、`docker-compose.override.yml`、`Dockerfile`
3. **❌ 修改迁移配置**: 不得修改 `alembic.ini`、`alembic/env.py` 等迁移核心文件
4. **❌ 使用datetime.utcnow()**: 必须使用 `app.core.timezone_utils.now_utc()`
5. **❌ 直接操作数据库**: 必须通过 `get_async_session` 依赖注入获取会话
6. **❌ 🐳 破坏容器架构**: 禁止创建临时容器、修改资源限制、破坏标准三容器架构
7. **❌ 🐳 容器外操作**: 禁止在容器外直接操作数据库、运行Python脚本或执行迁移
8. **❌ 🐳 跳过容器检查**: 开发前必须运行 `make check` 确保容器环境正常

### **必须遵循**
1. **✅ 统一会话管理**: 使用 `from app.core.db import get_async_session`
2. **✅ 统一时间工具**: 使用 `from app.core.timezone_utils import now_utc, now_beijing`
3. **✅ 分层架构**: API → Service → Repository → Model
4. **✅ 类型安全**: 所有函数必须有类型注解
5. **✅ 🐳 容器化开发**: 使用 `make` 命令管理开发环境，而不是原生Docker命令
6. **✅ 🐳 标准容器架构**: 维护 backend + db + redis 的标准三容器架构
7. **✅ 🐳 环境验证**: 开发前运行 `make check`，问题排查使用 `make debug`
 
**主流APP的状态管理最佳实践**
  1. 统一状态管理 - 避免多套状态系统
  2. 单向数据流 - 清晰的数据流向
  3. 分层架构 - UI层、业务层、数据层分离
  4. 异步状态管理 - 使用专业工具如TanStack Query
  5. 类型安全 - 完整的TypeScript支持
  6. 可测试性 - 易于单元测试和集成测试
---

## 🔄 **容器重构触发条件**

### **需要重构容器的修改**
```bash
# 以下修改后必须执行: docker compose down && docker compose up -d --build

1. 修改 requirements.txt 或 pyproject.toml (依赖变更)
2. 修改 Dockerfile (构建配置变更)
3. 修改 app/main.py (应用入口变更)
4. 添加新的环境变量到 .env
5. 修改 scripts/prestart.sh (启动脚本变更)
```

### **不需要重构容器的修改**
```bash
# 以下修改会自动热重载 (开发环境)

1. 修改 app/ 目录下的业务代码
2. 修改 API 路由
3. 修改 Service、Repository 层代码
4. 修改 Pydantic Schema
```

---

## 🐳 **容器化开发规范**

### **必须遵循的容器规范**

#### **✅ 必须使用**
```bash
make dev && make check      # 启动并检查环境
make shell                  # 进入容器（而不是docker exec）
make debug                  # 故障排查（遇到问题时首先运行）
```

#### **❌ 绝对禁止**
```bash
docker run --name backend-*             # 禁止创建临时容器
docker compose up backend db            # 禁止破坏标准架构
python manage.py migrate                # 禁止在容器外操作
```

### **标准架构**
```
backend (8082:80) + db (5432) + redis (6379) + backend-test
```

### **标准开发流程**

#### **开发新功能**
```bash
1. make dev && make check   # 启动并检查环境
2. make shell              # 进入容器开发
3. # 修改代码（热重载生效）
4. make test && make lint   # 测试和检查
5. make migrate            # 数据库迁移（如需）
```

#### **问题排查**
```bash
1. make debug              # 自动诊断
2. make logs               # 查看日志
3. make shell              # 进入容器排查
```

### **常用命令快速参考**
```bash
make help                   # 查看所有可用命令
make dev && make check      # 启动并检查环境
make shell                  # 进入容器
make logs                   # 查看日志
make debug                  # 故障排查
make test                   # 运行测试
make migrate                # 数据库迁移
```

**详细容器开发指南请参考**: [容器开发指南](./ 容器开发指南.md)

---

## 📝 **编码规范**

### **组件接口一致性检查**

#### **组件重构后的接口验证**
```typescript
// ❌ 常见错误：重构后组件接口不匹配
// 组件定义
interface ComplexProgressProps {
  progress: { percentage: number };
}

// 调用方使用旧接口
<PropertyFormProgress 
  currentStep={1} 
  totalSteps={4}  // 接口不匹配！
/>

// ✅ 修复方案1：使用匹配的组件版本
import { PropertyFormProgress } from '../components/SimplePropertyFormProgress';

// ✅ 修复方案2：创建适配器
const progressData = {
  percentage: (currentStep / totalSteps) * 100
};
<PropertyFormProgress progress={progressData} />
```

#### **企业级组件设计原则**
1. **接口稳定性**: 组件接口一旦定义，避免破坏性变更
2. **版本兼容**: 提供向后兼容的接口或明确的迁移路径
3. **类型安全**: 使用TypeScript严格检查接口匹配
4. **文档完整**: 每个组件接口都有完整的类型定义和使用示例

### **后端开发规范**

#### **1. 数据库操作**
```python
# ✅ 正确方式
from sqlmodel.ext.asyncio.session import AsyncSession
from app.core.db import get_async_session

async def my_function(session: AsyncSession = Depends(get_async_session)):
    result = await session.exec(select(User))
    return result.all()
```

#### **2. 时间处理**
```python
# ✅ 正确方式
from app.core.timezone_utils import now_utc, now_beijing

class User(SQLModel, table=True):
    created_at: datetime = Field(default_factory=now_utc)  # UTC存储
    
# API响应时转换为北京时间
def format_for_api(user: User) -> dict:
    return {
        "created_at": user.created_at.astimezone(BEIJING_TZ).isoformat()
    }
```
#### **3. 分层架构**
```python
# ✅ 正确的分层结构
# API层 (app/api/routes/)
@router.post("/users")
async def create_user(user_data: UserCreate, service: UserService = Depends()):
    return await service.create_user(user_data)

# Service层 (app/services/)
class UserService:
    def __init__(self, session: AsyncSession):
        self.session = session
        self.user_repo = UserRepository(session)
    
    async def create_user(self, user_data: UserCreate) -> User:
        # 业务逻辑处理
        return await self.user_repo.create(user_data)

# Repository层 (app/repositories/)
class UserRepository:
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create(self, user_data: UserCreate) -> User:
        # 数据库操作
        pass
```

### **前端开发规范**

#### **1. 状态管理**
```typescript
// ✅ 使用 Zustand Store
import { useAuthStore } from '@/stores/authStore';

// ✅ 合理使用 Context (仅用于依赖注入)
const ThemeProvider = ({ children }) => { ... };
```

#### **2. 导入规范**
```typescript
// ✅ 使用 barrel exports
import { FloatingLoginBar } from '@auth';
import { PropertyCard } from '@property';
import { Button } from '@shared';

```
---

## 🗄️ **数据库迁移规范**

### **模型变更流程**
```bash
# 1. 修改模型文件 (app/models/*.py)
# 2. 确保模型在env.py中正确导入
# 3. 生成迁移文件
docker exec -it backend alembic revision --autogenerate -m "描述变更"

# 4. 【关键步骤】检查生成的迁移文件
# 5. 应用迁移
docker exec -it backend alembic upgrade head

# 6. 验证迁移
docker exec -it backend /app/scripts/check_migrations.sh
```

### **env.py模型导入规范**

#### **必须导入所有SQLModel模型**
```python
# app/alembic/env.py

# 1. 核心用户模型
from app.models import (
    User, UserDeactivation, UserDataSnapshot,
    UserVerification, UserPreferences, UserDevice,
    UserSocialAccount, UserActivity, Item
)

# 2. 需求相关模型
from app.models.user_demand import (
    UserDemand, DemandPropertyMatch, DemandResponse, DemandTag
)

# 3. 房源相关模型
from app.models.property.property import Property, PropertyFeature, PropertyPrice

# 4. 其他业务模型
from app.models.city import City
from app.models.user_favorite import UserFavorite, FavoriteFolder, FavoriteTag
from app.models.user_appointment import UserAppointment
from app.models.user_notification_preference import UserNotificationPreference
```

#### **模型导入检查清单**
- [ ] 所有SQLModel且table=True的模型都已导入
- [ ] 导入路径正确，无语法错误
- [ ] 模型间的关系正确定义
- [ ] 外键约束正确设置

### **迁移最佳实践**

#### **1. 单一职责原则**
```bash
# ✅ 正确：每个迁移只做一类变更
alembic revision --autogenerate -m "添加用户地址字段"
alembic revision --autogenerate -m "添加房源标签字段"

# ❌ 错误：一个迁移包含多种变更
alembic revision --autogenerate -m "添加多个表和字段"
```

#### **2. 迁移文件检查**
生成迁移文件后，**必须检查**以下内容：
- [ ] 是否使用ADD COLUMN而不是DROP/CREATE TABLE
- [ ] 外键约束是否正确处理
- [ ] 数据类型是否正确
- [ ] downgrade函数是否正确

#### **3. 常见问题和解决方案**

**问题1: 生成删除重建表的迁移**
```python
# ❌ 错误的迁移（会丢失数据）
op.drop_table('properties')
op.create_table('properties', ...)

# ✅ 正确的迁移（增量变更）
op.add_column('properties', sa.Column('address', sa.String(200), nullable=True))
```

**根本原因**: env.py中缺少模型导入
**解决方案**: 确保所有模型在env.py中正确导入

**问题2: 外键约束错误**
```bash
# 错误信息：cannot drop table because other objects depend on it
```
**解决方案**: 使用单一职责迁移，避免复杂的表结构变更

**问题3: 枚举类型错误**
```bash
# 错误信息：type "enumname" does not exist
```
**解决方案**: 分别创建枚举类型和使用枚举类型的迁移

#### **4. 迁移验证流程**
```bash
# 1. 检查当前状态
docker exec -it backend alembic current

# 2. 检查迁移历史
docker exec -it backend alembic history

# 3. 应用迁移
docker exec -it backend alembic upgrade head

# 4. 验证结果
docker exec -it backend /app/scripts/check_migrations.sh

# 5. 如果失败，回滚
docker exec -it backend alembic downgrade -1
```

### **SQLModel风格要求**
1. **统一使用SQLModel**: 禁止混用SQLAlchemy ORM
2. **正确的表定义**: 必须使用table=True
3. **关系定义**: 使用Relationship而不是relationship
4. **字段定义**: 使用Field而不是Column（除特殊情况）
5. **类型注解**: 必须有完整的类型注解

---
## 🔧 **常用开发命令**

### **后端开发**
```bash
# 启动开发环境
docker compose up -d

# 查看日志
docker compose logs -f backend

# 进入容器
docker exec -it backend /bin/bash

# 运行测试
docker exec -it backend pytest

# 检查迁移状态
docker exec -it backend /app/scripts/check_migrations.sh

# 生成迁移
docker exec -it backend alembic revision --autogenerate -m "描述"

# 应用迁移
docker exec -it backend alembic upgrade head
```

### **前端开发**
```bash
# 进入前端目录
cd packages/frontend

# 启动开发服务器
npm start

# 运行测试
npm test

# 类型检查
npm run type-check

# 构建应用
npm run build
```

---

## 📋 **代码审查检查清单**

### **后端代码审查**
- [ ] 是否使用了 `get_async_session` 依赖注入？
- [ ] 是否使用了 `now_utc()` 而不是 `datetime.utcnow()`？
- [ ] 是否遵循了分层架构 (API → Service → Repository)？
- [ ] 是否有适当的类型注解？
- [ ] 是否有必要的错误处理？
- [ ] 模型变更是否生成了迁移文件？

### **前端代码审查**
- [ ] 是否使用了 barrel imports？
- [ ] 状态管理是否合理 (Zustand vs Context)？
- [ ] 是否有适当的 TypeScript 类型？
- [ ] 组件是否放在正确的 domain 目录？
- [ ] 是否遵循了命名规范？

---

## 🚨 **紧急情况处理**

### **容器启动失败**
```bash
# 1. 查看错误日志
docker compose logs backend

# 2. 重新构建
docker compose down
docker compose up -d --build

# 3. 检查配置文件
# 确认 .env、docker-compose.yml 没有被误修改
```

### **迁移失败**
```bash
# 1. 查看迁移状态
docker exec -it backend alembic current

# 2. 查看迁移历史
docker exec -it backend alembic history

# 3. 回滚到上一个版本
docker exec -it backend alembic downgrade -1
```

### **数据库连接失败**
```bash
# 1. 检查数据库容器状态
docker compose ps

# 2. 重启数据库容器
docker compose restart db

# 3. 检查数据库日志
docker compose logs db
```

---
## 📚 **参考文档**

1. **项目文档**: `/data/my-real-estate-app/ALL_docs/`
2. **开发日志**: `/data/my-real-estate-app/ALL_docs/开发日志/`
3. **Docker配置说明**: `/data/my-real-estate-app/ALL_docs/开发日志/Docker配置策略说明.md`
4. **重构方案**: `/data/my-real-estate-app/ALL_docs/开发日志/重构方案最终决策.md`
---
## 🔧 **企业级问题排查方法论**

### **核心原则：绝不盲目猜测**
作为顶尖的运维工程师和软件工程师，必须采用系统性的问题排查方法，严禁基于猜测直接修改代码。

#### 🔍 **第一步：收集完整的错误信息**
- **禁止盲目猜测** - 绝不能基于假设直接修改代码
- **查阅官方文档** - 必须查看相关技术栈的官方文档
- **搜索已知问题** - 在GitHub Issues、Stack Overflow等平台搜索类似问题
- **收集环境信息** - 检查版本、依赖、配置等环境因素

#### 🔬 **第二步：系统性分析根本原因**
- **版本兼容性检查** - 确认所有依赖的版本兼容性
- **语法变更分析** - 检查是否有API或语法变更
- **全局搜索影响范围** - 使用`find`、`grep`等工具系统性搜索相关代码
- **日志深度分析** - 分析完整的错误堆栈和日志信息

#### ⚖️ **第三步：制定基于官方文档的解决方案**
- **官方文档优先** - 解决方案必须基于官方文档和最佳实践
- **渐进式修复** - 优先选择影响范围最小的修复方案
- **备份和回滚计划** - 制定详细的回滚计划
- **测试策略** - 制定全面的测试验证策略

#### ✅ **第四步：系统性验证和文档记录**
- **全面测试** - 测试修复的功能和相关功能
- **回归测试** - 确保修复没有引入新问题
- **文档更新** - 更新相关文档和变更日志
- **知识沉淀** - 将解决方案和经验记录到知识库

#### 🚫 **严禁的错误做法**
- ❌ 基于猜测直接修改代码
- ❌ 不查阅官方文档就实施解决方案
- ❌ 只修复表面问题而不解决根本原因
- ❌ 不进行全面测试就部署修复
- ❌ 不记录问题解决过程和经验

#### 📋 **企业级排查工具和命令**
```bash
# 系统性搜索代码问题
find packages/backend -name "*.py" -exec grep -l "问题关键词" {} \;

# 检查依赖版本
docker exec -it backend pip show 包名

# 分析日志错误
docker logs backend --tail=50 | grep -i error

# 检查API兼容性
curl -X GET "http://localhost:8082/docs" # 查看API文档

# 搜索特定语法使用
find packages/backend -name "*.py" -exec grep -n "@validator" {} \;
```

#### 🎯 **实际案例：Pydantic v2 Validator问题**
**问题现象**: FastAPI返回"Field required" "args" "kwargs"错误
**错误做法**: 直接猜测并修改validator参数
**正确做法**:
1. 查阅Pydantic官方文档确认v2语法变更
2. 检查项目使用的Pydantic版本
3. 系统性搜索所有使用旧语法的文件
4. 按照官方文档标准逐一修复
5. 全面测试验证修复效果
---
**重要提醒**: 在进行任何重大修改前，请先查阅相关文档，确保理解项目架构和约束条件。企业级开发必须遵循系统性的问题排查方法，绝不能基于猜测进行修改
## 🧠 **思路错误记录与改进指导**
基于2025年7月9日中期重构实施过程的经验总结，以下记录了常见的思路错误和改进建议：

### **❌ 常见思路错误*

#### **1. TypeScript类型错误处理错误**
**错误思路**: 发现TypeScript错误时直接使用`any`类型或忽略类型检查

**正确思路**: 系统性分析类型错误根源，创建完整的类型定义
```typescript
// ✅ 正确做法
interface ExtendedNavigationProp {
  navigate: (name: string, params?: any) => void;
  goBack: () => void;
}

export const safeNavigate = (navigation: any, screenName: string, params?: any) => {
  try {
    if (navigation && typeof navigation.navigate === 'function') {
      navigation.navigate(screenName, params);
    }
  } catch (error) {
    console.error(`Navigation error:`, error);
  }
};
```

#### **2. API类型不匹配问题**
**错误思路**: 发现API响应类型不匹配时直接修改前端类型定义
**正确思路**: 创建DTO转换层，处理前后端数据格式差异
```typescript
// ✅ 正确做法
export class DemandFormDTO {
  static toBackend(frontendData: DemandForm): DemandCreateRequest {
    return {
      demand_type: frontendData.demandType,
      property_type: frontendData.propertyType[0] as PropertyType,
      // 完整的数据转换逻辑
    };
  }
  
  static toFrontend(backendData: DemandResponse): DemandForm {
    return {
      demandType: backendData.demand_type,
      propertyType: [backendData.property_type as PropertyType],
      // 完整的数据转换逻辑
    };
  }
}
```

#### **3. 模块导入错误处理**
**错误思路**: 发现导入错误时随意更改导入
**正确思路**: 检查模块的实际导出方式，确保导入一致性
```typescript
// ✅ 正确做法
// 检查 auth.ts 文件确认导出方式
export default authService; // 默认导出
export { authService };     // 命名导出

// 对应的导入方式
import authService from '../services/auth'; // 默认导入
```

#### **4. 缓存机制设计错误**
**错误思路**: 实现复杂的缓存过期提醒机制
**正确思路**: 实现简单有效的智能缓存策略
```typescript
// ✅ 正确做法 - 简单有效
const getCacheTimeout = (completeness: number): number => {
  if (completeness >= 0.8) return 30 * 60 * 1000; // 30分钟
  if (completeness >= 0.5) return 15 * 60 * 1000; // 15分钟
  return 5 * 60 * 1000; // 5分钟
};
```

### **✅ 正确的架构思路**

#### **1. 分层架构原则**
```typescript
// ✅ 正确的分层结构
// 1. Types层 - 类型定义
export interface DemandForm { ... }

// 2. DTO层 - 数据转换
export class DemandFormDTO { ... }

// 3. API层 - 网络调用
export class DemandAPI { ... }

// 4. Service层 - 业务逻辑
export class DemandService { ... }

// 5. Component层 - UI组件
export const DemandFormScreen = () => { ... }
```

#### **2. 错误处理策略**
```typescript
// ✅ 企业级错误处理
export const handleApiError = (error: any): string => {
  if (error.response) {
    const { status, data } = error.response;
    switch (status) {
      case 400: return data.message || '请求参数错误';
      case 401: return '请先登录';
      case 403: return '权限不足';
      case 500: return '服务器错误，请稍后重试';
      default: return `请求失败 (${status})`;
    }
  }
  return '网络连接失败，请检查网络设置';
};
```

#### **3. 状态管理原则**
```typescript
// ✅ 正确的状态管理
// AuthContext - 仅用于认证状态
interface AuthState {
  isLoading: boolean;
  isSignout: boolean;
  userToken: string | null;
  user: any | null;
}

// Zustand Store - 用于业务数据
interface AppStore {
  demands: DemandItem[];
  filters: SearchFilters;
  setDemands: (demands: DemandItem[]) => void;
  updateFilters: (filters: Partial<SearchFilters>) => void;
}
```

### **🔧 重构流程最佳实践**

#### **四阶段重构方法论**
基于实际实施经验，形成了以下标准重构流程：

1. **第一阶段：基础设施修复**
   - 修复API路径404错误
   - 解决数据库连接问题
   - 修复认证服务问题

2. **第二阶段：用户体验优化**
   - 实现智能表单缓存机制
   - 优化登录页面跳转逻辑
   - 实现登录后表单自动恢复

3. **第三阶段：架构层优化**
   - 创建DTO转换服务
   - 重构前端API调用逻辑
   - 实现企业级错误处理

4. **第四阶段：类型安全增强**
   - 修复所有TypeScript类型错误
   - 统一组件接口定义
   - 完善类型文档

### **📋 思路改进检查清单**

每次重构前必须检查：
- [ ] 是否分析了问题的根本原因？
- [ ] 是否考虑了对整个系统的影响？
- [ ] 是否选择了最简单有效的解决方案？
- [ ] 是否遵循了分层架构原则？
- [ ] 是否有完整的错误处理机制？
- [ ] 是否有回滚计划？
- [ ] 是否进行了全面测试？

### **🎯 经验总结**

1. **复杂度控制**: 优先选择简单有效的解决方案，避免过度设计
2. **类型安全**: 投入时间建立完善的类型系统，长期回报巨大
3. **分层清晰**: 严格遵循分层架构，确保代码可维护性
4. **错误处理**: 建立统一的错误处理机制，提升用户体验
5. **渐进优化**: 分阶段实施重构，确保系统稳定性

---

## 🎯 **统一接口应用的正确理念** (2025-07-09)

### **核心理念：规范而非重构**

基于房东发布模块开发过程的重要反思，统一接口的正确应用应该是：

#### **✅ 正确的统一接口应用**
- **让现有组件符合统一接口规范** - 不是创建新组件
- **重点是类型安全和代码规范** - 不是UI统一
- **保持现有功能的UI设计** - 在类型层面进行统一
- **渐进式规范化** - 不破坏现有功能的前提下逐步规范

#### **❌ 错误的统一接口应用**
- **为了统一而创建新组件** - 增加系统复杂度
- **强制UI统一** - 忽略业务场景差异
- **过度抽象** - 创建不必要的抽象层
- **破坏现有功能** - 为了规范而重写已有功能
### **统一接口的正确应用策略**

#### **1. 类型层面的统一**
```typescript
// ✅ 定义统一的接口规范
interface TagSelectorProps extends BaseComponentProps {
  selectedTags: string[];
  availableTags: string[];
  onTagSelect?: (tags: string[]) => void;
  maxSelection?: number;
  // ... 其他标准属性
}

// ✅ 让现有组件符合接口规范（不重写组件）
const DemandTagSelector: React.FC<TagSelectorProps> = ({ ... }) => {
  // 现有实现，但确保Props符合统一接口
};

const PropertyTagSelector: React.FC<TagSelectorProps> = ({ ... }) => {
  // 现有实现，但确保Props符合统一接口
};
```

#### **2. API逻辑的复用**
```typescript
// ✅ 复用API逻辑，不强制UI统一
// 共享的AI标签生成服务
export class AITagService {
  static async generateTags(data: any): Promise<string[]> {
    const result = await DemandAPI.getTagRecommendations(data);
    return result.data?.recommendedTags || [];
  }
}

// 各页面独立使用，保持UI差异
```

#### **3. 渐进式规范化**
```typescript
// ✅ 逐步让现有组件符合规范
// 第一步：确保Props类型符合统一接口
// 第二步：确保错误处理符合统一标准
// 第三步：确保状态管理符合统一模式
// 不需要重写整个组件
```

### **经验教训**

1. **统一接口 ≠ 统一组件** - 接口统一是为了类型安全和代码规范
2. **保持业务差异** - 不同业务场景的UI应该有差异
3. **渐进式改进** - 在不破坏现有功能的前提下逐步规范
4. **复用逻辑，不复用UI** - API逻辑可以复用，UI应该符合具体场景

### **指导原则**

- **规范优于重构** - 让现有代码符合规范，而不是重写
- **类型安全优于UI统一** - 重点关注类型安全和接口规范
- **业务导向** - UI设计应该服务于具体的业务场景
- **渐进改进** - 分步骤、有计划地进行规范化

**重要提醒**: 在进行任何重大修改前，请先查阅相关文档，确保理解项目架构和约束条件。企业级开发必须遵循系统性的问题排查方法，绝不能基于猜测进行修改。统一接口的目的是规范化，不是为了统一而统一。
## 🏗️ **前端企业级架构规范（2025-07-22更新）**

**重要**: 基于房源发布页面重构成功经验，制定了完整的前端架构标准。所有AI编码工具**必须严格遵循**以下规范：

### **📋 强制性架构要求**

详细规范请查阅：**前端企业级架构规范.md**

#### **五层架构（强制遵循）**
```
🎨 UI层 (Components + Styles)     ← React Native + 独立样式
🔧 Hook层 (Business Logic)       ← 业务逻辑 + 性能优化  
🏪 Store层 (Zustand Management)  ← 统一状态管理
🔄 DTO层 (Data Transform)        ← 统一转换层
🌐 API层 (Network Services)      ← 网络请求
```

#### **技术栈统一要求**
- ✅ **状态管理**: 只使用Zustand + 三个中间件
- ✅ **数据转换**: 必须使用统一转换层
- ✅ **类型安全**: TypeScript严格模式 + zod验证
- ✅ **性能优化**: useCallback/useMemo + React.memo
- ✅ **文件结构**: 标准化目录组织

#### **绝对禁止的行为**
- ❌ **绝对禁止**: UI层直接调用API
- ❌ **绝对禁止**: 混用Redux、Context API等其他状态管理
- ❌ **绝对禁止**: 绕过DTO层的数据转换
- ❌ **绝对禁止**: 组件超过300行不拆分
- ❌ **绝对禁止**: 使用any类型（除类型导入）

### **🎯 架构应用原则**

1. **统一 vs 差异化**
   - **必须统一**: 架构层次、状态管理、数据转换、错误处理
   - **可以差异**: UI设计、交互逻辑、业务特性

2. **开发流程标准化**
   - 新功能开发：五层架构设计 → Store状态管理 → Hook业务逻辑 → 组件实现
   - 代码审查：架构合规 → 性能优化 → 类型安全 → 错误处理
   - 重构原则：保持架构一致性，最小化修改影响

3. **性能要求**
   - 组件渲染 < 16ms
   - 状态更新避免不必要re-render  
   - 大列表使用虚拟化
   - 异步操作使用懒加载

### **📚 学习资源**
- **成功案例**: PropertyDetailFormScreen重构实现
- **Store模板**: PropertyFormStore.ts
- **Hook模板**: useAITagRecommendations.ts
- **详细规范**: 前端企业级架构规范.md

**提醒**: 这些不是建议而是**强制要求**，确保项目长期可维护性和团队开发效率。

## 🏗️ **后端企业级架构规范（2025-07-22更新）**

**重要**: 基于项目现状分析（当前B+级 80/100），制定了完整的后端架构标准。所有AI编码工具**必须严格遵循**以下规范：

### **📋 强制性架构要求**

详细规范请查阅：**后端企业级架构规范.md**

#### **五层架构（强制遵循）**
```
🌐 API层 (Routes + Endpoints)      ← FastAPI路由 + 请求处理
🔧 Service层 (Business Logic)      ← 业务逻辑 + 事务管理
🏪 Repository层 (Data Access)      ← 数据访问 + 查询封装
📊 Model层 (SQLModel + Schemas)    ← 数据模型 + 验证模式
🗄️ Database层 (PostgreSQL + Redis) ← 数据持久化 + 缓存
```

#### **技术栈统一要求**
- ✅ **ORM框架**: 只使用SQLModel（禁止SQLAlchemy）
- ✅ **异步操作**: 全链路异步处理
- ✅ **依赖注入**: get_async_session统一会话管理
- ✅ **时间处理**: 只使用now_utc()工具函数
- ✅ **数据验证**: Pydantic模型验证
- ✅ **事务管理**: Service层统一事务控制

#### **绝对禁止的行为**
- ❌ **绝对禁止**: API层直接使用Repository
- ❌ **绝对禁止**: Service层直接使用SQLModel查询
- ❌ **绝对禁止**: 使用SQLAlchemy Session/declarative_base
- ❌ **绝对禁止**: 使用datetime.utcnow()
- ❌ **绝对禁止**: 跨层级调用（必须按层次）

### **🎯 架构应用原则**

1. **分层职责明确**
   - **API层**: 只处理HTTP请求响应，调用Service
   - **Service层**: 封装业务逻辑，管理事务，调用Repository
   - **Repository层**: 只负责数据访问，使用SQLModel异步查询
   - **Model层**: 数据模型定义，支持软删除和时间戳

2. **开发流程标准化**
   - 新功能开发：Model设计 → Repository实现 → Service封装 → API路由
   - 数据库操作：Repository → Service事务管理 → API响应
   - 错误处理：统一异常类型 → Service层处理 → API层返回

3. **性能和安全要求**
   - 全异步数据库操作
   - UUID主键 + 软删除支持
   - 完整的数据验证和约束
   - 业务操作日志记录

### **📚 学习资源**
- **架构现状**: 当前B+级别（80/100），目标A级别（95/100）
- **成功案例**: 用户认证模块的完整分层实现
- **改进重点**: Repository层规范化、Service层完善、异常处理优化
- **详细规范**: 后端企业级架构规范.md

### **🔧 关键改进点**
1. **完善Repository层**: 继承BaseRepository，标准化数据访问
2. **规范Service层**: 统一业务逻辑封装，完整事务管理
3. **强化异常处理**: BusinessError/NotFoundError统一异常体系
4. **优化查询性能**: 异步操作、索引优化、批量处理

**提醒**: 后端架构规范与前端架构规范同等重要，都是**强制要求**，确保全栈架构的一致性。
# 🎯 TypeScript类型系统文档

## 📋 概述

下面的内容描述了前端项目中的完整TypeScript类型系统，包括类型定义、使用规范和最佳实践。

## 🗂️ 类型文件结构

```
src/shared/types/
├── README.md                 #内容具体讲解
├── api.types.ts             # API响应类型定义
├── navigation.types.ts      # 导航类型定义
├── component.types.ts       # 组件接口定义
├── activity.types.ts        # 活动类型枚举
└── index.ts                 # 类型导出入口
```

## �� 核心类型系统

### 1. API类型 (`api.types.ts`)

#### **标准API响应格式**
```typescript
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  code?: string;
  timestamp?: string;
}
```

**使用场景**：
- 所有API调用的标准响应格式
- 统一错误处理机制
- 类型安全的数据获取

**示例用法**：
```typescript
const response: ApiResponse<UserData> = await userAPI.getUserInfo();
if (response.success && response.data) {
  setUser(response.data);
}
```

#### **分页响应格式**
```typescript
interface PaginatedResponse<T = any> {
  items: T[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  hasMore: boolean;
  totalPages: number;
}
```

**使用场景**：
- 列表数据的分页显示
- 无限滚动实现
- 数据统计信息

### 2. 导航类型 (`navigation.types.ts`)

#### **类型安全导航**
```typescript
export const safeNavigate = (
  navigation: any,
  screenName: string,
  params?: any
) => {
  try {
    if (navigation && typeof navigation.navigate === 'function') {
      navigation.navigate(screenName, params);
    }
  } catch (error) {
    console.error(`Navigation error:`, error);
  }
};
```
**推荐用法**：
```typescript
// ✅ 推荐：使用safeNavigate
safeNavigate(navigation, 'ProfileScreen', { userId: '123' });

// ❌ 不推荐：直接使用navigation
navigation.navigate('ProfileScreen', { userId: '123' });
```
### 3. 组件类型 (`component.types.ts`)
#### **基础组件Props**
```typescript
interface BaseComponentProps {
  children?: ReactNode;
  style?: StyleProp<ViewStyle>;
  testID?: string;
}
```

#### **表单组件Props**
```typescript
interface FormFieldProps extends BaseComponentProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
}
```

**设计原则**：
- 继承基础Props，保持一致性
- 支持可选属性，提高灵活性
- 包含错误状态和禁用状态

### 4. 活动类型 (`activity.types.ts`)

#### **活动类型枚举**
```typescript
export enum ActivityType {
  PROPERTY_VIEWED = 'property_viewed',
  PROPERTY_FAVORITED = 'property_favorited',
  DEMAND_CREATED = 'demand_created',
  INQUIRY_SENT = 'inquiry_sent',
  // ... 更多活动类型
}
```

**使用场景**：
- 用户行为追踪
- 活动日志记录
- 统计分析

## 📝 类型使用规范

### 1. 导入规范

#### **推荐的导入方式**
```typescript
// ✅ 使用具体类型导入
import { ApiResponse, PaginatedResponse } from '@/shared/types/api.types';
import { safeNavigate } from '@/shared/types/navigation.types';
import { BaseComponentProps } from '@/shared/types/component.types';
```

#### **统一导出入口**
```typescript
// src/shared/types/index.ts
export * from './api.types';
export * from './navigation.types';
export * from './component.types';
export * from './activity.types';

// 使用方式
import { ApiResponse, safeNavigate, BaseComponentProps } from '@/shared/types';
```

### 2. 类型定义规范

#### **接口命名规范**
- **Props接口**：以`Props`结尾，如`UserCardProps`
- **数据接口**：使用描述性名称，如`UserData`, `PropertyInfo`
- **响应接口**：以`Response`结尾，如`LoginResponse`
- **请求接口**：以`Request`结尾，如`CreateUserRequest`

#### **类型注解规范**
```typescript
// ✅ 推荐：明确的类型注解
const handleUserUpdate = async (userData: UserData): Promise<ApiResponse<UserData>> => {
  return await userAPI.updateUser(userData);
};

// ❌ 不推荐：缺少类型注解
const handleUserUpdate = async (userData) => {
  return await userAPI.updateUser(userData);
};
```

### 3. 泛型使用规范

#### **API响应泛型**
```typescript
// ✅ 推荐：明确指定泛型类型
const getUserData = (): Promise<ApiResponse<UserData>> => {
  return apiClient.get<UserData>('/users/me');
};

// ✅ 推荐：列表数据使用分页响应
const getUserList = (): Promise<ApiResponse<PaginatedResponse<UserData>>> => {
  return apiClient.get('/users');
};
```

#### **组件Props泛型**
```typescript
// ✅ 推荐：可复用的泛型组件
interface ListComponentProps<T> extends BaseComponentProps {
  items: T[];
  renderItem: (item: T) => ReactNode;
  onItemPress?: (item: T) => void;
}

const ListComponent = <T,>({ items, renderItem, onItemPress }: ListComponentProps<T>) => {
  // 组件实现
};
```

## 🔍 类型检查和验证

### 1. 编译时检查

#### **TypeScript配置**
确保`tsconfig.json`包含严格的类型检查：
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  }
}
```

#### **常用检查命令**
```bash
# 类型检查（不生成文件）
npx tsc --noEmit

# 类型检查（跳过库文件检查）
npx tsc --noEmit --skipLibCheck

# ESLint类型相关检查
npm run lint
```

### 2. 运行时验证

#### **类型守卫函数**
```typescript
// API响应验证
export const isApiSuccess = <T>(response: ApiResponse<T>): response is ApiResponse<T> & { success: true; data: T } => {
  return response.success === true && response.data !== undefined;
};

// 使用示例
const response = await getUserData();
if (isApiSuccess(response)) {
  // 这里TypeScript知道response.data存在且类型为UserData
  setUser(response.data);
}
```

#### **数据验证**
```typescript
// 使用zod进行运行时验证
import { z } from 'zod';

const UserDataSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  avatar: z.string().optional(),
});

export type UserData = z.infer<typeof UserDataSchema>;

// 验证API响应数据
export const validateUserData = (data: unknown): UserData => {
  return UserDataSchema.parse(data);
};
```

## 🚀 最佳实践

### 1. 类型设计原则

#### **单一职责原则**
```typescript
// ✅ 推荐：职责明确的接口
interface UserBasicInfo {
  id: string;
  name: string;
  avatar?: string;
}

interface UserContactInfo {
  email: string;
  phone?: string;
}

interface UserProfile extends UserBasicInfo, UserContactInfo {
  bio?: string;
  preferences: UserPreferences;
}
```

#### **开放封闭原则**
```typescript
// ✅ 推荐：可扩展的基础接口
interface BaseEvent {
  id: string;
  timestamp: string;
  type: string;
}

interface UserEvent extends BaseEvent {
  type: 'user_action';
  userId: string;
  action: UserAction;
}

interface SystemEvent extends BaseEvent {
  type: 'system_event';
  severity: 'info' | 'warning' | 'error';
  message: string;
}
```

### 2. 错误处理模式

#### **统一错误类型**
```typescript
interface ApiError {
  success: false;
  message: string;
  code: string;
  details?: any;
  timestamp?: string;
}

// 错误处理工具函数
export const handleApiError = (error: ApiError): string => {
  switch (error.code) {
    case 'VALIDATION_ERROR':
      return '输入数据有误，请检查后重试';
    case 'UNAUTHORIZED':
      return '请先登录';
    case 'FORBIDDEN':
      return '权限不足';
    default:
      return error.message || '操作失败';
  }
};
```

### 3. 性能优化

#### **类型导入优化**
```typescript
// ✅ 推荐：按需导入
import type { UserData } from '@/shared/types/user.types';
import type { ApiResponse } from '@/shared/types/api.types';

// ❌ 不推荐：全量导入
import * as Types from '@/shared/types';
```

#### **条件类型优化**
```typescript
// 复杂类型计算可以使用条件类型
type ApiResponseData<T> = T extends ApiResponse<infer U> ? U : never;

// 使用示例
type UserDataFromResponse = ApiResponseData<ApiResponse<UserData>>; // UserData
```

## 📚 相关资源

### 1. 官方文档
- [TypeScript官方文档](https://www.typescriptlang.org/docs/)
- [React Native TypeScript](https://reactnative.dev/docs/typescript)

### 2. 项目内相关文件
- `src/shared/types/` - 类型定义目录
- `src/shared/dto/` - 数据传输对象
- `src/shared/services/` - API服务层

### 3. 代码检查工具
- ESLint TypeScript规则
- TypeScript编译器
- Prettier代码格式化
### **核心原则：精确修复，最小化影响**
#### **✅ 精确修复方法论**
 **深度分析用户反馈**
   - 仔细理解用户描述的具体问题
   - 区分表面现象和根本原因
   - 定位到具体的代码逻辑和行数
### **前端逻辑修复检查清单**
在修复前端逻辑问题时，必须检查：
- [ ] **问题定位精确**：是否准确定位到具体的代码逻辑？
- [ ] **影响范围最小**：是否只修改了必要的代码？
- [ ] **业务逻辑完整**：是否保持了原有的业务逻辑？
- [ ] **用户体验提升**：是否真正解决了用户反馈的问题？
- [ ] **测试验证充分**：是否创建了测试脚本验证修复效果？
- [ ] **代码可读性**：是否添加了必要的注释说明？
- [ ] **边界情况考虑**：是否处理了无数据的边界情况？
### **经验总结**

1. **用户反馈是金标准**：用户的具体描述通常指向准确的问题点
2. **最小化修改是核心**：能用过滤解决的问题，不要重构组件
3. **测试验证是必须**：修复后必须验证所有显示的标签都可以选择
4. **代码封装是良好实践**：使用IIFE保持代码封装性

### **指导原则总结**

- **精确修复 > 过度重构**
- **用户体验 > 代码美观**
- **最小影响 > 全面优化**  
- **问题导向 > 技术导向**

---
### **企业级修改标准**
  🏢 企业级开发最佳实践对比

  ✅ 我们采用的方式（企业级标准）

  1. 渐进式修复策略
  阶段1: 后端配置修复 → 阶段2: 核心功能验证 → 阶段3: 前端功能增强
  - ✅ 降低风险，每个阶段都可回滚
  - ✅ 便于问题定位和调试
  - ✅ 符合CI/CD流水线思维

  2. 配置与代码分离
  配置修改（.env）→ 无需重新部署应用
  代码修改 → 需要重新构建部署
  - ✅ 符合12-Factor App原则
  - ✅ 配置热更新，提高开发效率
  - ✅ 生产环境配置管理安全

  3. 测试驱动修复
  配置验证 → API测试 → 集成测试 → UI测试
  - ✅ 每层验证通过后再进入下一层
  - ✅ 问题早发现，修复成本低
## 📝 注意事项

1. **类型安全优先**：始终使用明确的类型注解，避免`any`类型
2. **渐进式增强**：在现有代码基础上逐步完善类型定义
3. **文档同步更新**：类型变更时及时更新相关文档
4. **团队规范统一**：确保团队成员遵循相同的类型定义规范
5. **精确修复优先**：前端逻辑问题采用最小化修改原则，严格按用户要求进行精确修复
# 前端企业级架构规范 v2.0

## 📋 架构总览

基于房源发布页面重构的成功实践，制定前端开发的统一架构标准。

### 🏗️ **强制性五层架构**

所有前端功能必须严格遵循以下架构层次：

```
┌─────────────────────────────────────────┐
│  🎨 UI层 (Components + Styles)          │  ← React Native组件 + 独立样式
├─────────────────────────────────────────┤
│  🔧 Hook层 (Business Logic Hooks)      │  ← 业务逻辑封装 + 性能优化
├─────────────────────────────────────────┤
│  🏪 Store层 (Zustand State Management)  │  ← 统一状态管理 + 中间件
├─────────────────────────────────────────┤
│  🔄 DTO层 (Data Transform Layer)       │  ← 数据转换 + 类型安全
├─────────────────────────────────────────┤
│  🌐 API层 (Network & Services)         │  ← 网络请求 + 错误处理
└─────────────────────────────────────────┘
```

### 🎯 **架构原则**

1. **单向数据流**: Store → Hook → Component
2. **职责分离**: 每层职责明确，不可越级调用
3. **类型安全**: 全链路TypeScript + zod运行时验证
4. **性能优先**: 懒加载 + memo优化 + 异步处理
5. **可测试性**: 每层都可独立测试和mock

## 📂 **标准化文件结构**

### **功能模块结构（必须遵循）**
```
src/screens/功能名称/
├── components/              # UI层组件
│   ├── Section1.tsx        # 页面区块组件
│   ├── Section2.tsx
│   └── styles.ts           # 样式文件（必须独立）
├── hooks/                  # Hook层
│   ├── useFeatureData.ts   # 数据处理Hook
│   ├── useFeatureLogic.ts  # 业务逻辑Hook
│   └── useValidation.ts    # 验证逻辑Hook
├── stores/                 # Store层
│   └── FeatureStore.ts     # Zustand状态管理
├── types/                  # 类型定义
│   └── feature.types.ts    # 功能类型定义
├── services/               # API层（如需要）
│   └── featureAPI.ts       # 功能专用API
└── MainScreen.tsx          # 主屏幕组件（<200行）
```

### **共享模块结构（推荐遵循）**
```
src/shared/
├── components/             # 通用UI组件
├── hooks/                  # 通用Hook
├── stores/                 # 全局Store
├── services/               # API服务 + 数据转换
├── types/                  # 全局类型
├── utils/                  # 工具函数
└── constants/              # 常量定义
```

## 🔧 **各层开发规范**

### **1. UI层规范**

#### **组件开发模板（必须遵循）**
```typescript
/**
 * 标准React Native组件模板
 */
import React, { useState, useCallback, useMemo } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { wp, hp, fp } from '@/shared/utils/responsive';

// 1. 类型定义（必须有）
interface ComponentProps {
  data: ComponentData;
  onAction: (item: ActionData) => void;
  disabled?: boolean;
}

// 2. 主组件（必须函数组件）
export const ComponentName: React.FC<ComponentProps> = ({
  data,
  onAction,
  disabled = false,
}) => {
  // 3. 本地状态（最小化）
  const [localState, setLocalState] = useState<LocalState>(initialState);

  // 4. 事件处理（必须useCallback）
  const handlePress = useCallback((item: ActionData) => {
    if (disabled) return;
    onAction(item);
  }, [disabled, onAction]);

  // 5. 计算属性（必须useMemo）
  const computedValue = useMemo(() => {
    return calculateSomething(data);
  }, [data]);

  return (
    <View style={styles.container}>
      {/* JSX结构 */}
    </View>
  );
};

// 6. 样式定义（必须独立，使用响应式函数）
const styles = {
  container: {
    padding: wp(16),
    marginBottom: hp(12),
  },
  text: {
    fontSize: fp(14),
    color: '#333333',
  },
};
```

#### **UI层强制要求**
- ✅ **必须**: 使用TypeScript严格模式
- ✅ **必须**: Props接口完整定义
- ✅ **必须**: 使用wp/hp响应式工具函数
- ✅ **必须**: 样式独立定义（styles对象或独立文件）
- ✅ **必须**: 组件 < 300行，超过必须拆分
- ✅ **必须**: 事件处理器使用useCallback
- ✅ **必须**: 计算属性使用useMemo
- ❌ **禁止**: 在组件内直接调用API
- ❌ **禁止**: 在组件内进行数据转换
- ❌ **禁止**: 使用内联样式（除特殊情况）

### **2. Hook层规范**

#### **业务逻辑Hook模板（必须遵循）**
```typescript
/**
 * 业务逻辑Hook标准实现
 */
import { useState, useCallback, useEffect, useMemo } from 'react';
import { useFeatureStore } from '../stores/FeatureStore';

export const useFeatureLogic = (options?: FeatureOptions) => {
  // 1. Store状态（统一状态管理）
  const {
    data,
    loading,
    error,
    updateData,
    setLoading,
    setError,
  } = useFeatureStore();

  // 2. 本地状态（最小化）
  const [localState, setLocalState] = useState({
    isProcessing: false,
  });

  // 3. 副作用处理
  useEffect(() => {
    // 初始化逻辑
    if (options?.autoLoad) {
      loadInitialData();
    }
  }, [options?.autoLoad]);

  // 4. 业务方法（必须useCallback + 错误处理）
  const loadInitialData = useCallback(async () => {
    if (loading) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // 使用API层 + 转换层
      const result = await featureAPI.getData();
      if (result.success) {
        updateData(result.data);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载失败');
      console.error('[useFeatureLogic] 加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  }, [loading, updateData, setLoading, setError]);

  // 5. 计算属性（性能优化）
  const processedData = useMemo(() => {
    return data.map(item => ({
      ...item,
      displayName: formatDisplayName(item.name),
    }));
  }, [data]);

  // 6. 返回接口（稳定的对象结构）
  return {
    // 状态
    data: processedData,
    loading,
    error,
    isProcessing: localState.isProcessing,
    
    // 方法
    loadInitialData,
    
    // 计算属性
    isEmpty: processedData.length === 0,
  };
};
```

#### **Hook层强制要求**
- ✅ **必须**: 封装所有业务逻辑
- ✅ **必须**: 使用Store管理共享状态
- ✅ **必须**: 完整的错误处理和日志记录
- ✅ **必须**: 异步操作防重复提交
- ✅ **必须**: 返回稳定的接口结构
- ❌ **禁止**: 在Hook内直接操作DOM
- ❌ **禁止**: 副作用无清理机制

### **3. Store层规范**

#### **Zustand Store标准模板（严格遵循）**
```typescript
/**
 * 企业级Zustand Store实现
 */
import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';

// 1. 状态接口定义
interface FeatureState {
  // 数据状态
  items: FeatureItem[];
  currentItem: FeatureItem | null;
  
  // UI状态
  loading: boolean;
  error: string | null;
  selectedIds: string[];
  
  // 表单状态
  formData: FeatureFormData;
  formErrors: Record<string, string>;
}

// 2. 操作接口定义
interface FeatureActions {
  // 数据操作
  setItems: (items: FeatureItem[]) => void;
  addItem: (item: FeatureItem) => void;
  updateItem: (id: string, updates: Partial<FeatureItem>) => void;
  removeItem: (id: string) => void;
  
  // UI操作
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  toggleSelection: (id: string) => void;
  clearSelection: () => void;
  
  // 表单操作
  updateFormField: (field: string, value: any) => void;
  setFormErrors: (errors: Record<string, string>) => void;
  resetForm: () => void;
  
  // 复合操作
  reset: () => void;
}

// 3. Store实现（必须使用三个中间件）
export const useFeatureStore = create<FeatureState & FeatureActions>()(
  devtools(
    persist(
      subscribeWithSelector((set, get) => ({
        // === 初始状态 ===
        items: [],
        currentItem: null,
        loading: false,
        error: null,
        selectedIds: [],
        formData: {
          // 表单初始值
        },
        formErrors: {},

        // === 数据操作 ===
        setItems: (items) => set(
          { items, error: null },
          false,
          'setItems'
        ),

        addItem: (item) => set(
          (state) => ({ items: [...state.items, item] }),
          false,
          'addItem'
        ),

        updateItem: (id, updates) => set(
          (state) => ({
            items: state.items.map(item =>
              item.id === id ? { ...item, ...updates } : item
            ),
          }),
          false,
          'updateItem'
        ),

        removeItem: (id) => set(
          (state) => ({
            items: state.items.filter(item => item.id !== id),
            selectedIds: state.selectedIds.filter(selectedId => selectedId !== id),
          }),
          false,
          'removeItem'
        ),

        // === UI操作 ===
        setLoading: (loading) => set({ loading }, false, 'setLoading'),
        setError: (error) => set({ error }, false, 'setError'),

        toggleSelection: (id) => set(
          (state) => ({
            selectedIds: state.selectedIds.includes(id)
              ? state.selectedIds.filter(selectedId => selectedId !== id)
              : [...state.selectedIds, id],
          }),
          false,
          'toggleSelection'
        ),

        clearSelection: () => set({ selectedIds: [] }, false, 'clearSelection'),

        // === 表单操作 ===
        updateFormField: (field, value) => set(
          (state) => ({
            formData: { ...state.formData, [field]: value },
            formErrors: { ...state.formErrors, [field]: '' }, // 清除对应字段错误
          }),
          false,
          'updateFormField'
        ),

        setFormErrors: (errors) => set({ formErrors: errors }, false, 'setFormErrors'),

        resetForm: () => set(
          {
            formData: {
              // 重置为初始值
            },
            formErrors: {},
          },
          false,
          'resetForm'
        ),

        // === 复合操作 ===
        reset: () => set(
          {
            items: [],
            currentItem: null,
            loading: false,
            error: null,
            selectedIds: [],
            formData: {
              // 初始值
            },
            formErrors: {},
          },
          false,
          'reset'
        ),
      })),
      {
        name: 'feature-store',
        // 选择性持久化（不持久化UI状态）
        partialize: (state) => ({
          items: state.items,
          formData: state.formData,
        }),
      }
    ),
    {
      name: 'FeatureStore',
    }
  )
);

// 4. 选择器Hook（性能优化）
export const useFeatureItems = () => useFeatureStore(state => state.items);
export const useFeatureLoading = () => useFeatureStore(state => state.loading);
export const useFeatureFormData = () => useFeatureStore(state => state.formData);
export const useFeatureActions = () => useFeatureStore(state => ({
  setItems: state.setItems,
  addItem: state.addItem,
  updateItem: state.updateItem,
  removeItem: state.removeItem,
  setLoading: state.setLoading,
  setError: state.setError,
  updateFormField: state.updateFormField,
  resetForm: state.resetForm,
}));
```

#### **Store层强制要求**
- ✅ **必须**: 使用Zustand作为唯一状态管理方案
- ✅ **必须**: 配置三个中间件：devtools + persist + subscribeWithSelector
- ✅ **必须**: 状态和操作接口分离定义
- ✅ **必须**: 操作方法使用描述性的action名称
- ✅ **必须**: 选择性持久化（避免持久化UI状态）
- ✅ **必须**: 提供选择器Hook优化性能
- ✅ **推荐**: 复合操作封装复杂状态变更
- ❌ **禁止**: 混用Redux、Context API等其他状态方案
- ❌ **禁止**: 在Store内执行副作用（网络请求等）

### **4. DTO层规范**

#### **必须使用统一转换层的场景**
- ✅ **必须**: 所有API请求数据转换
- ✅ **必须**: API响应数据转换
- ✅ **必须**: 表单提交数据转换
- ✅ **必须**: 列表数据展示转换

#### **标准使用方式**
```typescript
/**
 * DTO层标准使用方式
 */

// 1. 表单提交数据转换
const handleSubmit = async (formData: FormData) => {
  try {
    // 使用统一转换层转换数据
    const transformResult = Transformers.feature.toAPI(formData, {
      context: 'create',
      validateOnly: false,
    });

    if (!transformResult.success) {
      throw new Error(transformResult.error);
    }

    // 调用API
    const response = await featureAPI.create(transformResult.data);
    
    if (response.success && response.data) {
      // 转换响应数据
      const frontendData = Transformers.feature.fromAPI(response.data);
      return frontendData;
    }
  } catch (error) {
    console.error('[handleSubmit] 提交失败:', error);
    throw error;
  }
};

// 2. 列表数据转换
const loadListData = async (params: QueryParams) => {
  try {
    // 转换查询参数
    const apiParams = Transformers.feature.toAPI(params, {
      context: 'query',
    });

    const response = await featureAPI.getList(apiParams.data);
    
    // 转换响应数据
    const listResult = Transformers.feature.fromAPI(response.data, {
      context: 'list',
    });

    return listResult.data;
  } catch (error) {
    console.error('[loadListData] 加载失败:', error);
    throw error;
  }
};
```

### **5. API层规范**

#### **API服务标准模板**
```typescript
/**
 * API服务标准实现
 */
import apiClient from '@/shared/services/client';
import { ApiResponse } from '@/shared/types/api.types';

export class FeatureAPI {
  private static readonly BASE_PATH = '/features';

  /**
   * 创建功能项
   */
  static async create(data: CreateFeatureRequest): Promise<ApiResponse<FeatureResponse>> {
    try {
      const response = await apiClient.post(`${this.BASE_PATH}`, data);
      
      return {
        success: true,
        data: response.data,
        message: '创建成功',
      };
    } catch (error: any) {
      console.error('[FeatureAPI.create] 创建失败:', error);
      
      return {
        success: false,
        message: error.userMessage || '创建失败',
        code: error.code || 'CREATE_FAILED',
      };
    }
  }

  /**
   * 获取功能列表
   */
  static async getList(params: GetFeatureListParams): Promise<ApiResponse<FeatureListResponse>> {
    try {
      const response = await apiClient.get(`${this.BASE_PATH}`, { params });
      
      return {
        success: true,
        data: response.data,
        message: '获取成功',
      };
    } catch (error: any) {
      console.error('[FeatureAPI.getList] 获取列表失败:', error);
      
      return {
        success: false,
        data: {
          items: [],
          totalCount: 0,
          hasMore: false,
        },
        message: error.userMessage || '获取列表失败',
        code: error.code || 'GET_LIST_FAILED',
      };
    }
  }

  /**
   * 更新功能项
   */
  static async update(id: string, data: UpdateFeatureRequest): Promise<ApiResponse<FeatureResponse>> {
    try {
      const response = await apiClient.put(`${this.BASE_PATH}/${id}`, data);
      
      return {
        success: true,
        data: response.data,
        message: '更新成功',
      };
    } catch (error: any) {
      console.error('[FeatureAPI.update] 更新失败:', error);
      
      return {
        success: false,
        message: error.userMessage || '更新失败',
        code: error.code || 'UPDATE_FAILED',
      };
    }
  }

  /**
   * 删除功能项
   */
  static async delete(id: string): Promise<ApiResponse<void>> {
    try {
      await apiClient.delete(`${this.BASE_PATH}/${id}`);
      
      return {
        success: true,
        message: '删除成功',
      };
    } catch (error: any) {
      console.error('[FeatureAPI.delete] 删除失败:', error);
      
      return {
        success: false,
        message: error.userMessage || '删除失败',
        code: error.code || 'DELETE_FAILED',
      };
    }
  }
}

// 导出默认实例
export const featureAPI = FeatureAPI;
```

## 🚫 **严格禁止的架构违反行为**

### **架构层级违反（绝对禁止）**
- ❌ **绝对禁止**: UI层直接调用API
- ❌ **绝对禁止**: Hook层直接操作Store（应通过actions）
- ❌ **绝对禁止**: 跨层级调用（必须按层次调用）
- ❌ **绝对禁止**: 在组件内进行数据转换
- ❌ **绝对禁止**: 绕过DTO层直接传递后端数据

### **状态管理违反（绝对禁止）**
- ❌ **绝对禁止**: 混用Redux、MobX等其他状态管理
- ❌ **绝对禁止**: 使用Context API管理业务状态
- ❌ **绝对禁止**: 在多个组件间使用localStorage共享状态
- ❌ **绝对禁止**: 直接修改Store状态（必须通过actions）

### **性能违反（绝对禁止）**
- ❌ **绝对禁止**: 在render中进行复杂计算
- ❌ **绝对禁止**: 事件处理器不使用useCallback
- ❌ **绝对禁止**: 列表渲染不使用key或使用index作为key
- ❌ **绝对禁止**: 大量数据不使用虚拟化或分页

### **类型安全违反（绝对禁止）**
- ❌ **绝对禁止**: 使用any类型（除类型导入场景）
- ❌ **绝对禁止**: 不定义Props接口
- ❌ **绝对禁止**: API调用不定义响应类型
- ❌ **绝对禁止**: 不使用统一转换层的类型验证

## 📋 **开发流程标准化**

### **新功能开发检查清单**
开发任何新功能前必须检查：

- [ ] **架构设计**: 是否遵循五层架构？
- [ ] **文件结构**: 是否符合标准目录结构？
- [ ] **状态管理**: 是否使用Zustand Store？
- [ ] **数据转换**: 是否使用统一转换层？
- [ ] **类型定义**: 是否有完整的TypeScript类型？
- [ ] **性能考虑**: 是否使用了必要的性能优化？
- [ ] **错误处理**: 是否有完整的错误处理机制？
- [ ] **测试计划**: 是否有测试覆盖计划？

### **代码审查标准化**
每次代码审查必须检查：

- [ ] **组件复杂度**: 组件是否 < 300行？
- [ ] **Hook使用**: 是否正确使用useCallback/useMemo？
- [ ] **状态管理**: 是否遵循Store使用规范？
- [ ] **API调用**: 是否通过Hook层封装？
- [ ] **数据转换**: 是否使用DTO层？
- [ ] **类型安全**: 是否有完整类型注解？
- [ ] **命名规范**: 是否遵循命名约定？
- [ ] **错误处理**: 是否有try-catch和错误边界？

### **性能优化检查清单**
性能相关必须检查：

- [ ] **组件优化**: 是否使用React.memo？
- [ ] **状态更新**: 是否避免不必要的re-render？
- [ ] **计算缓存**: 是否使用useMemo缓存计算结果？
- [ ] **事件处理**: 是否使用useCallback稳定引用？
- [ ] **数据加载**: 是否使用懒加载和分页？
- [ ] **资源管理**: 是否有useEffect清理函数？

## 🎯 **企业级架构的业务价值**

### **开发效率提升**
1. **标准化减少决策时间**: 不需要每次都决定技术方案
2. **代码复用率提高**: 统一的组件和Hook可复用
3. **新人上手更快**: 清晰的架构层次易于理解
4. **团队协作效率**: 统一的代码结构和规范

### **质量和维护性**
1. **Bug率降低**: 统一的错误处理和类型检查
2. **代码可维护性**: 清晰的职责分离和模块化
3. **重构风险低**: 分层架构降低修改影响范围
4. **测试覆盖率高**: 每层可独立测试

### **性能和用户体验**
1. **加载性能**: 懒加载和性能优化措施
2. **交互响应**: useCallback和useMemo优化
3. **内存管理**: Store的选择性持久化
4. **用户体验一致性**: 统一的交互模式

## 📚 **学习和参考资源**

### **官方文档**
- [React Native官方文档](https://reactnative.dev/)
- [TypeScript官方文档](https://www.typescriptlang.org/)
- [Zustand官方文档](https://github.com/pmndrs/zustand)
- [Zod官方文档](https://zod.dev/)

### **项目内参考**
- **成功案例**: `src/screens/Publish/PropertyDetailFormScreen/`
- **Store模板**: `src/screens/Publish/PropertyDetailFormScreen/stores/PropertyFormStore.ts`
- **Hook模板**: `src/shared/hooks/useAITagRecommendations.ts`
- **组件模板**: `src/shared/components/TagSelector/TagSelector.tsx`

### **开发工具**
- **类型检查**: `npm run type-check`
- **依赖检查**: `npm run analyze:deps`
- **代码格式**: `npm run format`
- **测试覆盖**: `npm run test:coverage`
**重要提醒**: 这些规范不是建议，而是**强制要求**。所有新功能开发和旧功能重构都必须严格遵循，确保项目的长期可维护性和团队开发效率。
# 后端企业级架构规范 v2.0

## 📋 架构总览

基于项目现状分析，制定后端开发的统一架构标准。当前架构评级：**企业级标准 B+ (80/100)**

### 🏗️ **强制性五层架构**

所有后端功能必须严格遵循以下架构层次：

```
┌─────────────────────────────────────────┐
│  🌐 API层 (Routes + Endpoints)          │  ← FastAPI路由 + 请求处理
├─────────────────────────────────────────┤
│  🔧 Service层 (Business Logic)          │  ← 业务逻辑 + 事务管理
├─────────────────────────────────────────┤
│  🏪 Repository层 (Data Access)          │  ← 数据访问 + 查询封装
├─────────────────────────────────────────┤
│  📊 Model层 (SQLModel + Schemas)        │  ← 数据模型 + 验证模式
├─────────────────────────────────────────┤
│  🗄️ Database层 (PostgreSQL + Redis)     │  ← 数据持久化 + 缓存
└─────────────────────────────────────────┘
```

### 🎯 **架构原则**

1. **单向依赖**: API → Service → Repository → Model → Database
2. **职责分离**: 每层职责明确，不可越级调用
3. **异步优先**: 全链路异步处理，提升并发性能
4. **类型安全**: SQLModel + Pydantic保证类型安全
5. **可测试性**: 每层都可独立测试和mock

## 📂 **标准化目录结构**

### **当前结构（优化后）**
```
packages/backend/
├── app/
│   ├── api/                    # API层
│   │   ├── deps.py            # 依赖注入
│   │   ├── routes/            # 路由模块
│   │   │   ├── auth.py        # 认证相关
│   │   │   ├── users.py       # 用户管理
│   │   │   ├── properties.py  # 房源管理
│   │   │   └── demands.py     # 需求管理
│   │   └── middleware.py      # 中间件配置
│   ├── services/              # Service层（需完善）
│   │   ├── base.py           # 基础服务类
│   │   ├── user_service.py   # 用户业务逻辑
│   │   ├── property_service.py # 房源业务逻辑
│   │   └── demand_service.py # 需求业务逻辑
│   ├── repositories/          # Repository层（需完善）
│   │   ├── base.py           # 基础仓储类
│   │   ├── user_repository.py # 用户数据访问
│   │   ├── property_repository.py # 房源数据访问
│   │   └── demand_repository.py # 需求数据访问
│   ├── models/               # Model层
│   │   ├── user.py          # 用户模型
│   │   ├── property/        # 房源模型目录
│   │   │   ├── property.py  # 主要房源模型
│   │   │   └── property_media.py # 房源媒体模型
│   │   └── demand.py        # 需求模型
│   ├── schemas/             # Schema层
│   │   ├── user.py         # 用户验证模式
│   │   ├── property.py     # 房源验证模式
│   │   └── demand.py       # 需求验证模式
│   ├── core/               # 核心配置
│   │   ├── config.py       # 配置管理
│   │   ├── db.py          # 数据库配置
│   │   ├── security.py    # 安全配置
│   │   └── timezone_utils.py # 时区工具
│   └── tests/              # 测试代码
│       ├── conftest.py     # 测试配置
│       ├── test_services/  # 服务层测试
│       ├── test_repositories/ # 仓储层测试
│       └── test_api/       # API层测试
├── alembic/                # 数据库迁移
│   ├── env.py             # 迁移环境配置
│   └── versions/          # 迁移文件
├── scripts/                # 运维脚本
│   ├── prestart.sh        # 启动前脚本
│   └── check_migrations.sh # 迁移检查
├── Dockerfile              # 容器构建文件
├── requirements.txt        # 依赖管理
└── pyproject.toml         # 项目配置
```

## 🔧 **各层开发规范**

### **1. API层规范**

#### **路由定义标准模板（必须遵循）**
```python
"""
API层标准实现模板
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import get_current_user
from app.core.db import get_async_session
from app.services.feature_service import FeatureService
from app.schemas.feature import FeatureCreate, FeatureUpdate, FeatureResponse
from app.models.user import User

router = APIRouter()

@router.post("/", response_model=FeatureResponse)
async def create_feature(
    *,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user),
    feature_in: FeatureCreate,
) -> FeatureResponse:
    """
    创建功能项
    """
    try:
        service = FeatureService(session)
        feature = await service.create(
            user_id=current_user.id,
            feature_data=feature_in
        )
        return FeatureResponse.model_validate(feature)
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/", response_model=List[FeatureResponse])
async def list_features(
    *,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
) -> List[FeatureResponse]:
    """
    获取功能列表
    """
    service = FeatureService(session)
    features = await service.list_by_user(
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )
    return [FeatureResponse.model_validate(feature) for feature in features]

@router.get("/{feature_id}", response_model=FeatureResponse)
async def get_feature(
    *,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user),
    feature_id: str,
) -> FeatureResponse:
    """
    获取功能详情
    """
    service = FeatureService(session)
    feature = await service.get_by_id_and_user(
        feature_id=feature_id,
        user_id=current_user.id
    )
    
    if not feature:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="功能不存在"
        )
    
    return FeatureResponse.model_validate(feature)

@router.put("/{feature_id}", response_model=FeatureResponse)
async def update_feature(
    *,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user),
    feature_id: str,
    feature_in: FeatureUpdate,
) -> FeatureResponse:
    """
    更新功能项
    """
    service = FeatureService(session)
    feature = await service.update(
        feature_id=feature_id,
        user_id=current_user.id,
        feature_data=feature_in
    )
    
    if not feature:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="功能不存在或无权限"
        )
    
    return FeatureResponse.model_validate(feature)

@router.delete("/{feature_id}")
async def delete_feature(
    *,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user),
    feature_id: str,
):
    """
    删除功能项
    """
    service = FeatureService(session)
    success = await service.delete(
        feature_id=feature_id,
        user_id=current_user.id
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="功能不存在或无权限"
        )
    
    return {"message": "删除成功"}
```

#### **API层强制要求**
- ✅ **必须**: 使用依赖注入获取数据库会话
- ✅ **必须**: 使用Service层处理业务逻辑
- ✅ **必须**: 完整的错误处理和状态码
- ✅ **必须**: 使用Pydantic模型验证输入输出
- ✅ **必须**: 完整的API文档字符串
- ✅ **必须**: 权限控制和用户验证
- ❌ **禁止**: 在路由中直接编写业务逻辑
- ❌ **禁止**: 直接使用Repository层（必须通过Service）

### **2. Service层规范**

#### **业务服务标准模板（必须遵循）**
```python
"""
Service层标准实现模板
"""
from typing import List, Optional
from sqlmodel.ext.asyncio.session import AsyncSession

from app.repositories.feature_repository import FeatureRepository
from app.models.feature import Feature
from app.schemas.feature import FeatureCreate, FeatureUpdate
from app.core.exceptions import BusinessError, NotFoundError
from app.core.timezone_utils import now_utc
import logging

logger = logging.getLogger(__name__)

class FeatureService:
    """
    功能管理服务层
    """
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.repository = FeatureRepository(session)
    
    async def create(
        self, 
        *, 
        user_id: str, 
        feature_data: FeatureCreate
    ) -> Feature:
        """
        创建功能项
        """
        try:
            # 1. 业务验证
            await self._validate_create_permissions(user_id, feature_data)
            
            # 2. 构建数据模型
            feature = Feature(
                **feature_data.model_dump(),
                user_id=user_id,
                created_at=now_utc(),
                updated_at=now_utc(),
            )
            
            # 3. 数据持久化
            created_feature = await self.repository.create(feature)
            
            # 4. 业务日志记录
            await self._log_business_operation(
                operation="CREATE_FEATURE",
                user_id=user_id,
                resource_id=created_feature.id,
                details={"name": feature_data.name}
            )
            
            # 5. 提交事务
            await self.session.commit()
            await self.session.refresh(created_feature)
            
            return created_feature
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"创建功能失败: {str(e)}")
            raise BusinessError(
                message="创建功能失败",
                code="CREATE_FEATURE_FAILED",
                details={"error": str(e)}
            )
    
    async def get_by_id_and_user(
        self, 
        *, 
        feature_id: str, 
        user_id: str
    ) -> Optional[Feature]:
        """
        根据ID和用户ID获取功能
        """
        feature = await self.repository.get_by_id_and_user(
            feature_id=feature_id,
            user_id=user_id
        )
        
        if not feature:
            raise NotFoundError(
                message="功能不存在或无权限",
                code="FEATURE_NOT_FOUND"
            )
        
        return feature
    
    async def list_by_user(
        self, 
        *, 
        user_id: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Feature]:
        """
        获取用户的功能列表
        """
        return await self.repository.list_by_user(
            user_id=user_id,
            skip=skip,
            limit=limit
        )
    
    async def update(
        self, 
        *, 
        feature_id: str, 
        user_id: str, 
        feature_data: FeatureUpdate
    ) -> Optional[Feature]:
        """
        更新功能项
        """
        try:
            # 1. 权限检查
            existing_feature = await self.get_by_id_and_user(
                feature_id=feature_id,
                user_id=user_id
            )
            
            if not existing_feature:
                return None
            
            # 2. 更新数据
            update_data = feature_data.model_dump(exclude_unset=True)
            update_data["updated_at"] = now_utc()
            
            updated_feature = await self.repository.update(
                feature_id=feature_id,
                update_data=update_data
            )
            
            # 3. 业务日志记录
            await self._log_business_operation(
                operation="UPDATE_FEATURE",
                user_id=user_id,
                resource_id=feature_id,
                details={"updates": update_data}
            )
            
            # 4. 提交事务
            await self.session.commit()
            await self.session.refresh(updated_feature)
            
            return updated_feature
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"更新功能失败: {str(e)}")
            raise BusinessError(
                message="更新功能失败",
                code="UPDATE_FEATURE_FAILED",
                details={"error": str(e)}
            )
    
    async def delete(
        self, 
        *, 
        feature_id: str, 
        user_id: str
    ) -> bool:
        """
        删除功能项
        """
        try:
            # 1. 权限检查
            existing_feature = await self.get_by_id_and_user(
                feature_id=feature_id,
                user_id=user_id
            )
            
            if not existing_feature:
                return False
            
            # 2. 执行删除
            success = await self.repository.delete(feature_id=feature_id)
            
            if success:
                # 3. 业务日志记录
                await self._log_business_operation(
                    operation="DELETE_FEATURE",
                    user_id=user_id,
                    resource_id=feature_id,
                    details={"name": existing_feature.name}
                )
                
                # 4. 提交事务
                await self.session.commit()
            
            return success
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"删除功能失败: {str(e)}")
            raise BusinessError(
                message="删除功能失败",
                code="DELETE_FEATURE_FAILED",
                details={"error": str(e)}
            )
    
    async def _validate_create_permissions(
        self, 
        user_id: str, 
        feature_data: FeatureCreate
    ) -> None:
        """
        验证创建权限
        """
        # 业务规则验证逻辑
        pass
    
    async def _log_business_operation(
        self,
        operation: str,
        user_id: str,
        resource_id: str,
        details: dict
    ) -> None:
        """
        记录业务操作日志
        """
        logger.info(
            f"业务操作: {operation}",
            extra={
                "operation": operation,
                "user_id": user_id,
                "resource_id": resource_id,
                "details": details,
                "timestamp": now_utc().isoformat()
            }
        )
```

#### **Service层强制要求**
- ✅ **必须**: 封装所有业务逻辑
- ✅ **必须**: 使用Repository层进行数据访问
- ✅ **必须**: 完整的事务管理（commit/rollback）
- ✅ **必须**: 业务验证和权限检查
- ✅ **必须**: 异常处理和错误日志
- ✅ **必须**: 业务操作日志记录
- ❌ **禁止**: 直接使用SQLModel查询
- ❌ **禁止**: 在Service中处理HTTP请求响应

### **3. Repository层规范**

#### **数据访问标准模板（必须遵循）**
```python
"""
Repository层标准实现模板
"""
from typing import List, Optional, Dict, Any
from sqlmodel import select, update, delete
from sqlmodel.ext.asyncio.session import AsyncSession

from app.models.feature import Feature
from app.repositories.base import BaseRepository

class FeatureRepository(BaseRepository[Feature]):
    """
    功能数据访问层
    """
    
    def __init__(self, session: AsyncSession):
        super().__init__(session, Feature)
    
    async def get_by_id_and_user(
        self, 
        *, 
        feature_id: str, 
        user_id: str
    ) -> Optional[Feature]:
        """
        根据ID和用户ID获取功能
        """
        statement = select(Feature).where(
            Feature.id == feature_id,
            Feature.user_id == user_id,
            Feature.deleted_at.is_(None)  # 软删除检查
        )
        result = await self.session.exec(statement)
        return result.first()
    
    async def list_by_user(
        self, 
        *, 
        user_id: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Feature]:
        """
        获取用户的功能列表
        """
        statement = select(Feature).where(
            Feature.user_id == user_id,
            Feature.deleted_at.is_(None)
        ).offset(skip).limit(limit).order_by(Feature.created_at.desc())
        
        result = await self.session.exec(statement)
        return result.all()
    
    async def count_by_user(self, *, user_id: str) -> int:
        """
        统计用户的功能数量
        """
        statement = select(func.count(Feature.id)).where(
            Feature.user_id == user_id,
            Feature.deleted_at.is_(None)
        )
        result = await self.session.exec(statement)
        return result.first() or 0
    
    async def find_by_name_and_user(
        self, 
        *, 
        name: str, 
        user_id: str
    ) -> Optional[Feature]:
        """
        根据名称和用户ID查找功能
        """
        statement = select(Feature).where(
            Feature.name == name,
            Feature.user_id == user_id,
            Feature.deleted_at.is_(None)
        )
        result = await self.session.exec(statement)
        return result.first()
    
    async def batch_update_status(
        self, 
        *, 
        feature_ids: List[str], 
        user_id: str, 
        status: str
    ) -> int:
        """
        批量更新功能状态
        """
        statement = (
            update(Feature)
            .where(
                Feature.id.in_(feature_ids),
                Feature.user_id == user_id,
                Feature.deleted_at.is_(None)
            )
            .values(
                status=status,
                updated_at=now_utc()
            )
        )
        
        result = await self.session.exec(statement)
        return result.rowcount
    
    async def search_features(
        self,
        *,
        user_id: str,
        search_term: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Feature]:
        """
        搜索功能
        """
        statement = (
            select(Feature)
            .where(
                Feature.user_id == user_id,
                Feature.deleted_at.is_(None),
                Feature.name.ilike(f"%{search_term}%")
            )
            .offset(skip)
            .limit(limit)
            .order_by(Feature.created_at.desc())
        )
        
        result = await self.session.exec(statement)
        return result.all()
    
    async def get_features_by_status(
        self,
        *,
        user_id: str,
        status: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Feature]:
        """
        根据状态获取功能列表
        """
        statement = (
            select(Feature)
            .where(
                Feature.user_id == user_id,
                Feature.status == status,
                Feature.deleted_at.is_(None)
            )
            .offset(skip)
            .limit(limit)
            .order_by(Feature.updated_at.desc())
        )
        
        result = await self.session.exec(statement)
        return result.all()
```

#### **Repository基础类模板**
```python
"""
Repository基础类
"""
from typing import Generic, TypeVar, Type, List, Optional, Dict, Any
from sqlmodel import SQLModel, select, func
from sqlmodel.ext.asyncio.session import AsyncSession
from app.core.timezone_utils import now_utc

ModelType = TypeVar("ModelType", bound=SQLModel)

class BaseRepository(Generic[ModelType]):
    """
    Repository基础类，提供通用的CRUD操作
    """
    
    def __init__(self, session: AsyncSession, model: Type[ModelType]):
        self.session = session
        self.model = model
    
    async def create(self, *, obj_in: ModelType) -> ModelType:
        """
        创建对象
        """
        self.session.add(obj_in)
        await self.session.flush()
        return obj_in
    
    async def get(self, id: Any) -> Optional[ModelType]:
        """
        根据ID获取对象
        """
        statement = select(self.model).where(self.model.id == id)
        result = await self.session.exec(statement)
        return result.first()
    
    async def get_multi(
        self, 
        *, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[ModelType]:
        """
        获取对象列表
        """
        statement = select(self.model).offset(skip).limit(limit)
        result = await self.session.exec(statement)
        return result.all()
    
    async def update(
        self, 
        *, 
        db_obj: ModelType, 
        obj_in: Dict[str, Any]
    ) -> ModelType:
        """
        更新对象
        """
        for field, value in obj_in.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
        
        # 自动更新时间戳
        if hasattr(db_obj, 'updated_at'):
            setattr(db_obj, 'updated_at', now_utc())
        
        self.session.add(db_obj)
        await self.session.flush()
        return db_obj
    
    async def delete(self, *, id: Any) -> bool:
        """
        删除对象（软删除）
        """
        db_obj = await self.get(id)
        if not db_obj:
            return False
        
        # 如果支持软删除
        if hasattr(db_obj, 'deleted_at'):
            setattr(db_obj, 'deleted_at', now_utc())
            self.session.add(db_obj)
        else:
            await self.session.delete(db_obj)
        
        await self.session.flush()
        return True
    
    async def count(self) -> int:
        """
        统计对象数量
        """
        statement = select(func.count(self.model.id))
        result = await self.session.exec(statement)
        return result.first() or 0
```

#### **Repository层强制要求**
- ✅ **必须**: 继承BaseRepository基础类
- ✅ **必须**: 只负责数据访问，不包含业务逻辑
- ✅ **必须**: 使用SQLModel的异步查询
- ✅ **必须**: 支持软删除（如果模型有deleted_at字段）
- ✅ **必须**: 查询优化（合适的索引和查询条件）
- ✅ **推荐**: 复杂查询封装成方法
- ❌ **禁止**: 在Repository中处理事务（交给Service层）
- ❌ **禁止**: 在Repository中进行业务验证

### **4. Model层规范**

#### **SQLModel标准模板（必须遵循）**
```python
"""
SQLModel标准实现模板
"""
from datetime import datetime
from typing import Optional, List
from uuid import UUID, uuid4
from sqlmodel import SQLModel, Field, Relationship
from app.core.timezone_utils import now_utc

class FeatureBase(SQLModel):
    """
    功能基础模型
    """
    name: str = Field(
        min_length=1, 
        max_length=100, 
        description="功能名称"
    )
    description: Optional[str] = Field(
        default=None, 
        max_length=500, 
        description="功能描述"
    )
    status: str = Field(
        default="active", 
        description="状态"
    )
    priority: int = Field(
        default=0, 
        ge=0, 
        le=10, 
        description="优先级"
    )

class Feature(FeatureBase, table=True):
    """
    功能数据模型
    """
    __tablename__ = "features"
    
    # 主键（使用UUID）
    id: UUID = Field(
        default_factory=uuid4,
        primary_key=True,
        description="功能ID"
    )
    
    # 外键
    user_id: UUID = Field(
        foreign_key="users.id",
        description="用户ID"
    )
    
    # 时间戳
    created_at: datetime = Field(
        default_factory=now_utc,
        description="创建时间"
    )
    updated_at: datetime = Field(
        default_factory=now_utc,
        description="更新时间"
    )
    deleted_at: Optional[datetime] = Field(
        default=None,
        description="删除时间（软删除）"
    )
    
    # 关系定义
    user: Optional["User"] = Relationship(back_populates="features")
    tags: List["FeatureTag"] = Relationship(
        back_populates="feature",
        cascade_delete=True
    )

class FeatureCreate(FeatureBase):
    """
    创建功能的输入模型
    """
    pass

class FeatureUpdate(SQLModel):
    """
    更新功能的输入模型
    """
    name: Optional[str] = Field(
        default=None, 
        min_length=1, 
        max_length=100
    )
    description: Optional[str] = Field(
        default=None, 
        max_length=500
    )
    status: Optional[str] = Field(default=None)
    priority: Optional[int] = Field(default=None, ge=0, le=10)

class FeatureResponse(FeatureBase):
    """
    功能响应模型
    """
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime
    
    # 计算字段
    is_active: bool = Field(default=True)
    
    @property
    def is_active(self) -> bool:
        """
        计算属性：是否激活
        """
        return self.status == "active" and self.deleted_at is None

class FeatureListResponse(SQLModel):
    """
    功能列表响应模型
    """
    items: List[FeatureResponse]
    total_count: int
    skip: int
    limit: int
    has_more: bool
```

#### **Model层强制要求**
- ✅ **必须**: 使用SQLModel定义所有模型
- ✅ **必须**: 使用UUID作为主键
- ✅ **必须**: 包含created_at和updated_at时间戳
- ✅ **必须**: 支持软删除（deleted_at字段）
- ✅ **必须**: 完整的字段验证和约束
- ✅ **必须**: 分离Base、Table、Create、Update、Response模型
- ✅ **推荐**: 使用计算属性提供业务逻辑
- ❌ **禁止**: 在模型中包含业务逻辑方法

## 🚫 **严格禁止的架构违反行为**

### **架构层级违反（绝对禁止）**
- ❌ **绝对禁止**: API层直接使用Repository
- ❌ **绝对禁止**: Repository层包含业务逻辑
- ❌ **绝对禁止**: Service层直接使用SQLModel查询
- ❌ **绝对禁止**: 跨层级调用（必须按层次调用）

### **数据库操作违反（绝对禁止）**
- ❌ **绝对禁止**: 使用SQLAlchemy（项目使用SQLModel）
- ❌ **绝对禁止**: 使用同步数据库操作
- ❌ **绝对禁止**: 不通过get_async_session获取会话
- ❌ **绝对禁止**: 手动管理数据库连接

### **时间处理违反（绝对禁止）**
- ❌ **绝对禁止**: 使用datetime.utcnow()
- ❌ **绝对禁止**: 不使用统一的时区工具函数
- ❌ **绝对禁止**: 时区混用和不一致

### **异常处理违反（绝对禁止）**
- ❌ **绝对禁止**: 不处理数据库异常
- ❌ **绝对禁止**: 不使用统一的异常类型
- ❌ **绝对禁止**: 直接抛出SQLModel异常到API层

## 📋 **开发流程标准化**

### **新功能开发检查清单**
开发任何新功能前必须检查：

- [ ] **模型设计**: 是否遵循SQLModel规范？
- [ ] **Repository实现**: 是否继承BaseRepository？
- [ ] **Service封装**: 是否包含完整的业务逻辑？
- [ ] **API设计**: 是否遵循RESTful规范？
- [ ] **异常处理**: 是否有完整的错误处理？
- [ ] **事务管理**: 是否正确使用commit/rollback？
- [ ] **权限控制**: 是否有适当的权限检查？
- [ ] **日志记录**: 是否有业务操作日志？

### **代码审查标准化**
每次代码审查必须检查：

- [ ] **分层架构**: 是否遵循五层架构？
- [ ] **依赖注入**: 是否使用标准的依赖注入？
- [ ] **异步操作**: 是否使用异步数据库操作？
- [ ] **类型安全**: 是否有完整的类型注解？
- [ ] **数据验证**: 是否使用Pydantic模型验证？
- [ ] **SQL优化**: 是否有合适的查询优化？
- [ ] **安全性**: 是否有SQL注入防护？
- [ ] **测试覆盖**: 是否有对应的测试用例？

### **性能优化检查清单**
性能相关必须检查：

- [ ] **数据库查询**: 是否有合适的索引？
- [ ] **N+1查询**: 是否使用了预加载？
- [ ] **批量操作**: 是否使用批量更新？
- [ ] **缓存策略**: 是否使用Redis缓存？
- [ ] **连接池**: 是否配置了数据库连接池？
- [ ] **分页查询**: 是否实现了分页？
- [ ] **异步处理**: 是否使用异步I/O？
## 📚 **学习和参考资源**

### **官方文档**
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [SQLModel官方文档](https://sqlmodel.tiangolo.com/)
- [Pydantic官方文档](https://docs.pydantic.dev/)
### **开发工具**
- **数据库迁移**: `make migrate`
- **测试执行**: `make test`
- **代码质量**: `make lint`
- **容器管理**: `make dev && make check`
**重要提醒**: 这些规范是**强制要求**，所有新功能开发和旧功能重构都必须严格遵循，确保后端架构的一致性和可维护性。
