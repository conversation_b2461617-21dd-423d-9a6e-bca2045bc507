{"timestamp": "2025-07-17T12:08:47.412Z", "testConfig": {"mainFile": "src/screens/Publish/PropertyDetailFormScreen.tsx", "mediaUploadComponent": "src/screens/Publish/PropertyDetailFormScreen/components/MediaUploadSection.tsx", "aiTagComponent": "src/shared/components/AITagSelector/AITagSelector.tsx", "stylesDir": "src/screens/Publish/PropertyDetailFormScreen/styles/", "metrics": {"fileSize": true, "lineCount": true, "compileTime": true, "memoryUsage": true}}, "results": {"文件大小分析": {"主文件 (PropertyDetailFormScreen.tsx)": "82.75 KB", "媒体上传组件 (MediaUploadSection.tsx)": "10.5 KB", "AI标签组件 (AITagSelector.tsx)": "7.43 KB", "样式文件目录总大小": "28.87 KB"}, "代码行数分析": {"主文件行数": "2252 行", "媒体上传组件行数": "300 行", "AI标签组件行数": "240 行", "拆分出的总行数": "540 行"}, "编译性能分析": {"主文件编译时间": "5358 ms", "媒体上传组件编译时间": "2516 ms", "AI标签组件编译时间": "2450 ms", "组件并行编译优势": "可节省 2842 ms"}, "拆分效果分析": {"主文件减少比例": "29% (3164 → 2252 行)", "模块化程度": "19% 代码已模块化", "维护性提升": "✅ 样式、组件、逻辑分离", "复用性提升": "✅ AITagSelector可在两个场景使用", "性能优化": "✅ 懒加载、缓存策略实现"}}, "summary": {"mainFileReduction": "29%", "totalModularization": "19%", "compileTimeOptimization": "2842ms"}}