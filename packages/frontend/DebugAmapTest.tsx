/**
 * 高德地图SDK调试测试组件
 * 用于检测SDK初始化状态和原生模块连接
 */

import React, { useEffect, useState } from 'react';
import { View, Text, Button, StyleSheet, Alert, NativeModules } from 'react-native';
import { MapView } from 'react-native-amap3d';

const DebugAmapTest = () => {
  const [sdkStatus, setSdkStatus] = useState<string>('未知');
  const [nativeModules, setNativeModules] = useState<any>({});

  useEffect(() => {
    checkAMapSdkStatus();
  }, []);

  const checkAMapSdkStatus = async () => {
    try {
      console.log('🔍 [DebugAmapTest] 开始检查高德SDK状态...');
      
      // 检查原生模块是否可用
      const { AMapSdk } = NativeModules;
      console.log('🔍 [DebugAmapTest] AMapSdk原生模块:', AMapSdk);
      
      if (!AMapSdk) {
        setSdkStatus('原生模块不可用');
        console.error('❌ [DebugAmapTest] AMapSdk原生模块未找到');
        return;
      }

      // 检查SDK版本
      try {
        const version = await AMapSdk.getVersion();
        console.log('✅ [DebugAmapTest] SDK版本:', version);
        setSdkStatus(`SDK已连接，版本: ${version}`);
        
        setNativeModules({
          AMapSdk: '可用',
          version: version,
          methods: Object.keys(AMapSdk || {})
        });
      } catch (error) {
        console.error('❌ [DebugAmapTest] 获取SDK版本失败:', error);
        setSdkStatus('SDK连接失败');
      }

    } catch (error) {
      console.error('❌ [DebugAmapTest] 检查SDK状态失败:', error);
      setSdkStatus('检查失败');
    }
  };

  const testManualLocationRequest = () => {
    console.log('🧪 [DebugAmapTest] 手动测试定位请求...');
    Alert.alert('测试', '观察控制台是否有定位回调');
  };

  const handleMapLocation = (event: any) => {
    console.log('🎯 [DebugAmapTest] *** 收到定位回调! ***', event);
    console.log('🎯 [DebugAmapTest] 定位数据详情:', JSON.stringify(event, null, 2));
    
    Alert.alert(
      '定位成功！',
      `收到定位回调\n纬度: ${event.nativeEvent?.latitude}\n经度: ${event.nativeEvent?.longitude}`,
      [{ text: '确定' }]
    );
  };

  const handleMapLoad = () => {
    console.log('🗺️ [DebugAmapTest] 地图加载完成');
  };

  const handleMapError = (error: any) => {
    console.error('❌ [DebugAmapTest] 地图错误:', error);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>高德地图SDK调试</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>SDK状态:</Text>
        <Text style={styles.statusValue}>{sdkStatus}</Text>
      </View>
      
      <View style={styles.infoContainer}>
        <Text style={styles.infoLabel}>原生模块信息:</Text>
        <Text style={styles.infoValue}>
          {JSON.stringify(nativeModules, null, 2)}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button 
          title="重新检查SDK状态" 
          onPress={checkAMapSdkStatus}
        />
        <Button 
          title="测试定位请求" 
          onPress={testManualLocationRequest}
        />
      </View>

      <Text style={styles.mapLabel}>测试地图组件:</Text>
      <MapView
        style={styles.map}
        myLocationEnabled={true}
        myLocationButtonEnabled={true}
        showsUserLocation={true}
        onLocation={handleMapLocation}
        onLoad={handleMapLoad}
        onError={handleMapError}
        initialCameraPosition={{
          target: {
            latitude: 39.9042,
            longitude: 116.4074,
          },
          zoom: 15,
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#ffffff',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333333',
  },
  statusContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  statusLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666666',
    width: 80,
  },
  statusValue: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
  },
  infoContainer: {
    marginBottom: 20,
    backgroundColor: '#f5f5f5',
    padding: 10,
    borderRadius: 5,
  },
  infoLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666666',
    marginBottom: 5,
  },
  infoValue: {
    fontSize: 10,
    color: '#333333',
    fontFamily: 'monospace',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  mapLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333333',
  },
  map: {
    flex: 1,
    minHeight: 200,
  },
});

export default DebugAmapTest;