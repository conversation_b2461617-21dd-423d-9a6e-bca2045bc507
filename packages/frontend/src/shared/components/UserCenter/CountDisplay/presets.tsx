/**
 * 通用计数组件预设
 * 为常见使用场景提供预配置组件
 */
import React, { memo } from 'react';
import { CountDisplay, CountDisplayProps } from './CountDisplay';

// 🏗️ Tab计数组件
export interface TabCountProps extends Omit<CountDisplayProps, 'variant'> {
  active?: boolean; // 是否为激活状态
}

export const TabCount = memo<TabCountProps>(({ active, color, ...props }) => (
  <CountDisplay
    {...props}
    variant="tab"
    color={active ? 'primary' : 'secondary'}
    showZero
  />
));

TabCount.displayName = 'TabCount';

// 🏗️ 徽章计数组件
export interface BadgeCountProps extends Omit<CountDisplayProps, 'variant'> {}

export const BadgeCount = memo<BadgeCountProps>(props => (
  <CountDisplay {...props} variant="badge" hideWhenZero maxCount={99} />
));

BadgeCount.displayName = 'BadgeCount';

// 🏗️ 统计卡片计数组件
export interface StatCountProps extends Omit<CountDisplayProps, 'variant'> {
  label?: string; // 统计标签
}

export const StatCount = memo<StatCountProps>(({ label, ...props }) => (
  <CountDisplay
    {...props}
    variant="stat-card"
    showZero
    maxCount={9999}
    suffix={label ? ` ${label}` : ''}
  />
));

StatCount.displayName = 'StatCount';

// 🏗️ 大型统计组件
export interface LargeStatProps extends Omit<CountDisplayProps, 'variant'> {
  title?: string; // 统计标题
}

export const LargeStat = memo<LargeStatProps>(({ title, ...props }) => (
  <CountDisplay {...props} variant="large-stat" showZero maxCount={99999} />
));

LargeStat.displayName = 'LargeStat';

// 🏗️ 内联计数组件
export interface InlineCountProps extends Omit<CountDisplayProps, 'variant'> {}

export const InlineCount = memo<InlineCountProps>(props => (
  <CountDisplay {...props} variant="inline" />
));

InlineCount.displayName = 'InlineCount';
