/**
 * 位置服务 - 纯高德原生定位架构
 *
 * 功能：
 * 1. 处理高德地图SDK的原生定位数据
 * 2. 智能城市识别和距离计算
 * 3. 城市信息缓存和管理
 * 4. 定位数据状态管理
 *
 * 技术架构：
 * - 完全基于react-native-amap3d的原生定位
 * - 通过MapView的onLocation回调获取定位数据
 * - WGS84坐标系（标准GPS坐标）
 * - 企业级缓存策略
 *
 * 2025年7月24日重构：移除Expo Location，纯高德原生定位架构
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { PermissionsAndroid, Platform, Alert, Linking } from 'react-native';

// 城市信息接口
export interface CityInfo {
  id: string;
  name: string;
  code: string;
  province: string;
  latitude: number;
  longitude: number;
}

// 定位结果接口
export interface LocationResult {
  success: boolean;
  city?: CityInfo;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  accuracy?: number; // 定位精度（米）
  error?: string;
}

// 缓存键
const CACHE_KEYS = {
  LAST_LOCATION: 'amap_last_coordinates',
  LAST_CITY: 'amap_last_city',
  LAST_ACCURACY: 'amap_last_accuracy',
};

// 南宁市默认信息
const DEFAULT_CITY: CityInfo = {
  id: 'nanning',
  name: '南宁',
  code: '450100',
  province: '广西壮族自治区',
  latitude: 22.8167,
  longitude: 108.3669,
};

// 支持的城市列表（目前只有南宁）
const SUPPORTED_CITIES: CityInfo[] = [DEFAULT_CITY];

class LocationService {
  private static instance: LocationService;
  private currentLocation: {
    latitude: number;
    longitude: number;
    accuracy?: number;
  } | null = null;
  private currentCity: CityInfo | null = null;
  private locationUpdateCallbacks: Array<(result: LocationResult) => void> = [];

  // 🔧 防重复定位：记录最后一次处理的坐标
  private lastProcessedCoordinates: {
    latitude: number;
    longitude: number;
    accuracy: number;
  } | null = null;
  // 🔧 修复防抖设置：参考房源详情页的成熟防抖机制
  private readonly COORDINATE_THRESHOLD = 0.0001; // 坐标变化阈值（约10米）- 参考PropertyNavigationMapSimple
  private readonly ACCURACY_THRESHOLD = 10; // 精度变化阈值（10米）- 适当放宽

  private constructor() {
    // 纯高德原生定位服务
    console.log('🗺️ [LocationService] 初始化纯高德原生定位服务');
  }

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  /**
   * 处理高德原生定位更新 - 核心方法
   * 接收来自MapView onLocation回调的定位数据
   */
  async handleNativeLocationUpdate(nativeEvent: any): Promise<LocationResult> {
    try {
      const { coords } = nativeEvent;
      if (!coords) {
        throw new Error('定位数据无效');
      }

      const coordinates = {
        latitude: coords.latitude,
        longitude: coords.longitude,
        accuracy: coords.accuracy,
      };

      // 🔧 智能防抖：参考房源详情页的成熟防抖机制
      if (this.lastProcessedCoordinates) {
        const latDiff = Math.abs(
          coordinates.latitude - this.lastProcessedCoordinates.latitude
        );
        const lngDiff = Math.abs(
          coordinates.longitude - this.lastProcessedCoordinates.longitude
        );
        // 🔧 优化防抖逻辑：只有位置变化超过10米才处理，参考PropertyNavigationMapSimple
        const hasSignificantLocationChange =
          latDiff > this.COORDINATE_THRESHOLD ||
          lngDiff > this.COORDINATE_THRESHOLD;

        const hasSignificantAccuracyImprovement =
          coordinates.accuracy < this.lastProcessedCoordinates.accuracy - this.ACCURACY_THRESHOLD;

        // 如果位置变化很小且精度没有显著提升，静默跳过（不打印日志避免刷屏）
        if (!hasSignificantLocationChange && !hasSignificantAccuracyImprovement) {
          // 静默跳过，避免日志刷屏，但要包含完整的精度信息
          return {
            success: true,
            coordinates: this.lastProcessedCoordinates,
            city: this.currentCity || DEFAULT_CITY,
            accuracy: this.lastProcessedCoordinates.accuracy, // 🔧 修复：包含精度信息
          };
        }
      }

      console.log('🎯 [LocationService] 收到高德原生定位数据:', coordinates);
      console.log(
        `📊 [LocationService] 定位精度: ±${coordinates.accuracy?.toFixed(0) || '未知'}米`
      );

      // 更新最后处理的坐标
      this.lastProcessedCoordinates = coordinates;

      // 获取城市信息
      const city = this.getCityFromCoordinates(coordinates);

      // 更新当前状态
      this.currentLocation = coordinates;
      this.currentCity = city;

      // 缓存定位数据
      await this.cacheLocationData(coordinates, city);

      const result: LocationResult = {
        success: true,
        city,
        coordinates: {
          latitude: coordinates.latitude,
          longitude: coordinates.longitude,
        },
        accuracy: coordinates.accuracy,
      };

      // 通知所有监听器
      this.notifyLocationUpdate(result);

      console.log('✅ [LocationService] 高德原生定位处理完成');
      return result;
    } catch (error: any) {
      console.error('❌ [LocationService] 处理高德原生定位失败:', error);
      const errorResult: LocationResult = {
        success: false,
        error: error?.message || String(error),
      };
      this.notifyLocationUpdate(errorResult);
      return errorResult;
    }
  }

  /**
   * 获取当前位置信息
   * 主动获取GPS定位或返回缓存/默认数据
   */
  async getCurrentLocation(): Promise<LocationResult> {
    try {
      console.log('📍 [LocationService] 获取当前位置信息...');

      // 如果有实时定位数据，直接返回
      if (this.currentLocation && this.currentCity) {
        console.log('✅ [LocationService] 返回实时高德原生定位数据');
        return {
          success: true,
          city: this.currentCity,
          coordinates: {
            latitude: this.currentLocation.latitude,
            longitude: this.currentLocation.longitude,
          },
          accuracy: this.currentLocation.accuracy,
        };
      }

      // 🎯 关键说明：等待高德地图组件的onLocation回调
      // 权限获得后，地图组件会自动触发定位更新，我们等待回调数据
      console.log('🗺️ [LocationService] 等待高德地图组件提供定位数据...');

      // 尝试获取缓存数据
      console.log('💾 [LocationService] 尝试获取缓存定位数据...');
      const cachedResult = await this.getCachedLocationResult();
      if (cachedResult.success) {
        console.log('✅ [LocationService] 返回缓存定位数据');
        return { ...cachedResult, error: 'using_cached_location' };
      }

      // 返回默认位置
      console.log('🏠 [LocationService] 返回默认南宁位置');
      return {
        success: true,
        city: DEFAULT_CITY,
        coordinates: {
          latitude: DEFAULT_CITY.latitude,
          longitude: DEFAULT_CITY.longitude,
        },
        error: 'using_default_location',
      };
    } catch (error: any) {
      console.error('❌ [LocationService] 获取位置信息失败:', error);
      return {
        success: false,
        error: error?.message || String(error),
      };
    }
  }

  /**
   * 根据坐标判断城市信息
   */
  private getCityFromCoordinates(coordinates: {
    latitude: number;
    longitude: number;
  }): CityInfo {
    try {
      console.log('🏙️ [LocationService] 根据坐标判断城市信息...');

      // 检查是否在南宁范围内
      const distance = this.calculateDistance(
        coordinates.latitude,
        coordinates.longitude,
        DEFAULT_CITY.latitude,
        DEFAULT_CITY.longitude
      );

      console.log(
        `🏙️ [LocationService] 距离南宁市中心: ${distance.toFixed(2)}公里`
      );

      // 如果在南宁范围内（100公里），返回南宁城市信息
      if (distance <= 100) {
        console.log('🏙️ [LocationService] 位置在南宁服务范围内');
        return DEFAULT_CITY;
      } else {
        // 如果不在南宁范围内，仍然返回南宁作为服务城市
        console.log(
          '🏙️ [LocationService] 当前位置不在南宁范围内，但仍返回南宁作为服务城市'
        );
        return DEFAULT_CITY;
      }
    } catch (error: any) {
      console.error('❌ [LocationService] 城市判断失败:', error);
      return DEFAULT_CITY;
    }
  }

  /**
   * 计算两点间距离（公里）
   * 使用Haversine公式
   */
  private calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // 地球半径（公里）
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * 缓存位置数据
   */
  private async cacheLocationData(
    coordinates: { latitude: number; longitude: number; accuracy?: number },
    city: CityInfo
  ): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.setItem(
          CACHE_KEYS.LAST_LOCATION,
          JSON.stringify({
            latitude: coordinates.latitude,
            longitude: coordinates.longitude,
          })
        ),
        AsyncStorage.setItem(CACHE_KEYS.LAST_CITY, JSON.stringify(city)),
        AsyncStorage.setItem(
          CACHE_KEYS.LAST_ACCURACY,
          String(coordinates.accuracy || 0)
        ),
      ]);
      console.log('💾 [LocationService] 高德定位数据已缓存');
    } catch (error) {
      console.error('❌ [LocationService] 缓存失败:', error);
    }
  }

  /**
   * 获取缓存的位置结果
   */
  private async getCachedLocationResult(): Promise<LocationResult> {
    try {
      const [cachedLocation, cachedCity, cachedAccuracy] = await Promise.all([
        AsyncStorage.getItem(CACHE_KEYS.LAST_LOCATION),
        AsyncStorage.getItem(CACHE_KEYS.LAST_CITY),
        AsyncStorage.getItem(CACHE_KEYS.LAST_ACCURACY),
      ]);

      if (cachedLocation && cachedCity) {
        const coordinates = JSON.parse(cachedLocation);
        const city = JSON.parse(cachedCity);
        const accuracy = cachedAccuracy
          ? parseFloat(cachedAccuracy)
          : undefined;

        return {
          success: true,
          city,
          coordinates,
          accuracy,
        };
      }

      return { success: false, error: 'No cached data' };
    } catch (error) {
      console.error('❌ [LocationService] 读取缓存失败:', error);
      return { success: false, error: 'Cache read error' };
    }
  }

  /**
   * 注册位置更新监听器
   */
  onLocationUpdate(callback: (result: LocationResult) => void): () => void {
    this.locationUpdateCallbacks.push(callback);

    // 返回取消监听的函数
    return () => {
      const index = this.locationUpdateCallbacks.indexOf(callback);
      if (index > -1) {
        this.locationUpdateCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * 通知所有位置更新监听器
   */
  private notifyLocationUpdate(result: LocationResult): void {
    this.locationUpdateCallbacks.forEach(callback => {
      try {
        callback(result);
      } catch (error) {
        console.error('❌ [LocationService] 通知监听器失败:', error);
      }
    });
  }

  /**
   * 清除缓存
   */
  async clearCache(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(CACHE_KEYS.LAST_LOCATION),
        AsyncStorage.removeItem(CACHE_KEYS.LAST_CITY),
        AsyncStorage.removeItem(CACHE_KEYS.LAST_ACCURACY),
      ]);
      console.log('🗑️ [LocationService] 缓存已清除');
    } catch (error) {
      console.error('❌ [LocationService] 清除缓存失败:', error);
    }
  }

  /**
   * 获取支持的城市列表
   */
  getSupportedCities(): CityInfo[] {
    return [...SUPPORTED_CITIES];
  }

  /**
   * 获取当前位置状态
   */
  getCurrentLocationState(): {
    hasLocation: boolean;
    coordinates: { latitude: number; longitude: number } | null;
    city: CityInfo | null;
    accuracy?: number;
  } {
    return {
      hasLocation: this.currentLocation !== null,
      coordinates: this.currentLocation
        ? {
            latitude: this.currentLocation.latitude,
            longitude: this.currentLocation.longitude,
          }
        : null,
      city: this.currentCity,
      accuracy: this.currentLocation?.accuracy,
    };
  }

  /**
   * 检查位置权限状态
   */
  async checkLocationPermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
        );
        console.log(
          `🔐 [LocationService] Android位置权限状态: ${granted ? '已授权' : '未授权'}`
        );
        return granted;
      } catch (error) {
        console.error('❌ [LocationService] 检查Android权限失败:', error);
        return false;
      }
    }
    // iOS权限由系统管理，在首次使用地图时自动请求
    console.log('🔐 [LocationService] iOS权限由系统自动管理');
    return true;
  }

  /**
   * 请求位置权限
   * 标准地图应用权限请求流程
   */
  async requestLocationPermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        console.log('🔐 [LocationService] 请求Android位置权限...');

        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: '位置权限请求',
            message:
              '此应用需要位置权限来显示附近房源和提供精准的地图搜索服务。\n\n允许后您可以：\n• 查看附近房源\n• 获取精准定位服务\n• 享受个性化推荐',
            buttonNeutral: '稍后询问',
            buttonNegative: '拒绝',
            buttonPositive: '允许',
          }
        );

        const isGranted = granted === PermissionsAndroid.RESULTS.GRANTED;
        console.log(
          `🔐 [LocationService] Android权限请求结果: ${isGranted ? '用户允许' : '用户拒绝'}`
        );

        // 如果用户拒绝权限，提供友好引导
        if (!isGranted) {
          this.showPermissionGuide();
        }

        return isGranted;
      } catch (error) {
        console.error('❌ [LocationService] Android权限请求失败:', error);
        return false;
      }
    }

    // iOS权限由系统自动处理，当地图组件启用定位时会自动弹出系统权限对话框
    console.log('🔐 [LocationService] iOS权限由系统自动处理');
    return true;
  }

  /**
   * 权限被拒绝后的用户引导
   */
  private showPermissionGuide(): void {
    Alert.alert(
      '需要位置权限',
      '为了为您提供更好的房源推荐服务，请允许访问位置信息。\n\n您可以：\n• 在设置中手动开启位置权限\n• 或继续使用默认的南宁区域浏览房源',
      [
        {
          text: '继续浏览',
          style: 'cancel',
          onPress: () => {
            console.log('🔐 [LocationService] 用户选择继续浏览，使用默认位置');
          },
        },
        {
          text: '去设置',
          onPress: () => {
            console.log('🔐 [LocationService] 引导用户到设置页面');
            Linking.openSettings().catch(() => {
              console.error('❌ [LocationService] 打开设置页面失败');
            });
          },
        },
      ]
    );
  }

  /**
   * 确保有位置权限后再执行定位相关操作
   * 这是标准地图应用的权限处理流程
   */
  async ensureLocationPermission(): Promise<boolean> {
    console.log('🔐 [LocationService] 检查并确保位置权限...');

    // 1. 先检查是否已有权限
    const hasPermission = await this.checkLocationPermission();
    if (hasPermission) {
      console.log('✅ [LocationService] 位置权限已存在');
      return true;
    }

    // 2. 如果没有权限，请求权限
    console.log('⚠️ [LocationService] 位置权限不存在，开始请求权限');
    const granted = await this.requestLocationPermission();

    if (granted) {
      console.log('✅ [LocationService] 位置权限请求成功');
      // 🔧 关键新增：权限刚获得时，清除当前定位状态，强制重新定位
      console.log(
        '🔄 [LocationService] 权限刚获得，清除缓存状态，等待新的定位数据'
      );
      this.currentLocation = null;
      this.currentCity = null;
    } else {
      console.log('❌ [LocationService] 位置权限请求被拒绝');
    }

    return granted;
  }
}

export default LocationService.getInstance();
