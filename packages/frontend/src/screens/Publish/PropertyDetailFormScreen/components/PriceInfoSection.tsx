/**
 * 价格信息区块组件
 * 
 * 职责：
 * 1. 交易类型选择（租赁/出售/转让）
 * 2. 动态价格输入字段
 * 3. 押金设置（租赁时）
 * 4. 物业费输入
 */

import * as React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';
import { CommonFormStyles } from '../../../../shared/components/Form/FormStyles';

// 租金支付方式配置
const RENT_PAYMENT_METHODS = [
  { value: 'MONTHLY', label: '月付' },
  { value: 'QUARTERLY', label: '季付' },
  { value: 'HALF_YEARLY', label: '半年付' },
  { value: 'YEARLY', label: '年付' },
];

interface PriceInfoSectionProps {
  // 表单数据
  transactionTypes: string[];
  rentPrice: string;
  salePrice: string;
  transferPrice: string;
  rentDepositMonths: string;
  rentPaymentMethod?: string;
  propertyFee: string;
  
  // 更新函数
  onTransactionTypeToggle: (type: string) => void;
  onRentPriceChange: (value: string) => void;
  onSalePriceChange: (value: string) => void;
  onTransferPriceChange: (value: string) => void;
  onRentDepositMonthsChange: (value: string) => void;
  onRentPaymentMethodChange?: (value: string) => void;
  onPropertyFeeChange: (value: string) => void;
  
  // 字段位置记录
  onFieldLayout: (fieldName: string, layout: { y: number; height: number }) => void;
  
  // 错误状态
  errors: Record<string, string>;
  
  // 配置数据
  transactionTypeConfig: any;
}

export const PriceInfoSection: React.FC<PriceInfoSectionProps> = ({
  transactionTypes,
  rentPrice,
  salePrice,
  transferPrice,
  rentDepositMonths,
  rentPaymentMethod = 'QUARTERLY',
  propertyFee,
  onTransactionTypeToggle,
  onRentPriceChange,
  onSalePriceChange,
  onTransferPriceChange,
  onRentDepositMonthsChange,
  onRentPaymentMethodChange,
  onPropertyFeeChange,
  onFieldLayout,
  errors,
  transactionTypeConfig,
}) => {

  // 处理价格输入（只允许数字和小数点）
  const handlePriceChange = (value: string, onChange: (value: string) => void) => {
    // 只允许数字和小数点
    const cleanValue = value.replace(/[^0-9.]/g, '');
    
    // 确保只有一个小数点
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      return; // 如果有多个小数点，忽略输入
    }
    
    onChange(cleanValue);
  };

  return (
    <View style={styles.container}>
      {/* 交易方式 */}
      <View 
        style={styles.formSection}
        onLayout={({ nativeEvent }) => onFieldLayout('transaction_types', nativeEvent.layout)}
      >
        <Text style={styles.sectionTitle}>交易方式<Text style={styles.required}> *</Text></Text>
        <View style={styles.transactionTypesContainer}>
          {Object.entries(transactionTypeConfig).map(([key, config]: [string, any]) => (
            <TouchableOpacity
              key={key}
              style={[
                styles.transactionChip,
                transactionTypes.includes(key) && styles.transactionChipSelected
              ]}
              onPress={() => onTransactionTypeToggle(key)}
            >
              <Text style={[
                styles.transactionChipText,
                transactionTypes.includes(key) && styles.transactionChipTextSelected
              ]}>
                {config.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        {errors.transaction_types && (
          <Text style={styles.errorText}>{errors.transaction_types}</Text>
        )}
      </View>

      {/* 租金 - 仅在选择租赁时显示 */}
      {transactionTypes.includes('RENT') && (
        <View 
          style={styles.formSection}
          onLayout={({ nativeEvent }) => onFieldLayout('rent_price', nativeEvent.layout)}
        >
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>租金<Text style={styles.required}> *</Text></Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                value={rentPrice}
                onChangeText={(text) => handlePriceChange(text, onRentPriceChange)}
                placeholder="请输入租金"
                placeholderTextColor="#C7C7CC"
                keyboardType="numeric"
              />
              <Text style={styles.inputUnit}>元/月</Text>
            </View>
            <Text style={styles.inputHint}>只能输入数字和小数点</Text>
          </View>
        </View>
      )}

      {/* 售价 - 仅在选择出售时显示 */}
      {transactionTypes.includes('SALE') && (
        <View 
          style={styles.formSection}
          onLayout={({ nativeEvent }) => onFieldLayout('sale_price', nativeEvent.layout)}
        >
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>售价<Text style={styles.required}> *</Text></Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                value={salePrice}
                onChangeText={(text) => handlePriceChange(text, onSalePriceChange)}
                placeholder="请输入售价"
                placeholderTextColor="#C7C7CC"
                keyboardType="numeric"
              />
              <Text style={styles.inputUnit}>万元</Text>
            </View>
            <Text style={styles.inputHint}>只能输入数字和小数点</Text>
          </View>
        </View>
      )}

      {/* 转让费 - 仅在选择转让时显示 */}
      {transactionTypes.includes('TRANSFER') && (
        <View 
          style={styles.formSection}
          onLayout={({ nativeEvent }) => onFieldLayout('transfer_price', nativeEvent.layout)}
        >
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>转让费<Text style={styles.required}> *</Text></Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                value={transferPrice}
                onChangeText={(text) => handlePriceChange(text, onTransferPriceChange)}
                placeholder="请输入转让费"
                placeholderTextColor="#C7C7CC"
                keyboardType="numeric"
              />
              <Text style={styles.inputUnit}>万元</Text>
            </View>
            <Text style={styles.inputHint}>只能输入数字和小数点</Text>
          </View>
        </View>
      )}

      {/* 押金和支付方式 - 仅在选择租赁时显示 */}
      {transactionTypes.includes('RENT') && (
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>押金</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                value={rentDepositMonths}
                onChangeText={onRentDepositMonthsChange}
                placeholder="请输入押金月数"
                placeholderTextColor="#C7C7CC"
                keyboardType="numeric"
              />
              <Text style={styles.inputUnit}>个月</Text>
            </View>
          </View>
          
          {/* 租金支付方式 */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>支付方式<Text style={styles.required}> *</Text></Text>
            <View style={styles.paymentMethodContainer}>
              {RENT_PAYMENT_METHODS.map((method) => (
                <TouchableOpacity
                  key={method.value}
                  style={[
                    styles.paymentMethodChip,
                    rentPaymentMethod === method.value && styles.paymentMethodChipSelected
                  ]}
                  onPress={() => onRentPaymentMethodChange?.(method.value)}
                >
                  <Text style={[
                    styles.paymentMethodText,
                    rentPaymentMethod === method.value && styles.paymentMethodTextSelected
                  ]}>
                    {method.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            {errors.rent_payment_method && (
              <Text style={styles.errorText}>{errors.rent_payment_method}</Text>
            )}
          </View>
        </View>
      )}

      {/* 物业费 */}
      <View style={styles.formSection}>
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>物业费</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              value={propertyFee}
              onChangeText={onPropertyFeeChange}
              placeholder="请输入物业费"
              placeholderTextColor="#C7C7CC"
              keyboardType="numeric"
            />
            <Text style={styles.inputUnit}>元/㎡/月</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: wp(16),
    marginBottom: hp(6),
  },
  sectionTitle: {
    fontSize: fp(15),
    fontWeight: '600',
    color: '#000000',
    paddingTop: hp(16),
    paddingBottom: hp(12),
  },
  required: {
    color: '#FF3B30',
  },
  inputGroup: {
    paddingVertical: hp(12),
  },
  inputLabel: {
    fontSize: fp(14),
    fontWeight: '500',
    color: '#000000',
    marginBottom: hp(8),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: wp(12),
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    minHeight: hp(48),
  },
  textInput: {
    flex: 1,
    fontSize: fp(16),
    color: '#000000',
    padding: 0,
  },
  inputUnit: {
    fontSize: fp(16),
    color: '#8E8E93',
    marginLeft: wp(8),
  },
  inputHint: {
    fontSize: fp(11),
    color: '#8E8E93',
    marginTop: hp(4),
    marginLeft: wp(2),
    fontStyle: 'italic',
  },
  transactionTypesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: wp(8),
  },
  transactionChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
  },
  transactionChipSelected: {
    backgroundColor: '#007AFF',
  },
  transactionChipText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  transactionChipTextSelected: {
    color: '#FFFFFF',
  },
  // 使用统一的表单样式
  paymentMethodContainer: CommonFormStyles.paymentMethodContainer,
  paymentMethodChip: CommonFormStyles.paymentMethodChip,
  paymentMethodChipSelected: CommonFormStyles.paymentMethodChipSelected,
  paymentMethodText: CommonFormStyles.paymentMethodText,
  paymentMethodTextSelected: CommonFormStyles.paymentMethodTextSelected,
  errorText: CommonFormStyles.errorText,
});

export default PriceInfoSection;