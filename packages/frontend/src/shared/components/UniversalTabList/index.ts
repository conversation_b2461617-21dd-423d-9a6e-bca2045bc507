/**
 * 🏗️ 企业级架构：通用Tab列表组件入口
 * 遵循前端企业级架构规范 - 导出层
 */

// 主要组件
export { UniversalTabListContainer } from './UniversalTabListContainer';

// 子组件
export { UniversalTabNavigation } from './components/UniversalTabNavigation';
export { UniversalSearchModal } from './components/UniversalSearchModal';
export { UniversalListScene } from './components/UniversalListScene';

// Hook
export { useTabListLogic } from './hooks/useTabListLogic';
export { useUniversalSearch } from './hooks/useUniversalSearch';

// 类型
export type {
  TabStatus,
  TabConfig,
  SearchConfig,
  UniversalTabListProps,
  TabListState,
  UseTabListLogicProps,
  UseUniversalSearchProps,
  SearchModalProps,
  TabListTheme,
} from './types';

export { defaultTheme } from './types';
