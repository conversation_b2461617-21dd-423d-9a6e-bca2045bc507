/**
 * 地图搜索屏幕状态管理Hook
 * 企业级架构：整合所有屏幕级状态和业务逻辑
 *
 * 核心优化：解决useEffect无限循环问题
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useMapSearch } from './useMapSearch';
import LocationService from '../../../shared/services/LocationService';
import {
  useCurrentCityData,
  useSetCurrentCity,
} from '../../../shared/stores/globalStore';

// 南宁市中心默认位置
const DEFAULT_CENTER = {
  latitude: 22.8167,
  longitude: 108.3669,
};

// 房源类型选项（按照设计图）
const PROPERTY_TYPES = [
  '商铺',
  '写字楼',
  '摊位',
  '土地厂房',
  '共享办公',
  '活动会场',
];

// 底部功能图标网格数据 - 已删除，不再使用

// Android动画配置 - 已删除，不再需要

interface PropertyMarker {
  id: string;
  latitude: number;
  longitude: number;
  title: string;
  price?: number;
  area?: number;
  propertyType?: string;
}

interface UseMapScreenStateReturn {
  // 地图相关状态
  mapProps: {
    center: { latitude: number; longitude: number };
    userLocation: { latitude: number; longitude: number } | null;
    markers: PropertyMarker[];
    onMapPress: (event: any) => void;
    onMarkerPress: (marker: PropertyMarker) => void;
  };

  // 搜索相关状态
  searchProps: {
    searchQuery: string;
    onSearchChange: (query: string) => void;
    onSearchSubmit: () => void;
    selectedPropertyType: string;
    onPropertyTypeChange: (type: string) => void;
    propertyTypes: string[];
    loading: boolean;
  };

  // 操作按钮状态
  actionProps: {
    onLocationPress: () => void;
    isLocating: boolean;
    onRentFilterPress: () => void;
    onSaleFilterPress: () => void;
    selectedDealType: 'rent' | 'sale' | null;
  };

  // 统计和功能区状态 - 已简化，不再需要

  // 筛选弹窗状态
  filterModalProps: {
    rentModalVisible: boolean;
    saleModalVisible: boolean;
    onRentModalClose: () => void;
    onSaleModalClose: () => void;
    onRentFiltersApply: (filters: any) => void;
    onSaleFiltersApply: (filters: any) => void;
  };

  // 房源类型选择器状态
  selectedPropertyType: string;
  handlePropertyTypeChange: (type: string) => void;
}

export const useMapScreenState = (): UseMapScreenStateReturn => {
  // === 城市状态管理 - 修复无限循环：使用稳定的选择器 ===
  // 🔧 关键修复：分别获取状态和动作，避免对象引用变化
  const currentCity = useCurrentCityData();
  const setCurrentCity = useSetCurrentCity();

  // 稳定化引用 - 现在setCurrentCity和currentCity都是稳定的
  const setCurrentCityRef = useRef(setCurrentCity);
  const currentCityRef = useRef(currentCity);

  // 更新ref值（但引用本身是稳定的）
  useEffect(() => {
    setCurrentCityRef.current = setCurrentCity;
    currentCityRef.current = currentCity;
  });

  // === 地图状态管理 - 独立管理，不依赖城市状态 ===
  // 使用useMemo避免对象引用变化导致的无限循环
  const [center, setCenter] = useState(() => ({
    latitude: DEFAULT_CENTER.latitude,
    longitude: DEFAULT_CENTER.longitude,
  }));

  // 用户定位状态 - 独立于城市状态
  const [userLocation, setUserLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);

  // 定位操作状态
  const [isLocating, setIsLocating] = useState(false);

  // 🔧 修复：使用ref跟踪初始化状态，避免状态依赖
  const isInitializedRef = useRef(false);

  // 增加定位状态锁，防止并发定位请求
  const locationLockRef = useRef(false);

  // 防抖定位：避免频繁定位调用
  const locationCallTimeRef = useRef<number>(0);
  const LOCATION_DEBOUNCE_MS = 5000; // 5秒内不重复定位

  // 距离计算工具函数
  const calculateDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number => {
    const R = 6371; // 地球半径（公里）
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  // 搜索状态
  const [searchQuery, setSearchQuery] = useState('50平青秀商铺');
  const [selectedPropertyType, setSelectedPropertyType] = useState('商铺');

  // 租买选择状态（用于筛选弹窗内部）
  const [dealType, setDealType] = useState<'rent' | 'sale'>('rent');

  // 使用地图搜索Hook
  const {
    properties: searchResults, // 注意：useMapSearch返回的是properties，命名为searchResults便于理解
    loading,
    searchProperties,
  } = useMapSearch({
    onSearchError: error => {
      // 🎯 静默处理：搜索错误时不显示弹窗，通过控制台记录即可
      // 业界最佳实践：地图应用的搜索失败应该静默处理，避免打断用户体验
      console.error('🔍 [MapScreen] 搜索房源失败:', error.message);
    },
  });

  // 使用ref存储搜索函数，避免循环依赖
  const searchPropertiesRef = useRef(searchProperties);
  searchPropertiesRef.current = searchProperties;

  // 房源数据状态
  const [properties, setProperties] = useState<PropertyMarker[]>([]);

  // 🔧 关键修复：获取用户位置和城市信息（完全避免依赖循环）
  const getCurrentLocationRef = useRef<() => Promise<void>>();

  getCurrentLocationRef.current = async () => {
    console.log('🌍 [定位] getCurrentLocation被调用');

    // 多重保护：防止重复定位和循环调用
    if (locationLockRef.current) {
      console.log('🌍 [定位] 定位锁定中，跳过重复调用');
      return;
    }

    // 防抖检查：避免频繁调用
    const now = Date.now();
    if (now - locationCallTimeRef.current < LOCATION_DEBOUNCE_MS) {
      console.log('🌍 [定位] 防抖跳过，距离上次定位不足5秒');
      return;
    }

    // 检查是否已经初始化，避免重复定位
    if (isInitializedRef.current) {
      console.log('🌍 [定位] 已经初始化完成，跳过重复定位');
      return;
    }

    // 设置定位锁和时间戳
    locationLockRef.current = true;
    locationCallTimeRef.current = now;

    // 使用函数式状态更新，完全避免闭包依赖
    setIsLocating(true);
    console.log('🌍 [定位] 开始定位...');

    try {
      // 🎯 完全简化：只获取缓存位置，让高德SDK自动处理权限和实时定位
      console.log('🔓 [MapScreen] 让高德地图SDK完全自动处理权限请求和定位...');

      // 只获取缓存位置用于初始中心点，不强制权限检查
      const locationResult = await LocationService.getCurrentLocation();

      if (
        locationResult.success &&
        locationResult.coordinates &&
        locationResult.city
      ) {
        const { coordinates, city } = locationResult;
        console.log('📍 [MapScreen] 定位成功:', { coordinates, city });

        // 批量更新状态
        setUserLocation(coordinates);
        setCenter(coordinates);

        // 🔧 关键修复：直接比较城市ID，避免ref依赖
        const currentCitySnapshot = currentCityRef.current;
        if (city.id !== currentCitySnapshot.id) {
          console.log(
            `🏙️ [定位] 城市变化: ${currentCitySnapshot.name} → ${city.name}`
          );
          // 使用setTimeout异步更新城市状态，彻底打破同步循环
          setTimeout(() => {
            const latestSetCity = setCurrentCityRef.current;
            latestSetCity(city);
          }, 0);
        }

        // 标记初始化完成
        isInitializedRef.current = true;

        // 🎯 静默定位：不显示弹窗，通过地图箭头显示用户位置
        // 标准地图应用做法：静默定位 + 位置箭头显示，而非弹窗提示
        console.log(
          `✅ [MapScreen] 定位${locationResult.error ? '(降级)' : '(GPS)'}成功: ${city.name}`
        );
        if (locationResult.error) {
          console.log(`📍 [MapScreen] 定位来源: ${locationResult.error}`);
        } else {
          // 真实GPS定位成功 - 记录精度信息
          const distance = calculateDistance(
            coordinates.latitude,
            coordinates.longitude,
            DEFAULT_CENTER.latitude,
            DEFAULT_CENTER.longitude
          );
          console.log(
            `📍 [MapScreen] GPS定位精度: 距离${city.name}市中心${distance.toFixed(1)}公里`
          );
        }
      } else {
        console.log('⚠️ [MapScreen] 定位完全失败，使用默认位置');
        // 🎯 静默处理：不显示弹窗，通过控制台记录即可
        console.log('📍 [MapScreen] 使用南宁默认区域，等待用户手动定位');

        setUserLocation(DEFAULT_CENTER);
        setCenter(DEFAULT_CENTER);
        isInitializedRef.current = true;
      }
    } catch (error) {
      console.error('❌ [MapScreen] 定位异常:', error);
      // 🎯 静默处理：定位异常时不显示错误弹窗，避免干扰用户体验
      console.log('📍 [MapScreen] 定位服务异常，使用默认位置');

      setUserLocation(DEFAULT_CENTER);
      setCenter(DEFAULT_CENTER);
      isInitializedRef.current = true;
    } finally {
      setIsLocating(false);
      locationLockRef.current = false;
      console.log('🌍 [定位] 定位完成');
    }
  };

  // 创建稳定的getCurrentLocation函数
  const getCurrentLocation = useCallback(async () => {
    if (getCurrentLocationRef.current) {
      await getCurrentLocationRef.current();
    }
  }, []); // 🔧 关键修复：空依赖数组，使用ref避免闭包问题

  // 搜索房源
  const handleSearch = useCallback(async () => {
    if (!userLocation) {
      // 🎯 静默处理：不显示弹窗，通过控制台记录即可
      console.log('📍 [MapScreen] 搜索房源需要先获取用户位置');
      return;
    }

    try {
      console.log('🔍 搜索房源:', searchQuery, selectedPropertyType);

      // 使用地图搜索Hook
      await searchPropertiesRef.current({
        center: {
          latitude: userLocation.latitude,
          longitude: userLocation.longitude,
        },
        radius: 5000, // 5公里搜索半径
        query: searchQuery,
        limit: 20,
      });
    } catch (error) {
      console.error('❌ 搜索错误:', error);
      // 错误处理已在Hook中处理
    }
  }, [userLocation, searchQuery, selectedPropertyType]);

  // 功能网格已删除，不再需要切换功能

  // 租赁筛选按钮处理
  const handleRentFilterPress = useCallback(() => {
    console.log('🔍 [Filter] 租赁筛选按钮点击');
    setDealType('rent');
    setRentModalVisible(true);
  }, []);

  // 买房筛选按钮处理
  const handleSaleFilterPress = useCallback(() => {
    console.log('🔍 [Filter] 买房筛选按钮点击');
    setDealType('sale');
    setSaleModalVisible(true);
  }, []);

  // 租买切换处理 - 现在通过左侧按钮直接筛选，不再需要弹窗内切换

  // 地图点击处理
  const handleMapPress = useCallback((event: any) => {
    console.log('[MapView] 地图点击:', event.nativeEvent);
  }, []);

  // 标记点击处理
  const handleMarkerPress = useCallback((marker: PropertyMarker) => {
    console.log('[Marker] 标记点击:', marker.title);
  }, []);

  // 🔧 关键修复：页面加载时自动定位用户位置，只执行一次
  useEffect(() => {
    console.log('🚀 [MapScreen] 页面初始化，开始自动定位用户位置...');
    // 延迟执行，确保组件完全挂载并避免同步状态更新
    const initTimer = setTimeout(() => {
      getCurrentLocation();
    }, 100);

    return () => {
      if (initTimer) {
        clearTimeout(initTimer);
        console.log('🚀 [MapScreen] 组件卸载，清理定时器');
      }
    };
  }, [getCurrentLocation]); // 添加getCurrentLocation依赖

  // 自动搜索房源：当用户位置获取成功后（只执行一次）
  const hasAutoSearchedRef = useRef(false);
  useEffect(() => {
    if (userLocation && !loading && !hasAutoSearchedRef.current) {
      console.log('📍 [MapScreen] 用户位置已获取，开始自动搜索房源');
      hasAutoSearchedRef.current = true; // 🔧 防止重复搜索

      // 延迟一点时间，确保地图已经渲染完成
      const timer = setTimeout(async () => {
        try {
          await searchPropertiesRef.current({
            center: {
              latitude: userLocation.latitude,
              longitude: userLocation.longitude,
            },
            radius: 5000,
            query: searchQuery,
            limit: 20,
          });
        } catch (error) {
          console.error('🔍 [MapScreen] 自动搜索失败:', error);
          // 🔧 搜索失败时不重试，避免无限循环
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [userLocation, loading]); // 🔧 移除handleSearch依赖，避免循环

  // 更新房源数据（仅在用户主动搜索后）
  useEffect(() => {
    if (searchResults.length > 0) {
      // 转换搜索结果为组件需要的格式
      const convertedProperties = searchResults.map(prop => ({
        id: prop.id,
        latitude: prop.coordinate?.latitude || 0,
        longitude: prop.coordinate?.longitude || 0,
        title: prop.title,
        price:
          typeof prop.price === 'string'
            ? parseInt(prop.price) || 0
            : prop.price || 0,
        // 注意：MapMarker类型中没有area属性，使用address和district信息
        area: 0, // 默认值，后续可从title中解析或后端添加area字段
        propertyType: prop.type || '商铺',
      }));
      setProperties(convertedProperties);
      console.log(`✅ [MapScreen] 显示${convertedProperties.length}个房源`);
    } else {
      // 清空房源数据（用户清除搜索或搜索无结果时）
      setProperties([]);
      console.log('🧹 [MapScreen] 清空房源显示');
    }
  }, [searchResults]);

  // 使用useMemo稳定化mapProps对象引用
  const mapProps = useMemo(
    () => ({
      center,
      userLocation,
      markers: properties, // 🔧 修复：MapContainer期望markers属性
      onMapPress: handleMapPress,
      onMarkerPress: handleMarkerPress,
    }),
    [center, userLocation, properties, handleMapPress, handleMarkerPress]
  );

  // 使用useMemo稳定化所有props对象引用
  const searchProps = useMemo(
    () => ({
      searchQuery,
      onSearchChange: setSearchQuery,
      onSearchSubmit: handleSearch,
      selectedPropertyType,
      onPropertyTypeChange: setSelectedPropertyType,
      propertyTypes: PROPERTY_TYPES,
      loading,
    }),
    [searchQuery, handleSearch, selectedPropertyType, loading]
  );

  const actionProps = useMemo(
    () => ({
      onLocationPress: getCurrentLocation,
      isLocating,
      onRentFilterPress: handleRentFilterPress,
      onSaleFilterPress: handleSaleFilterPress,
      selectedDealType: dealType,
    }),
    [
      getCurrentLocation,
      isLocating,
      handleRentFilterPress,
      handleSaleFilterPress,
      dealType,
    ]
  );

  // statsProps已删除，不再需要

  // 筛选弹窗状态
  const [rentModalVisible, setRentModalVisible] = useState(false);
  const [saleModalVisible, setSaleModalVisible] = useState(false);

  const filterModalProps = useMemo(
    () => ({
      rentModalVisible,
      saleModalVisible,
      onRentModalClose: () => setRentModalVisible(false),
      onSaleModalClose: () => setSaleModalVisible(false),
      onRentFiltersApply: (filters: any) => {
        console.log('🏠 [筛选] 应用租赁筛选:', filters);
        setRentModalVisible(false);
        // TODO: 应用筛选条件到地图房源显示
      },
      onSaleFiltersApply: (filters: any) => {
        console.log('🏠 [筛选] 应用出售筛选:', filters);
        setSaleModalVisible(false);
        // TODO: 应用筛选条件到地图房源显示
      },
    }),
    [rentModalVisible, saleModalVisible]
  );

  // 房源类型选择器状态（复用searchProps中的selectedPropertyType）
  const handlePropertyTypeChange = useCallback((type: string) => {
    console.log('🏷️ [PropertyTypeSelector] 房源类型变更:', type);
    setSelectedPropertyType(type);
  }, []);

  return {
    mapProps,
    searchProps,
    actionProps,
    filterModalProps,
    selectedPropertyType,
    handlePropertyTypeChange,
  };
};
