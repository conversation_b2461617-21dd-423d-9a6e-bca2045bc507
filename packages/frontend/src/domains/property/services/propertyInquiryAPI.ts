/**
 * 房源咨询API服务 - 企业级版本
 *
 * 处理房源咨询记录的创建和管理，用于聊天系统集成
 * 使用统一数据转换层架构，确保类型安全和数据一致性
 */

import { apiClient } from '../../../shared/services/client';
import { Transformers } from '../../../shared/services/dataTransform';
import type {
  InquiryFormData,
  InquiryFormResponse,
  UserRoleInfoUI,
  ChatInitUIResponse
} from '../../../shared/services/dataTransform/transformers/InquiryTransformer';

// 保留原有接口定义以兼容现有代码，但标记为已废弃
/** @deprecated 请使用 InquiryFormData 替代 */
export interface PropertyInquiryRequest {
  property_id: string;
  inquiry_type: 'viewed' | 'favorited' | 'messaged';
  message_content: string;
}

/** @deprecated 请使用 InquiryFormResponse 替代 */
export interface PropertyInquiryResponse {
  id: string;
  property_id: string;
  tenant_id: string;
  landlord_id: string;
  inquiry_type: string;
  status: string;
  message?: string;
  created_at: string;
  updated_at: string;
}

/** @deprecated 请使用 ChatInitUIResponse 替代 */
export interface ChatInitResponse {
  inquiry_id: string;
  chat_room_id: string;
  landlord_info: {
    id: string;
    name: string;
    avatar?: string;
  };
  property_info: {
    id: string;
    title: string;
    location: string;
    price: string;
    area: string;
    property_type: string;
  };
}

/**
 * 房源咨询API服务类
 */
export class PropertyInquiryAPI {
  
  /**
   * 创建房源咨询记录 - 使用转换层
   */
  static async createInquiry(inquiryData: InquiryFormData): Promise<InquiryFormResponse> {
    try {
      console.log('[PropertyInquiryAPI] 创建咨询记录:', inquiryData);

      // 使用转换层将前端数据转换为API格式
      const apiDataResult = Transformers.inquiry.toAPI(inquiryData);
      if (!apiDataResult.success || !apiDataResult.data) {
        throw new Error(apiDataResult.error || '数据转换失败');
      }

      // 调用API
      const response = await apiClient.post('/inquiries', apiDataResult.data);

      // 使用转换层将API响应转换为前端格式
      const uiResponseResult = Transformers.inquiry.transformAPIResponseToUI(response.data);
      if (!uiResponseResult.success || !uiResponseResult.data) {
        throw new Error(uiResponseResult.error || '响应转换失败');
      }

      const result = uiResponseResult.data;

      // 特殊处理：如果是重复咨询但有inquiryId，视为成功
      if (!result.success && result.inquiryId && result.error?.includes('已经咨询过')) {
        console.log('[PropertyInquiryAPI] 检测到重复咨询，使用现有咨询记录:', result.inquiryId);
        result.success = true;  // 标记为成功，允许继续聊天流程
      }

      console.log('[PropertyInquiryAPI] 咨询记录创建成功:', result);
      return result;

    } catch (error: any) {
      console.error('[PropertyInquiryAPI] 创建咨询记录失败:', error);
      throw new Error(error.response?.data?.detail || error.message || '创建咨询记录失败');
    }
  }
  
  /**
   * 初始化聊天会话 - 使用转换层
   * 创建咨询记录并返回聊天所需信息
   */
  static async initializeChat(propertyId: string, message?: string): Promise<ChatInitUIResponse> {
    try {
      console.log('[PropertyInquiryAPI] 初始化聊天会话:', { propertyId, message });

      // 创建咨询记录
      const inquiryData: InquiryFormData = {
        propertyId: propertyId,
        inquiryType: 'messaged',
        messageContent: message || '您好！我对您的房源很感兴趣，想了解更多详情。'
      };

      const inquiry = await this.createInquiry(inquiryData);

      // 获取房源详情和业主信息
      const chatInfo = await this.getChatInfo(inquiry.inquiryId!);

      return chatInfo;

    } catch (error: any) {
      console.error('[PropertyInquiryAPI] 初始化聊天会话失败:', error);
      throw new Error(error.message || '初始化聊天失败');
    }
  }
  
  /**
   * 获取聊天信息 - 使用转换层
   */
  static async getChatInfo(inquiryId: string): Promise<ChatInitUIResponse> {
    try {
      console.log('[PropertyInquiryAPI] 获取聊天信息:', inquiryId);

      const response = await apiClient.get(`/inquiries/${inquiryId}/chat-info`);

      // 使用转换层将API响应转换为前端格式
      const uiResponseResult = Transformers.inquiry.transformChatInitResponseToUI(response.data);
      if (!uiResponseResult.success || !uiResponseResult.data) {
        throw new Error(uiResponseResult.error || '响应转换失败');
      }

      console.log('[PropertyInquiryAPI] 聊天信息获取成功:', uiResponseResult.data);
      return uiResponseResult.data;

    } catch (error: any) {
      console.error('[PropertyInquiryAPI] 获取聊天信息失败:', error);

      // 🚨 删除模拟数据，强制使用真实API
      throw new Error(error.response?.data?.detail || error.message || '获取聊天信息失败');
    }
  }
  

  
  /**
   * 获取用户的咨询记录列表
   */
  static async getUserInquiries(
    role: 'tenant' | 'landlord',
    page: number = 1,
    pageSize: number = 20
  ): Promise<{
    inquiries: PropertyInquiryResponse[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      console.log('[PropertyInquiryAPI] 获取用户咨询记录:', { role, page, pageSize });
      
      const response = await apiClient.get('/inquiries/my', {
        params: {
          role,
          page,
          page_size: pageSize
        }
      });
      
      console.log('[PropertyInquiryAPI] 咨询记录获取成功:', response.data);
      return response.data;
      
    } catch (error: any) {
      console.error('[PropertyInquiryAPI] 获取咨询记录失败:', error);
      
      // 返回空列表，保证UI正常显示
      return {
        inquiries: [],
        total: 0,
        hasMore: false
      };
    }
  }
  
  /**
   * 更新咨询记录状态
   */
  static async updateInquiryStatus(
    inquiryId: string,
    status: 'ACTIVE' | 'CLOSED' | 'ARCHIVED'
  ): Promise<PropertyInquiryResponse> {
    try {
      console.log('[PropertyInquiryAPI] 更新咨询状态:', { inquiryId, status });
      
      const response = await apiClient.patch(`/inquiries/${inquiryId}`, {
        status
      });
      
      console.log('[PropertyInquiryAPI] 咨询状态更新成功:', response.data);
      return response.data;
      
    } catch (error: any) {
      console.error('[PropertyInquiryAPI] 更新咨询状态失败:', error);
      throw new Error(error.response?.data?.detail || '更新咨询状态失败');
    }
  }
}

/**
 * React Hook for property inquiry operations
 */
export const usePropertyInquiry = () => {
  const initializeChat = async (propertyId: string, message?: string) => {
    try {
      const chatInfo = await PropertyInquiryAPI.initializeChat(propertyId, message);
      return chatInfo;
    } catch (error) {
      console.error('[usePropertyInquiry] 初始化聊天失败:', error);
      throw error;
    }
  };
  
  const createInquiry = async (inquiryData: PropertyInquiryRequest) => {
    try {
      const inquiry = await PropertyInquiryAPI.createInquiry(inquiryData);
      return inquiry;
    } catch (error) {
      console.error('[usePropertyInquiry] 创建咨询失败:', error);
      throw error;
    }
  };
  
  return {
    initializeChat,
    createInquiry
  };
};
