/**
 * 通用计数显示组件
 * 适用于Tab计数、统计卡片、徽章等所有计数场景
 * 遵循企业级架构规范 - UI层
 */
import React, { memo } from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { wp, hp, fp } from '@shared/utils/responsiveUtils';

// 🏗️ 企业级架构：计数显示类型定义
export type CountDisplayVariant =
  | 'tab' // Tab计数：已发布(5)
  | 'badge' // 徽章计数：消息小红点
  | 'stat-card' // 统计卡片：收藏数量
  | 'inline' // 内联计数：文本中的数字
  | 'large-stat'; // 大型统计：用户中心主要数据

export type CountDisplaySize = 'small' | 'medium' | 'large';
export type CountDisplayColor =
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error';

export interface CountDisplayProps {
  // 核心数据
  count: number;
  maxCount?: number; // 最大显示数量，超过显示99+

  // 样式变体
  variant?: CountDisplayVariant;
  size?: CountDisplaySize;
  color?: CountDisplayColor;

  // 显示控制
  showZero?: boolean; // 是否显示0
  hideWhenZero?: boolean; // 为0时是否隐藏
  prefix?: string; // 前缀文本
  suffix?: string; // 后缀文本

  // 自定义样式
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;

  // 动画
  animated?: boolean;
}

export const CountDisplay = memo<CountDisplayProps>(
  ({
    count,
    maxCount = 99,
    variant = 'inline',
    size = 'medium',
    color = 'primary',
    showZero = false,
    hideWhenZero = false,
    prefix = '',
    suffix = '',
    containerStyle,
    textStyle,
    animated = false,
  }) => {
    // 🔧 计数显示逻辑
    const shouldHide = hideWhenZero && count === 0;
    const shouldShowZero = showZero && count === 0;

    if (shouldHide) return null;

    // 🔧 格式化计数文本
    const formatCount = (num: number): string => {
      if (num === 0 && !shouldShowZero) return '';
      if (num > maxCount) return `${maxCount}+`;
      return num.toString();
    };

    const displayText = `${prefix}${formatCount(count)}${suffix}`;

    // 🎨 样式计算
    const containerStyles = [
      styles.base,
      styles[`${variant}Container`],
      styles[`${size}Container`],
      styles[`${color}Container`],
      containerStyle,
    ];

    const textStyles = [
      styles.baseText,
      styles[`${variant}Text`],
      styles[`${size}Text`],
      styles[`${color}Text`],
      textStyle,
    ];

    return (
      <View style={containerStyles}>
        <Text style={textStyles}>{displayText}</Text>
      </View>
    );
  }
);

CountDisplay.displayName = 'CountDisplay';

// 🎨 样式定义
const styles = StyleSheet.create({
  // 基础样式
  base: {
    justifyContent: 'center',
    alignItems: 'center',
  },

  baseText: {
    fontWeight: '500',
    textAlign: 'center',
  },

  // === 变体样式 ===

  // Tab计数样式
  tabContainer: {
    backgroundColor: 'transparent',
    paddingHorizontal: wp(8),
    paddingVertical: hp(2),
    borderRadius: wp(12),
  },

  tabText: {
    fontSize: fp(14),
    fontWeight: '600',
  },

  // 徽章样式
  badgeContainer: {
    backgroundColor: '#FF6B35',
    borderRadius: wp(10),
    minWidth: wp(18),
    height: wp(18),
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },

  badgeText: {
    fontSize: fp(10),
    fontWeight: '600',
    color: '#FFFFFF',
  },

  // 统计卡片样式
  'stat-cardContainer': {
    backgroundColor: 'transparent',
    paddingHorizontal: wp(4),
  },

  'stat-cardText': {
    fontSize: fp(16),
    fontWeight: '700',
  },

  // 内联样式
  inlineContainer: {
    backgroundColor: 'transparent',
  },

  inlineText: {
    fontSize: fp(14),
    fontWeight: '500',
  },

  // 大型统计样式
  'large-statContainer': {
    backgroundColor: 'transparent',
    paddingVertical: hp(8),
  },

  'large-statText': {
    fontSize: fp(24),
    fontWeight: '700',
  },

  // === 尺寸样式 ===

  smallContainer: {
    transform: [{ scale: 0.8 }],
  },

  smallText: {
    fontSize: fp(10),
  },

  mediumContainer: {
    // 默认尺寸
  },

  mediumText: {
    fontSize: fp(14),
  },

  largeContainer: {
    transform: [{ scale: 1.2 }],
  },

  largeText: {
    fontSize: fp(16),
  },

  // === 颜色样式 ===

  primaryContainer: {
    // 主色调容器样式
  },

  primaryText: {
    color: '#FF6B35',
  },

  secondaryContainer: {
    // 次要色调容器样式
  },

  secondaryText: {
    color: '#666666',
  },

  successContainer: {
    // 成功色调容器样式
  },

  successText: {
    color: '#4CAF50',
  },

  warningContainer: {
    // 警告色调容器样式
  },

  warningText: {
    color: '#FF9800',
  },

  errorContainer: {
    // 错误色调容器样式
  },

  errorText: {
    color: '#F44336',
  },
});

export default CountDisplay;
