/**
 * 认证相关类型定义
 * 独立的类型文件，避免循环依赖
 */

// 用户信息接口
export interface User {
  id: string;
  phone_number: string;
  display_name?: string;
  avatar_url?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
  
  // 扩展字段
  role?: 'tenant' | 'landlord' | 'agent';
  email?: string;
  real_name?: string;
  id_card?: string;
  company_name?: string;
  business_license?: string;
}

// 认证状态枚举
export enum AuthStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  AUTHENTICATED = 'authenticated',
  UNAUTHENTICATED = 'unauthenticated',
  ERROR = 'error'
}

// 登录凭据接口
export interface LoginCredentials {
  phone_number: string;
  verification_code?: string;
  password?: string;
}

// 认证令牌接口
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  tokenExpiry: number;
}

// 登录响应接口
export interface LoginResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  tokenExpiry: number;
}

// 刷新令牌响应接口
export interface RefreshTokenResponse {
  accessToken: string;
  tokenExpiry: number;
}

// 验证码请求接口
export interface SendSMSRequest {
  phone_number: string;
  purpose: 'login' | 'register' | 'reset_password' | 'change_phone';
}

// 验证码响应接口
export interface SendSMSResponse {
  success: boolean;
  message: string;
  expires_in: number;
}

// 用户更新请求接口
export interface UpdateUserRequest {
  display_name?: string;
  avatar_url?: string;
  email?: string;
  real_name?: string;
  company_name?: string;
}

// 认证错误类型
export interface AuthError {
  code: string;
  message: string;
  details?: any;
}
