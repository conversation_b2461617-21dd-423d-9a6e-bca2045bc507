"""
短信积分管理API路由
企业级房源咨询消息系统的短信积分管理接口
"""

import uuid
from typing import Dict, Any, Optional
from decimal import Decimal
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query, status, Request
from sqlmodel.ext.asyncio.session import AsyncSession
from pydantic import BaseModel, Field

from app.api.deps import CurrentUser, get_current_user_simple
from app.core.db import get_async_session
from app.models.user import User
from app.models.sms_credit import SMSType
from app.services.notification_service import NotificationService
from app.services.paid_sms_service import PaidSMSService

router = APIRouter(prefix="/sms-credit", tags=["sms_credit"])


# Pydantic模型定义
class SMSBalanceResponse(BaseModel):
    """短信余额响应模型"""
    success: bool
    user_id: Optional[str] = None
    account_id: Optional[str] = None
    subscription_sms_balance: Optional[int] = None
    subscription_sms_total: Optional[int] = None
    credits_balance: Optional[int] = None
    money_balance: Optional[float] = None
    total_available: Optional[int] = None
    auto_recharge_enabled: Optional[bool] = None
    auto_recharge_threshold: Optional[int] = None
    auto_recharge_amount: Optional[float] = None
    paid_notifications: Optional[Dict[str, bool]] = None
    low_balance_warning: Optional[bool] = None
    error: Optional[str] = None

class RechargeRequest(BaseModel):
    """充值请求模型"""
    recharge_amount: float = Field(..., gt=0, le=10000, description="充值金额（超级用户可充值更多）")
    payment_method: str = Field(..., description="支付方式")
    mock_payment: Optional[bool] = Field(default=False, description="是否为模拟支付（开发阶段使用）")
    package_id: Optional[str] = Field(default=None, description="套餐ID（如professional_399）")
    
class RechargeResponse(BaseModel):
    """充值响应模型"""
    success: bool
    message: Optional[str] = None
    recharge_id: Optional[str] = None
    recharge_amount: Optional[float] = None
    credits_amount: Optional[int] = None
    new_balance: Optional[Dict[str, int]] = None
    error: Optional[str] = None

class UsageHistoryResponse(BaseModel):
    """使用历史响应模型"""
    success: bool
    usage_history: Optional[list] = None
    pagination: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class NotificationSettingsRequest(BaseModel):
    """通知设置请求模型"""
    inquiry_received: Optional[bool] = None
    message_received: Optional[bool] = None
    appointment_request: Optional[bool] = None
    payment_success: Optional[bool] = None

class NotificationSettingsResponse(BaseModel):
    """通知设置响应模型"""
    success: bool
    message: Optional[str] = None
    notification_settings: Optional[Dict[str, bool]] = None
    error: Optional[str] = None

class MonthlyStatsResponse(BaseModel):
    """月度统计响应模型"""
    success: bool
    period: Optional[str] = None
    statistics: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.get("/balance", response_model=SMSBalanceResponse, summary="查询短信余额")
async def get_sms_balance(
    request: Request,
    current_user: User = Depends(get_current_user_simple),
    db: AsyncSession = Depends(get_async_session)
):
    """
    查询用户短信余额
    
    返回:
    - 订阅短信余额 (399元订阅包含的50条短信)
    - 积分余额 (充值购买的积分)
    - 总可用短信数
    - 自动充值设置
    - 付费通知设置
    """
    try:
        # 初始化服务
        notification_service = NotificationService(db)
        paid_sms_service = PaidSMSService(db, notification_service)
        
        # 获取余额信息
        result = await paid_sms_service.check_sms_balance(current_user.id)
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )
        
        return SMSBalanceResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询余额失败: {str(e)}"
        )


@router.post("/recharge", response_model=RechargeResponse, summary="短信积分充值")
async def recharge_sms_credits(
    request: RechargeRequest,
    current_user: CurrentUser,
    db: AsyncSession = Depends(get_async_session)
):
    """
    短信积分充值
    
    - **recharge_amount**: 充值金额 (1-1000元)
    - **payment_method**: 支付方式 (wechat/alipay/bank_card)
    
    充值规则:
    - 1元 = 10积分 = 10条短信
    - 最低充值1元，最高充值1000元
    - 支持微信、支付宝、银行卡支付
    """
    try:
        # 验证充值金额（超级用户有更高限额）
        max_amount = 10000 if current_user.is_superuser else 1000
        if request.recharge_amount < 1 or request.recharge_amount > max_amount:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"充值金额必须在1-{max_amount}元之间"
            )

        # 验证支付方式（支持模拟支付）
        valid_payment_methods = ["wechat", "alipay", "bank_card", "mock_alipay", "mock_wechat"]
        if request.payment_method not in valid_payment_methods:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的支付方式，支持: {', '.join(valid_payment_methods)}"
            )

        # 开发阶段模拟支付验证
        is_mock_payment = request.mock_payment or request.payment_method.startswith('mock_')
        if is_mock_payment:
            print(f"[SMS充值] 模拟支付模式: 用户={current_user.phone_number}, 金额={request.recharge_amount}元")

        # 超级用户可以随意充值（开发便利性）
        if current_user.is_superuser and is_mock_payment:
            print(f"[SMS充值] 超级用户模拟充值: {request.recharge_amount}元")
        
        # 初始化服务
        notification_service = NotificationService(db)
        paid_sms_service = PaidSMSService(db, notification_service)
        
        # 执行充值
        result = await paid_sms_service.recharge_sms_credits(
            user_id=current_user.id,
            recharge_amount=Decimal(str(request.recharge_amount)),
            payment_method=request.payment_method
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )
        
        return RechargeResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"充值失败: {str(e)}"
        )


@router.post("/dev-recharge", response_model=RechargeResponse, summary="开发阶段快速充值（超级用户专用）")
async def dev_quick_recharge(
    amount: float = Query(default=100.0, description="充值金额"),
    current_user: User = Depends(CurrentUser),
    db: AsyncSession = Depends(get_async_session)
):
    """
    开发阶段快速充值接口（仅超级用户可用）

    - **amount**: 充值金额，默认100元

    这个接口专为开发阶段设计，允许超级用户快速充值任意金额的积分，
    方便测试各种支付和积分扣费场景。
    """
    # 验证超级用户权限
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="此接口仅限超级用户使用"
        )

    try:
        print(f"[开发充值] 超级用户快速充值: {current_user.phone_number} -> {amount}元")

        # 初始化服务
        notification_service = NotificationService(db)
        paid_sms_service = PaidSMSService(db, notification_service)

        # 执行充值
        result = await paid_sms_service.recharge_sms_credits(
            user_id=current_user.id,
            recharge_amount=Decimal(str(amount)),
            payment_method="dev_recharge"
        )

        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )

        print(f"[开发充值] 充值成功: +{result['credits_amount']}积分")
        return RechargeResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"开发充值失败: {str(e)}"
        )


@router.get("/usage-history", response_model=UsageHistoryResponse, summary="查询使用历史")
async def get_usage_history(
    current_user: CurrentUser,
    db: AsyncSession = Depends(get_async_session),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    sms_type: Optional[SMSType] = Query(None, description="短信类型过滤")
):
    """
    查询短信使用历史
    
    - **page**: 页码 (默认1)
    - **page_size**: 每页数量 (默认20，最大100)
    - **sms_type**: 短信类型过滤 (可选)
    
    返回:
    - 使用历史记录
    - 发送状态和结果
    - 费用扣减详情
    - 余额变化记录
    """
    try:
        # 初始化服务
        notification_service = NotificationService(db)
        paid_sms_service = PaidSMSService(db, notification_service)
        
        # 获取使用历史
        result = await paid_sms_service.get_sms_usage_history(
            user_id=current_user.id,
            page=page,
            page_size=page_size,
            sms_type_filter=sms_type
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )
        
        return UsageHistoryResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询使用历史失败: {str(e)}"
        )


@router.get("/monthly-stats", response_model=MonthlyStatsResponse, summary="获取月度统计")
async def get_monthly_stats(
    current_user: CurrentUser,
    db: AsyncSession = Depends(get_async_session),
    year: int = Query(..., ge=2020, le=2030, description="年份"),
    month: int = Query(..., ge=1, le=12, description="月份")
):
    """
    获取月度短信统计
    
    - **year**: 年份 (2020-2030)
    - **month**: 月份 (1-12)
    
    返回:
    - 月度发送统计
    - 成功率分析
    - 费用统计
    - 类型分布
    """
    try:
        # 初始化服务
        notification_service = NotificationService(db)
        paid_sms_service = PaidSMSService(db, notification_service)
        
        # 获取月度统计
        result = await paid_sms_service.get_monthly_sms_statistics(
            user_id=current_user.id,
            year=year,
            month=month
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )
        
        return MonthlyStatsResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取月度统计失败: {str(e)}"
        )


@router.put("/notification-settings", response_model=NotificationSettingsResponse, summary="更新通知设置")
async def update_notification_settings(
    request: NotificationSettingsRequest,
    current_user: CurrentUser,
    db: AsyncSession = Depends(get_async_session)
):
    """
    更新付费通知设置
    
    - **inquiry_received**: 是否接收咨询通知
    - **message_received**: 是否接收消息通知
    - **appointment_request**: 是否接收预约通知
    - **payment_success**: 是否接收付款成功通知
    
    注意:
    - 只更新提供的字段
    - 未提供的字段保持原有设置
    """
    try:
        # 构建更新字典
        notification_updates = {}
        if request.inquiry_received is not None:
            notification_updates["inquiry_received"] = request.inquiry_received
        if request.message_received is not None:
            notification_updates["message_received"] = request.message_received
        if request.appointment_request is not None:
            notification_updates["appointment_request"] = request.appointment_request
        if request.payment_success is not None:
            notification_updates["payment_success"] = request.payment_success
        
        if not notification_updates:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="至少需要更新一个通知设置"
            )
        
        # 初始化服务
        notification_service = NotificationService(db)
        paid_sms_service = PaidSMSService(db, notification_service)
        
        # 更新通知设置
        result = await paid_sms_service.update_notification_settings(
            user_id=current_user.id,
            notification_settings=notification_updates
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )
        
        return NotificationSettingsResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新通知设置失败: {str(e)}"
        )


@router.get("/notification-settings", response_model=NotificationSettingsResponse, summary="获取通知设置")
async def get_notification_settings(
    current_user: CurrentUser,
    db: AsyncSession = Depends(get_async_session)
):
    """
    获取当前通知设置
    
    返回:
    - 各类通知的开关状态
    - 当前配置概览
    """
    try:
        # 初始化服务
        notification_service = NotificationService(db)
        paid_sms_service = PaidSMSService(db, notification_service)
        
        # 获取账户信息
        account_info = await paid_sms_service.check_sms_balance(current_user.id)
        
        if not account_info["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=account_info["error"]
            )
        
        return NotificationSettingsResponse(
            success=True,
            message="获取通知设置成功",
            notification_settings=account_info["paid_notifications"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取通知设置失败: {str(e)}"
        )


@router.post("/auto-recharge", summary="设置自动充值")
async def setup_auto_recharge(
    current_user: CurrentUser,
    db: AsyncSession = Depends(get_async_session),
    enabled: bool = Query(..., description="是否启用自动充值"),
    threshold: int = Query(5, ge=1, le=50, description="自动充值阈值"),
    amount: float = Query(10.0, ge=1.0, le=100.0, description="自动充值金额")
):
    """
    设置自动充值
    
    - **enabled**: 是否启用自动充值
    - **threshold**: 自动充值阈值 (1-50积分)
    - **amount**: 自动充值金额 (1-100元)
    
    当余额低于阈值时自动充值指定金额
    """
    try:
        # 初始化服务
        notification_service = NotificationService(db)
        paid_sms_service = PaidSMSService(db, notification_service)
        
        # 获取账户
        account = await paid_sms_service.get_or_create_sms_account(current_user.id)
        
        # 更新自动充值设置
        account.auto_recharge_enabled = enabled
        account.auto_recharge_threshold = threshold
        account.auto_recharge_amount = Decimal(str(amount))
        
        db.add(account)
        await db.commit()
        await db.refresh(account)
        
        return {
            "success": True,
            "message": "自动充值设置更新成功",
            "settings": {
                "enabled": account.auto_recharge_enabled,
                "threshold": account.auto_recharge_threshold,
                "amount": float(account.auto_recharge_amount)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"设置自动充值失败: {str(e)}"
        )


@router.get("/recharge-history", summary="查询充值历史")
async def get_recharge_history(
    current_user: CurrentUser,
    db: AsyncSession = Depends(get_async_session),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """
    查询充值历史
    
    - **page**: 页码 (默认1)
    - **page_size**: 每页数量 (默认20，最大100)
    
    返回:
    - 充值记录列表
    - 支付状态和结果
    - 充值金额和积分
    """
    try:
        from app.models.sms_credit import SMSCreditRecharge
        from sqlmodel import select, func
        
        # 构建查询条件
        conditions = [SMSCreditRecharge.user_id == current_user.id]
        
        # 分页查询
        offset = (page - 1) * page_size
        
        statement = select(SMSCreditRecharge).where(*conditions).order_by(
            SMSCreditRecharge.created_at.desc()
        ).offset(offset).limit(page_size)
        
        recharge_records = (await db.exec(statement)).all()
        
        # 统计总数
        count_statement = select(func.count(SMSCreditRecharge.id)).where(*conditions)
        total_count = (await db.exec(count_statement)).first()
        
        # 构建返回数据
        recharge_list = []
        for record in recharge_records:
            recharge_data = {
                "id": record.id,
                "recharge_amount": float(record.recharge_amount),
                "credits_amount": record.credits_amount,
                "payment_method": record.payment_method,
                "status": record.status.value,
                "payment_order_id": record.payment_order_id,
                "is_auto_recharge": record.is_auto_recharge,
                "created_at": record.created_at.isoformat() if record.created_at else None,
                "completed_at": record.completed_at.isoformat() if record.completed_at else None
            }
            recharge_list.append(recharge_data)
        
        return {
            "success": True,
            "recharge_history": recharge_list,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": (total_count + page_size - 1) // page_size
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询充值历史失败: {str(e)}"
        )


@router.get("/account-overview", summary="获取账户概览")
async def get_account_overview(
    current_user: CurrentUser,
    db: AsyncSession = Depends(get_async_session)
):
    """
    获取账户概览信息
    
    返回:
    - 余额概览
    - 使用统计
    - 充值统计
    - 通知设置
    """
    try:
        # 初始化服务
        notification_service = NotificationService(db)
        paid_sms_service = PaidSMSService(db, notification_service)
        
        # 获取基本信息
        balance_info = await paid_sms_service.check_sms_balance(current_user.id)
        
        if not balance_info["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=balance_info["error"]
            )
        
        # 获取本月统计
        now = datetime.now()
        monthly_stats = await paid_sms_service.get_monthly_sms_statistics(
            user_id=current_user.id,
            year=now.year,
            month=now.month
        )
        
        return {
            "success": True,
            "account_overview": {
                "balance": {
                    "subscription_sms": balance_info["subscription_sms_balance"],
                    "credits": balance_info["credits_balance"],
                    "total_available": balance_info["total_available"],
                    "money_balance": balance_info["money_balance"]
                },
                "monthly_usage": monthly_stats.get("statistics", {}) if monthly_stats["success"] else {},
                "auto_recharge": {
                    "enabled": balance_info["auto_recharge_enabled"],
                    "threshold": balance_info["auto_recharge_threshold"],
                    "amount": balance_info["auto_recharge_amount"]
                },
                "notifications": balance_info["paid_notifications"],
                "warnings": {
                    "low_balance": balance_info["low_balance_warning"]
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取账户概览失败: {str(e)}"
        )