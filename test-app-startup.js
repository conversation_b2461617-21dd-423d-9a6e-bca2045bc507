#!/usr/bin/env node

/**
 * 应用启动测试脚本
 * 验证关键导入和配置是否正确
 */

const fs = require('fs');

console.log('🚀 应用启动测试');
console.log('================');

// 1. 检查关键文件的React导入
const checkReactImports = () => {
  console.log('\n📋 检查React导入...');
  
  const filesToCheck = [
    {
      path: 'packages/frontend/src/contexts/OrderContext.tsx',
      requiredImports: ['useCallback', 'useMemo', 'useReducer', 'useEffect']
    },
    {
      path: 'packages/frontend/src/contexts/AuthContext.tsx',
      requiredImports: ['useReducer', 'useEffect']
    },
    {
      path: 'packages/frontend/src/contexts/MessageContext.tsx',
      requiredImports: ['createContext', 'useContext']
    }
  ];
  
  let allImportsOk = true;
  
  filesToCheck.forEach(({ path, requiredImports }) => {
    if (fs.existsSync(path)) {
      const content = fs.readFileSync(path, 'utf8');
      const importLines = content.split('\n').slice(0, 20).join('\n'); // 检查前20行
      
      console.log(`\n  ${path}:`);
      
      if (importLines) {
        requiredImports.forEach(importName => {
          const hasImport = importLines.includes(importName);
          console.log(`    ${importName}: ${hasImport ? '✅' : '❌'}`);
          if (!hasImport) allImportsOk = false;
        });
      } else {
        console.log('    ❌ 没有找到React导入');
        allImportsOk = false;
      }
    } else {
      console.log(`    ❌ 文件不存在: ${path}`);
      allImportsOk = false;
    }
  });
  
  return allImportsOk;
};

// 2. 检查MapSearchScreen配置
const checkMapSearchScreen = () => {
  console.log('\n🗺️ 检查MapSearchScreen配置...');
  
  const mapScreenFile = 'packages/frontend/src/domains/map/screens/MapSearchScreen.tsx';
  
  if (fs.existsSync(mapScreenFile)) {
    const content = fs.readFileSync(mapScreenFile, 'utf8');
    
    // 检查是否有useEffect导入
    const hasUseEffectImport = content.includes('useEffect');
    console.log(`  useEffect导入: ${hasUseEffectImport ? '✅' : '❌'}`);
    
    // 检查是否有生命周期监控
    const hasLifecycleMonitoring = content.includes('组件挂载') && content.includes('组件卸载');
    console.log(`  生命周期监控: ${hasLifecycleMonitoring ? '✅' : '❌'}`);
    
    // 检查是否简洁（没有复杂组件）
    const isSimple = !content.includes('FilterModal') && !content.includes('PropertyTypeSelector');
    console.log(`  简洁版本: ${isSimple ? '✅' : '❌'}`);
    
    return hasUseEffectImport && hasLifecycleMonitoring && isSimple;
  } else {
    console.log('  ❌ MapSearchScreen文件不存在');
    return false;
  }
};

// 3. 生成启动测试报告
const generateStartupReport = () => {
  console.log('\n📊 应用启动测试报告:');
  console.log('======================');
  
  const importsOk = checkReactImports();
  const mapScreenOk = checkMapSearchScreen();
  
  console.log('\n🎯 测试结果:');
  console.log(`✅ React导入检查: ${importsOk ? '通过' : '失败'}`);
  console.log(`✅ MapSearchScreen配置: ${mapScreenOk ? '通过' : '失败'}`);
  
  if (importsOk && mapScreenOk) {
    console.log('\n🎉 启动测试通过！');
    console.log('💡 应用应该能正常启动了');
    
    console.log('\n📋 现在请测试:');
    console.log('1. 启动应用，确认没有useCallback错误');
    console.log('2. 点击"地图找房"Tab');
    console.log('3. 观察生命周期日志:');
    console.log('   - [MapSearchScreen] 🚀 组件挂载 - 开始初始化');
    console.log('4. 点击"首页"Tab');
    console.log('5. 观察卸载日志:');
    console.log('   - [MapSearchScreen] 🧹 组件卸载 - 开始清理');
    console.log('6. 确认导航切换稳定');
  } else {
    console.log('\n⚠️  启动测试失败');
    
    if (!importsOk) {
      console.log('❌ React导入有问题，可能导致运行时错误');
    }
    
    if (!mapScreenOk) {
      console.log('❌ MapSearchScreen配置有问题');
    }
  }
  
  console.log('\n✅ 测试完成！');
};

// 执行测试
generateStartupReport();
