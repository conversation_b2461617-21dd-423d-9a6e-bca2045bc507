/**
 * EventBusService - 事件总线服务
 * @fileoverview 实现页面间数据更新事件通信
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🎯 功能：
 * - 支持事件订阅和发布
 * - 支持事件类型过滤
 * - 自动清理内存泄漏
 */

// 事件类型定义
export interface DataUpdateEvent {
  entityType: 'demand' | 'property' | 'user';
  entityId: string;
  action: 'create' | 'update' | 'delete' | 'statusChange';
  data?: any;
  timestamp: number;
  source?: string; // 事件来源页面
}

export interface StatusUpdateEvent extends DataUpdateEvent {
  action: 'statusChange';
  oldStatus: string;
  newStatus: string;
}

// 事件监听器类型
export type EventListener<T = DataUpdateEvent> = (
  event: T
) => void | Promise<void>;

// 事件过滤器类型
export interface EventFilter {
  entityType?: string[];
  action?: string[];
  entityId?: string[];
}

/**
 * 事件总线服务类 - 单例模式
 */
class EventBusServiceClass {
  private static instance: EventBusServiceClass | null = null;
  private listeners: Map<string, Set<EventListener>> = new Map();
  private globalListeners: Set<EventListener> = new Set();
  private eventHistory: DataUpdateEvent[] = [];
  private maxHistorySize = 100;
  private isInitialized = false;

  private constructor() {
    // 私有构造函数，防止外部实例化
  }

  /**
   * 获取单例实例（按需初始化）
   */
  public static getInstance(): EventBusServiceClass {
    if (!EventBusServiceClass.instance) {
      EventBusServiceClass.instance = new EventBusServiceClass();
      EventBusServiceClass.instance.initialize();
    }
    return EventBusServiceClass.instance;
  }

  /**
   * 初始化服务
   */
  private initialize(): void {
    if (this.isInitialized) return;

    console.log('📡 [EventBus] 按需初始化事件总线服务');
    this.isInitialized = true;
  }

  /**
   * 订阅特定类型的事件
   */
  subscribe<T extends DataUpdateEvent = DataUpdateEvent>(
    eventType: string,
    listener: EventListener<T>,
    filter?: EventFilter
  ): () => void {
    console.log(`📡 [EventBus] 订阅事件: ${eventType}`);

    // 包装监听器以支持过滤
    const wrappedListener: EventListener = (event: DataUpdateEvent) => {
      if (this.shouldTriggerListener(event, filter)) {
        listener(event as T);
      }
    };

    // 添加到监听器集合
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType)!.add(wrappedListener);

    // 返回取消订阅函数
    return () => {
      console.log(`📡 [EventBus] 取消订阅事件: ${eventType}`);
      this.listeners.get(eventType)?.delete(wrappedListener);
      if (this.listeners.get(eventType)?.size === 0) {
        this.listeners.delete(eventType);
      }
    };
  }

  /**
   * 订阅所有事件
   */
  subscribeAll(listener: EventListener, filter?: EventFilter): () => void {
    console.log('📡 [EventBus] 订阅所有事件');

    const wrappedListener: EventListener = (event: DataUpdateEvent) => {
      if (this.shouldTriggerListener(event, filter)) {
        listener(event);
      }
    };

    this.globalListeners.add(wrappedListener);

    return () => {
      console.log('📡 [EventBus] 取消订阅所有事件');
      this.globalListeners.delete(wrappedListener);
    };
  }

  /**
   * 发布事件
   */
  async emit(
    eventType: string,
    event: Omit<DataUpdateEvent, 'timestamp'>
  ): Promise<void> {
    const fullEvent: DataUpdateEvent = {
      ...event,
      timestamp: Date.now(),
    };

    console.log(`📡 [EventBus] 发布事件: ${eventType}`, fullEvent);

    // 添加到历史记录
    this.addToHistory(fullEvent);

    // 通知特定类型的监听器
    const typeListeners = this.listeners.get(eventType);
    if (typeListeners) {
      const promises = Array.from(typeListeners).map(listener => {
        try {
          return Promise.resolve(listener(fullEvent));
        } catch (error) {
          console.error(`📡 [EventBus] 监听器执行错误:`, error);
          return Promise.resolve();
        }
      });
      await Promise.allSettled(promises);
    }

    // 通知全局监听器
    const globalPromises = Array.from(this.globalListeners).map(listener => {
      try {
        return Promise.resolve(listener(fullEvent));
      } catch (error) {
        console.error(`📡 [EventBus] 全局监听器执行错误:`, error);
        return Promise.resolve();
      }
    });
    await Promise.allSettled(globalPromises);
  }

  /**
   * 发布状态更新事件（专用方法）
   */
  async emitStatusUpdate(
    entityType: 'demand' | 'property',
    entityId: string,
    oldStatus: string,
    newStatus: string,
    data?: any
  ): Promise<void> {
    await this.emit('statusUpdated', {
      entityType,
      entityId,
      action: 'statusChange',
      oldStatus,
      newStatus,
      data,
    } as StatusUpdateEvent);
  }

  /**
   * 获取事件历史
   */
  getEventHistory(filter?: EventFilter): DataUpdateEvent[] {
    if (!filter) {
      return [...this.eventHistory];
    }

    return this.eventHistory.filter(event =>
      this.shouldTriggerListener(event, filter)
    );
  }

  /**
   * 清理事件历史
   */
  clearHistory(): void {
    console.log('📡 [EventBus] 清理事件历史');
    this.eventHistory = [];
  }

  /**
   * 获取当前监听器统计
   */
  getListenerStats(): { [eventType: string]: number } {
    const stats: { [eventType: string]: number } = {};

    for (const [eventType, listeners] of this.listeners.entries()) {
      stats[eventType] = listeners.size;
    }

    stats['__global__'] = this.globalListeners.size;

    return stats;
  }

  /**
   * 清理所有监听器（用于内存泄漏防护）
   */
  cleanup(): void {
    console.log('📡 [EventBus] 清理所有监听器');
    this.listeners.clear();
    this.globalListeners.clear();
    this.eventHistory = [];
  }

  // === 私有方法 ===

  /**
   * 判断是否应该触发监听器
   */
  private shouldTriggerListener(
    event: DataUpdateEvent,
    filter?: EventFilter
  ): boolean {
    if (!filter) return true;

    // 检查实体类型过滤
    if (filter.entityType && !filter.entityType.includes(event.entityType)) {
      return false;
    }

    // 检查操作类型过滤
    if (filter.action && !filter.action.includes(event.action)) {
      return false;
    }

    // 检查实体ID过滤
    if (filter.entityId && !filter.entityId.includes(event.entityId)) {
      return false;
    }

    return true;
  }

  /**
   * 添加事件到历史记录
   */
  private addToHistory(event: DataUpdateEvent): void {
    this.eventHistory.push(event);

    // 限制历史记录大小
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }
  }
}

// 导出单例访问器
export const EventBusService = {
  getInstance: () => EventBusServiceClass.getInstance(),
  // 兼容性方法，直接代理到实例
  subscribe: (...args: any[]) =>
    EventBusServiceClass.getInstance().subscribe(...args),
  subscribeAll: (...args: any[]) =>
    EventBusServiceClass.getInstance().subscribeAll(...args),
  emit: (...args: any[]) => EventBusServiceClass.getInstance().emit(...args),
  emitStatusUpdate: (...args: any[]) =>
    EventBusServiceClass.getInstance().emitStatusUpdate(...args),
  getEventHistory: (...args: any[]) =>
    EventBusServiceClass.getInstance().getEventHistory(...args),
  clearHistory: () => EventBusServiceClass.getInstance().clearHistory(),
  getListenerStats: () => EventBusServiceClass.getInstance().getListenerStats(),
  cleanup: () => EventBusServiceClass.getInstance().cleanup(),
};

// 导出类型
export type { DataUpdateEvent, StatusUpdateEvent, EventListener, EventFilter };

// React Hook 用于在组件中使用事件总线
import { useEffect, useRef } from 'react';

/**
 * React Hook：订阅数据更新事件
 */
export const useDataUpdateEvent = <T extends DataUpdateEvent = DataUpdateEvent>(
  eventType: string,
  listener: EventListener<T>,
  filter?: EventFilter,
  deps: any[] = []
) => {
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    // 取消之前的订阅
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
    }

    // 按需获取服务实例并订阅新的事件
    const eventBusInstance = EventBusServiceClass.getInstance();
    unsubscribeRef.current = eventBusInstance.subscribe(
      eventType,
      listener,
      filter
    );

    // 清理函数
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [eventType, ...deps]);
};

/**
 * React Hook：订阅状态更新事件
 */
export const useStatusUpdateEvent = (
  entityType: 'demand' | 'property' | 'all',
  listener: EventListener<StatusUpdateEvent>,
  entityId?: string
) => {
  const filter: EventFilter = {
    action: ['statusChange'],
    ...(entityType !== 'all' && { entityType: [entityType] }),
    ...(entityId && { entityId: [entityId] }),
  };

  useDataUpdateEvent('statusUpdated', listener, filter, [entityType, entityId]);
};
