import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';

export const mediaUploadStyles = StyleSheet.create({
  // 媒体上传容器
  mediaUploadContainer: {
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    backgroundColor: '#FFFFFF'
  },
  mediaUploadTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#000000',
    marginBottom: hp(12)
  },
  
  // 上传按钮样式
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(16),
    paddingHorizontal: wp(20),
    borderWidth: 2,
    borderColor: '#007AFF',
    borderStyle: 'dashed',
    borderRadius: wp(12),
    backgroundColor: '#F8F9FA',
    marginBottom: hp(16)
  },
  uploadButtonContent: {
    alignItems: 'center',
    marginLeft: wp(8)
  },
  uploadButtonText: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: hp(2)
  },
  uploadButtonHint: {
    fontSize: fp(12),
    color: '#8E8E93'
  },
  
  // 媒体预览网格
  mediaGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -wp(4)
  },
  mediaItem: {
    width: `${(100 - 32) / 3}%`, // 3列布局，考虑间距
    aspectRatio: 1,
    marginHorizontal: wp(4),
    marginBottom: hp(8),
    borderRadius: wp(8),
    overflow: 'hidden',
    backgroundColor: '#F2F2F7'
  },
  mediaImage: {
    width: '100%',
    height: '100%',
    borderRadius: wp(8)
  },
  mediaVideo: {
    width: '100%',
    height: '100%',
    borderRadius: wp(8),
    backgroundColor: '#000000'
  },
  
  // 媒体项操作
  mediaItemOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  mediaItemActions: {
    position: 'absolute',
    top: wp(4),
    right: wp(4),
    flexDirection: 'row'
  },
  mediaActionButton: {
    width: wp(24),
    height: wp(24),
    borderRadius: wp(12),
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: wp(4)
  },
  
  // 视频播放按钮
  videoPlayButton: {
    width: wp(40),
    height: wp(40),
    borderRadius: wp(20),
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  videoDuration: {
    position: 'absolute',
    bottom: wp(4),
    right: wp(4),
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: wp(6),
    paddingVertical: wp(2),
    borderRadius: wp(4)
  },
  videoDurationText: {
    fontSize: fp(10),
    color: '#FFFFFF',
    fontWeight: '500'
  },
  
  // 上传进度
  uploadProgressOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  uploadProgressCircle: {
    width: wp(40),
    height: wp(40),
    borderRadius: wp(20),
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  uploadProgressText: {
    fontSize: fp(12),
    color: '#007AFF',
    fontWeight: '600'
  },
  
  // 媒体上传提示
  mediaUploadHint: {
    fontSize: fp(12),
    color: '#8E8E93',
    textAlign: 'center',
    marginTop: hp(8),
    lineHeight: fp(16)
  },

  // 上传提示文本
  uploadHint: {
    fontSize: fp(12),
    color: '#8E8E93',
    textAlign: 'center',
    marginTop: hp(8),
    fontStyle: 'italic',
  },
  mediaUploadError: {
    fontSize: fp(12),
    color: '#FF3B30',
    textAlign: 'center',
    marginTop: hp(8),
    backgroundColor: '#FFF5F5',
    paddingVertical: hp(8),
    paddingHorizontal: wp(12),
    borderRadius: wp(8)
  },
  
  // 媒体类型标签
  mediaTypeTag: {
    position: 'absolute',
    top: wp(4),
    left: wp(4),
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: wp(6),
    paddingVertical: wp(2),
    borderRadius: wp(4)
  },
  mediaTypeText: {
    fontSize: fp(10),
    color: '#FFFFFF',
    fontWeight: '500'
  },
  
  // 拖拽排序
  draggableItem: {
    opacity: 0.8,
    transform: [{ scale: 1.05 }]
  },
  dropZone: {
    borderColor: '#007AFF',
    borderWidth: 2,
    backgroundColor: 'rgba(0, 122, 255, 0.1)'
  },
  
  // 批量操作
  batchActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: hp(12),
    paddingHorizontal: wp(16),
    backgroundColor: '#F8F9FA',
    borderRadius: wp(8),
    marginTop: hp(12)
  },
  batchActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(8),
    paddingHorizontal: wp(12),
    borderRadius: wp(6),
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E5EA'
  },
  batchActionText: {
    fontSize: fp(14),
    color: '#007AFF',
    marginLeft: wp(4),
    fontWeight: '500'
  },

  // 补充缺失的媒体上传样式 (移除重复定义)
  sectionTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#000000',
    marginBottom: hp(16),
  },
  errorContainer: {
    backgroundColor: '#FFF5F5',
    padding: wp(12),
    borderRadius: wp(8),
    marginBottom: hp(12),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  clearErrorButton: {
    paddingHorizontal: wp(12),
    paddingVertical: wp(4),
    backgroundColor: '#FF3B30',
    borderRadius: wp(4),
  },
  clearErrorText: {
    color: '#FFFFFF',
    fontSize: fp(12),
    fontWeight: '500',
  },
  mediaUploadRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: hp(16),
  },
  mediaUploadButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(12),
    paddingHorizontal: wp(16),
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: wp(8),
    marginHorizontal: wp(4),
  },
  mediaUploadText: {
    fontSize: fp(14),
    color: '#007AFF',
    fontWeight: '500',
    marginLeft: wp(8),
  },

  // 补充更多缺失的媒体样式
  mediaThumbnailContainer: {
    position: 'relative',
    width: wp(80),
    height: wp(80),
    borderRadius: wp(8),
    overflow: 'hidden',
    backgroundColor: '#F2F2F7',
  },
  videoPreview: {
    width: '100%',
    height: '100%',
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePreview: {
    width: '100%',
    height: '100%',
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playIcon: {
    position: 'absolute',
  },
  deleteMediaButton: {
    position: 'absolute',
    top: wp(4),
    right: wp(4),
    width: wp(24),
    height: wp(24),
    borderRadius: wp(12),
    backgroundColor: 'rgba(255, 59, 48, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
    borderRadius: wp(8),
  },
  videoInfo: {
    fontSize: fp(12),
    color: '#8E8E93',
    textAlign: 'center',
    marginTop: hp(4),
  },
  imageInfo: {
    fontSize: fp(12),
    color: '#8E8E93',
    textAlign: 'center',
    marginTop: hp(4),
  },
  imageNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: hp(8),
  },
  navButton: {
    paddingHorizontal: wp(12),
    paddingVertical: wp(6),
    backgroundColor: '#007AFF',
    borderRadius: wp(4),
  },
  navButtonDisabled: {
    opacity: 0.3,
    backgroundColor: '#C7C7CC',
  },
  imageCounter: {
    fontSize: fp(14),
    color: '#8E8E93',
    fontWeight: '500',
  },

  // AI生成标签按钮样式
  generateTagsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    backgroundColor: '#34C759',
    borderRadius: wp(8),
    marginTop: hp(16),
    elevation: 2,
    shadowColor: '#34C759',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.2,
    shadowRadius: 4
  },
  generateTagsButtonText: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: wp(8),
  },
  generateTagsButtonHint: {
    fontSize: fp(12),
    color: 'rgba(255, 255, 255, 0.8)',
    marginLeft: wp(4),
  },
});
