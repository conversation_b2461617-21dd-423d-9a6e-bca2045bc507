/**
 * SwipeableTabView - 通用的可滑动标签页组件
 * 
 * 功能特点：
 * - 支持点击切换和左右滑动
 * - 自定义标签样式（选中/未选中状态）
 * - 支持标签计数显示
 * - 懒加载内容页面
 * - 响应式设计
 * 
 * 使用场景：
 * - 我的房源页面（已发布/草稿/已下架）
 * - 消息页面（业主消息/租买消息）
 * - 其他需要分类展示的页面
 */

import React, { useState, useCallback, ReactNode } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  useWindowDimensions,
  ActivityIndicator,
} from 'react-native';
import { TabView, SceneMap, TabBar, SceneRendererProps, NavigationState, Route as TabViewRoute } from 'react-native-tab-view';

// 响应式工具函数
import {
  spacing,
  fontSize,
  borderRadius,
} from '../../utils/responsiveUtils';

// 标签页配置接口
export interface SwipeableTab extends TabViewRoute {
  title: string;
  count?: number;
  icon?: string;
  badge?: boolean;
}

// 场景渲染器接口
export interface SceneRenderer {
  [key: string]: () => ReactNode;
}

// 组件Props接口
export interface SwipeableTabViewProps {
  tabs: SwipeableTab[];
  scenes: SceneRenderer;
  initialIndex?: number;
  onIndexChange?: (index: number) => void;
  
  // 样式自定义
  tabBarStyle?: object;
  indicatorStyle?: object;
  selectedTabStyle?: {
    textColor: string;
    backgroundColor?: string;
    fontWeight?: string;
  };
  unselectedTabStyle?: {
    textColor: string;
    backgroundColor?: string;
    fontWeight?: string;
  };
  
  // 功能配置
  lazy?: boolean;
  showTabCounts?: boolean;
  scrollEnabled?: boolean;
  
  // 加载状态
  renderLazyPlaceholder?: () => ReactNode;
}

export const SwipeableTabView: React.FC<SwipeableTabViewProps> = ({
  tabs,
  scenes,
  initialIndex = 0,
  onIndexChange,
  tabBarStyle = {},
  indicatorStyle = {},
  selectedTabStyle = {
    textColor: '#000000',
    fontWeight: '700',
  },
  unselectedTabStyle = {
    textColor: '#999999',
    fontWeight: 'normal',
  },
  lazy = true,
  showTabCounts = true,
  scrollEnabled = true,
  renderLazyPlaceholder,
}) => {
  const layout = useWindowDimensions();
  const [index, setIndex] = useState(initialIndex);

  // 处理索引变化
  const handleIndexChange = useCallback((newIndex: number) => {
    setIndex(newIndex);
    onIndexChange?.(newIndex);
  }, [onIndexChange]);

  // 渲染自定义标签栏
  const renderCustomTabBar = useCallback((props: any) => (
    <TabBar
      {...props}
      indicatorStyle={[
        {
          backgroundColor: '#FF6B35',
          height: 3,
        },
        indicatorStyle,
      ]}
      style={[
        {
          backgroundColor: '#FFFFFF',
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 0.5,
          borderBottomColor: '#E0E0E0',
          marginTop: 0,
        },
        tabBarStyle,
      ]}
      renderLabel={({ route, focused }: { route: SwipeableTab; focused: boolean }) => (
        <View style={styles.tabLabelContainer}>
          <Text
            style={[
              styles.tabLabel,
              {
                color: focused ? selectedTabStyle.textColor : unselectedTabStyle.textColor,
                fontWeight: focused ? (selectedTabStyle.fontWeight as any) : (unselectedTabStyle.fontWeight as any),
                fontSize: fontSize.base,
              },
            ]}
          >
            {route.title}
          </Text>
          {showTabCounts && route.count !== undefined && (
            <Text
              style={[
                styles.tabCount,
                {
                  color: focused ? selectedTabStyle.textColor : unselectedTabStyle.textColor,
                  fontSize: fontSize.sm,
                  marginLeft: spacing.xs,
                  fontWeight: focused ? '600' : 'normal',
                },
              ]}
            >
              {route.count}
            </Text>
          )}
          {route.badge && (
            <View style={styles.badgeIndicator} />
          )}
        </View>
      )}
      tabStyle={styles.tabStyle}
      scrollEnabled={scrollEnabled}
      bounces={false}
    />
  ), [
    indicatorStyle,
    tabBarStyle,
    selectedTabStyle,
    unselectedTabStyle,
    showTabCounts,
    scrollEnabled,
  ]);

  // 默认懒加载占位符
  const defaultLazyPlaceholder = useCallback(() => (
    <View style={styles.lazyPlaceholder}>
      <ActivityIndicator size="large" color="#FF6B35" />
    </View>
  ), []);

  return (
    <TabView
      navigationState={{ index, routes: tabs }}
      renderScene={SceneMap(scenes)}
      onIndexChange={handleIndexChange}
      initialLayout={{ width: layout.width }}
      renderTabBar={renderCustomTabBar}
      renderLazyPlaceholder={renderLazyPlaceholder || defaultLazyPlaceholder}
      lazy={lazy}
    />
  );
};

const styles = StyleSheet.create({
  tabLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabLabel: {
    fontSize: fontSize.base,
    textAlign: 'center',
  },
  tabCount: {
    fontSize: fontSize.sm,
    textAlign: 'center',
  },
  tabStyle: {
    height: 'auto',
    paddingVertical: spacing.md,
  },
  badgeIndicator: {
    position: 'absolute',
    top: -4,
    right: -8,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF6B35',
  },
  lazyPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    marginTop: spacing.xxl,
  },
});

export default SwipeableTabView;