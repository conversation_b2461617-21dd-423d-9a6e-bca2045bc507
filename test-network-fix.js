#!/usr/bin/env node

/**
 * 网络错误修复验证脚本
 * 验证无限循环问题是否已解决
 */

const fs = require('fs');

console.log('🔧 网络错误修复验证');
console.log('==================');

// 1. 验证自动搜索防重复机制
const checkAutoSearchFix = () => {
  console.log('\n📍 验证自动搜索防重复机制...');
  
  const hookFile = 'packages/frontend/src/domains/map/hooks/useMapScreenState.ts';
  
  if (fs.existsSync(hookFile)) {
    const content = fs.readFileSync(hookFile, 'utf8');
    
    const hasPreventDuplicate = content.includes('hasAutoSearchedRef') && 
                               content.includes('hasAutoSearchedRef.current = true');
    
    const hasErrorHandling = content.includes('自动搜索失败') && 
                            content.includes('搜索失败时不重试，避免无限循环');
    
    const removedHandleSearchDep = !content.includes('}, [userLocation, handleSearch, loading]);') &&
                                  content.includes('}, [userLocation, loading]);');
    
    console.log(`  防重复搜索机制: ${hasPreventDuplicate ? '✅' : '❌'}`);
    console.log(`  错误处理机制: ${hasErrorHandling ? '✅' : '❌'}`);
    console.log(`  移除循环依赖: ${removedHandleSearchDep ? '✅' : '❌'}`);
    
    return hasPreventDuplicate && hasErrorHandling && removedHandleSearchDep;
  }
  
  return false;
};

// 2. 验证租买切换防循环机制
const checkDealTypeChangeFix = () => {
  console.log('\n🏠 验证租买切换防循环机制...');
  
  const hookFile = 'packages/frontend/src/domains/map/hooks/useMapScreenState.ts';
  
  if (fs.existsSync(hookFile)) {
    const content = fs.readFileSync(hookFile, 'utf8');
    
    const hasDirectSearch = content.includes('await searchPropertiesRef.current({') && 
                           content.includes('切换搜索失败');
    
    const removedHandleSearchDep2 = content.includes('}, [userLocation, searchQuery]);') &&
                                   content.includes('移除handleSearch依赖');
    
    const hasErrorHandling = content.includes('搜索失败时静默处理，不重试');
    
    console.log(`  直接调用搜索: ${hasDirectSearch ? '✅' : '❌'}`);
    console.log(`  移除循环依赖: ${removedHandleSearchDep2 ? '✅' : '❌'}`);
    console.log(`  错误静默处理: ${hasErrorHandling ? '✅' : '❌'}`);
    
    return hasDirectSearch && removedHandleSearchDep2 && hasErrorHandling;
  }
  
  return false;
};

// 3. 验证功能完整性
const checkFunctionalityIntegrity = () => {
  console.log('\n🔍 验证功能完整性...');
  
  const hookFile = 'packages/frontend/src/domains/map/hooks/useMapScreenState.ts';
  const modalFile = 'packages/frontend/src/domains/map/components/FilterModal/RentFilterModal.tsx';
  const screenFile = 'packages/frontend/src/domains/map/screens/MapSearchScreen.tsx';
  
  let hookIntegrity = false;
  let modalIntegrity = false;
  let screenIntegrity = false;
  
  // 检查Hook完整性
  if (fs.existsSync(hookFile)) {
    const content = fs.readFileSync(hookFile, 'utf8');
    hookIntegrity = content.includes('dealType') && 
                   content.includes('handleDealTypeChange') &&
                   content.includes('PropertyTypeSelector');
    console.log(`  useMapScreenState完整性: ${hookIntegrity ? '✅' : '❌'}`);
  }
  
  // 检查Modal完整性
  if (fs.existsSync(modalFile)) {
    const content = fs.readFileSync(modalFile, 'utf8');
    modalIntegrity = content.includes('dealType') && 
                    content.includes('onDealTypeChange') &&
                    content.includes('dealTypeContainer');
    console.log(`  RentFilterModal完整性: ${modalIntegrity ? '✅' : '❌'}`);
  }
  
  // 检查Screen完整性
  if (fs.existsSync(screenFile)) {
    const content = fs.readFileSync(screenFile, 'utf8');
    screenIntegrity = content.includes('PropertyTypeSelector') && 
                     content.includes('dealType={filterModalProps.dealType}') &&
                     content.includes('onDealTypeChange={filterModalProps.onDealTypeChange}');
    console.log(`  MapSearchScreen完整性: ${screenIntegrity ? '✅' : '❌'}`);
  }
  
  return hookIntegrity && modalIntegrity && screenIntegrity;
};

// 4. 生成修复验证报告
const generateFixReport = () => {
  console.log('\n📊 网络错误修复验证报告:');
  console.log('============================');
  
  const autoSearchFixed = checkAutoSearchFix();
  const dealTypeChangeFixed = checkDealTypeChangeFix();
  const functionalityIntact = checkFunctionalityIntegrity();
  
  console.log('\n🎯 修复结果汇总:');
  console.log(`✅ 自动搜索防重复: ${autoSearchFixed ? '修复成功' : '需要检查'}`);
  console.log(`✅ 租买切换防循环: ${dealTypeChangeFixed ? '修复成功' : '需要检查'}`);
  console.log(`✅ 功能完整性: ${functionalityIntact ? '保持完整' : '需要检查'}`);
  
  const allFixed = autoSearchFixed && dealTypeChangeFixed && functionalityIntact;
  
  if (allFixed) {
    console.log('\n🎉 网络错误无限循环问题修复成功！');
    console.log('💡 现在应用应该能正常处理网络错误');
    
    console.log('\n📋 修复要点:');
    console.log('1. 🔒 自动搜索只执行一次：使用hasAutoSearchedRef防止重复');
    console.log('2. 🚫 移除循环依赖：不再依赖handleSearch函数');
    console.log('3. 🛡️ 错误静默处理：网络失败时不重试，避免无限循环');
    console.log('4. 🎯 直接调用搜索：租买切换时直接调用searchPropertiesRef');
    
    console.log('\n🧪 测试建议:');
    console.log('1. 断开网络连接测试：应该只显示一次错误，不会无限重试');
    console.log('2. 切换租买类型：网络失败时应该静默处理');
    console.log('3. 观察日志：不应该看到重复的搜索请求');
    console.log('4. 功能验证：筛选弹窗、房源类型选择器应该正常工作');
  } else {
    console.log('\n⚠️  部分修复验证失败');
    console.log('需要检查失败的修复项目');
  }
  
  console.log('\n✅ 验证完成！');
};

// 执行验证
generateFixReport();
