import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

// 导入工具函数
import { wp, hp, fp } from '../../../shared/utils/responsiveUtils';
import { safeNavigate } from '../../../shared/types/navigation.types';
import { getPropertyTypeDisplayName } from '../../property/config/propertyTypeConfigs';
import { getIndustryTypesDisplayString } from '../utils/industryTypeUtils';
import { getRegionsDisplayString } from '../utils/regionUtils';

// 导入API服务
import { demandAPI } from '../services/demandAPI';

// 导入通用组件
import { LayoutFactory } from '../../../shared/components/LayoutFactory';

// 导入认证Hook
import { useAuth } from '../../../contexts/AuthContext';

interface DemandDetailScreenProps {}

interface RouteParams {
  demandId: string;
}

export const DemandDetailScreen: React.FC<DemandDetailScreenProps> = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { demandId } = route.params as RouteParams;
  const { user } = useAuth(); // 获取当前用户信息

  const [demand, setDemand] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载需求详情
  const loadDemandDetail = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('[DemandDetail] 🔍 加载需求详情:', demandId);
      const response = await demandAPI.getDemandById(demandId);

      if (response.success && response.data) {
        setDemand(response.data);
        console.log('[DemandDetail] ✅ 需求详情加载成功:', response.data);
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (err: any) {
      console.error('[DemandDetail] ❌ 加载需求详情失败:', err);
      setError(err.message || '加载失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDemandDetail();
  }, [demandId]);

  // 编辑需求
  const handleEdit = () => {
    if (demand) {
      console.log('[DemandDetail] ✏️ 编辑需求:', demand.id);
      safeNavigate(navigation, 'DemandForm', {
        demandType: demand.demand_type,
        editMode: true,
        demandId: demand.id,
        demandData: demand,
      });
    }
  };

  // 发布新需求
  const handlePublishNew = () => {
    console.log('[DemandDetail] 🚀 发布新需求');
    safeNavigate(navigation, 'DemandForm', {
      demandType: 'RENTAL', // 默认求租
      editMode: false,
    });
  };

  // 状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return '草稿';
      case 'ACTIVE':
        return '已发布';
      case 'OFFLINE':
        return '已下架';
      case 'COMPLETED':
        return '已完成';
      case 'EXPIRED':
        return '已过期';
      default:
        return '未知';
    }
  };

  // 状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return '#FFA726';
      case 'ACTIVE':
        return '#66BB6A';
      case 'OFFLINE':
        return '#EF5350';
      case 'COMPLETED':
        return '#42A5F5';
      case 'EXPIRED':
        return '#BDBDBD';
      default:
        return '#9E9E9E';
    }
  };

  // 需求类型显示
  const getDemandTypeText = (type: string) => {
    return type === 'RENTAL' ? '求租' : '求购';
  };

  if (loading) {
    return (
      <LayoutFactory pageType="DemandDetail" backgroundColor="#FFFFFF">
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Ionicons name="chevron-back" size={24} color="#333333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>需求详情</Text>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B35" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </LayoutFactory>
    );
  }

  if (error || !demand) {
    return (
      <UniversalScreenLayout
        title="需求详情"
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      >
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#EF5350" />
          <Text style={styles.errorText}>{error || '需求不存在'}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={loadDemandDetail}
          >
            <Text style={styles.retryButtonText}>重试</Text>
          </TouchableOpacity>
        </View>
      </UniversalScreenLayout>
    );
  }

  return (
    <UniversalScreenLayout
      title="需求详情"
      showBackButton={true}
      onBackPress={() => navigation.goBack()}
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* 基本信息卡片 */}
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>基本信息</Text>
            <View
              style={[
                styles.statusBadge,
                { backgroundColor: getStatusColor(demand.status) },
              ]}
            >
              <Text style={styles.statusText}>
                {getStatusText(demand.status)}
              </Text>
            </View>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.label}>需求类型：</Text>
            <Text style={styles.value}>
              {getDemandTypeText(demand.demand_type)}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.label}>房源类型：</Text>
            <Text style={styles.value}>
              {getPropertyTypeDisplayName(demand.property_type)}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.label}>目标区域：</Text>
            <Text style={styles.value}>
              {demand.target_regions?.length > 0
                ? getRegionsDisplayString(demand.target_regions)
                : '不限'}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.label}>面积范围：</Text>
            <Text style={styles.value}>
              {demand.display_area_range || '不限'}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.label}>价格范围：</Text>
            <Text style={styles.value}>
              {demand.display_price_range || '面议'}
            </Text>
          </View>
        </View>

        {/* 详细要求卡片 */}
        {(demand.industry_types?.length > 0 ||
          demand.description ||
          demand.special_requirements) && (
          <View style={styles.card}>
            <Text style={styles.cardTitle}>详细要求</Text>

            {demand.industry_types?.length > 0 && (
              <View style={styles.infoRow}>
                <Text style={styles.label}>行业类型：</Text>
                <Text style={styles.value}>
                  {getIndustryTypesDisplayString(demand.industry_types)}
                </Text>
              </View>
            )}

            {demand.description && (
              <View style={styles.infoRow}>
                <Text style={styles.label}>需求描述：</Text>
                <Text style={styles.value}>{demand.description}</Text>
              </View>
            )}

            {demand.special_requirements && (
              <View style={styles.infoRow}>
                <Text style={styles.label}>特殊要求：</Text>
                <Text style={styles.value}>{demand.special_requirements}</Text>
              </View>
            )}
          </View>
        )}

        {/* 联系信息卡片 */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>联系信息</Text>

          <View style={styles.infoRow}>
            <Text style={styles.label}>联系人：</Text>
            <Text style={styles.value}>
              {demand.contact_surname || '未填写'}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.label}>联系电话：</Text>
            <Text style={styles.value}>{demand.contact_phone || '未填写'}</Text>
          </View>

          {demand.contact_wechat && (
            <View style={styles.infoRow}>
              <Text style={styles.label}>微信号：</Text>
              <Text style={styles.value}>{demand.contact_wechat}</Text>
            </View>
          )}
        </View>

        {/* 标签卡片 */}
        {demand.all_tags?.length > 0 && (
          <View style={styles.card}>
            <Text style={styles.cardTitle}>相关标签</Text>
            <View style={styles.tagsContainer}>
              {demand.all_tags.map((tag: string, index: number) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* 时间信息卡片 */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>时间信息</Text>

          <View style={styles.infoRow}>
            <Text style={styles.label}>创建时间：</Text>
            <Text style={styles.value}>
              {new Date(demand.created_at).toLocaleString('zh-CN')}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.label}>更新时间：</Text>
            <Text style={styles.value}>
              {new Date(demand.updated_at).toLocaleString('zh-CN')}
            </Text>
          </View>

          {demand.expires_at && (
            <View style={styles.infoRow}>
              <Text style={styles.label}>过期时间：</Text>
              <Text style={styles.value}>
                {new Date(demand.expires_at).toLocaleString('zh-CN')}
              </Text>
            </View>
          )}
        </View>

        {/* 底部间距 */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* 底部操作按钮 - 根据用户身份显示不同按钮 */}
      {demand.status !== 'COMPLETED' && demand.status !== 'EXPIRED' && (
        <View style={styles.bottomActions}>
          {/* 🚀 判断是否为需求发布者 */}
          {user && user.id === demand.user_id ? (
            // 本人查看：显示编辑按钮
            <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
              <Ionicons name="create-outline" size={20} color="#FFFFFF" />
              <Text style={styles.editButtonText}>编辑需求</Text>
            </TouchableOpacity>
          ) : (
            // 业主查看：显示发布新需求按钮
            <TouchableOpacity style={styles.publishButton} onPress={handlePublishNew}>
              <Ionicons name="add-outline" size={20} color="#FFFFFF" />
              <Text style={styles.publishButtonText}>发布新需求</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </UniversalScreenLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF', // 🔧 改为白色背景
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: hp(16),
    fontSize: fp(16),
    color: '#666666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(32),
  },
  errorText: {
    marginTop: hp(16),
    fontSize: fp(16),
    color: '#666666',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: hp(24),
    paddingHorizontal: wp(24),
    paddingVertical: hp(12),
    backgroundColor: '#FF6B35',
    borderRadius: wp(8),
  },
  retryButtonText: {
    fontSize: fp(16),
    color: '#FFFFFF',
    fontWeight: '500',
  },
  card: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: wp(16),
    paddingVertical: hp(16),
    // 🔧 移除所有卡片样式：边距、圆角、阴影
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(16),
  },
  cardTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#333333',
  },
  statusBadge: {
    paddingHorizontal: wp(8),
    paddingVertical: hp(4),
    borderRadius: wp(12),
  },
  statusText: {
    fontSize: fp(12),
    color: '#FFFFFF',
    fontWeight: '500',
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: hp(12),
    alignItems: 'flex-start',
  },
  label: {
    fontSize: fp(14),
    color: '#666666',
    width: wp(80),
    flexShrink: 0,
  },
  value: {
    fontSize: fp(14),
    color: '#333333',
    flex: 1,
    lineHeight: fp(20),
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: hp(8),
  },
  tag: {
    backgroundColor: '#F0F0F0',
    paddingHorizontal: wp(8),
    paddingVertical: hp(4),
    borderRadius: wp(12),
    marginRight: wp(8),
    marginBottom: hp(8),
  },
  tagText: {
    fontSize: fp(12),
    color: '#666666',
  },
  bottomSpacing: {
    height: hp(100),
  },
  bottomActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: wp(16),
    paddingVertical: hp(16),
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF6B35',
    paddingVertical: hp(14),
    borderRadius: wp(8),
  },
  editButtonText: {
    marginLeft: wp(8),
    fontSize: fp(16),
    color: '#FFFFFF',
    fontWeight: '500',
  },
  publishButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50', // 绿色，区别于编辑按钮
    paddingVertical: hp(14),
    borderRadius: wp(8),
  },
  publishButtonText: {
    marginLeft: wp(8),
    fontSize: fp(16),
    color: '#FFFFFF',
    fontWeight: '500',
  },
});

// 🔧 修复：添加默认导出
export default DemandDetailScreen;
