/**
 * 房源类型选择器组件
 * 职责：顶部房源类型选择（商铺、写字楼、厂房等）
 *
 * 设计理念：
 * 1. 一次只能选择一个房源类型
 * 2. 默认选中商铺
 * 3. 极简风格，横向滚动
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { wp, hp, fp } from '@shared/utils/responsive';

interface PropertyTypeSelectorProps {
  selectedType: string;
  onTypeChange: (type: string) => void;
}

export const PropertyTypeSelector: React.FC<PropertyTypeSelectorProps> = ({
  selectedType,
  onTypeChange,
}) => {
  // 房源类型选项
  const propertyTypes = [
    { key: 'shop', label: '商铺', icon: '🏪' },
    { key: 'office', label: '写字楼', icon: '🏢' },
    { key: 'factory', label: '厂房', icon: '🏭' },
    { key: 'warehouse', label: '仓库', icon: '📦' },
    { key: 'land', label: '土地', icon: '🌍' },
    { key: 'parking', label: '车位', icon: '🅿️' },
    { key: 'other', label: '其他', icon: '🏘️' },
  ];

  console.log('🏷️ [PropertyTypeSelector] 当前选中类型:', selectedType);

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {propertyTypes.map(type => {
          const isSelected = selectedType === type.key;

          return (
            <TouchableOpacity
              key={type.key}
              style={[
                styles.typeButton,
                isSelected && styles.typeButtonSelected,
              ]}
              onPress={() => {
                console.log(
                  '🏷️ [PropertyTypeSelector] 选择房源类型:',
                  type.label
                );
                onTypeChange(type.key);
              }}
              activeOpacity={0.7}
            >
              <Text style={styles.typeIcon}>{type.icon}</Text>
              <Text
                style={[styles.typeText, isSelected && styles.typeTextSelected]}
              >
                {type.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingVertical: hp(12),
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  scrollContent: {
    paddingHorizontal: wp(16),
    gap: wp(12),
  },
  typeButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: wp(16),
    paddingVertical: hp(8),
    borderRadius: 20,
    backgroundColor: '#f8f8f8',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    minWidth: wp(70),
  },
  typeButtonSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  typeIcon: {
    fontSize: fp(16),
    marginBottom: hp(2),
  },
  typeText: {
    fontSize: fp(12),
    color: '#666',
    fontWeight: '500',
  },
  typeTextSelected: {
    color: '#fff',
  },
});
