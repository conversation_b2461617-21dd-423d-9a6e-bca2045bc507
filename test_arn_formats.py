#!/usr/bin/env python3
"""
测试不同的ARN格式，找出正确的格式
"""
import os
import sys
sys.path.append('/app')

# 设置环境变量
os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID'] = 'LTAI5tBTVvG2eG7VuPdRFEuq'
os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET'] = '******************************'

try:
    from alibabacloud_tea_openapi.models import Config
    from alibabacloud_sts20150401.client import Client as STSClient
    from alibabacloud_sts20150401.models import AssumeRoleRequest
    print("✅ SDK导入成功")
    
    # 测试不同的ARN格式
    test_arns = [
        "acs:ram::1080445593952125:role/huixuanzhiossuploadrole",
        "acs:ram::1080445593952125:role/HuixuanzhiOSSUploadRole", 
        "acs:ram::1080445593952125:role/huixuanzhi-oss-upload-role",
        "acs:ram::1080445593952125:role/HuixuanzhiOssUploadRole"
    ]
    
    # 初始化STS客户端
    config = Config(
        access_key_id=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID'],
        access_key_secret=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET'],
        endpoint='sts.cn-hangzhou.aliyuncs.com'
    )
    client = STSClient(config)
    
    for i, arn in enumerate(test_arns, 1):
        print(f"\n🧪 测试ARN格式 {i}: {arn}")
        try:
            request = AssumeRoleRequest(
                role_arn=arn,
                role_session_name=f"test-session-{i}",
                duration_seconds=3600
            )
            
            response = client.assume_role(request)
            print(f"✅ 成功！ARN格式正确: {arn}")
            print(f"   AccessKeyId: {response.body.credentials.access_key_id[:10]}...")
            break
            
        except Exception as e:
            print(f"❌ 失败: {str(e)[:100]}...")
            
except ImportError as e:
    print(f"❌ SDK导入失败: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")
