/**
 * 地图重复渲染问题调试脚本
 * 分析为什么地图组件频繁重新渲染
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 [调试] 开始分析地图重复渲染问题...\n');

// 分析useMapScreenState Hook的依赖关系
function analyzeHookDependencies() {
  console.log('📋 [分析] useMapScreenState Hook依赖关系');

  const hookPath = path.join(
    __dirname,
    'src/domains/map/hooks/useMapScreenState.ts'
  );
  const content = fs.readFileSync(hookPath, 'utf8');

  // 提取所有useEffect和useMemo的依赖
  const useEffectMatches =
    content.match(/useEffect\([^,]+,\s*\[([^\]]*)\]/g) || [];
  const useMemoMatches = content.match(/useMemo\([^,]+,\s*\[([^\]]*)\]/g) || [];
  const useCallbackMatches =
    content.match(/useCallback\([^,]+,\s*\[([^\]]*)\]/g) || [];

  console.log('  useEffect依赖:');
  useEffectMatches.forEach((match, index) => {
    const deps = match.match(/\[([^\]]*)\]/)[1];
    console.log(`    ${index + 1}: [${deps}]`);
  });

  console.log('  useMemo依赖:');
  useMemoMatches.forEach((match, index) => {
    const deps = match.match(/\[([^\]]*)\]/)[1];
    console.log(`    ${index + 1}: [${deps}]`);
  });

  console.log('  useCallback依赖:');
  useCallbackMatches.forEach((match, index) => {
    const deps = match.match(/\[([^\]]*)\]/)[1];
    console.log(`    ${index + 1}: [${deps}]`);
  });

  console.log();
}

// 分析mapProps的依赖变化
function analyzeMapPropsDependencies() {
  console.log('🗺️ [分析] mapProps依赖变化');

  const hookPath = path.join(
    __dirname,
    'src/domains/map/hooks/useMapScreenState.ts'
  );
  const content = fs.readFileSync(hookPath, 'utf8');

  // 查找mapProps的useMemo定义
  const mapPropsMatch = content.match(
    /const mapProps = useMemo\([^}]+\}, \[([^\]]+)\]/s
  );

  if (mapPropsMatch) {
    const dependencies = mapPropsMatch[1];
    console.log(`  mapProps依赖: [${dependencies}]`);

    // 分析每个依赖的来源
    const deps = dependencies.split(',').map(dep => dep.trim());
    deps.forEach(dep => {
      if (dep.includes('center')) {
        console.log(`    ⚠️ center - 地图中心点状态，可能频繁变化`);
      } else if (dep.includes('properties')) {
        console.log(`    ⚠️ properties - 房源数据，可能频繁变化`);
      } else if (dep.includes('selectedDealType')) {
        console.log(`    ✅ selectedDealType - 筛选类型，正常变化`);
      } else if (dep.includes('handle')) {
        console.log(`    ⚠️ ${dep} - 事件处理函数，可能不稳定`);
      } else {
        console.log(`    ? ${dep} - 需要检查`);
      }
    });
  } else {
    console.log('  ❌ 未找到mapProps定义');
  }

  console.log();
}

// 分析状态更新频率
function analyzeStateUpdates() {
  console.log('📊 [分析] 状态更新频率');

  const hookPath = path.join(
    __dirname,
    'src/domains/map/hooks/useMapScreenState.ts'
  );
  const content = fs.readFileSync(hookPath, 'utf8');

  // 查找所有setState调用
  const setStateMatches = content.match(/set[A-Z][a-zA-Z]*\(/g) || [];
  const uniqueSetStates = [...new Set(setStateMatches)];

  console.log('  发现的状态更新函数:');
  uniqueSetStates.forEach(setState => {
    const count = (
      content.match(new RegExp(setState.replace('(', '\\('), 'g')) || []
    ).length;
    console.log(`    ${setState} - 调用${count}次`);

    if (setState.includes('setCenter')) {
      console.log(`      ⚠️ 地图中心点更新 - 可能导致mapProps重新计算`);
    } else if (setState.includes('setProperties')) {
      console.log(`      ⚠️ 房源数据更新 - 可能导致mapProps重新计算`);
    } else if (setState.includes('setSelectedDealType')) {
      console.log(`      ✅ 筛选类型更新 - 正常行为`);
    }
  });

  console.log();
}

// 分析MapContainer组件的props变化
function analyzeMapContainerProps() {
  console.log('🗺️ [分析] MapContainer组件props');

  const mapContainerPath = path.join(
    __dirname,
    'src/shared/components/MapContainer.tsx'
  );
  const content = fs.readFileSync(mapContainerPath, 'utf8');

  // 查找useEffect依赖
  const useEffectMatches =
    content.match(/useEffect\([^,]+,\s*\[([^\]]*)\]/g) || [];

  console.log('  MapContainer内部useEffect依赖:');
  useEffectMatches.forEach((match, index) => {
    const deps = match.match(/\[([^\]]*)\]/)[1];
    console.log(`    ${index + 1}: [${deps}]`);

    if (deps.includes('center')) {
      console.log(`      ⚠️ 依赖center - 每次center变化都会触发useEffect`);
    }
    if (deps.includes('markers')) {
      console.log(`      ⚠️ 依赖markers - 每次markers变化都会触发useEffect`);
    }
  });

  console.log();
}

// 生成问题分析报告
function generateAnalysisReport() {
  console.log('📋 [报告] 地图重复渲染问题分析');

  const issues = [];
  const solutions = [];

  // 基于分析结果识别问题
  issues.push('1. mapProps依赖过多状态，导致频繁重新计算');
  issues.push('2. center状态可能在定位过程中频繁变化');
  issues.push('3. properties数组可能在筛选时重新创建');
  issues.push('4. 事件处理函数可能不稳定，导致依赖变化');

  // 提出解决方案
  solutions.push('1. 使用useRef缓存稳定的center值');
  solutions.push('2. 优化properties数组的创建，使用useMemo缓存');
  solutions.push('3. 确保事件处理函数使用useCallback稳定化');
  solutions.push('4. 减少mapProps的依赖项，只包含真正需要的状态');
  solutions.push('5. 在MapContainer中添加React.memo优化');

  console.log('\n🔍 [问题识别]:');
  issues.forEach(issue => console.log(`  ${issue}`));

  console.log('\n💡 [解决方案]:');
  solutions.forEach(solution => console.log(`  ${solution}`));

  // 生成修复优先级
  console.log('\n🎯 [修复优先级]:');
  console.log('  1. 高优先级: 稳定化mapProps依赖');
  console.log('  2. 中优先级: 优化center状态管理');
  console.log('  3. 低优先级: 添加React.memo优化');

  const report = `# 地图重复渲染问题分析报告

## 问题识别
${issues.map(issue => `- ${issue}`).join('\n')}

## 解决方案
${solutions.map(solution => `- ${solution}`).join('\n')}

## 修复优先级
1. **高优先级**: 稳定化mapProps依赖
2. **中优先级**: 优化center状态管理  
3. **低优先级**: 添加React.memo优化

## 下一步行动
1. 实施mapProps依赖优化
2. 添加center状态缓存
3. 验证修复效果
4. 进行性能测试

生成时间: ${new Date().toLocaleString()}
`;

  fs.writeFileSync(path.join(__dirname, 'map-rendering-analysis.md'), report);
  console.log('\n📄 分析报告已生成: map-rendering-analysis.md');
}

// 执行分析
analyzeHookDependencies();
analyzeMapPropsDependencies();
analyzeStateUpdates();
analyzeMapContainerProps();
generateAnalysisReport();

console.log('\n✅ 地图重复渲染问题分析完成');
console.log('💡 建议: 根据分析报告实施修复方案，优先处理高优先级问题');
