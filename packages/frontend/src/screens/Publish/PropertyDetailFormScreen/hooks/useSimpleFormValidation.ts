/**
 * 简化的表单验证Hook
 * 
 * 职责：
 * 1. 字段验证逻辑
 * 2. 错误状态管理
 * 3. 自动滚动到错误字段
 * 4. 表单提交验证
 */

import { useState, useCallback, useRef } from 'react';
import { ScrollView } from 'react-native';
import FeedbackService from '../../../../shared/services/FeedbackService';

interface FieldError {
  fieldName: string;
  message: string;
  y?: number; // 字段在页面中的位置
}

interface FieldLayout {
  y: number;
  height: number;
}

interface ValidationRule {
  required?: boolean;
  minLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

interface FormData {
  sub_type: string;
  area: string;
  transaction_types: string[];
  rent_price?: string;
  sale_price?: string;
  transfer_price?: string;
  title: string;
  description: string;
  property_certificate_address: string;
}

export const useSimpleFormValidation = () => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [fieldLayouts, setFieldLayouts] = useState<Record<string, FieldLayout>>({});
  const scrollViewRef = useRef<ScrollView>(null);
  const [scrollOffset, setScrollOffset] = useState(0);

  // 验证规则定义
  const validationRules: Record<string, ValidationRule> = {
    sub_type: {
      required: true,
    },
    area: {
      required: true,
      pattern: /^[0-9]+(\.[0-9]+)?$/,
      custom: (value: string) => {
        const num = parseFloat(value);
        if (isNaN(num) || num <= 0) {
          return '面积必须大于0';
        }
        if (num > 10000) {
          return '面积不能超过10000平方米';
        }
        return null;
      },
    },
    transaction_types: {
      required: true,
      custom: (value: string[]) => {
        if (!Array.isArray(value) || value.length === 0) {
          return '请选择至少一种交易方式';
        }
        return null;
      },
    },
    rent_price: {
      required: true,
      pattern: /^[0-9]+(\.[0-9]+)?$/,
      custom: (value: string) => {
        const num = parseFloat(value);
        if (isNaN(num) || num <= 0) {
          return '租金必须大于0';
        }
        return null;
      },
    },
    sale_price: {
      required: true,
      pattern: /^[0-9]+(\.[0-9]+)?$/,
      custom: (value: string) => {
        const num = parseFloat(value);
        if (isNaN(num) || num <= 0) {
          return '售价必须大于0';
        }
        return null;
      },
    },
    transfer_price: {
      required: true,
      pattern: /^[0-9]+(\.[0-9]+)?$/,
      custom: (value: string) => {
        const num = parseFloat(value);
        if (isNaN(num) || num <= 0) {
          return '转让费必须大于0';
        }
        return null;
      },
    },
    title: {
      required: true,
      minLength: 5,
      custom: (value: string) => {
        if (value.length > 50) {
          return '标题不能超过50个字符';
        }
        return null;
      },
    },
    description: {
      required: true,
      minLength: 20,
      custom: (value: string) => {
        if (value.length > 500) {
          return '描述不能超过500个字符';
        }
        return null;
      },
    },
    property_certificate_address: {
      required: true,
      minLength: 5,
    },
  };

  // 字段显示名称映射
  const fieldNameMap: Record<string, string> = {
    sub_type: '房源类型',
    area: '面积',
    transaction_types: '交易方式',
    rent_price: '租金',
    sale_price: '售价',
    transfer_price: '转让费',
    title: '标题',
    description: '房源描述',
    property_certificate_address: '房产证地址',
  };

  // 验证单个字段
  const validateField = useCallback((fieldName: string, value: any): string | null => {
    const rule = validationRules[fieldName];
    if (!rule) return null;

    const displayName = fieldNameMap[fieldName] || fieldName;

    // 必填验证
    if (rule.required) {
      if (value === undefined || value === null || value === '' || 
          (Array.isArray(value) && value.length === 0)) {
        return `请填写${displayName}`;
      }
    }

    // 字符串长度验证
    if (typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        return `${displayName}至少需要${rule.minLength}个字符`;
      }

      // 正则验证
      if (rule.pattern && !rule.pattern.test(value)) {
        return `${displayName}格式不正确`;
      }
    }

    // 自定义验证
    if (rule.custom) {
      return rule.custom(value);
    }

    return null;
  }, []);

  // 记录字段位置
  const recordFieldLayout = useCallback((fieldName: string, layout: FieldLayout) => {
    setFieldLayouts(prev => ({
      ...prev,
      [fieldName]: {
        y: layout.y + scrollOffset,
        height: layout.height,
      },
    }));
  }, [scrollOffset]);

  // 更新滚动偏移量
  const updateScrollOffset = useCallback((offset: number) => {
    setScrollOffset(offset);
  }, []);

  // 清除错误
  const clearError = useCallback((fieldName: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  // 清除所有错误
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  // 滚动到第一个错误字段
  const scrollToFirstError = useCallback(async (fieldErrors: FieldError[]): Promise<void> => {
    if (!fieldErrors.length || !scrollViewRef.current) return;

    // 按字段在页面中的位置排序
    const sortedErrors = fieldErrors
      .map(error => ({
        ...error,
        y: fieldLayouts[error.fieldName]?.y || 0,
      }))
      .sort((a, b) => a.y - b.y);

    const firstError = sortedErrors[0];
    const targetY = Math.max(0, firstError.y - 100); // 向上偏移100px

    console.log(`[SimpleFormValidation] 🎯 滚动到错误字段: ${firstError.fieldName}`, {
      targetY,
      fieldY: firstError.y,
    });

    // 执行滚动
    scrollViewRef.current.scrollTo({
      y: targetY,
      animated: true,
    });

    // 等待滚动动画完成
    return new Promise(resolve => {
      setTimeout(resolve, 300);
    });
  }, [fieldLayouts]);

  // 验证整个表单
  const validateForm = useCallback(async (formData: FormData): Promise<boolean> => {
    console.log('[SimpleFormValidation] 🔍 开始表单验证...', formData);
    
    const newErrors: Record<string, string> = {};
    const fieldErrors: FieldError[] = [];

    // 基础字段验证
    const basicFields = ['sub_type', 'area', 'transaction_types', 'title', 'description', 'property_certificate_address'];
    
    for (const fieldName of basicFields) {
      const error = validateField(fieldName, formData[fieldName as keyof FormData]);
      if (error) {
        newErrors[fieldName] = error;
        fieldErrors.push({ fieldName, message: error });
      }
    }

    // 价格字段验证（根据交易类型）
    if (formData.transaction_types?.includes('RENT')) {
      const rentError = validateField('rent_price', formData.rent_price);
      if (rentError) {
        newErrors.rent_price = rentError;
        fieldErrors.push({ fieldName: 'rent_price', message: rentError });
      }
    }

    if (formData.transaction_types?.includes('SALE')) {
      const saleError = validateField('sale_price', formData.sale_price);
      if (saleError) {
        newErrors.sale_price = saleError;
        fieldErrors.push({ fieldName: 'sale_price', message: saleError });
      }
    }

    if (formData.transaction_types?.includes('TRANSFER')) {
      const transferError = validateField('transfer_price', formData.transfer_price);
      if (transferError) {
        newErrors.transfer_price = transferError;
        fieldErrors.push({ fieldName: 'transfer_price', message: transferError });
      }
    }

    // 更新错误状态
    setErrors(newErrors);

    // 如果有错误，滚动到第一个错误并显示提示
    if (fieldErrors.length > 0) {
      console.log('[SimpleFormValidation] ❌ 验证失败，错误字段:', fieldErrors.map(e => e.fieldName));
      
      await scrollToFirstError(fieldErrors);
      FeedbackService.showError('请完善标记为红色的必填信息');
      return false;
    }

    console.log('[SimpleFormValidation] ✅ 表单验证通过');
    return true;
  }, [validateField, scrollToFirstError]);

  return {
    errors,
    scrollViewRef,
    validateForm,
    validateField,
    recordFieldLayout,
    updateScrollOffset,
    clearError,
    clearAllErrors,
  };
};

export default useSimpleFormValidation;