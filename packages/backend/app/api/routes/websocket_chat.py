"""
WebSocket聊天路由

企业级WebSocket聊天API：
1. 连接认证和权限验证
2. 消息收发处理
3. 异常处理和重连机制
4. 性能监控
"""

import json
import logging
from typing import Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID

from ...core.database import get_async_session
from ...core.auth import get_current_user_from_token
from ...models.user import User
from ...services.websocket_chat_manager import websocket_manager
from ...services.property_inquiry_service import PropertyInquiryService

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()


@router.websocket("/ws/chat/{inquiry_id}")
async def websocket_chat_endpoint(
    websocket: WebSocket,
    inquiry_id: str,
    token: str,
    db: AsyncSession = Depends(get_async_session)
):
    """
    WebSocket聊天端点
    
    连接URL: /ws/chat/{inquiry_id}?token={jwt_token}
    
    Args:
        websocket: WebSocket连接对象
        inquiry_id: 咨询记录ID
        token: JWT认证令牌
        db: 数据库会话
    """
    connection_id = None
    user = None
    
    try:
        # 验证JWT令牌
        try:
            user = await get_current_user_from_token(token, db)
            if not user:
                await websocket.close(code=4001, reason="认证失败")
                return
        except Exception as e:
            logger.error(f"WebSocket认证失败: {e}")
            await websocket.close(code=4001, reason="认证失败")
            return
        
        # 建立连接
        connection_id = await websocket_manager.connect(
            websocket=websocket,
            user_id=user.id,
            inquiry_id=inquiry_id,
            db=db
        )
        
        if not connection_id:
            logger.error(f"用户 {user.id} 连接失败")
            return
        
        # 发送历史消息
        await _send_chat_history(connection_id, inquiry_id, db)
        
        # 消息循环处理
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # 处理不同类型的消息
                await _handle_websocket_message(
                    connection_id=connection_id,
                    message_data=message_data,
                    user=user,
                    db=db
                )
                
            except WebSocketDisconnect:
                logger.info(f"用户 {user.id} 主动断开连接")
                break
            except json.JSONDecodeError:
                await _send_error_to_connection(connection_id, "消息格式错误")
            except Exception as e:
                logger.error(f"处理WebSocket消息失败: {e}")
                await _send_error_to_connection(connection_id, "消息处理失败")
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: {user.id if user else 'unknown'}")
    except Exception as e:
        logger.error(f"WebSocket连接异常: {e}")
    finally:
        # 清理连接
        if connection_id:
            await websocket_manager.disconnect(connection_id, db)


async def _handle_websocket_message(
    connection_id: str,
    message_data: Dict[str, Any],
    user: User,
    db: AsyncSession
):
    """
    处理WebSocket消息
    
    Args:
        connection_id: 连接ID
        message_data: 消息数据
        user: 当前用户
        db: 数据库会话
    """
    message_type = message_data.get("type")
    
    if message_type == "send_message":
        # 发送聊天消息
        await websocket_manager.send_message(
            connection_id=connection_id,
            message_data=message_data,
            db=db
        )
    
    elif message_type == "mark_as_read":
        # 标记消息为已读
        await _handle_mark_as_read(connection_id, message_data, db)
    
    elif message_type == "typing":
        # 输入状态通知
        await _handle_typing_status(connection_id, message_data, user)
    
    elif message_type == "ping":
        # 心跳检测
        await _handle_ping(connection_id)
    
    elif message_type == "request_contact":
        # 请求联系方式
        await _handle_contact_request(connection_id, message_data, user, db)
    
    else:
        await _send_error_to_connection(connection_id, f"未知消息类型: {message_type}")


async def _send_chat_history(connection_id: str, inquiry_id: str, db: AsyncSession):
    """
    发送聊天历史记录
    
    Args:
        connection_id: 连接ID
        inquiry_id: 咨询记录ID
        db: 数据库会话
    """
    try:
        from sqlalchemy import select, desc
        from ...models.chat_message import ChatMessage
        
        # 查询最近50条消息
        result = await db.execute(
            select(ChatMessage)
            .where(ChatMessage.inquiry_id == inquiry_id)
            .order_by(desc(ChatMessage.created_at))
            .limit(50)
        )
        messages = result.scalars().all()
        
        # 转换为前端格式
        history_messages = []
        for msg in reversed(messages):  # 按时间正序
            history_messages.append({
                "id": msg.id,
                "sender_id": str(msg.sender_id),
                "receiver_id": str(msg.receiver_id),
                "message_type": msg.message_type,
                "content": msg.content,
                "media_url": msg.media_url,
                "media_duration": msg.media_duration,
                "status": msg.status,
                "contains_contact_info": msg.contains_contact_info,
                "requires_payment_to_view": msg.requires_payment_to_view,
                "created_at": msg.created_at.isoformat(),
                "sent_at": msg.sent_at.isoformat() if msg.sent_at else None,
                "read_at": msg.read_at.isoformat() if msg.read_at else None
            })
        
        # 发送历史消息
        await websocket_manager._send_to_connection(connection_id, {
            "type": "chat_history",
            "messages": history_messages,
            "total_count": len(history_messages)
        })
        
    except Exception as e:
        logger.error(f"发送聊天历史失败: {e}")
        await _send_error_to_connection(connection_id, "获取聊天历史失败")


async def _handle_mark_as_read(
    connection_id: str, 
    message_data: Dict[str, Any], 
    db: AsyncSession
):
    """处理标记消息为已读"""
    try:
        message_ids = message_data.get("message_ids", [])
        if not message_ids:
            return
        
        from sqlalchemy import update
        from ...models.chat_message import ChatMessage
        from datetime import datetime
        
        # 批量更新消息状态为已读
        await db.execute(
            update(ChatMessage)
            .where(ChatMessage.id.in_(message_ids))
            .values(
                status="read",
                read_at=datetime.utcnow()
            )
        )
        await db.commit()
        
        # 通知发送者消息已读
        await websocket_manager._send_to_connection(connection_id, {
            "type": "messages_read",
            "message_ids": message_ids,
            "read_at": datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"标记消息已读失败: {e}")


async def _handle_typing_status(
    connection_id: str, 
    message_data: Dict[str, Any], 
    user: User
):
    """处理输入状态通知"""
    try:
        room_id = websocket_manager.connection_rooms.get(connection_id)
        if not room_id:
            return
        
        is_typing = message_data.get("is_typing", False)
        
        # 广播输入状态到房间内其他用户
        await websocket_manager._broadcast_to_room(room_id, {
            "type": "typing_status",
            "user_id": str(user.id),
            "is_typing": is_typing,
            "timestamp": message_data.get("timestamp")
        }, exclude_connection=connection_id)
        
    except Exception as e:
        logger.error(f"处理输入状态失败: {e}")


async def _handle_ping(connection_id: str):
    """处理心跳检测"""
    await websocket_manager._send_to_connection(connection_id, {
        "type": "pong",
        "timestamp": message_data.get("timestamp")
    })


async def _handle_contact_request(
    connection_id: str,
    message_data: Dict[str, Any],
    user: User,
    db: AsyncSession
):
    """
    处理联系方式请求
    
    基于BOSS直聘模式的联系方式交换机制
    """
    try:
        # 检查用户付费状态
        from ...services.sms_credit_service import SMSCreditService
        credit_service = SMSCreditService()
        
        has_permission = await credit_service.check_contact_permission(user.id, db)
        
        if not has_permission:
            # 发送付费提示
            await websocket_manager._send_to_connection(connection_id, {
                "type": "payment_required",
                "message": "查看联系方式需要升级会员",
                "action": "upgrade_membership",
                "price": 29.9,
                "benefits": [
                    "无限查看联系方式",
                    "优先推荐展示",
                    "专属客服支持"
                ]
            })
            return
        
        # 发送联系方式请求消息
        await websocket_manager.send_message(
            connection_id=connection_id,
            message_data={
                "type": "contact_request",
                "content": "希望获取您的联系方式，方便进一步沟通"
            },
            db=db
        )
        
    except Exception as e:
        logger.error(f"处理联系方式请求失败: {e}")
        await _send_error_to_connection(connection_id, "联系方式请求失败")


async def _send_error_to_connection(connection_id: str, error_message: str):
    """发送错误消息到连接"""
    await websocket_manager._send_to_connection(connection_id, {
        "type": "error",
        "message": error_message,
        "timestamp": datetime.utcnow().isoformat()
    })


# 聊天历史API
@router.get("/chat/{inquiry_id}/history")
async def get_chat_history(
    inquiry_id: str,
    page: int = 1,
    page_size: int = 50,
    current_user: User = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_async_session)
):
    """
    获取聊天历史记录
    
    Args:
        inquiry_id: 咨询记录ID
        page: 页码
        page_size: 每页数量
        current_user: 当前用户
        db: 数据库会话
    """
    try:
        # 验证权限
        inquiry_service = PropertyInquiryService()
        inquiry = await inquiry_service.get_inquiry_by_id(inquiry_id, db)
        
        if not inquiry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="咨询记录不存在"
            )
        
        if str(current_user.id) not in [str(inquiry.tenant_id), str(inquiry.landlord_id)]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问此聊天记录"
            )
        
        # 查询聊天记录
        from sqlalchemy import select, desc, func
        from ...models.chat_message import ChatMessage
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询消息
        result = await db.execute(
            select(ChatMessage)
            .where(ChatMessage.inquiry_id == inquiry_id)
            .order_by(desc(ChatMessage.created_at))
            .offset(offset)
            .limit(page_size)
        )
        messages = result.scalars().all()
        
        # 查询总数
        count_result = await db.execute(
            select(func.count(ChatMessage.id))
            .where(ChatMessage.inquiry_id == inquiry_id)
        )
        total_count = count_result.scalar()
        
        # 转换格式
        message_list = []
        for msg in reversed(messages):  # 按时间正序返回
            message_list.append({
                "id": msg.id,
                "sender_id": str(msg.sender_id),
                "receiver_id": str(msg.receiver_id),
                "message_type": msg.message_type,
                "content": msg.content,
                "media_url": msg.media_url,
                "media_duration": msg.media_duration,
                "status": msg.status,
                "contains_contact_info": msg.contains_contact_info,
                "requires_payment_to_view": msg.requires_payment_to_view,
                "created_at": msg.created_at.isoformat(),
                "sent_at": msg.sent_at.isoformat() if msg.sent_at else None,
                "read_at": msg.read_at.isoformat() if msg.read_at else None
            })
        
        return {
            "messages": message_list,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": (total_count + page_size - 1) // page_size
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取聊天历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取聊天历史失败"
        )
