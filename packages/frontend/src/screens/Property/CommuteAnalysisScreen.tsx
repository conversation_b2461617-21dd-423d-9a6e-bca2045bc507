/**
 * 上班通勤分析页面
 * 企业级架构 - 房源详情页通勤功能
 * 
 * 功能特点：
 * 1. 房源地址自动填充为目的地
 * 2. 用户可选择或输入出发地
 * 3. 地图路线规划和导航
 * 4. 通勤时间和距离计算
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StackScreenProps } from '@react-navigation/stack';
import { RootStackParamList } from '@navigation/types';
import { hp, wp, fp } from '@shared/utils/responsive';
import FeedbackService from '@shared/services/FeedbackService';

type CommuteAnalysisScreenProps = StackScreenProps<RootStackParamList, 'CommuteAnalysis'>;

interface RouteInfo {
  distance: string;
  duration: string;
  traffic_duration?: string;
  route_type: 'driving' | 'walking' | 'transit';
}

interface LocationPoint {
  latitude: number;
  longitude: number;
  address: string;
}

/**
 * 上班通勤分析页面组件
 */
const CommuteAnalysisScreen: React.FC<CommuteAnalysisScreenProps> = ({
  navigation,
  route,
}) => {
  const insets = useSafeAreaInsets();
  const { propertyId, propertyAddress, propertyLocation } = route.params;

  // 状态管理
  const [startLocation, setStartLocation] = useState<LocationPoint | null>(null);
  const [startAddress, setStartAddress] = useState('');
  const [routeInfo, setRouteInfo] = useState<RouteInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedTransportMode, setSelectedTransportMode] = useState<'driving' | 'walking' | 'transit'>('driving');

  // 交通方式选项
  const transportModes = [
    { key: 'driving', label: '驾车', icon: 'car-outline' },
    { key: 'walking', label: '步行', icon: 'walk-outline' },
    { key: 'transit', label: '公交', icon: 'bus-outline' },
  ] as const;

  /**
   * 计算路线
   */
  const calculateRoute = async () => {
    if (!startLocation) {
      FeedbackService.showError('请先选择出发地');
      return;
    }

    setLoading(true);
    try {
      // 调用后端地图服务计算距离
      const response = await fetch(
        `http://*************:8082/api/v1/map/detail/property/${propertyId}/distance?target_lat=${startLocation.latitude}&target_lng=${startLocation.longitude}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        
        // 模拟路线信息（实际应该集成高德地图路线规划API）
        const mockRouteInfo: RouteInfo = {
          distance: data.distance_display || '未知距离',
          duration: estimateDuration(data.distance_meters, selectedTransportMode),
          traffic_duration: selectedTransportMode === 'driving' ? estimateTrafficDuration(data.distance_meters) : undefined,
          route_type: selectedTransportMode,
        };

        setRouteInfo(mockRouteInfo);
        FeedbackService.showSuccess('路线计算完成');
      } else {
        throw new Error('路线计算失败');
      }
    } catch (error) {
      console.error('路线计算错误:', error);
      FeedbackService.showError('路线计算失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 估算通勤时间
   */
  const estimateDuration = (distanceMeters: number, mode: string): string => {
    if (!distanceMeters) return '未知';
    
    const distanceKm = distanceMeters / 1000;
    let minutes: number;
    
    switch (mode) {
      case 'driving':
        minutes = Math.round(distanceKm * 2.5); // 假设平均速度24km/h
        break;
      case 'walking':
        minutes = Math.round(distanceKm * 12); // 假设步行速度5km/h
        break;
      case 'transit':
        minutes = Math.round(distanceKm * 4); // 假设公交平均速度15km/h
        break;
      default:
        minutes = Math.round(distanceKm * 2.5);
    }
    
    if (minutes < 60) {
      return `${minutes}分钟`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}小时${remainingMinutes}分钟`;
    }
  };

  /**
   * 估算拥堵时间
   */
  const estimateTrafficDuration = (distanceMeters: number): string => {
    const normalMinutes = Math.round((distanceMeters / 1000) * 2.5);
    const trafficMinutes = Math.round(normalMinutes * 1.5); // 拥堵时增加50%时间
    
    if (trafficMinutes < 60) {
      return `${trafficMinutes}分钟`;
    } else {
      const hours = Math.floor(trafficMinutes / 60);
      const remainingMinutes = trafficMinutes % 60;
      return `${hours}小时${remainingMinutes}分钟`;
    }
  };

  /**
   * 选择出发地（模拟地图选择）
   */
  const selectStartLocation = () => {
    Alert.alert(
      '选择出发地',
      '请选择获取位置的方式',
      [
        {
          text: '当前位置',
          onPress: () => {
            // 模拟获取当前位置
            const mockCurrentLocation: LocationPoint = {
              latitude: propertyLocation.latitude + 0.01,
              longitude: propertyLocation.longitude + 0.01,
              address: '当前位置（模拟）',
            };
            setStartLocation(mockCurrentLocation);
            setStartAddress(mockCurrentLocation.address);
            FeedbackService.showSuccess('已获取当前位置');
          },
        },
        {
          text: '地图选择',
          onPress: () => {
            // 模拟地图选择
            const mockSelectedLocation: LocationPoint = {
              latitude: propertyLocation.latitude + 0.005,
              longitude: propertyLocation.longitude + 0.005,
              address: '南宁市青秀区民族大道（模拟选择）',
            };
            setStartLocation(mockSelectedLocation);
            setStartAddress(mockSelectedLocation.address);
            FeedbackService.showSuccess('已选择出发地');
          },
        },
        { text: '取消', style: 'cancel' },
      ]
    );
  };

  /**
   * 开始导航
   */
  const startNavigation = () => {
    if (!routeInfo) {
      FeedbackService.showError('请先计算路线');
      return;
    }

    Alert.alert(
      '开始导航',
      `即将为您规划从"${startAddress}"到"${propertyAddress}"的${transportModes.find(m => m.key === selectedTransportMode)?.label}路线`,
      [
        {
          text: '确定',
          onPress: () => {
            FeedbackService.showInfo('导航功能开发中，将集成高德地图导航');
          },
        },
        { text: '取消', style: 'cancel' },
      ]
    );
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* 头部导航 */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={fp(24)} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>上班通勤</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 目的地信息 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>目的地</Text>
          <View style={styles.locationCard}>
            <Ionicons name="location" size={fp(20)} color="#FF6B35" />
            <Text style={styles.locationText}>{propertyAddress}</Text>
          </View>
        </View>

        {/* 出发地选择 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>出发地</Text>
          <TouchableOpacity
            style={styles.locationCard}
            onPress={selectStartLocation}
          >
            <Ionicons 
              name={startLocation ? "checkmark-circle" : "add-circle-outline"} 
              size={fp(20)} 
              color={startLocation ? "#4CAF50" : "#999"} 
            />
            <Text style={[
              styles.locationText,
              !startLocation && styles.placeholderText
            ]}>
              {startAddress || '点击选择出发地或输入地址'}
            </Text>
          </TouchableOpacity>
          
          {/* 地址输入框 */}
          <TextInput
            style={styles.addressInput}
            placeholder="或直接输入出发地地址"
            value={startAddress}
            onChangeText={setStartAddress}
            onEndEditing={() => {
              if (startAddress.trim()) {
                // 模拟地址解析
                const mockLocation: LocationPoint = {
                  latitude: propertyLocation.latitude + 0.008,
                  longitude: propertyLocation.longitude + 0.008,
                  address: startAddress.trim(),
                };
                setStartLocation(mockLocation);
              }
            }}
          />
        </View>

        {/* 交通方式选择 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>交通方式</Text>
          <View style={styles.transportModes}>
            {transportModes.map((mode) => (
              <TouchableOpacity
                key={mode.key}
                style={[
                  styles.transportMode,
                  selectedTransportMode === mode.key && styles.selectedTransportMode,
                ]}
                onPress={() => setSelectedTransportMode(mode.key)}
              >
                <Ionicons
                  name={mode.icon}
                  size={fp(24)}
                  color={selectedTransportMode === mode.key ? '#FF6B35' : '#666'}
                />
                <Text
                  style={[
                    styles.transportModeText,
                    selectedTransportMode === mode.key && styles.selectedTransportModeText,
                  ]}
                >
                  {mode.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 路线信息 */}
        {routeInfo && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>路线信息</Text>
            <View style={styles.routeCard}>
              <View style={styles.routeInfo}>
                <View style={styles.routeItem}>
                  <Ionicons name="location-outline" size={fp(18)} color="#666" />
                  <Text style={styles.routeLabel}>距离</Text>
                  <Text style={styles.routeValue}>{routeInfo.distance}</Text>
                </View>
                <View style={styles.routeItem}>
                  <Ionicons name="time-outline" size={fp(18)} color="#666" />
                  <Text style={styles.routeLabel}>预计时间</Text>
                  <Text style={styles.routeValue}>{routeInfo.duration}</Text>
                </View>
                {routeInfo.traffic_duration && (
                  <View style={styles.routeItem}>
                    <Ionicons name="car-outline" size={fp(18)} color="#FF9800" />
                    <Text style={styles.routeLabel}>拥堵时间</Text>
                    <Text style={[styles.routeValue, styles.trafficTime]}>
                      {routeInfo.traffic_duration}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        )}

        {/* 操作按钮 */}
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.calculateButton]}
            onPress={calculateRoute}
            disabled={loading || !startLocation}
          >
            {loading ? (
              <ActivityIndicator color="#FFF" size="small" />
            ) : (
              <>
                <Ionicons name="calculator-outline" size={fp(20)} color="#FFF" />
                <Text style={styles.actionButtonText}>计算路线</Text>
              </>
            )}
          </TouchableOpacity>

          {routeInfo && (
            <TouchableOpacity
              style={[styles.actionButton, styles.navigateButton]}
              onPress={startNavigation}
            >
              <Ionicons name="navigate-outline" size={fp(20)} color="#FFF" />
              <Text style={styles.actionButtonText}>开始导航</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  backButton: {
    padding: wp(8),
  },
  headerTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#333',
  },
  headerRight: {
    width: wp(40),
  },
  content: {
    flex: 1,
    paddingHorizontal: wp(16),
  },
  section: {
    marginTop: hp(20),
  },
  sectionTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#333',
    marginBottom: hp(12),
  },
  locationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    padding: wp(16),
    borderRadius: wp(12),
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  locationText: {
    flex: 1,
    fontSize: fp(14),
    color: '#333',
    marginLeft: wp(12),
  },
  placeholderText: {
    color: '#999',
  },
  addressInput: {
    backgroundColor: '#FFF',
    padding: wp(16),
    borderRadius: wp(12),
    borderWidth: 1,
    borderColor: '#E5E5E5',
    fontSize: fp(14),
    color: '#333',
    marginTop: hp(8),
  },
  transportModes: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  transportMode: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: '#FFF',
    padding: wp(16),
    borderRadius: wp(12),
    borderWidth: 1,
    borderColor: '#E5E5E5',
    marginHorizontal: wp(4),
  },
  selectedTransportMode: {
    borderColor: '#FF6B35',
    backgroundColor: '#FFF5F2',
  },
  transportModeText: {
    fontSize: fp(12),
    color: '#666',
    marginTop: hp(4),
  },
  selectedTransportModeText: {
    color: '#FF6B35',
    fontWeight: '600',
  },
  routeCard: {
    backgroundColor: '#FFF',
    borderRadius: wp(12),
    padding: wp(16),
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  routeInfo: {
    gap: hp(12),
  },
  routeItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  routeLabel: {
    fontSize: fp(14),
    color: '#666',
    marginLeft: wp(8),
    flex: 1,
  },
  routeValue: {
    fontSize: fp(14),
    fontWeight: '600',
    color: '#333',
  },
  trafficTime: {
    color: '#FF9800',
  },
  actions: {
    marginTop: hp(24),
    marginBottom: hp(32),
    gap: hp(12),
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: hp(16),
    borderRadius: wp(12),
    gap: wp(8),
  },
  calculateButton: {
    backgroundColor: '#FF6B35',
  },
  navigateButton: {
    backgroundColor: '#4CAF50',
  },
  actionButtonText: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#FFF',
  },
});

export default CommuteAnalysisScreen;
