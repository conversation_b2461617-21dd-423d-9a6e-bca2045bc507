/**
 * 描述信息区块组件
 * 
 * 负责房源标题、描述和房产证地址的输入
 * 从SimplePropertyForm中抽取，减少主组件复杂度
 */

import React from 'react';
import { View, Text, TextInput, StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';

interface DescriptionSectionProps {
  title: string;
  description: string;
  propertyAddress: string;
  onTitleChange: (value: string) => void;
  onDescriptionChange: (value: string) => void;
  onPropertyAddressChange: (value: string) => void;
  onFieldLayout: (field: string, layout: any) => void;
  errors: {
    title?: string;
    description?: string;
    property_certificate_address?: string;
  };
}

export const DescriptionSection: React.FC<DescriptionSectionProps> = ({
  title,
  description,
  propertyAddress,
  onTitleChange,
  onDescriptionChange,
  onPropertyAddressChange,
  onFieldLayout,
  errors
}) => {
  return (
    <View>
      {/* 标题 */}
      <View 
        style={styles.inputGroup}
        onLayout={({ nativeEvent }) => onFieldLayout('title', nativeEvent.layout)}
      >
        <Text style={styles.inputLabel}>标题<Text style={styles.required}> *</Text></Text>
        <TextInput
          style={[styles.textInput, errors.title ? styles.textInputError : null].filter(Boolean)}
          value={title}
          onChangeText={onTitleChange}
          placeholder="请输入房源标题"
          placeholderTextColor="#C7C7CC"
        />
        {errors.title && (
          <Text style={styles.fieldError}>{errors.title}</Text>
        )}
      </View>

      {/* 房源描述 */}
      <View 
        style={styles.inputGroup}
        onLayout={({ nativeEvent }) => onFieldLayout('description', nativeEvent.layout)}
      >
        <Text style={styles.inputLabel}>房源描述<Text style={styles.required}> *</Text></Text>
        <TextInput
          style={[styles.descriptionInput, errors.description ? styles.textInputError : null].filter(Boolean)}
          value={description}
          onChangeText={onDescriptionChange}
          placeholder="请详细描述房源特色、周边配套、交通情况等..."
          placeholderTextColor="#C7C7CC"
          multiline
          numberOfLines={6}
          textAlignVertical="top"
        />
        {errors.description && (
          <Text style={styles.fieldError}>{errors.description}</Text>
        )}
      </View>

      {/* 房产证地址 */}
      <View 
        style={styles.inputGroup}
        onLayout={({ nativeEvent }) => onFieldLayout('property_certificate_address', nativeEvent.layout)}
      >
        <Text style={styles.inputLabel}>房产证地址<Text style={styles.required}> *</Text></Text>
        <TextInput
          style={[styles.textInput, errors.property_certificate_address ? styles.textInputError : null].filter(Boolean)}
          value={propertyAddress}
          onChangeText={onPropertyAddressChange}
          placeholder="请输入房产证上的地址"
          placeholderTextColor="#C7C7CC"
        />
        {errors.property_certificate_address && (
          <Text style={styles.fieldError}>{errors.property_certificate_address}</Text>
        )}
      </View>

    </View>
  );
};

const styles = StyleSheet.create({
  inputGroup: {
    marginBottom: hp(16),
  },
  inputLabel: {
    fontSize: fp(14),
    fontWeight: '600',
    color: '#333333',
    marginBottom: hp(8),
  },
  required: {
    color: '#FF6B35',
  },
  textInput: {
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: wp(8),
    paddingHorizontal: wp(12),
    paddingVertical: hp(12),
    fontSize: fp(14),
    color: '#333333',
    minHeight: hp(44),
  },
  descriptionInput: {
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: wp(8),
    paddingHorizontal: wp(12),
    paddingVertical: hp(12),
    fontSize: fp(14),
    color: '#333333',
    minHeight: hp(120),
  },
  textInputError: {
    borderColor: '#FF6B35',
  },
  fieldError: {
    fontSize: fp(12),
    color: '#FF6B35',
    marginTop: hp(4),
  },
});

console.log('✅ [DescriptionSection] 描述信息组件创建完成');