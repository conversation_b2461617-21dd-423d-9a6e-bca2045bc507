#!/usr/bin/env node

/**
 * 修复FeedbackService导入问题
 * 为所有使用FeedbackService的文件添加正确的导入语句
 */

const fs = require('fs');
const path = require('path');

// 需要添加FeedbackService导入的文件列表（从TypeScript错误中提取）
const filesToFix = [
  'src/components/dev/DevAuthController.tsx',
  'src/domains/auth/components/UserInfoModal.tsx',
  'src/domains/auth/components/UserProfileCompletionModal.tsx',
  'src/domains/auth/screens/EnhancedRegisterScreen.tsx',
  'src/domains/auth/screens/HelpScreen.tsx',
  'src/domains/auth/screens/LoginScreen.tsx',
  'src/domains/auth/screens/QuickLoginScreen.tsx',
  'src/domains/auth/screens/RegisterScreen.tsx',
  'src/domains/auth/screens/SwitchAccountScreen.tsx',
  'src/domains/demand/screens/DemandFormScreen.tsx',
  'src/domains/demand/screens/MyDemandsScreen.tsx',
  'src/domains/message/screens/ChatDetailScreen.tsx',
  'src/domains/message/screens/LandlordSubscriptionScreen.tsx',
  'src/domains/property/components/detail/CommuteMapSection.tsx',
  'src/domains/property/components/detail/CommuteMapSectionAdvanced.tsx',
  'src/domains/property/components/detail/HotQuestionsSection.tsx',
  'src/domains/property/components/detail/VideoPlayer.tsx',
  'src/domains/publish/components/AIContent/AIContentGenerator.tsx',
  'src/domains/publish/components/IntelligentTags/IntelligentTagSelector.tsx',
  'src/domains/user/components/ExpoDateTimePicker.tsx',
  'src/domains/user/components/PasswordConfirmModal.tsx',
  'src/domains/user/components/VerificationCodeConfirmModal.tsx',
  'src/domains/user/screens/AccountManagementScreen.tsx',
  'src/domains/user/screens/DeviceManagementScreen.tsx',
  'src/domains/user/screens/FeedbackScreen.tsx',
  'src/domains/user/screens/MyPropertiesScreen.tsx',
  'src/domains/user/screens/ProfileScreen.tsx',
  'src/domains/user/screens/RoleVerificationScreen.tsx',
  'src/domains/user/screens/SecuritySettingsScreen.tsx',
  'src/domains/verification/components/DocumentUpload.tsx',
  'src/domains/verification/components/VerificationForm.tsx',
  'src/hooks/useMediaUpload.ts',
  'src/hooks/useOptimizedBatchUpload.ts',
  'src/screens/Property/PropertyDetailScreen.tsx',
  'src/screens/Publish/PropertyDetailFormScreen.tsx',
  'src/screens/Publish/PropertyDetailFormScreen/components/MediaUploadSection.tsx',
  'src/screens/Publish/PropertyPublishScreen.tsx',
  'src/screens/Publish/PublishOptionsScreen.tsx',
  'src/screens/Publish/VerificationScreen.tsx',
  'src/screens/TenantRequirementsScreen.tsx',
  'src/screens/TestRollback.tsx',
  'src/services/media/MediaPickerService.ts',
  'src/shared/hooks/useVerificationCode.ts',
  'src/shared/services/verificationCodeService.examples.tsx'
];

console.log('🔧 开始修复FeedbackService导入问题...\n');

let totalFixed = 0;

filesToFix.forEach(filePath => {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否已经有FeedbackService导入
    if (content.includes('import FeedbackService') || content.includes("import FeedbackService")) {
      console.log(`ℹ️  ${filePath}: 已有FeedbackService导入`);
      return;
    }

    // 检查是否使用了FeedbackService
    if (!content.includes('FeedbackService.')) {
      console.log(`ℹ️  ${filePath}: 未使用FeedbackService`);
      return;
    }

    // 计算相对路径深度
    const pathParts = filePath.split('/');
    const depth = pathParts.length - 2; // 减去src和文件名
    const relativePath = '../'.repeat(depth) + 'shared/services/FeedbackService';

    // 找到合适的位置插入导入
    const lines = content.split('\n');
    let insertIndex = -1;
    let hasReactImport = false;

    // 寻找最后一个import语句的位置
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.startsWith('import ') && !line.includes('type ')) {
        insertIndex = i;
        if (line.includes('react-native')) {
          hasReactImport = true;
        }
      }
      // 如果遇到非import语句，停止搜索
      if (line && !line.startsWith('import ') && !line.startsWith('//') && !line.startsWith('/*') && !line.startsWith('*') && !line.startsWith('*/')) {
        break;
      }
    }

    if (insertIndex === -1) {
      // 如果没找到import语句，在文件开头插入
      insertIndex = 0;
    } else {
      // 在最后一个import语句后插入
      insertIndex++;
    }

    // 插入FeedbackService导入
    const importStatement = `import FeedbackService from '${relativePath}';`;
    lines.splice(insertIndex, 0, importStatement);

    // 写回文件
    const newContent = lines.join('\n');
    fs.writeFileSync(filePath, newContent, 'utf8');
    
    console.log(`✅ ${filePath}: 已添加FeedbackService导入`);
    totalFixed++;

  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
  }
});

console.log(`\n🎉 修复完成!`);
console.log(`📊 统计信息:`);
console.log(`   - 修复文件: ${totalFixed} 个`);
