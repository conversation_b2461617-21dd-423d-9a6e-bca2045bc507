"""
付费短信服务 (PaidSMSService)
基于现有NotificationService，集成企业级房源咨询消息系统的付费短信功能
"""

import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from decimal import Decimal
from sqlmodel.ext.asyncio.session import AsyncSession
from sqlmodel import select, and_, func

from app.models.sms_credit import (
    SMSCreditAccount, SMSUsageLog, SMSCreditRecharge,
    SMSType, SMSStatus, RechargeStatus
)
from app.models.user import User
from app.models.property_inquiry import PropertyInquiry
from app.core.sms import send_sms, SMSTemplate
from app.services.notification_service import NotificationService

logger = logging.getLogger(__name__)


class PaidSMSService:
    """
    付费短信服务
    
    核心功能：
    1. 基于现有NotificationService的付费短信扩展
    2. 399元订阅模式的短信积分管理
    3. 房源咨询消息的付费通知
    4. 完整的积分消费和充值记录
    """
    
    def __init__(self, db: AsyncSession, notification_service: NotificationService):
        self.db = db
        self.notification_service = notification_service
        self.logger = logging.getLogger(__name__)
    
    async def send_inquiry_notification_sms(
        self,
        landlord_id: uuid.UUID,
        inquiry_id: str,
        tenant_name: str,
        property_title: str,
        message_preview: str
    ) -> Dict[str, Any]:
        """
        发送房源咨询通知短信
        
        Args:
            landlord_id: 业主ID
            inquiry_id: 咨询ID
            tenant_name: 租客姓名
            property_title: 房源标题
            message_preview: 消息预览
            
        Returns:
            发送结果
        """
        try:
            # 1. 获取业主信息
            landlord = (await self.db.exec(
                select(User).where(User.id == landlord_id)
            )).first()
            
            if not landlord:
                return {"success": False, "error": "业主不存在"}
            
            # 2. 获取或创建SMS积分账户
            account = await self.get_or_create_sms_account(landlord_id)
            
            # 3. 检查付费通知设置
            if not account.paid_notifications.get("inquiry_received", False):
                return {
                    "success": False, 
                    "error": "业主未开启咨询通知",
                    "skipped": True
                }
            
            # 4. 检查并扣减积分
            cost_result = await self._deduct_sms_cost(
                account, 
                SMSType.INQUIRY_RECEIVED, 
                inquiry_id
            )
            
            if not cost_result["success"]:
                return cost_result
            
            # 5. 构建短信内容
            sms_content = self._build_inquiry_sms_content(
                tenant_name, property_title, message_preview
            )
            
            # 6. 发送短信
            sms_result = await send_sms(
                phone_number=landlord.phone,
                code="123456",  # 临时验证码，实际应该是通知内容
                template_type=SMSTemplate.LOGIN.value
            )
            
            # 7. 记录使用日志
            await self._log_sms_usage(
                account=account,
                sms_type=SMSType.INQUIRY_RECEIVED,
                phone_number=landlord.phone,
                cost_info=cost_result["cost_info"],
                send_result=sms_result,
                related_inquiry_id=inquiry_id
            )
            
            # 8. 发送应用内通知 (通过NotificationService)
            await self.notification_service.send_notification(
                user_id=str(landlord_id),
                notification_type="inquiry_received_sms",
                title="房源咨询通知已发送",
                content=f"已通过短信通知您收到来自{tenant_name}的房源咨询",
                data={
                    "inquiry_id": inquiry_id,
                    "sms_sent": True,
                    "cost_deducted": cost_result["cost_info"]["cost_amount"]
                }
            )
            
            return {
                "success": True,
                "message": "咨询通知短信发送成功",
                "sms_result": sms_result,
                "cost_info": cost_result["cost_info"],
                "remaining_balance": {
                    "subscription_sms": account.subscription_sms_balance,
                    "credits": account.credits_balance
                }
            }
            
        except Exception as e:
            self.logger.error(f"发送咨询通知短信失败: {str(e)}")
            return {"success": False, "error": f"发送失败: {str(e)}"}
    
    async def get_or_create_sms_account(self, user_id: uuid.UUID) -> SMSCreditAccount:
        """
        获取或创建用户的SMS积分账户
        
        Args:
            user_id: 用户ID
            
        Returns:
            SMS积分账户
        """
        try:
            # 查找现有账户
            account = (await self.db.exec(
                select(SMSCreditAccount).where(SMSCreditAccount.user_id == user_id)
            )).first()
            
            if account:
                return account
            
            # 创建新账户 (399元订阅模式默认配置)
            account = SMSCreditAccount(
                id=str(uuid.uuid4()),
                user_id=user_id,
                subscription_sms_balance=50,  # 399元订阅包含50条短信
                subscription_sms_total=50,
                credits_balance=0,
                money_balance=Decimal("0.00"),
                auto_recharge_enabled=False,
                auto_recharge_threshold=5,
                auto_recharge_amount=Decimal("10.00"),
                paid_notifications={
                    "inquiry_received": True,      # 默认开启咨询通知
                    "message_received": True,      # 默认开启消息通知
                    "appointment_request": True,   # 默认开启预约通知
                    "payment_success": True        # 默认开启付款成功通知
                }
            )
            
            self.db.add(account)
            await self.db.commit()
            await self.db.refresh(account)
            
            self.logger.info(f"为用户 {user_id} 创建SMS积分账户")
            return account
            
        except Exception as e:
            self.logger.error(f"获取或创建SMS账户失败: {str(e)}")
            raise
    
    async def check_sms_balance(self, user_id: uuid.UUID) -> Dict[str, Any]:
        """
        检查用户短信余额
        
        Args:
            user_id: 用户ID
            
        Returns:
            余额信息
        """
        try:
            account = await self.get_or_create_sms_account(user_id)
            
            # 计算总可用短信数
            total_available = account.subscription_sms_balance + account.credits_balance
            
            return {
                "success": True,
                "user_id": str(user_id),
                "account_id": account.id,
                "subscription_sms_balance": account.subscription_sms_balance,
                "subscription_sms_total": account.subscription_sms_total,
                "credits_balance": account.credits_balance,
                "money_balance": float(account.money_balance),
                "total_available": total_available,
                "auto_recharge_enabled": account.auto_recharge_enabled,
                "auto_recharge_threshold": account.auto_recharge_threshold,
                "auto_recharge_amount": float(account.auto_recharge_amount),
                "paid_notifications": account.paid_notifications,
                "low_balance_warning": total_available <= account.auto_recharge_threshold
            }
            
        except Exception as e:
            self.logger.error(f"检查短信余额失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def recharge_sms_credits(
        self,
        user_id: uuid.UUID,
        recharge_amount: Decimal,
        payment_method: str,
        payment_order_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        短信积分充值
        
        Args:
            user_id: 用户ID
            recharge_amount: 充值金额
            payment_method: 支付方式
            payment_order_id: 支付订单ID
            
        Returns:
            充值结果
        """
        try:
            account = await self.get_or_create_sms_account(user_id)
            
            # 计算积分数量 (1元 = 10积分 = 10条短信)
            credits_amount = int(recharge_amount * 10)
            
            # 创建充值记录
            recharge_record = SMSCreditRecharge(
                id=str(uuid.uuid4()),
                user_id=user_id,
                account_id=account.id,
                recharge_amount=recharge_amount,
                credits_amount=credits_amount,
                payment_method=payment_method,
                status=RechargeStatus.PENDING,
                payment_order_id=payment_order_id,
                is_auto_recharge=False,
                created_at=datetime.now()
            )
            
            self.db.add(recharge_record)
            await self.db.commit()
            await self.db.refresh(recharge_record)
            
            # 模拟支付成功 (实际应用中应该接收支付回调)
            await self._complete_recharge(recharge_record.id, {
                "transaction_id": f"txn_{uuid.uuid4().hex[:16]}",
                "payment_time": datetime.now().isoformat(),
                "status": "success"
            })
            
            return {
                "success": True,
                "message": "充值成功",
                "recharge_id": recharge_record.id,
                "recharge_amount": float(recharge_amount),
                "credits_amount": credits_amount,
                "new_balance": {
                    "subscription_sms": account.subscription_sms_balance,
                    "credits": account.credits_balance + credits_amount
                }
            }
            
        except Exception as e:
            self.logger.error(f"短信积分充值失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def get_sms_usage_history(
        self,
        user_id: uuid.UUID,
        page: int = 1,
        page_size: int = 20,
        sms_type_filter: Optional[SMSType] = None
    ) -> Dict[str, Any]:
        """
        获取短信使用历史
        
        Args:
            user_id: 用户ID
            page: 页码
            page_size: 每页数量
            sms_type_filter: 短信类型过滤
            
        Returns:
            使用历史记录
        """
        try:
            account = await self.get_or_create_sms_account(user_id)
            
            # 构建查询条件
            conditions = [SMSUsageLog.user_id == user_id]
            if sms_type_filter:
                conditions.append(SMSUsageLog.sms_type == sms_type_filter)
            
            # 分页查询
            offset = (page - 1) * page_size
            
            statement = select(SMSUsageLog).where(*conditions).order_by(
                SMSUsageLog.created_at.desc()
            ).offset(offset).limit(page_size)
            
            usage_logs = (await self.db.exec(statement)).all()
            
            # 统计总数
            count_statement = select(func.count(SMSUsageLog.id)).where(*conditions)
            total_count = (await self.db.exec(count_statement)).first()
            
            # 构建返回数据
            usage_list = []
            for log in usage_logs:
                usage_data = {
                    "id": log.id,
                    "sms_type": log.sms_type.value,
                    "phone_number": log.phone_number,
                    "template_code": log.template_code,
                    "cost_source": log.cost_source,
                    "cost_amount": log.cost_amount,
                    "send_status": log.send_status.value,
                    "created_at": log.created_at.isoformat() if log.created_at else None,
                    "related_inquiry_id": log.related_inquiry_id,
                    "balances": {
                        "subscription_before": log.subscription_balance_before,
                        "subscription_after": log.subscription_balance_after,
                        "credits_before": log.credits_balance_before,
                        "credits_after": log.credits_balance_after
                    }
                }
                usage_list.append(usage_data)
            
            return {
                "success": True,
                "usage_history": usage_list,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": (total_count + page_size - 1) // page_size
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取短信使用历史失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def update_notification_settings(
        self,
        user_id: uuid.UUID,
        notification_settings: Dict[str, bool]
    ) -> Dict[str, Any]:
        """
        更新付费通知设置
        
        Args:
            user_id: 用户ID
            notification_settings: 通知设置
            
        Returns:
            更新结果
        """
        try:
            account = await self.get_or_create_sms_account(user_id)
            
            # 合并新的通知设置
            current_settings = account.paid_notifications.copy()
            current_settings.update(notification_settings)
            account.paid_notifications = current_settings
            
            self.db.add(account)
            await self.db.commit()
            await self.db.refresh(account)
            
            return {
                "success": True,
                "message": "通知设置更新成功",
                "notification_settings": account.paid_notifications
            }
            
        except Exception as e:
            self.logger.error(f"更新通知设置失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _deduct_sms_cost(
        self,
        account: SMSCreditAccount,
        sms_type: SMSType,
        related_inquiry_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        扣减短信费用
        
        Args:
            account: SMS积分账户
            sms_type: 短信类型
            related_inquiry_id: 相关咨询ID
            
        Returns:
            扣费结果
        """
        try:
            cost_amount = 1  # 每条短信1积分
            
            # 记录扣费前余额
            subscription_before = account.subscription_sms_balance
            credits_before = account.credits_balance
            
            # 扣费逻辑：优先使用订阅短信余额，然后使用积分余额
            if account.subscription_sms_balance >= cost_amount:
                account.subscription_sms_balance -= cost_amount
                cost_source = "subscription"
            elif account.credits_balance >= cost_amount:
                account.credits_balance -= cost_amount
                cost_source = "credits"
            else:
                return {
                    "success": False,
                    "error": "短信余额不足",
                    "current_balance": {
                        "subscription_sms": account.subscription_sms_balance,
                        "credits": account.credits_balance
                    }
                }
            
            # 更新账户
            self.db.add(account)
            await self.db.commit()
            await self.db.refresh(account)
            
            return {
                "success": True,
                "cost_info": {
                    "cost_amount": cost_amount,
                    "cost_source": cost_source,
                    "subscription_balance_before": subscription_before,
                    "subscription_balance_after": account.subscription_sms_balance,
                    "credits_balance_before": credits_before,
                    "credits_balance_after": account.credits_balance
                }
            }
            
        except Exception as e:
            self.logger.error(f"扣减短信费用失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _complete_recharge(
        self,
        recharge_id: str,
        payment_result: Dict[str, Any]
    ) -> None:
        """
        完成充值流程
        
        Args:
            recharge_id: 充值记录ID
            payment_result: 支付结果
        """
        try:
            # 获取充值记录
            recharge_record = await self.db.exec(
                select(SMSCreditRecharge).where(SMSCreditRecharge.id == recharge_id)
            ).first()
            
            if not recharge_record:
                raise ValueError("充值记录不存在")
            
            # 获取账户
            account = await self.db.exec(
                select(SMSCreditAccount).where(SMSCreditAccount.id == recharge_record.account_id)
            ).first()
            
            if not account:
                raise ValueError("账户不存在")
            
            # 更新充值记录
            recharge_record.status = RechargeStatus.SUCCESS
            recharge_record.payment_result = payment_result
            recharge_record.completed_at = datetime.now()
            
            # 增加积分余额
            account.credits_balance += recharge_record.credits_amount
            
            # 更新数据库
            self.db.add(recharge_record)
            self.db.add(account)
            await self.db.commit()
            
            self.logger.info(f"充值完成: {recharge_id}, 积分: {recharge_record.credits_amount}")
            
        except Exception as e:
            self.logger.error(f"完成充值失败: {str(e)}")
            raise
    
    async def _log_sms_usage(
        self,
        account: SMSCreditAccount,
        sms_type: SMSType,
        phone_number: str,
        cost_info: Dict[str, Any],
        send_result: Dict[str, Any],
        related_inquiry_id: Optional[str] = None,
        related_property_id: Optional[uuid.UUID] = None
    ) -> None:
        """
        记录短信使用日志
        
        Args:
            account: SMS积分账户
            sms_type: 短信类型
            phone_number: 手机号码
            cost_info: 费用信息
            send_result: 发送结果
            related_inquiry_id: 相关咨询ID
            related_property_id: 相关房源ID
        """
        try:
            usage_log = SMSUsageLog(
                id=str(uuid.uuid4()),
                user_id=account.user_id,
                account_id=account.id,
                sms_type=sms_type,
                phone_number=phone_number,
                template_code=send_result.get("template_code", ""),
                template_params=send_result.get("template_params", {}),
                cost_source=cost_info["cost_source"],
                cost_amount=cost_info["cost_amount"],
                subscription_balance_before=cost_info["subscription_balance_before"],
                subscription_balance_after=cost_info["subscription_balance_after"],
                credits_balance_before=cost_info["credits_balance_before"],
                credits_balance_after=cost_info["credits_balance_after"],
                send_status=SMSStatus.SENT if send_result.get("success") else SMSStatus.FAILED,
                send_result=send_result,
                related_inquiry_id=related_inquiry_id,
                related_property_id=related_property_id,
                created_at=datetime.now()
            )
            
            self.db.add(usage_log)
            await self.db.commit()
            
        except Exception as e:
            self.logger.error(f"记录短信使用日志失败: {str(e)}")
            # 不抛出异常，避免影响主流程
    
    def _build_inquiry_sms_content(
        self,
        tenant_name: str,
        property_title: str,
        message_preview: str
    ) -> str:
        """
        构建咨询短信内容
        
        Args:
            tenant_name: 租客姓名
            property_title: 房源标题
            message_preview: 消息预览
            
        Returns:
            短信内容
        """
        return f"【慧选址】您的房源《{property_title}》收到来自{tenant_name}的咨询：{message_preview}。请及时回复。"
    
    async def get_monthly_sms_statistics(
        self,
        user_id: uuid.UUID,
        year: int,
        month: int
    ) -> Dict[str, Any]:
        """
        获取月度短信统计
        
        Args:
            user_id: 用户ID
            year: 年份
            month: 月份
            
        Returns:
            统计结果
        """
        try:
            # 计算月份时间范围
            start_date = datetime(year, month, 1)
            if month == 12:
                end_date = datetime(year + 1, 1, 1)
            else:
                end_date = datetime(year, month + 1, 1)
            
            # 查询月度使用记录
            usage_logs = (await self.db.exec(
                select(SMSUsageLog).where(
                    and_(
                        SMSUsageLog.user_id == user_id,
                        SMSUsageLog.created_at >= start_date,
                        SMSUsageLog.created_at < end_date
                    )
                )
            )).all()
            
            # 统计各类型短信数量
            type_stats = {}
            total_cost = 0
            success_count = 0
            failed_count = 0
            
            for log in usage_logs:
                sms_type = log.sms_type.value
                if sms_type not in type_stats:
                    type_stats[sms_type] = 0
                type_stats[sms_type] += 1
                
                total_cost += log.cost_amount
                
                if log.send_status == SMSStatus.SENT:
                    success_count += 1
                else:
                    failed_count += 1
            
            return {
                "success": True,
                "period": f"{year}年{month}月",
                "statistics": {
                    "total_sent": len(usage_logs),
                    "success_count": success_count,
                    "failed_count": failed_count,
                    "success_rate": success_count / len(usage_logs) * 100 if usage_logs else 0,
                    "total_cost": total_cost,
                    "type_breakdown": type_stats
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取月度短信统计失败: {str(e)}")
            return {"success": False, "error": str(e)}