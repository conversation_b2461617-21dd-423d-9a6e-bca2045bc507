/**
 * 房源类型配置模板
 * 基于通用房源详情页设计方案的配置驱动架构
 * 
 * 支持的房源类型：
 * - SHOP: 商铺
 * - OFFICE: 写字楼
 * - FACTORY: 厂房
 * - WAREHOUSE: 仓库
 * - BOOTH: 摊位
 * - CLUB: 会所
 */

import type { 
  PropertyType, 
  KeyMetric, 
  InfoSection, 
  TagConfig, 
  ActionConfig 
} from '@property/types';

/**
 * 本地房源类型配置接口（包含默认值）
 */
interface LocalPropertyTypeConfig {
  propertyType: PropertyType;
  keyMetrics: KeyMetric[]; // 包含完整的KeyMetric，含默认值
  infoSections: InfoSection[]; // 包含完整的InfoSection，含默认值
  defaultTags: Omit<TagConfig, 'key'>[]; // 默认标签配置
  defaultActions: ActionConfig[]; // 默认操作配置
}

/**
 * 商铺类型配置
 */
const SHOP_CONFIG: LocalPropertyTypeConfig = {
  propertyType: 'SHOP',
  
  // 🔥 关键指标配置 - 移除图标，严格按照图片布局
  keyMetrics: [
    {
      key: 'rent_price',
      label: '租金',
      value: '',
      format: 'price',
      primary: true
      // 移除 icon: 'card-outline'
    },
    {
      key: 'transfer_fee',
      label: '转让费',
      value: '',
      format: 'price'
      // 移除 icon: 'swap-horizontal-outline'
    },
    {
      key: 'area',
      label: '面积',
      value: '',
      format: 'area'
      // 移除 icon: 'resize-outline'
    }
  ],
  
  // 信息区块配置
  infoSections: [
    {
      id: 'shop_basic_info',
      title: '商铺信息',
      layout: 'grid',
      columns: 2,
      collapsible: false,
      defaultExpanded: true,
      fields: [
        { key: 'shop_type', label: '商铺类型', type: 'text', value: '', visible: true },
        { key: 'floor_info', label: '楼层', type: 'text', value: '', visible: true },
        { key: 'street_status', label: '临街状态', type: 'text', value: '', visible: true },
        { key: 'industry', label: '适合行业', type: 'text', value: '', visible: true },
        { key: 'operation_status', label: '经营状态', type: 'text', value: '', visible: true },
        { key: 'customer_flow', label: '客流特征', type: 'text', value: '', visible: true }
      ]
    },
    {
      id: 'shop_specs',
      title: '规格参数',
      layout: 'list',
      collapsible: true,
      defaultExpanded: true,
      fields: [
        { key: 'dimensions', label: '店铺规格', type: 'text', value: '', visible: true },
        { key: 'payment_terms', label: '付款方式', type: 'text', value: '', visible: true },
        { key: 'lease_remaining', label: '剩余租期', type: 'text', value: '', visible: true }
      ]
    }
  ],
  
  // 默认标签配置
  defaultTags: [
    { label: '首次转让', type: 'highlight' },
    { label: '除转让费', type: 'normal' },
    { label: '免费咨询', type: 'success' }
  ],
  
  // 默认操作配置
  defaultActions: [
    {
      key: 'favorite',
      label: '收藏',
      type: 'icon',
      icon: 'heart-outline',
      action: 'toggle_favorite',
      enabled: true,
      visible: true
    },
    {
      key: 'track',
      label: '追踪',
      type: 'icon',
      icon: 'eye-outline',
      action: 'toggle_track',
      enabled: true,
      visible: true
    },
    {
      key: 'contact',
      label: '立即沟通',
      type: 'primary',
      action: 'contact_owner',
      enabled: true,
      visible: true
    },
    {
      key: 'reserve',
      label: '预约看房',
      type: 'secondary',
      action: 'reserve_viewing',
      enabled: true,
      visible: true,
      badge: '系统推荐'
    }
  ]
};

/**
 * 写字楼类型配置
 */
const OFFICE_CONFIG: LocalPropertyTypeConfig = {
  propertyType: 'OFFICE',
  
  keyMetrics: [
    {
      key: 'rent_price',
      label: '租金',
      value: '',
      format: 'price',
      primary: true
    },
    {
      key: 'area',
      label: '面积',
      value: '',
      format: 'area'
    },
    {
      key: 'workstations',
      label: '工位数',
      value: 0,
      format: 'number',
      unit: '个'
    }
  ],
  
  infoSections: [
    {
      id: 'office_basic_info',
      title: '写字楼信息',
      layout: 'grid',
      columns: 2,
      collapsible: false,
      defaultExpanded: true,
      fields: [
        { key: 'building_grade', label: '楼宇等级', type: 'text', value: '', visible: true },
        { key: 'floor_info', label: '楼层', type: 'text', value: '', visible: true },
        { key: 'orientation', label: '朝向', type: 'text', value: '', visible: true },
        { key: 'decoration', label: '装修状况', type: 'text', value: '', visible: true },
        { key: 'parking', label: '停车配套', type: 'text', value: '', visible: true },
        { key: 'facilities', label: '配套设施', type: 'list', value: [], visible: true }
      ]
    }
  ],
  
  defaultTags: [
    { label: '甲级写字楼', type: 'highlight' },
    { label: '精装修', type: 'success' },
    { label: '地铁直达', type: 'info' }
  ],
  
  defaultActions: [
    {
      key: 'favorite',
      label: '收藏',
      type: 'icon',
      icon: 'heart-outline',
      action: 'toggle_favorite',
      enabled: true,
      visible: true
    },
    {
      key: 'contact',
      label: '立即咨询',
      type: 'primary',
      action: 'contact_owner',
      enabled: true,
      visible: true
    },
    {
      key: 'reserve',
      label: '预约看房',
      type: 'secondary',
      action: 'reserve_viewing',
      enabled: true,
      visible: true
    }
  ]
};

/**
 * 厂房类型配置
 */
const FACTORY_CONFIG: LocalPropertyTypeConfig = {
  propertyType: 'FACTORY',
  
  keyMetrics: [
    {
      key: 'rent_price',
      label: '租金',
      value: '',
      format: 'price',
      primary: true
    },
    {
      key: 'area',
      label: '面积',
      value: '',
      format: 'area'
    },
    {
      key: 'load_capacity',
      label: '承重',
      value: 0,
      format: 'weight',
      unit: '吨/m²'
    }
  ],
  
  infoSections: [
    {
      id: 'factory_basic_info',
      title: '厂房信息',
      layout: 'grid',
      columns: 2,
      collapsible: false,
      defaultExpanded: true,
      fields: [
        { key: 'factory_type', label: '厂房类型', type: 'text', value: '', visible: true },
        { key: 'ceiling_height', label: '层高', type: 'text', value: '', visible: true },
        { key: 'power_capacity', label: '用电容量', type: 'text', value: '', visible: true },
        { key: 'loading_dock', label: '装卸平台', type: 'boolean', value: false, visible: true },
        { key: 'crane_equipment', label: '起重设备', type: 'text', value: '', visible: true },
        { key: 'fire_rating', label: '消防等级', type: 'text', value: '', visible: true }
      ]
    }
  ],
  
  defaultTags: [
    { label: '标准厂房', type: 'normal' },
    { label: '配套齐全', type: 'success' },
    { label: '可分租', type: 'info' }
  ],
  
  defaultActions: [
    {
      key: 'favorite',
      label: '收藏',
      type: 'icon',
      icon: 'heart-outline',
      action: 'toggle_favorite',
      enabled: true,
      visible: true
    },
    {
      key: 'contact',
      label: '咨询详情',
      type: 'primary',
      action: 'contact_owner',
      enabled: true,
      visible: true
    },
    {
      key: 'reserve',
      label: '实地看厂',
      type: 'secondary',
      action: 'reserve_viewing',
      enabled: true,
      visible: true
    }
  ]
};

/**
 * 房源类型配置映射
 */
export const PROPERTY_TYPE_CONFIGS: Record<PropertyType, LocalPropertyTypeConfig> = {
  SHOP: SHOP_CONFIG,
  OFFICE: OFFICE_CONFIG,
  FACTORY: FACTORY_CONFIG,
  
  // 其他类型使用商铺配置作为默认配置
  WAREHOUSE: { ...SHOP_CONFIG, propertyType: 'WAREHOUSE' },
  BOOTH: { ...SHOP_CONFIG, propertyType: 'BOOTH' },
  CLUB: { ...SHOP_CONFIG, propertyType: 'CLUB' },
};

/**
 * 获取房源类型配置
 */
export const getPropertyTypeConfig = (propertyType: PropertyType): LocalPropertyTypeConfig => {
  return PROPERTY_TYPE_CONFIGS[propertyType] || PROPERTY_TYPE_CONFIGS.SHOP;
};

/**
 * 获取支持的房源类型列表
 */
export const getSupportedPropertyTypes = (): PropertyType[] => {
  return Object.keys(PROPERTY_TYPE_CONFIGS) as PropertyType[];
};

/**
 * 房源类型显示名称映射
 */
export const PROPERTY_TYPE_DISPLAY_NAMES: Record<PropertyType, string> = {
  SHOP: '商铺',
  OFFICE: '写字楼',
  FACTORY: '厂房',
  WAREHOUSE: '仓库',
  BOOTH: '摊位',
  CLUB: '会所',
  LAND: '土地', // 🔧 新增：土地类型映射
  TERRACE: '露台', // 🔧 新增：露台类型映射
  CLUBHOUSE: '会所', // 🔧 新增：会所类型映射
  MEETING_ROOM: '会议室', // 🔧 新增：会议室类型映射
};

/**
 * 获取房源类型显示名称
 */
export const getPropertyTypeDisplayName = (propertyType: PropertyType): string => {
  return PROPERTY_TYPE_DISPLAY_NAMES[propertyType] || '未知类型';
};

export default PROPERTY_TYPE_CONFIGS;