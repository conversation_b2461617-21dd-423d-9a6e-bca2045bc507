/**
 * 验证导航系统修复 - 检查屏幕注册情况
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 导航系统修复验证开始...\n');

// 读取导航配置文件
const appNavigatorPath = './src/navigation/AppNavigator.tsx';
const typesPath = './src/navigation/types.ts';

try {
  // 检查AppNavigator.tsx中定义的屏幕
  const appNavigatorContent = fs.readFileSync(appNavigatorPath, 'utf8');
  const screenMatches = appNavigatorContent.match(/name="([^"]+)"/g) || [];
  const definedScreens = screenMatches.map(match =>
    match.replace(/name="|"/g, '')
  );

  console.log(
    '📱 AppNavigator.tsx中定义的屏幕 (' + definedScreens.length + '个):'
  );
  definedScreens.sort().forEach(screen => {
    console.log(`  - ${screen}`);
  });

  // 检查types.ts中的类型定义
  const typesContent = fs.readFileSync(typesPath, 'utf8');

  // 提取RootStackParamList中的键
  const paramListMatch = typesContent.match(
    /export type RootStackParamList = \{([\s\S]*?)\};/
  );
  if (paramListMatch) {
    const paramListContent = paramListMatch[1];
    const typeMatches =
      paramListContent.match(/^\s*([A-Za-z][A-Za-z0-9]*)\s*:/gm) || [];
    const definedTypes = typeMatches.map(match =>
      match.replace(/^\s*|\s*:.*$/g, '')
    );

    console.log('\n📋 types.ts中定义的类型 (' + definedTypes.length + '个):');
    definedTypes.sort().forEach(type => {
      console.log(`  - ${type}`);
    });

    // 检查缺失的类型
    const missingTypes = definedScreens.filter(
      screen => !definedTypes.includes(screen)
    );
    const unusedTypes = definedTypes.filter(
      type => !definedScreens.includes(type)
    );

    console.log('\n⚠️ 分析结果:');

    if (missingTypes.length > 0) {
      console.log(`\n❌ 缺失类型定义的屏幕 (${missingTypes.length}个):`);
      missingTypes.forEach(screen => {
        console.log(`  - ${screen}`);
      });
    } else {
      console.log('\n✅ 所有屏幕都有对应的类型定义');
    }

    if (unusedTypes.length > 0) {
      console.log(`\n📝 未使用的类型定义 (${unusedTypes.length}个):`);
      unusedTypes.forEach(type => {
        console.log(`  - ${type}`);
      });
    }

    // 关键检查：PropertyNavigation 和 AddressSearch
    console.log('\n🎯 关键屏幕检查:');

    const keyScreens = ['PropertyNavigation', 'AddressSearch'];
    keyScreens.forEach(screen => {
      const hasScreenDef = definedScreens.includes(screen);
      const hasTypeDef = definedTypes.includes(screen);

      console.log(`  ${screen}:`);
      console.log(`    - 屏幕定义: ${hasScreenDef ? '✅' : '❌'}`);
      console.log(`    - 类型定义: ${hasTypeDef ? '✅' : '❌'}`);
      console.log(
        `    - 状态: ${hasScreenDef && hasTypeDef ? '完整' : '不完整'}`
      );
    });
  }

  console.log('\n🎯 导航系统修复验证完成！');
} catch (error) {
  console.error('❌ 验证过程出错:', error.message);
}
