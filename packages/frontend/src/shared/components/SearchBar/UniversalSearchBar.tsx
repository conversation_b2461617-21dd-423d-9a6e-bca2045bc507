/**
 * 统一搜索栏组件
 * 支持多种模式和主题，可在不同页面复用
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { UniversalSearchBarProps, SearchBarMode } from './types';
import { getSearchBarTheme, animationConfigs } from './themes';

/**
 * 轮播搜索栏组件
 */
const CarouselSearchBar: React.FC<Extract<UniversalSearchBarProps, { mode: 'carousel' }>> = ({
  theme = 'homepage',
  placeholders,
  interval = animationConfigs.carousel.interval,
  onPress,
  citySelector,
  rightIcon,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const themeConfig = getSearchBarTheme(theme);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % placeholders.length);
    }, interval);

    return () => clearInterval(timer);
  }, [placeholders.length, interval]);

  const styles = createStyles(themeConfig);

  return (
    <View style={styles.container}>
      {/* 城市选择器 */}
      {citySelector && (
        <TouchableOpacity style={styles.citySelector} onPress={citySelector.onCityPress}>
          <Text style={styles.cityText}>{citySelector.currentCity}</Text>
          <Ionicons name="chevron-down" size={12} color={themeConfig.icon.color} />
        </TouchableOpacity>
      )}

      {/* 搜索框 */}
      <TouchableOpacity style={styles.searchBox} onPress={onPress}>
        <Ionicons name="search" size={themeConfig.icon.size} color={themeConfig.icon.color} />
        <Text style={styles.placeholder}>
          {placeholders[currentIndex]}
        </Text>
      </TouchableOpacity>

      {/* 右侧图标 */}
      {rightIcon && (
        <TouchableOpacity style={styles.rightIcon} onPress={rightIcon.onPress}>
          <Text style={styles.iconText}>{rightIcon.icon}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

/**
 * 输入搜索栏组件
 */
const InputSearchBar: React.FC<Extract<UniversalSearchBarProps, { mode: 'input' }>> = ({
  theme = 'map',
  placeholder = '搜索',
  value,
  onChangeText,
  onSearch,
  onFocus,
  showClearButton = true,
}) => {
  const [localValue, setLocalValue] = useState('');
  const currentValue = value !== undefined ? value : localValue;
  const themeConfig = getSearchBarTheme(theme);

  const handleChangeText = (text: string) => {
    if (value === undefined) {
      setLocalValue(text);
    }
    onChangeText?.(text);
  };

  const handleSearch = () => {
    if (onSearch && currentValue.trim()) {
      onSearch(currentValue.trim());
    }
  };

  const handleClear = () => {
    if (value === undefined) {
      setLocalValue('');
    }
    onChangeText?.('');
  };

  const styles = createStyles(themeConfig);

  return (
    <View style={styles.container}>
      <View style={styles.searchBox}>
        <Ionicons name="search" size={themeConfig.icon.size} color={themeConfig.icon.color} />
        <TextInput
          style={styles.input}
          placeholder={placeholder}
          placeholderTextColor={themeConfig.text.placeholderColor}
          value={currentValue}
          onChangeText={handleChangeText}
          onFocus={onFocus}
          onSubmitEditing={handleSearch}
          returnKeyType="search"
        />
        {showClearButton && currentValue.length > 0 && (
          <TouchableOpacity onPress={handleClear} style={styles.clearButton}>
            <Ionicons name="close-circle" size={18} color={themeConfig.icon.color} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

/**
 * 动画搜索栏组件（滚动时隐藏）
 */
const AnimatedSearchBar: React.FC<Extract<UniversalSearchBarProps, { mode: 'animated' }>> = ({
  theme = 'property',
  placeholder,
  propertyType,
  scrollY,
  onChangeText,
  onSearch,
}) => {
  const [searchText, setSearchText] = useState('');
  const themeConfig = getSearchBarTheme(theme);
  const { inputRange, opacityRange, translateYRange, scaleRange } = animationConfigs.scrollHide;

  const handleChangeText = (text: string) => {
    setSearchText(text);
    onChangeText?.(text);
  };

  const handleSearch = () => {
    if (onSearch) {
      onSearch(searchText);
    }
  };

  const finalPlaceholder = placeholder || `搜索你想要的${propertyType || '房源'}...`;
  const styles = createStyles(themeConfig);

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: scrollY.interpolate({
            inputRange,
            outputRange: opacityRange,
            extrapolate: 'clamp',
          }),
          transform: [
            {
              translateY: scrollY.interpolate({
                inputRange,
                outputRange: translateYRange,
                extrapolate: 'clamp',
              }),
            },
            {
              scale: scrollY.interpolate({
                inputRange,
                outputRange: scaleRange,
                extrapolate: 'clamp',
              }),
            },
          ],
        },
      ]}
    >
      <View style={styles.searchBox}>
        <Ionicons name="search" size={themeConfig.icon.size} color={themeConfig.icon.color} />
        <TextInput
          style={styles.input}
          placeholder={finalPlaceholder}
          placeholderTextColor={themeConfig.text.placeholderColor}
          value={searchText}
          onChangeText={handleChangeText}
          onSubmitEditing={handleSearch}
          returnKeyType="search"
        />
      </View>
    </Animated.View>
  );
};

/**
 * 紧凑搜索栏组件（滚动时显示）
 */
const CompactSearchBar: React.FC<Extract<UniversalSearchBarProps, { mode: 'compact' }>> = ({
  theme = 'minimal',
  placeholder,
  propertyType,
  scrollY,
  onChangeText,
  onSearch,
}) => {
  const [searchText, setSearchText] = useState('');
  const themeConfig = getSearchBarTheme(theme);
  const { inputRange, opacityRange, scaleInputRange, scaleRange } = animationConfigs.scrollShow;

  const handleChangeText = (text: string) => {
    setSearchText(text);
    onChangeText?.(text);
  };

  const handleSearch = () => {
    if (onSearch) {
      onSearch(searchText);
    }
  };

  const finalPlaceholder = placeholder || `搜索${propertyType || '房源'}`;
  const styles = createStyles(themeConfig);

  return (
    <Animated.View
      style={[
        styles.compactContainer,
        {
          opacity: scrollY.interpolate({
            inputRange,
            outputRange: opacityRange,
            extrapolate: 'clamp',
          }),
          transform: [
            {
              scale: scrollY.interpolate({
                inputRange: scaleInputRange,
                outputRange: scaleRange,
                extrapolate: 'clamp',
              }),
            },
          ],
        },
      ]}
    >
      <View style={styles.searchBox}>
        <Ionicons name="search" size={themeConfig.icon.size} color={themeConfig.icon.color} />
        <TextInput
          style={styles.input}
          placeholder={finalPlaceholder}
          placeholderTextColor={themeConfig.text.placeholderColor}
          value={searchText}
          onChangeText={handleChangeText}
          onSubmitEditing={handleSearch}
          returnKeyType="search"
        />
      </View>
    </Animated.View>
  );
};

/**
 * 统一搜索栏组件
 */
export const UniversalSearchBar: React.FC<UniversalSearchBarProps> = (props) => {
  switch (props.mode) {
    case 'carousel':
      return <CarouselSearchBar {...props} />;
    case 'input':
      return <InputSearchBar {...props} />;
    case 'animated':
      return <AnimatedSearchBar {...props} />;
    case 'compact':
      return <CompactSearchBar {...props} />;
    default:
      return null;
  }
};

/**
 * 创建样式
 */
const createStyles = (themeConfig: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themeConfig.container.backgroundColor,
    paddingHorizontal: themeConfig.container.paddingHorizontal,
    paddingVertical: themeConfig.container.paddingVertical,
  },
  compactContainer: {
    position: 'absolute',
    right: 72,
    top: 12,
    zIndex: 1000,
  },
  citySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
    paddingVertical: 2,
  },
  cityText: {
    fontSize: themeConfig.text.fontSize,
    color: themeConfig.text.color,
    marginRight: 4,
  },
  searchBox: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themeConfig.searchBox.backgroundColor,
    borderRadius: themeConfig.searchBox.borderRadius,
    height: themeConfig.searchBox.height,
    paddingHorizontal: themeConfig.searchBox.paddingHorizontal,
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontSize: themeConfig.text.fontSize,
    color: themeConfig.text.color,
    marginLeft: 8,
    paddingVertical: 0,
  },
  placeholder: {
    flex: 1,
    fontSize: themeConfig.text.fontSize,
    color: themeConfig.text.placeholderColor,
    marginLeft: 8,
  },
  clearButton: {
    marginLeft: 8,
    padding: 2,
  },
  rightIcon: {
    padding: 8,
  },
  iconText: {
    fontSize: themeConfig.icon.size,
  },
});
