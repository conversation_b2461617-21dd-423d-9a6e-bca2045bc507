/**
 * 消息系统类型定义
 * 符合企业级架构规范，支持统一转换层
 */
import { z } from 'zod';

/**
 * 消息角色枚举
 */
export type MessageRole = 'tenant_buyer' | 'landlord';

/**
 * 消息分类枚举
 */
export type MessageCategory = 'all' | 'inquiry' | 'appointment' | 'system';

/**
 * 消息状态枚举
 */
export type MessageStatus = 'unread' | 'read' | 'replied' | 'archived';

/**
 * 消息优先级枚举
 */
export type MessagePriority = 'low' | 'normal' | 'high' | 'urgent';

/**
 * 前端消息查询输入类型
 */
export interface MessageQueryInput {
  role: MessageRole;
  category: MessageCategory;
  skip?: number;
  limit?: number;
  status?: MessageStatus;
  startDate?: string;
  endDate?: string;
}

/**
 * 后端API消息查询类型
 */
export interface MessageQueryOutput {
  user_role: string;
  message_category: string;
  pagination: {
    offset: number;
    limit: number;
  };
  filters?: {
    status?: string;
    date_range?: {
      start: string;
      end: string;
    };
  };
}

/**
 * 前端消息数据类型
 */
export interface MessageData {
  id: string;
  title: string;
  content: string;
  category: MessageCategory;
  status: MessageStatus;
  priority: MessagePriority;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  recipientId: string;
  recipientRole: MessageRole;
  propertyId?: string;
  propertyTitle?: string;
  createdAt: Date;
  updatedAt: Date;
  readAt?: Date;
  repliedAt?: Date;
  attachments?: MessageAttachment[];
  metadata?: Record<string, any>;
}

/**
 * 后端API消息数据类型
 */
export interface MessageDataAPI {
  message_id: string;
  message_title: string;
  message_content: string;
  message_category: string;
  message_status: string;
  message_priority: string;
  sender_id: string;
  sender_name: string;
  sender_avatar?: string;
  recipient_id: string;
  recipient_role: string;
  property_id?: string;
  property_title?: string;
  created_at: string;
  updated_at: string;
  read_at?: string;
  replied_at?: string;
  attachments?: MessageAttachmentAPI[];
  metadata?: Record<string, any>;
}

/**
 * 消息附件类型
 */
export interface MessageAttachment {
  id: string;
  type: 'image' | 'video' | 'audio' | 'document';
  url: string;
  name: string;
  size: number;
  mimeType: string;
  createdAt: Date;
}

/**
 * 后端API消息附件类型
 */
export interface MessageAttachmentAPI {
  attachment_id: string;
  attachment_type: string;
  attachment_url: string;
  attachment_name: string;
  attachment_size: number;
  attachment_mime_type: string;
  created_at: string;
}

/**
 * 用户角色信息类型
 */
export interface UserRoleInfo {
  userId: string;
  roles: MessageRole[];
  permissions: string[];
  tenantBuyerProfile?: {
    verified: boolean;
    creditScore: number;
    preferences: Record<string, any>;
  };
  landlordProfile?: {
    verified: boolean;
    propertyCount: number;
    rating: number;
  };
}

/**
 * 后端API用户角色信息类型
 */
export interface UserRoleInfoAPI {
  user_id: string;
  user_roles: string[];
  user_permissions: string[];
  tenant_buyer_profile?: {
    is_verified: boolean;
    credit_score: number;
    user_preferences: Record<string, any>;
  };
  landlord_profile?: {
    is_verified: boolean;
    property_count: number;
    user_rating: number;
  };
}

/**
 * 付费解锁信息类型
 */
export interface PaymentUnlockInfo {
  category: MessageCategory;
  isUnlocked: boolean;
  requiredCredits: number;
  currentCredits: number;
  unlockPrice: number;
  validUntil?: Date;
  features: string[];
}

/**
 * 后端API付费解锁信息类型
 */
export interface PaymentUnlockInfoAPI {
  message_category: string;
  is_unlocked: boolean;
  required_credits: number;
  current_credits: number;
  unlock_price: number;
  valid_until?: string;
  unlock_features: string[];
}

/**
 * 互动用户信息类型
 */
export interface InteractedUser {
  userId: string;
  userName: string;
  userAvatar?: string;
  userRole: MessageRole;
  lastMessageAt: Date;
  unreadCount: number;
  isOnline: boolean;
  propertyId?: string;
  propertyTitle?: string;
}

/**
 * 后端API互动用户信息类型
 */
export interface InteractedUserAPI {
  user_id: string;
  user_name: string;
  user_avatar?: string;
  user_role: string;
  last_message_at: string;
  unread_count: number;
  is_online: boolean;
  property_id?: string;
  property_title?: string;
}

/**
 * SMS积分账户信息类型
 */
export interface SMSCreditAccount {
  userId: string;
  currentCredits: number;
  totalCredits: number;
  usedCredits: number;
  lastRechargeAt?: Date;
  expiresAt?: Date;
  packages: SMSCreditPackage[];
}

/**
 * 后端API SMS积分账户信息类型
 */
export interface SMSCreditAccountAPI {
  user_id: string;
  current_credits: number;
  total_credits: number;
  used_credits: number;
  last_recharge_at?: string;
  expires_at?: string;
  credit_packages: SMSCreditPackageAPI[];
}

/**
 * SMS积分套餐类型
 */
export interface SMSCreditPackage {
  id: string;
  name: string;
  credits: number;
  price: number;
  validDays: number;
  isActive: boolean;
  description: string;
}

/**
 * 后端API SMS积分套餐类型
 */
export interface SMSCreditPackageAPI {
  package_id: string;
  package_name: string;
  package_credits: number;
  package_price: number;
  valid_days: number;
  is_active: boolean;
  package_description: string;
}

/**
 * Zod验证规则
 */
export const MessageQueryInputSchema = z.object({
  role: z.enum(['tenant_buyer', 'landlord']),
  category: z.enum(['all', 'inquiry', 'appointment', 'system']),
  skip: z.number().min(0).optional(),
  limit: z.number().min(1).max(100).optional(),
  status: z.enum(['unread', 'read', 'replied', 'archived']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

export const MessageDataSchema = z.object({
  id: z.string(),
  title: z.string(),
  content: z.string(),
  category: z.enum(['all', 'inquiry', 'appointment', 'system']),
  status: z.enum(['unread', 'read', 'replied', 'archived']),
  priority: z.enum(['low', 'normal', 'high', 'urgent']),
  senderId: z.string(),
  senderName: z.string(),
  senderAvatar: z.string().optional(),
  recipientId: z.string(),
  recipientRole: z.enum(['tenant_buyer', 'landlord']),
  propertyId: z.string().optional(),
  propertyTitle: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  readAt: z.date().optional(),
  repliedAt: z.date().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * 转换结果类型
 */
export interface TransformResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  validationErrors?: string[];
}

/**
 * 转换选项类型
 */
export interface TransformOptions {
  validateInput?: boolean;
  validateOutput?: boolean;
  throwOnError?: boolean;
  logErrors?: boolean;
}
