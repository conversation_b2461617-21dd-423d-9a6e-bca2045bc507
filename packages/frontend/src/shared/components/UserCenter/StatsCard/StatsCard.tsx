/**
 * 用户中心统计卡片组件
 * 使用通用计数组件显示各类统计数据
 * 遵循企业级架构规范 - UI层
 */
import React, { memo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CountDisplay, StatCount, LargeStat } from '../CountDisplay';
import {
  wp,
  hp,
  fp,
  spacing,
  borderRadius,
} from '@shared/utils/responsiveUtils';

// 🏗️ 企业级架构：统计卡片类型定义
export interface StatsCardProps {
  // 基础信息
  title: string;
  icon?: string;
  iconColor?: string;

  // 统计数据
  mainCount: number;
  subStats?: Array<{
    label: string;
    count: number;
    color?: string;
  }>;

  // 样式配置
  variant?: 'default' | 'compact' | 'detailed';
  backgroundColor?: string;

  // 交互
  onPress?: () => void;
  disabled?: boolean;
}

export const StatsCard = memo<StatsCardProps>(
  ({
    title,
    icon,
    iconColor = '#FF6B35',
    mainCount,
    subStats = [],
    variant = 'default',
    backgroundColor = '#FFFFFF',
    onPress,
    disabled = false,
  }) => {
    // 🎨 样式计算
    const containerStyle = [
      styles.container,
      styles[`${variant}Container`],
      { backgroundColor },
      disabled && styles.disabled,
    ];

    const CardComponent = onPress ? TouchableOpacity : View;

    return (
      <CardComponent
        style={containerStyle}
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.7}
      >
        {/* 卡片头部 */}
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            {icon && (
              <Ionicons
                name={icon as any}
                size={wp(20)}
                color={iconColor}
                style={styles.icon}
              />
            )}
            <Text style={styles.title}>{title}</Text>
          </View>

          {onPress && (
            <Ionicons name="chevron-forward" size={wp(16)} color="#999999" />
          )}
        </View>

        {/* 主要统计数据 */}
        <View style={styles.mainStatContainer}>
          <LargeStat count={mainCount} color="primary" showZero />
        </View>

        {/* 子统计数据 */}
        {subStats.length > 0 && (
          <View style={styles.subStatsContainer}>
            {subStats.map((stat, index) => (
              <View key={index} style={styles.subStatItem}>
                <StatCount
                  count={stat.count}
                  color={(stat.color as any) || 'secondary'}
                  size="small"
                  showZero
                />
                <Text style={styles.subStatLabel}>{stat.label}</Text>
              </View>
            ))}
          </View>
        )}
      </CardComponent>
    );
  }
);

StatsCard.displayName = 'StatsCard';

// 🎨 样式定义
const styles = StyleSheet.create({
  // 基础容器样式
  container: {
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    marginVertical: spacing.sm,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // 变体样式
  defaultContainer: {
    minHeight: hp(120),
  },

  compactContainer: {
    minHeight: hp(80),
    padding: spacing.md,
  },

  detailedContainer: {
    minHeight: hp(160),
  },

  // 禁用状态
  disabled: {
    opacity: 0.6,
  },

  // 头部样式
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },

  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  icon: {
    marginRight: spacing.sm,
  },

  title: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },

  // 主要统计样式
  mainStatContainer: {
    alignItems: 'center',
    marginVertical: spacing.md,
  },

  // 子统计样式
  subStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },

  subStatItem: {
    alignItems: 'center',
    flex: 1,
  },

  subStatLabel: {
    fontSize: fp(12),
    color: '#666666',
    marginTop: spacing.xs,
    textAlign: 'center',
  },
});

export default StatsCard;
