import React from 'react';
import { View, Text } from 'react-native';
import { wp, hp, fp } from '../../../shared/utils/responsiveUtils';

interface PropertyFormProgressProps {
  currentStep: number;
  totalSteps: number;
  stepNames?: string[];
}

export const PropertyFormProgress: React.FC<PropertyFormProgressProps> = ({
  currentStep,
  totalSteps,
  stepNames = []
}) => {
  const progress = (currentStep / totalSteps) * 100;

  const getSegmentColor = (index: number) => {
    if (index < currentStep) {
      return '#34C759'; // 已完成 - 绿色
    } else if (index === currentStep) {
      return '#FF6B35'; // 当前步骤 - 橙色
    } else {
      return '#E5E5E5'; // 未开始 - 灰色
    }
  };

  const getTipText = () => {
    if (currentStep === totalSteps) {
      return '✅ 表单填写完成！';
    } else {
      const remaining = totalSteps - currentStep;
      return `还有 ${remaining} 个步骤`;
    }
  };

  return (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: wp(16),
      paddingVertical: hp(12),
      backgroundColor: '#FFFFFF',
    }}>
      <Text style={{
        fontSize: fp(16),
        fontWeight: 'bold',
        color: '#FF6B35',
        marginRight: wp(12),
        minWidth: wp(45),
      }}>
        {Math.round(progress)}%
      </Text>
      
      <View style={{
        flex: 1,
        flexDirection: 'row',
        height: hp(6),
        marginRight: wp(12),
      }}>
        {Array.from({ length: totalSteps }, (_, index) => (
          <View
            key={index}
            style={{
              flex: 1,
              height: '100%',
              marginRight: index < totalSteps - 1 ? wp(2) : 0,
              borderRadius: wp(3),
              backgroundColor: getSegmentColor(index),
            }}
          />
        ))}
      </View>
      
      <Text style={{
        fontSize: fp(12),
        color: currentStep === totalSteps ? '#34C759' : '#FF6B35',
        fontWeight: currentStep === totalSteps ? '600' : '500',
        flex: 2,
      }}>
        {getTipText()}
      </Text>
    </View>
  );
};
