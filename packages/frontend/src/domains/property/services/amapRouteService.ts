/**
 * 🚀 第二步：高德地图路线规划API服务
 *
 * 功能：
 * 1. 驾车路线规划
 * 2. 步行路线规划
 * 3. 公共交通路线规划
 * 4. 骑行路线规划
 * 5. 打车费用估算
 */

// 高德地图Web服务API基础URL
const AMAP_API_BASE = 'https://restapi.amap.com/v3';

// 从环境变量获取API KEY (后端已配置)
const AMAP_WEB_KEY = 'd4930e00ccca3f4e9ee4968cbc148aa4';

export interface Location {
  latitude: number;
  longitude: number;
}

export interface RouteRequest {
  origin: Location; // 起点
  destination: Location; // 终点
}

export interface RouteResponse {
  distance: string; // 距离，如 "3.2公里"
  duration: string; // 时间，如 "12分钟"
  cost?: string; // 费用，如 "15元" (仅打车)
  polyline?: string; // 原始polyline数据
  coordinates?: Array<{ latitude: number; longitude: number }>; // 解码后的坐标点
  routes?: RouteAlternative[]; // 多条路线备选方案
}

export interface RouteAlternative {
  distance: string;
  duration: string;
  description: string; // 路线描述，如"躲避拥堵"、"最少红绿灯"
  cost?: string;
  coordinates: Array<{ latitude: number; longitude: number }>;
}

/**
 * 🔧 解码高德地图polyline数据
 * polyline格式: "经度,纬度;经度,纬度;..."
 */
const decodePolyline = (
  polyline: string
): Array<{ latitude: number; longitude: number }> => {
  if (!polyline) return [];

  try {
    const points: Array<{ latitude: number; longitude: number }> = [];
    const coordPairs = polyline.split(';');

    for (const pair of coordPairs) {
      if (pair && pair.includes(',')) {
        const [lngStr, latStr] = pair.split(',');
        const longitude = parseFloat(lngStr);
        const latitude = parseFloat(latStr);

        // 验证坐标是否有效
        if (
          !isNaN(longitude) &&
          !isNaN(latitude) &&
          longitude >= -180 &&
          longitude <= 180 &&
          latitude >= -90 &&
          latitude <= 90
        ) {
          points.push({ latitude, longitude });
        }
      }
    }

    console.log('🔧 [polyline解码] 成功解码坐标点:', points.length);
    return points;
  } catch (error) {
    console.error('❌ [polyline解码] 解码失败:', error);
    return [];
  }
};

/**
 * 🚗 驾车路线规划
 */
export const getDrivingRoute = async (
  request: RouteRequest
): Promise<RouteResponse> => {
  try {
    const { origin, destination } = request;
    const originStr = `${origin.longitude},${origin.latitude}`;
    const destinationStr = `${destination.longitude},${destination.latitude}`;

    const url = `${AMAP_API_BASE}/direction/driving?origin=${originStr}&destination=${destinationStr}&key=${AMAP_WEB_KEY}`;

    console.log('🚗 [高德API] 请求驾车路线:', {
      origin: originStr,
      destination: destinationStr,
    });

    const response = await fetch(url);
    const data = await response.json();

    if (data.status === '1' && data.route?.paths?.length > 0) {
      const path = data.route.paths[0];
      const distanceKm = (parseInt(path.distance) / 1000).toFixed(1);
      const durationMin = Math.round(parseInt(path.duration) / 60);

      // 🔧 正确提取并拼接所有steps的polyline
      let fullPolyline = '';
      if (path.steps && path.steps.length > 0) {
        const polylines = path.steps
          .filter((step: any) => step.polyline)
          .map((step: any) => step.polyline);
        fullPolyline = polylines.join(';');
      }

      const coordinates = decodePolyline(fullPolyline);

      return {
        distance: `${distanceKm}公里`,
        duration: `${durationMin}分钟`,
        polyline: fullPolyline,
        coordinates: coordinates,
      };
    } else {
      throw new Error(data.info || '路线规划失败');
    }
  } catch (error) {
    console.error('❌ [高德API] 驾车路线规划失败:', error);
    return {
      distance: '计算中...',
      duration: '计算中...',
    };
  }
};

/**
 * 🚶 步行路线规划
 */
export const getWalkingRoute = async (
  request: RouteRequest
): Promise<RouteResponse> => {
  try {
    const { origin, destination } = request;
    const originStr = `${origin.longitude},${origin.latitude}`;
    const destinationStr = `${destination.longitude},${destination.latitude}`;

    const url = `${AMAP_API_BASE}/direction/walking?origin=${originStr}&destination=${destinationStr}&key=${AMAP_WEB_KEY}`;

    console.log('🚶 [高德API] 请求步行路线:', {
      origin: originStr,
      destination: destinationStr,
    });

    const response = await fetch(url);
    const data = await response.json();

    if (data.status === '1' && data.route?.paths?.length > 0) {
      const path = data.route.paths[0];
      const distanceKm = (parseInt(path.distance) / 1000).toFixed(1);
      const durationMin = Math.round(parseInt(path.duration) / 60);

      // 🔧 正确提取并拼接所有steps的polyline
      let fullPolyline = '';
      if (path.steps && path.steps.length > 0) {
        const polylines = path.steps
          .filter((step: any) => step.polyline)
          .map((step: any) => step.polyline);
        fullPolyline = polylines.join(';');
      }

      const coordinates = decodePolyline(fullPolyline);

      return {
        distance: `${distanceKm}公里`,
        duration: `${durationMin}分钟`,
        polyline: fullPolyline,
        coordinates: coordinates,
      };
    } else {
      throw new Error(data.info || '步行路线规划失败');
    }
  } catch (error) {
    console.error('❌ [高德API] 步行路线规划失败:', error);
    return {
      distance: '计算中...',
      duration: '计算中...',
    };
  }
};

/**
 * 🚌 公共交通路线规划
 */
export const getTransitRoute = async (
  request: RouteRequest
): Promise<RouteResponse> => {
  try {
    const { origin, destination } = request;
    const originStr = `${origin.longitude},${origin.latitude}`;
    const destinationStr = `${destination.longitude},${destination.latitude}`;

    // 公共交通需要指定城市代码，这里使用南宁市的城市代码
    const cityCode = '0771'; // 南宁市区号

    const url = `${AMAP_API_BASE}/direction/transit/integrated?origin=${originStr}&destination=${destinationStr}&city=${cityCode}&key=${AMAP_WEB_KEY}`;

    console.log('🚌 [高德API] 请求公交路线:', {
      origin: originStr,
      destination: destinationStr,
      city: cityCode,
    });

    const response = await fetch(url);
    const data = await response.json();

    if (data.status === '1' && data.route?.transits?.length > 0) {
      const transit = data.route.transits[0];
      const distanceKm = (parseInt(transit.distance) / 1000).toFixed(1);
      const durationMin = Math.round(parseInt(transit.duration) / 60);

      // 🔧 根据官方文档提取公交路线polyline
      // 结构: transits -> segments -> walking/bus -> steps -> polyline
      let fullPolyline = '';
      if (transit.segments && transit.segments.length > 0) {
        const polylines: string[] = [];

        for (const segment of transit.segments) {
          // 提取步行段polyline
          if (segment.walking && segment.walking.steps) {
            for (const step of segment.walking.steps) {
              if (step.polyline) {
                polylines.push(step.polyline);
              }
            }
          }

          // 提取公交段polyline
          if (segment.bus && segment.bus.buslines) {
            for (const busline of segment.bus.buslines) {
              if (busline.polyline) {
                polylines.push(busline.polyline);
              }
            }
          }
        }

        fullPolyline = polylines.join(';');
        console.log('🚌 [公交路线] 提取到polyline段数:', polylines.length);
      }

      const coordinates = decodePolyline(fullPolyline);

      return {
        distance: `${distanceKm}公里`,
        duration: `${durationMin}分钟`,
        polyline: fullPolyline,
        coordinates: coordinates,
      };
    } else {
      throw new Error(data.info || '公交路线规划失败');
    }
  } catch (error) {
    console.error('❌ [高德API] 公交路线规划失败:', error);
    return {
      distance: '计算中...',
      duration: '计算中...',
    };
  }
};

/**
 * 🚴 骑行路线规划
 */
export const getCyclingRoute = async (
  request: RouteRequest
): Promise<RouteResponse> => {
  try {
    const { origin, destination } = request;
    const originStr = `${origin.longitude},${origin.latitude}`;
    const destinationStr = `${destination.longitude},${destination.latitude}`;

    const url = `${AMAP_API_BASE}/direction/bicycling?origin=${originStr}&destination=${destinationStr}&key=${AMAP_WEB_KEY}`;

    console.log('🚴 [高德API] 请求骑行路线:', {
      origin: originStr,
      destination: destinationStr,
    });

    const response = await fetch(url);
    const data = await response.json();

    if (data.status === '1' && data.route?.paths?.length > 0) {
      const path = data.route.paths[0];
      const distanceKm = (parseInt(path.distance) / 1000).toFixed(1);
      const durationMin = Math.round(parseInt(path.duration) / 60);

      // 🔧 正确提取并拼接所有steps的polyline
      let fullPolyline = '';
      if (path.steps && path.steps.length > 0) {
        const polylines = path.steps
          .filter((step: any) => step.polyline)
          .map((step: any) => step.polyline);
        fullPolyline = polylines.join(';');
      }

      const coordinates = decodePolyline(fullPolyline);

      return {
        distance: `${distanceKm}公里`,
        duration: `${durationMin}分钟`,
        polyline: fullPolyline,
        coordinates: coordinates,
      };
    } else {
      // 🔧 处理SERVICE_NOT_AVAILABLE等特定错误
      if (data.info && data.info.includes('SERVICE_NOT_AVAILABLE')) {
        console.log(
          '🚴 [骑行路线] 当前区域不支持骑行路线规划，使用步行路线作为替代'
        );
        // 使用步行路线作为骑行的替代方案
        const walkingRoute = await getWalkingRoute(request);
        return {
          ...walkingRoute,
          duration: walkingRoute.duration.replace('分钟', '分钟 (步行替代)'),
        };
      }
      throw new Error(data.info || '骑行路线规划失败');
    }
  } catch (error) {
    console.error('❌ [高德API] 骑行路线规划失败:', error);

    // 如果是SERVICE_NOT_AVAILABLE错误，尝试步行路线替代
    if (
      error instanceof Error &&
      error.message.includes('SERVICE_NOT_AVAILABLE')
    ) {
      console.log('🚴 [骑行路线] 使用步行路线作为骑行替代方案');
      try {
        const walkingRoute = await getWalkingRoute(request);
        return {
          ...walkingRoute,
          duration: walkingRoute.duration.replace('分钟', '分钟 (步行替代)'),
        };
      } catch (walkingError) {
        console.error('❌ [骑行路线] 步行替代方案也失败:', walkingError);
      }
    }

    return {
      distance: '暂不支持',
      duration: '当前区域不支持',
    };
  }
};

/**
 * 🚕 打车费用估算 (简单估算)
 */
export const getTaxiCostEstimate = async (
  request: RouteRequest
): Promise<RouteResponse> => {
  try {
    // 先获取驾车路线来计算距离
    const drivingRoute = await getDrivingRoute(request);
    const distanceNum = parseFloat(drivingRoute.distance.replace('公里', ''));

    // 简单的打车费用估算 (基于南宁市打车费用)
    // 起步价: 8元 (3公里内)
    // 超出部分: 2.2元/公里
    let cost = 8; // 起步价
    if (distanceNum > 3) {
      cost += (distanceNum - 3) * 2.2;
    }

    // 🔧 修复：确保coordinates正确传递
    return {
      distance: drivingRoute.distance,
      duration: drivingRoute.duration,
      cost: `约${Math.round(cost)}元`,
      polyline: drivingRoute.polyline,
      coordinates: drivingRoute.coordinates, // 确保坐标数据传递
    };
  } catch (error) {
    console.error('❌ [高德API] 打车费用估算失败:', error);
    return {
      distance: '计算中...',
      duration: '计算中...',
      cost: '计算中...',
    };
  }
};

/**
 * 🔧 统一路线规划接口
 */
export const getRouteByMode = async (
  mode: 'driving' | 'taxi' | 'transit' | 'walking' | 'cycling',
  request: RouteRequest
): Promise<RouteResponse> => {
  switch (mode) {
    case 'driving':
      return getDrivingRoute(request);
    case 'taxi':
      return getTaxiCostEstimate(request);
    case 'transit':
      return getTransitRoute(request);
    case 'walking':
      return getWalkingRoute(request);
    case 'cycling':
      return getCyclingRoute(request);
    default:
      throw new Error(`不支持的路线模式: ${mode}`);
  }
};
