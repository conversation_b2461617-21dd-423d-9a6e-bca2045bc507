const { createRunOncePlugin, withAndroidManifest, withInfoPlist, withAppDelegate } = require('@expo/config-plugins');

const withAMapConfig = (config, { apiKey }) => {
  // Android配置
  config = withAndroidManifest(config, config => {
    const application = config.modResults.manifest.application[0];
    if (!application['meta-data']) {
      application['meta-data'] = [];
    }

    const existingKey = application['meta-data'].find(
      item => item.$['android:name'] === 'com.amap.api.v2.apikey'
    );

    if (!existingKey) {
      application['meta-data'].push({
        $: {
          'android:name': 'com.amap.api.v2.apikey',
          'android:value': apiKey
        }
      });
    }

    return config;
  });

  // iOS配置
  config = withAppDelegate(config, config => {
    const { contents } = config.modResults;

    // 添加导入
    if (!contents.includes('#import <AMapFoundationKit/AMapFoundationKit.h>')) {
      const importIndex = contents.indexOf('#import "AppDelegate.h"');
      config.modResults.contents = contents.replace(
        '#import "AppDelegate.h"',
        '#import "AppDelegate.h"\n#import <AMapFoundationKit/AMapFoundationKit.h>'
      );
    }

    // 添加初始化代码
    if (!contents.includes('[AMapServices sharedServices].apiKey')) {
      const initIndex = contents.indexOf('- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions\n{');
      config.modResults.contents = contents.replace(
        'self.initialProps = @{};',
        `self.initialProps = @{};\n  \n  // 初始化高德地图\n  [AMapServices sharedServices].apiKey = @"${apiKey}";`
      );
    }

    return config;
  });

  return config;
};

module.exports = createRunOncePlugin(withAMapConfig, 'withAMapConfig', '1.0.0');
