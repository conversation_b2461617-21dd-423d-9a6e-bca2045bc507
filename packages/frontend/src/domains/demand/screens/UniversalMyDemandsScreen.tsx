/**
 * 🏗️ 企业级架构：通用需求列表页面
 * 使用UniversalTabListContainer实现统一的列表架构
 * 遵循前端企业级架构规范 - 完整五层架构
 */

import React, { useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// 🏗️ 企业级架构：通用组件层
import { UniversalTabListContainer } from '../../../shared/components/UniversalTabList/UniversalTabListContainer';
import type { TabConfig, SearchConfig } from '../../../shared/components/UniversalTabList/types';

// 🔧 Hook层：业务逻辑
import { useDemandData } from '../hooks/useDemandData';

// 🎨 UI层：组件
import { DemandListItem } from '../components/DemandListItem';
import { DemandEmptyState } from '../components/DemandEmptyState';

// 🌐 API层：服务
import { DemandAPI } from '../services/demandAPI';

// 📊 类型定义
import type { DemandResponse } from '../services/demandService';
import { safeNavigate } from '../../../shared/types/navigation.types';

// 响应式工具
import { wp, hp, fp } from '../../../shared/utils/responsiveUtils';

interface UniversalMyDemandsScreenProps {}

const UniversalMyDemandsScreen: React.FC<UniversalMyDemandsScreenProps> = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  // 🔧 Hook层：需求数据管理
  const { statistics } = useDemandData();

  // 🏗️ 企业级架构：Tab配置
  const tabs: TabConfig[] = [
    {
      key: 'active',
      title: '已上架',
      count: statistics?.status_breakdown?.ACTIVE || 0,
    },
    {
      key: 'draft',
      title: '草稿',
      count: statistics?.status_breakdown?.DRAFT || 0,
    },
    {
      key: 'inactive',
      title: '已下架',
      count: statistics?.status_breakdown?.OFFLINE || 0,
    },
  ];

  // 🔍 搜索配置
  const searchConfig: SearchConfig = {
    placeholder: '搜索需求...',
    searchFields: ['property_type', 'target_regions', 'description'],
    filterFn: (item: DemandResponse, searchText: string) => {
      const searchLower = searchText.toLowerCase();
      return (
        item.property_type?.toLowerCase().includes(searchLower) ||
        item.target_regions?.some(region => 
          region.toLowerCase().includes(searchLower)
        ) ||
        item.description?.toLowerCase().includes(searchLower) ||
        false
      );
    },
  };

  // 🌐 API层：数据获取器
  const dataFetcher = useCallback(async (status: string) => {
    try {
      // 将Tab状态映射到API状态
      const statusMap: Record<string, string> = {
        'active': 'ACTIVE',
        'draft': 'DRAFT',
        'inactive': 'OFFLINE',
        'published': 'ACTIVE', // 兼容性映射
      };

      const apiStatus = statusMap[status] || status.toUpperCase();
      
      const response = await DemandAPI.getMyDemands({
        status: apiStatus,
        size: 50,
        page: 1
      });

      if (response.success && response.data) {
        return response.data.items || [];
      } else {
        console.warn('获取需求列表失败:', response.message);
        return [];
      }
    } catch (error) {
      console.error('数据获取失败:', error);
      return [];
    }
  }, []);

  // 🔧 事件处理：搜索
  const handleSearch = useCallback((searchText: string, activeTab: string) => {
    console.log('搜索需求:', { searchText, activeTab });
    // 搜索逻辑由UniversalTabListContainer内部处理
  }, []);

  // 🔧 事件处理：项目点击
  const handleItemPress = useCallback((item: DemandResponse) => {
    // 导航到需求详情页面
    safeNavigate(navigation, 'DemandInfo', { demandId: item.id });
  }, [navigation]);

  // 🔧 事件处理：编辑需求
  const handleEditDemand = useCallback((item: DemandResponse) => {
    // 导航到需求表单页面，传递需求类型和编辑数据
    safeNavigate(navigation, 'DemandForm', {
      demandType: item.demand_type, // 'RENTAL' 或 'PURCHASE'
      editMode: true,
      demandId: item.id,
      demandData: item, // 传递需求数据用于表单回填
    });
  }, [navigation]);

  // 🔧 事件处理：删除需求
  const handleDeleteDemand = useCallback((item: DemandResponse) => {
    console.log('[MyDemandsScreen] 🗑️ 删除需求回调:', item.id);
    // 删除成功后，需要刷新当前Tab的数据
    // 这里不需要做额外处理，刷新逻辑由onRefresh处理
  }, []);

  // 🔧 事件处理：状态变更
  const handleStatusChange = useCallback((item: DemandResponse, newStatus: any) => {
    console.log('需求状态已变更:', item.id, newStatus);
    // 状态变更逻辑在DemandListItem中处理
  }, []);

  // 🔧 事件处理：刷新
  const handleRefresh = useCallback(async (tab: string) => {
    console.log('[MyDemandsScreen] 🔄 刷新需求列表:', tab);
    // 刷新逻辑由UniversalTabListContainer内部处理
    // 这里可以添加额外的刷新逻辑，比如清除缓存
  }, []);

  // 🔧 事件处理：Tab切换
  const handleTabChange = useCallback((tab: string) => {
    console.log('切换Tab:', tab);
  }, []);

  // 🎨 UI层：渲染需求项
  const renderDemandItem = useCallback((item: DemandResponse) => {
    return (
      <DemandListItem
        item={item}
        onPress={() => handleItemPress(item)}
        onEdit={handleEditDemand}
        onDelete={handleDeleteDemand}
        onStatusChange={handleStatusChange}
        onRefresh={() => {
          // 触发列表刷新
          console.log('[DemandListItem] 🔄 触发列表刷新');
          handleRefresh('current'); // 刷新当前Tab
        }}
      />
    );
  }, [navigation, handleItemPress, handleEditDemand, handleDeleteDemand, handleStatusChange, handleRefresh]);

  // 🎨 UI层：Key提取器
  const keyExtractor = useCallback((item: DemandResponse) => item.id, []);

  // 🎨 UI层：头部组件
  const HeaderComponent = useCallback(() => (
    <View style={[styles.header, { paddingTop: insets.top }]}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <View style={styles.headerContent}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>我的需求</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => safeNavigate(navigation, 'DemandForm')}
        >
          <Ionicons name="add" size={24} color="#FF6B35" />
        </TouchableOpacity>
      </View>
    </View>
  ), [navigation, insets.top]);

  // 🎨 UI层：空状态组件
  const EmptyComponent = useCallback(({ status }: { status: string }) => (
    <DemandEmptyState
      status={status}
      onCreatePress={() => safeNavigate(navigation, 'DemandForm')}
    />
  ), [navigation]);

  return (
    <View style={styles.container}>
      <UniversalTabListContainer
        type="demand"
        tabs={tabs}
        searchConfig={searchConfig}
        onSearch={handleSearch}
        dataFetcher={dataFetcher}
        renderItem={renderDemandItem}
        keyExtractor={keyExtractor}
        headerComponent={HeaderComponent}
        emptyComponent={EmptyComponent}
        navigation={navigation}
        initialTab="active"
        onTabChange={handleTabChange}
        onItemPress={handleItemPress}
        onRefresh={handleRefresh}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
  },
  backButton: {
    padding: wp(8),
  },
  headerTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#333333',
  },
  addButton: {
    padding: wp(8),
  },
});

export default UniversalMyDemandsScreen;
