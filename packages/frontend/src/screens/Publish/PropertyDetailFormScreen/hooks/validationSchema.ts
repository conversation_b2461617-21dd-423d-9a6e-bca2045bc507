import { z } from 'zod';

// Zod验证Schema
export const propertyFormSchema = z.object({
  title: z.string().min(5, '标题至少需要5个字符').max(50, '标题不能超过50个字符'),
  property_certificate_address: z.string().min(1, '地址不能为空'),
  sub_type: z.string().min(1, '请选择户型'),
  area: z.any().refine(val => {
    if (typeof val === 'string') return /^\d+(\.\d{1,2})?$/.test(val);
    if (typeof val === 'object' && val.min && val.max) return val.min > 0 && val.max > val.min;
    return false;
  }, '请提供有效的面积'),
  floor: z.string().min(1, '请填写楼层'),
  total_floors: z.string().optional(),
  orientation: z.string().min(1, '请选择朝向'),
  decoration_level: z.string().min(1, '请选择装修情况'),
  transaction_types: z.array(z.string()).min(1, '请至少选择一种交易类型'),
  rent_price: z.string().optional(),
  sale_price: z.string().optional(),
  transfer_price: z.string().optional(),
  rent_deposit_months: z.string().optional(),
  rent_payment_method: z.string().optional(),
  property_fee: z.string().optional(),
  description: z.string().min(20, '房源描述至少需要20个字符').max(2000, '描述不能超过2000个字符'),
  industry_type: z.string().optional(),
  transfer_fee: z.string().optional(),
  
  
  // 商铺专用字段
  frontage_width: z.string().optional(),
  depth: z.string().optional(),
  can_open_fire: z.boolean().optional(),
  has_chimney: z.boolean().optional(),
  has_outdoor_area: z.boolean().optional(),
  suitable_business_types: z.string().optional(),
  has_private_toilet: z.boolean().optional(),
  has_water_drainage: z.boolean().optional(),
  space_efficiency: z.string().optional(),
  has_independent_ac: z.boolean().optional(),
  has_reception_service: z.boolean().optional(),
  
  // 写字楼专用字段
  office_grade: z.string().optional(),
  has_elevator: z.boolean().optional(),
  elevator_count: z.string().optional(),
  has_central_ac: z.boolean().optional(),
  has_parking: z.boolean().optional(),
  parking_spaces: z.string().optional(),
  has_meeting_room: z.boolean().optional(),
  has_security: z.boolean().optional(),

  // 厂房专用字段
  floor_load: z.string().optional(),
  power_capacity: z.string().optional(),
  column_spacing: z.string().optional(),
  fire_protection_grade: z.string().optional(),
  has_freight_elevator: z.boolean().optional(),
  has_environmental_assessment: z.boolean().optional(),
  has_loading_dock: z.boolean().optional(),

  // 会议室专用字段
  max_capacity: z.string().optional(),
  has_projector: z.boolean().optional(),
  has_sound_system: z.boolean().optional(),
  has_wifi: z.boolean().optional(),
  provides_catering: z.boolean().optional(),
  has_stage: z.boolean().optional(),
  is_24h_available: z.boolean().optional(),
}).refine(data => {
  if (data.transaction_types.includes('RENT') && !data.rent_price) return false;
  if (data.transaction_types.includes('SALE') && !data.sale_price) return false;
  if (data.transaction_types.includes('TRANSFER') && !data.transfer_price) return false;
  return true;
}, {
  message: "请填写所选交易类型对应的价格",
  path: ["rent_price"],
});

// 房源表单数据类型 - 使用Zod推断类型
export type PropertyFormData = z.infer<typeof propertyFormSchema>;

// 默认表单值
export const defaultFormValues: PropertyFormData = {
  title: '',
  property_certificate_address: '',
  sub_type: '',
  area: '',
  floor: '',
  total_floors: '',
  orientation: '',
  decoration_level: '',
  transaction_types: [],
  rent_price: '',
  sale_price: '',
  transfer_price: '',
  rent_deposit_months: '',
  rent_payment_method: 'QUARTERLY',
  property_fee: '',
  description: '',
  industry_type: '',
  transfer_fee: '',
  frontage_width: '',
  depth: '',
  can_open_fire: false,
  has_chimney: false,
  has_outdoor_area: false,
  suitable_business_types: '',
  has_private_toilet: false,
  has_water_drainage: false,
  space_efficiency: '',
  has_independent_ac: true,
  has_reception_service: true,
  office_grade: '',
  has_elevator: true,
  elevator_count: '',
  has_central_ac: true,
  has_parking: false,
  parking_spaces: '',
  has_meeting_room: false,
  has_security: false,
  floor_load: '',
  power_capacity: '',
  column_spacing: '',
  fire_protection_grade: '',
  has_freight_elevator: false,
  has_environmental_assessment: false,
  has_loading_dock: false,
  max_capacity: '',
  has_projector: false,
  has_sound_system: false,
  has_wifi: true,
  provides_catering: false,
  has_stage: false,
  is_24h_available: false,
};
