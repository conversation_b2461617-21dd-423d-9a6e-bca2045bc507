/**
 * 企业级表单组件入口文件
 * 
 * 统一导出所有表单相关组件和Hook
 */

// 组件
export { 
  default as EnterpriseFormInput,
  type EnterpriseFormInputProps,
  type EnterpriseFormInputRef
} from './EnterpriseFormInput';

export {
  default as EnterpriseFormContainer,
  FormContext,
  useFormContext,
  type EnterpriseFormContainerProps,
  type EnterpriseFormContainerRef
} from './EnterpriseFormContainer';

// Hook
export {
  default as useEnterpriseFormValidation,
  type FieldValidationRule,
  type EnterpriseFormConfig,
  type UseEnterpriseFormValidationReturn
} from '../../hooks/useEnterpriseFormValidation';

export {
  useKeyboardAwareForm,
  useKeyboardAwareField,
  type UseKeyboardAwareFormOptions,
  type UseKeyboardAwareFormReturn,
  type FormFieldError
} from '../../hooks/useKeyboardAwareForm';

export {
  useFormValidation,
  type FormValidationConfig,
  type UseFormValidationReturn,
  type ValidationResult,
  type FieldValidationRule as FormFieldValidationRule,
  type ProgressStep
} from '../../hooks/useFormValidation';

// 样式系统
export {
  CommonFormStyles,
  FormColors,
  FormFonts,
  FormSpacing,
  FormDimensions,
} from './FormStyles';