/**
 * 用户中心Zustand状态管理Store
 * 符合企业级架构规范，集成devtools、persist、subscribeWithSelector中间件
 * 集成统一转换层，完整保留原始功能，目标：150行
 */
import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { getProfileTransformers } from '../transformers/ProfileTransformerFactory';
import type {
  UserInfo,
  TenantBuyerStats,
  LandlordStats,
  OrderCounts,
} from '../types/profile.types';

// 使用DTO层的类型定义，移除临时类型

/**
 * 用户中心状态接口
 */
interface ProfileState {
  // 基础状态
  isLoggedIn: boolean;
  currentUser: UserInfo | null;
  
  // 数据状态
  userInfo: UserInfo | null;
  tenantBuyerStats: TenantBuyerStats | null;
  landlordStats: LandlordStats | null;
  orderCounts: OrderCounts | null;
  
  // 加载状态
  isLoadingUserInfo: boolean;
  isLoadingTenantStats: boolean;
  isLoadingLandlordStats: boolean;
  isLoadingOrderCounts: boolean;
  
  // 错误状态
  userInfoError: string | null;
  tenantStatsError: string | null;
  landlordStatsError: string | null;
  orderCountsError: string | null;
  
  // UI状态
  showLoginModal: boolean;
  showLogoutModal: boolean;
  refreshing: boolean;
  
  // 计算属性缓存
  isLoading: boolean;
  hasError: boolean;
  hasData: boolean;
}

/**
 * 用户中心操作接口
 */
interface ProfileActions {
  // 基础操作
  setIsLoggedIn: (isLoggedIn: boolean) => void;
  setCurrentUser: (user: UserInfo | null) => void;
  
  // 数据操作
  setUserInfo: (userInfo: UserInfo | null) => void;
  setTenantBuyerStats: (stats: TenantBuyerStats | null) => void;
  setLandlordStats: (stats: LandlordStats | null) => void;
  setOrderCounts: (counts: OrderCounts | null) => void;
  
  // 加载状态操作
  setLoadingUserInfo: (loading: boolean) => void;
  setLoadingTenantStats: (loading: boolean) => void;
  setLoadingLandlordStats: (loading: boolean) => void;
  setLoadingOrderCounts: (loading: boolean) => void;
  
  // 错误状态操作
  setUserInfoError: (error: string | null) => void;
  setTenantStatsError: (error: string | null) => void;
  setLandlordStatsError: (error: string | null) => void;
  setOrderCountsError: (error: string | null) => void;
  
  // UI状态操作
  setShowLoginModal: (show: boolean) => void;
  setShowLogoutModal: (show: boolean) => void;
  setRefreshing: (refreshing: boolean) => void;
  
  // 重置操作
  resetErrors: () => void;
  resetData: () => void;
  resetAll: () => void;

  // 转换层集成操作
  setUserInfoFromAPI: (apiData: any) => Promise<void>;
  setTenantBuyerStatsFromAPI: (apiData: any) => Promise<void>;
  setLandlordStatsFromAPI: (apiData: any) => Promise<void>;
  setOrderCountsFromAPI: (apiData: any) => Promise<void>;

  // 企业级修复：移除updateComputedProperties，避免无限循环
}

/**
 * 初始状态
 */
const initialState: ProfileState = {
  // 基础状态
  isLoggedIn: false,
  currentUser: null,
  
  // 数据状态
  userInfo: null,
  tenantBuyerStats: null,
  landlordStats: null,
  orderCounts: null,
  
  // 加载状态
  isLoadingUserInfo: false,
  isLoadingTenantStats: false,
  isLoadingLandlordStats: false,
  isLoadingOrderCounts: false,
  
  // 错误状态
  userInfoError: null,
  tenantStatsError: null,
  landlordStatsError: null,
  orderCountsError: null,
  
  // UI状态
  showLoginModal: false,
  showLogoutModal: false,
  refreshing: false,
  
  // 计算属性缓存
  isLoading: false,
  hasError: false,
  hasData: false,
};

/**
 * 用户中心Store类型
 */
export type ProfileStore = ProfileState & ProfileActions;

/**
 * 创建用户中心Store
 */
export const useProfileStore = create<ProfileStore>()(
  devtools(
    persist(
      subscribeWithSelector((set, get) => ({
        ...initialState,

        // 基础操作
        // 企业级修复：移除updateComputedProperties调用，避免无限循环（按专家建议）
        setIsLoggedIn: (isLoggedIn) => {
          console.log(`[ProfileStore] 设置登录状态: ${isLoggedIn}`);
          set({ isLoggedIn });
        },

        setCurrentUser: (user) => {
          console.log('[ProfileStore] 设置当前用户:', user?.id);
          set({ currentUser: user, isLoggedIn: !!user });
        },

        // 数据操作
        setUserInfo: (userInfo) => {
          set({ userInfo });
        },

        setTenantBuyerStats: (stats) => {
          set({ tenantBuyerStats: stats });
        },

        setLandlordStats: (stats) => {
          set({ landlordStats: stats });
        },

        setOrderCounts: (counts) => {
          set({ orderCounts: counts });
        },

        // 加载状态操作
        setLoadingUserInfo: (loading) => {
          set({ isLoadingUserInfo: loading });
        },

        setLoadingTenantStats: (loading) => {
          set({ isLoadingTenantStats: loading });
        },

        setLoadingLandlordStats: (loading) => {
          set({ isLoadingLandlordStats: loading });
        },

        setLoadingOrderCounts: (loading) => {
          set({ isLoadingOrderCounts: loading });
        },

        // 错误状态操作
        setUserInfoError: (error) => {
          set({ userInfoError: error });
        },

        setTenantStatsError: (error) => {
          set({ tenantStatsError: error });
        },

        setLandlordStatsError: (error) => {
          set({ landlordStatsError: error });
        },

        setOrderCountsError: (error) => {
          set({ orderCountsError: error });
        },

        // UI状态操作
        setShowLoginModal: (show) => {
          console.log(`[ProfileStore] ${show ? '显示' : '隐藏'}登录弹窗`);
          set({ showLoginModal: show });
        },

        setShowLogoutModal: (show) => {
          console.log(`[ProfileStore] ${show ? '显示' : '隐藏'}退出弹窗`);
          set({ showLogoutModal: show });
        },

        setRefreshing: (refreshing) => {
          set({ refreshing });
        },

        // 重置操作
        resetErrors: () => {
          console.log('[ProfileStore] 重置错误状态');
          set({
            userInfoError: null,
            tenantStatsError: null,
            landlordStatsError: null,
            orderCountsError: null,
          });
        },

        resetData: () => {
          console.log('[ProfileStore] 重置数据');
          set({
            userInfo: null,
            tenantBuyerStats: null,
            landlordStats: null,
            orderCounts: null,
          });
        },

        resetAll: () => {
          console.log('[ProfileStore] 重置所有状态');
          set(initialState);
        },

        // 转换层集成操作
        setUserInfoFromAPI: async (apiData) => {
          try {
            console.log('[ProfileStore] 转换用户信息数据');
            const transformers = getProfileTransformers();
            const result = transformers.userInfo.fromAPI(apiData);

            if (result.success && result.data) {
              set({ userInfo: result.data });
            } else {
              throw new Error(result.error || '用户信息转换失败');
            }
          } catch (error) {
            console.error('[ProfileStore] 用户信息转换错误:', error);
            set({ userInfoError: error instanceof Error ? error.message : '转换失败' });
          }
        },

        setTenantBuyerStatsFromAPI: async (apiData) => {
          try {
            console.log('[ProfileStore] 转换租买统计数据');
            const transformers = getProfileTransformers();
            const result = transformers.tenantBuyerStats.fromAPI(apiData);

            if (result.success && result.data) {
              set({ tenantBuyerStats: result.data });
            } else {
              throw new Error(result.error || '租买统计转换失败');
            }
          } catch (error) {
            console.error('[ProfileStore] 租买统计转换错误:', error);
            set({ tenantStatsError: error instanceof Error ? error.message : '转换失败' });
          }
        },

        setLandlordStatsFromAPI: async (apiData) => {
          try {
            console.log('[ProfileStore] 转换业主统计数据');
            const transformers = getProfileTransformers();
            const result = transformers.landlordStats.fromAPI(apiData);

            if (result.success && result.data) {
              set({ landlordStats: result.data });
            } else {
              throw new Error(result.error || '业主统计转换失败');
            }
          } catch (error) {
            console.error('[ProfileStore] 业主统计转换错误:', error);
            set({ landlordStatsError: error instanceof Error ? error.message : '转换失败' });
          }
        },

        setOrderCountsFromAPI: async (apiData) => {
          try {
            console.log('[ProfileStore] 转换订单统计数据');
            const transformers = getProfileTransformers();
            const result = transformers.orderCounts.fromAPI(apiData);

            if (result.success && result.data) {
              set({ orderCounts: result.data });
            } else {
              throw new Error(result.error || '订单统计转换失败');
            }
          } catch (error) {
            console.error('[ProfileStore] 订单统计转换错误:', error);
            set({ orderCountsError: error instanceof Error ? error.message : '转换失败' });
          }
        },

        // 企业级修复：移除updateComputedProperties方法，避免无限循环
        // 计算属性现在通过选择器实时计算，不需要存储在状态中
      })),
      {
        name: 'profile-store', // 持久化存储键名
        partialize: (state) => ({
          // 只持久化必要的状态，不持久化加载状态和错误状态
          isLoggedIn: state.isLoggedIn,
          currentUser: state.currentUser,
        }),
      }
    ),
    {
      name: 'ProfileStore', // DevTools中显示的名称
    }
  )
);
