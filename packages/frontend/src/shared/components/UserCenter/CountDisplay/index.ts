/**
 * 通用计数显示组件导出
 * 企业级架构 - 统一导出管理
 */

export {
  CountDisplay,
  type CountDisplayProps,
  type CountDisplayVariant,
  type CountDisplaySize,
  type CountDisplayColor,
} from './CountDisplay';
export { default } from './CountDisplay';

// 预设组件导出
export {
  TabCount,
  BadgeCount,
  StatCount,
  LargeStat,
  InlineCount,
} from './presets';
export type {
  TabCountProps,
  BadgeCountProps,
  StatCountProps,
  LargeStatProps,
  InlineCountProps,
} from './presets';
