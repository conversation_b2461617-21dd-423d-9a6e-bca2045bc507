/**
 * 统一的表单样式常量
 * 
 * 避免在各个组件中重复定义相同的样式
 */

import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../utils/responsiveUtils';

export const FormColors = {
  primary: '#007AFF',
  error: '#FF3B30',
  warning: '#FF9500', 
  success: '#34C759',
  text: '#000000',
  textSecondary: '#8E8E93',
  textDisabled: '#C7C7CC',
  background: '#F8F9FA',
  backgroundSecondary: '#F2F2F7',
  border: '#E5E5E5',
  borderError: '#FF3B30',
  borderFocus: '#007AFF',
  required: '#FF6B35',
} as const;

export const FormFonts = {
  title: {
    fontSize: fp(18),
    fontWeight: '600' as const,
    color: FormColors.text,
    lineHeight: fp(24),
  },
  subtitle: {
    fontSize: fp(12),
    color: FormColors.textSecondary,
    lineHeight: fp(18),
  },
  label: {
    fontSize: fp(14),
    fontWeight: '500' as const,
    color: FormColors.text,
    lineHeight: fp(20),
  },
  input: {
    fontSize: fp(16),
    color: FormColors.text,
  },
  error: {
    fontSize: fp(12),
    color: FormColors.error,
    fontWeight: '500' as const,
    lineHeight: fp(16),
  },
  hint: {
    fontSize: fp(11),
    color: FormColors.textSecondary,
    fontStyle: 'italic' as const,
    lineHeight: fp(15),
  },
  warning: {
    fontSize: fp(12),
    color: FormColors.warning,
    fontWeight: '400' as const,
    lineHeight: fp(16),
  },
} as const;

export const FormSpacing = {
  sectionMargin: hp(16),
  fieldMargin: hp(16),
  inputPadding: wp(16),
  inputVerticalPadding: hp(12),
  errorMargin: hp(4),
  hintMargin: hp(4),
} as const;

export const FormDimensions = {
  inputMinHeight: hp(48),
  multilineInputMinHeight: hp(80),
  borderRadius: wp(12),
  borderWidth: 1,
  chipBorderRadius: wp(20),
} as const;

// 通用表单样式
export const CommonFormStyles = StyleSheet.create({
  // 容器样式
  container: {
    backgroundColor: FormColors.backgroundSecondary,
  },
  
  formSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: FormDimensions.borderRadius,
    padding: FormSpacing.inputPadding,
    marginBottom: FormSpacing.sectionMargin,
    
    // 阴影效果
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  
  // 标题样式
  sectionTitle: FormFonts.title,
  sectionSubtitle: {
    ...FormFonts.subtitle,
    marginBottom: FormSpacing.fieldMargin,
  },
  
  // 输入组样式
  inputGroup: {
    marginBottom: FormSpacing.fieldMargin,
  },
  
  label: FormFonts.label,
  
  required: {
    color: FormColors.required,
    fontWeight: '600',
  },
  
  // 输入框样式
  inputContainer: {
    backgroundColor: FormColors.background,
    borderRadius: FormDimensions.borderRadius,
    borderWidth: FormDimensions.borderWidth,
    borderColor: FormColors.border,
    paddingHorizontal: FormSpacing.inputPadding,
    paddingVertical: FormSpacing.inputVerticalPadding,
    minHeight: FormDimensions.inputMinHeight,
  },
  
  inputContainerError: {
    borderColor: FormColors.borderError,
    borderWidth: 1.5,
    backgroundColor: '#FFF5F5',
  },
  
  inputContainerWarning: {
    borderColor: FormColors.warning,
    backgroundColor: '#FFFBF5',
  },
  
  textInput: {
    ...FormFonts.input,
    padding: 0,
    margin: 0,
    minHeight: hp(24),
  },
  
  multilineInput: {
    minHeight: FormDimensions.multilineInputMinHeight,
    textAlignVertical: 'top',
    paddingTop: hp(8),
  },
  
  // 消息样式
  messageContainer: {
    marginTop: FormSpacing.errorMargin,
    paddingHorizontal: wp(4),
  },
  
  errorText: {
    ...FormFonts.error,
    marginTop: FormSpacing.errorMargin,
  },
  
  warningText: FormFonts.warning,
  
  hintText: {
    ...FormFonts.hint,
    marginTop: FormSpacing.hintMargin,
  },
  
  // 选择器样式
  chip: {
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    borderRadius: wp(16),
    backgroundColor: FormColors.background,
    borderWidth: FormDimensions.borderWidth,
    borderColor: FormColors.border,
  },
  
  chipSelected: {
    backgroundColor: FormColors.primary,
    borderColor: FormColors.primary,
  },
  
  chipText: {
    fontSize: fp(14),
    color: FormColors.primary,
    fontWeight: '500',
  },
  
  chipTextSelected: {
    color: '#FFFFFF',
  },
  
  // 按钮样式
  paymentMethodContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: wp(12),
    marginTop: hp(8),
  },
  
  paymentMethodChip: {
    paddingHorizontal: wp(16),
    paddingVertical: hp(8),
    borderRadius: FormDimensions.chipBorderRadius,
    borderWidth: FormDimensions.borderWidth,
    borderColor: FormColors.border,
    backgroundColor: '#FFFFFF',
  },
  
  paymentMethodChipSelected: {
    backgroundColor: FormColors.primary,
    borderColor: FormColors.primary,
  },
  
  paymentMethodText: {
    fontSize: fp(14),
    color: FormColors.textSecondary,
    fontWeight: '500',
  },
  
  paymentMethodTextSelected: {
    color: '#FFFFFF',
  },
});

export default CommonFormStyles;