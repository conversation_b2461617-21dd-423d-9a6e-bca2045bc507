"""
需求搜索服务
处理需求搜索相关的业务逻辑
"""

import uuid
from typing import List, Optional, Dict, Any
from sqlmodel.ext.asyncio.session import AsyncSession
from sqlmodel import select, and_, or_, desc, asc, func
from app.models.user_demand import UserDemand
from app.repositories.demand_repository import DemandRepository
import logging

logger = logging.getLogger(__name__)


class DemandSearchService:
    """需求搜索服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.repository = DemandRepository(db)
    
    async def search_demands(
        self,
        keyword: Optional[str] = None,
        demand_type: Optional[str] = None,
        property_type: Optional[str] = None,
        location: Optional[str] = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[UserDemand]:
        """
        搜索需求
        
        Args:
            keyword: 搜索关键词
            demand_type: 需求类型
            property_type: 房源类型
            location: 位置
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            需求列表
        """
        try:
            return await self.repository.search_demands(
                keyword=keyword,
                demand_type=demand_type,
                property_type=property_type,
                skip=skip,
                limit=limit
            )
        except Exception as e:
            logger.error(f"搜索需求失败: {str(e)}")
            return []
    
    async def get_recommended_demands(
        self,
        user_id: uuid.UUID,
        limit: int = 10
    ) -> List[UserDemand]:
        """
        获取推荐需求
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            
        Returns:
            推荐需求列表
        """
        try:
            # 简单实现：返回最新的活跃需求
            return await self.repository.get_active_demands(limit=limit)
        except Exception as e:
            logger.error(f"获取推荐需求失败: {str(e)}")
            return []