/**
 * 核心功能菜单区域组件
 * 功能：4个核心功能菜单展示，数字动态显示，橙色激活状态
 * 完整保留原始样式，确保UI完全一致，目标：80行
 */
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './styles';

interface MenuItem {
  id: string;
  title: string;
  useNumber: boolean;
  number: number;
  color: string;
  activeColor: string;
  onPress: () => void;
}

interface CoreMenuSectionProps {
  menuItems: MenuItem[];
  isLoggedIn: boolean;
}

export const CoreMenuSection: React.FC<CoreMenuSectionProps> = ({
  menuItems,
  isLoggedIn,
}) => {
  return (
    <View style={styles.menuSection}>
      {menuItems.map(item => (
        <TouchableOpacity
          key={item.id}
          style={styles.menuItem}
          onPress={item.onPress}
        >
          <View style={styles.iconContainer}>
            {item.useNumber ? (
              // 显示纯数字，有数量时使用激活颜色 - 完全保留原始逻辑
              <Text style={[
                styles.numberText, 
                { color: item.number > 0 ? item.activeColor : item.color }
              ]}>
                {item.number}
              </Text>
            ) : (
              // 显示图标 (备用逻辑，当前不使用)
              <Ionicons
                name={(item as any).icon}
                size={28}
                color={item.color}
              />
            )}
            {(item as any).badge && (
              <View style={styles.badge}>
                <Text style={styles.badgeText}>{(item as any).badge}</Text>
              </View>
            )}
          </View>
          <Text style={[
            styles.menuText,
            item.useNumber && item.number > 0 
              ? { color: item.activeColor }
              : { color: item.color }
          ]}>
            {item.title}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};
