/**
 * FormFooter 样式定义
 * @fileoverview 表单底部组件的样式
 * <AUTHOR> Assistant
 * @since 2025-08-01
 */

import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../../../shared/utils/responsiveUtils';

export const styles = StyleSheet.create({
  // === 主容器样式 ===
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: wp(20),
    paddingTop: hp(15),
    paddingBottom: hp(30),
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },

  // === 错误提示样式 ===
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF5F5',
    borderColor: '#FEB2B2',
    borderWidth: 1,
    borderRadius: wp(8),
    padding: wp(12),
    marginBottom: hp(15),
  },

  errorText: {
    fontSize: fp(14),
    color: '#E53E3E',
    marginLeft: wp(8),
    flex: 1,
  },

  // === 按钮容器样式 ===
  buttonContainer: {
    flexDirection: 'row',
    gap: wp(15),
  },

  // === 草稿按钮样式 ===
  draftButton: {
    flex: 1,
    paddingVertical: hp(15),
    paddingHorizontal: wp(20),
    borderRadius: wp(12),
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E9ECEF',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: hp(50),
  },

  draftButtonText: {
    fontSize: fp(16),
    fontWeight: '500',
    color: '#666666',
  },

  // === 提交按钮样式 ===
  submitButton: {
    flex: 2,
    paddingVertical: hp(15),
    paddingHorizontal: wp(20),
    borderRadius: wp(12),
    backgroundColor: '#FF6B35',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: hp(50),
    shadowColor: '#FF6B35',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },

  submitButtonText: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#FFFFFF',
  },

  // === 禁用状态样式 ===
  buttonDisabled: {
    opacity: 0.5,
    shadowOpacity: 0,
    elevation: 0,
  },

  // === 进度提示样式 ===
  progressHint: {
    marginTop: hp(10),
    alignItems: 'center',
  },

  progressHintText: {
    fontSize: fp(12),
    color: '#999999',
  },
});
