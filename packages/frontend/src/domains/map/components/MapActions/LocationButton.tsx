/**
 * 定位按钮组件
 * 职责：用户位置重新定位功能
 */

import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface LocationButtonProps {
  onPress: () => void;
  isLocating?: boolean;
}

export const LocationButton: React.FC<LocationButtonProps> = ({
  onPress,
  isLocating = false,
}) => {
  return (
    <TouchableOpacity
      style={[styles.mapActionButton, isLocating && styles.locatingButton]}
      onPress={onPress}
      disabled={isLocating}
    >
      {isLocating ? (
        <ActivityIndicator size="small" color="#FF6B35" style={styles.mapActionIcon} />
      ) : (
        <Ionicons name="locate-outline" size={20} color="#333" style={styles.mapActionIcon} />
      )}
      <Text style={[styles.mapActionText, isLocating && styles.locatingText]}>
        {isLocating ? '定位中...' : '重新定位'}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  mapActionButton: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 12,
  },
  mapActionIcon: {
    marginRight: 4,
  },
  mapActionText: {
    fontSize: 12,
    color: '#333',
    fontWeight: '500',
  },
  locatingButton: {
    backgroundColor: '#FFF5F2',
    borderColor: '#FF6B35',
    borderWidth: 1,
  },
  locatingText: {
    color: '#FF6B35',
  },
});
