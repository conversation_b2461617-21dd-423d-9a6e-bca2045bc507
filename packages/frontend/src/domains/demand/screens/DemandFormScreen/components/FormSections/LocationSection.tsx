/**
 * LocationSection 组件
 * @fileoverview 位置需求区块 - 区域选择
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🔒 功能保证：100%保留原有DemandFormScreen.tsx中的位置选择区块
 */

import React from 'react';
import { View, Text } from 'react-native';
import { Controller, useForm } from 'react-hook-form';

// 导入组件
import { DemandDropdown } from '../../../components/FormInputs/DemandDropdown';
import { SmartFormField } from '../../../../../shared/components/form/SmartFormField';

// 导入配置
import { NANNING_DISTRICTS } from '../../../constants/demandConstants';

// 导入类型
import type { DemandFormData, FormErrors } from '../../types/demandForm.types';

// 样式
import { styles } from './locationSection.styles';

interface LocationSectionProps {
  formData: DemandFormData;
  formErrors: FormErrors;
  validationErrors: Record<string, string>;
  onFieldChange: (field: string, value: any) => void;
  onNestedFieldChange: (path: string, value: any) => void;
  onValidationError: (field: string, value: any) => void;
}

/**
 * 位置需求区块组件 - 完整保留原有位置选择字段
 */
export const LocationSection: React.FC<LocationSectionProps> = React.memo(
  ({
    formData,
    formErrors,
    validationErrors,
    onFieldChange,
    onNestedFieldChange,
    onValidationError,
  }) => {
    // 🔒 保留原有的react-hook-form配置
    const { control } = useForm({
      defaultValues: formData,
      mode: 'onChange',
    });

    // 🔒 保留原有的字段注册逻辑
    const registerField = (fieldName: string, ref: any) => {
      console.log('📝 [位置需求] 注册字段:', fieldName);
    };

    return (
      <View style={styles.container}>
        {/* 🔒 保留原有的区域选择 */}
        <SmartFormField
          fieldName="location.districts"
          label=""
          required
          error={validationErrors['location.districts']}
          onRegisterField={registerField}
        >
          <Controller
            name="location.districts"
            control={control}
            render={({ field: { onChange, value } }) => (
              <DemandDropdown
                label="目标区域"
                value={Array.isArray(value) ? value : []}
                options={NANNING_DISTRICTS}
                onSelect={newValue => {
                  onChange(newValue);
                  onNestedFieldChange('location.districts', newValue);
                  onValidationError('location.districts', newValue);
                }}
                placeholder="请选择你想要的区域的房源"
                multiSelect={true}
                required
                disabled={false}
                error={validationErrors['location.districts']}
                hideErrorText={true}
                onReset={() => {
                  onChange([]);
                  onNestedFieldChange('location.districts', []);
                }}
              />
            )}
          />
        </SmartFormField>
      </View>
    );
  }
);

LocationSection.displayName = 'LocationSection';
