/**
 * 🏗️ 企业级架构：房源列表组件演示页面
 * 展示通用房源组件的使用方式和配置选项
 * 可以作为参考来集成到现有页面中
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';

// 🏗️ 企业级架构：通用组件导入
import { PropertyListItem } from '../components/PropertyListItem';
import { EnhancedPropertyListItem } from '../components/EnhancedPropertyListItem';
import { UniversalMyPropertiesScreen } from './UniversalMyPropertiesScreen';

// 🎨 UI层：样式工具
import { spacing, fontSize } from '../../../shared/utils/responsiveUtils';

// 🔧 模拟数据
const mockProperties = [
  {
    id: '1',
    title: '五象新区精装写字楼出租',
    property_type: 'OFFICE',
    total_area: 150,
    rent_price: 10000,
    status: 'published',
    cover_image: 'https://picsum.photos/200/150?random=1',
    view_count: 320,
    favorite_count: 15,
    inquiry_count: 8,
    description: '精装修，拎包入住',
    building_name: '万科大厦',
    type: 'rental',
    price: '10000元/月',
    area: '150m²',
    location: '五象新区·万科大厦',
    imageUrl: 'https://picsum.photos/200/150?random=1',
    exposureCount: 450,
    favoriteCount: 15,
    inquiryCount: 8,
    viewCount: 320,
    tenantMatches: 8,
    isPremiumOwner: true,
    priceRange: { min: 7000, max: 13000 },
    areaRange: { min: 105, max: 195 },
    createdAt: new Date('2025-06-20'),
    updatedAt: new Date('2025-06-28'),
  },
  {
    id: '2',
    title: '青秀区临街商铺出售',
    property_type: 'SHOP',
    total_area: 68,
    sale_price: 180,
    status: 'published',
    cover_image: 'https://picsum.photos/200/150?random=2',
    view_count: 490,
    favorite_count: 22,
    inquiry_count: 12,
    description: '临街旺铺，人流量大',
    building_name: '民族大道商业街',
    type: 'sale',
    price: '180万',
    area: '68m²',
    location: '青秀区·民族大道',
    imageUrl: 'https://picsum.photos/200/150?random=2',
    exposureCount: 680,
    favoriteCount: 22,
    inquiryCount: 12,
    viewCount: 490,
    tenantMatches: 4,
    isPremiumOwner: true,
    priceRange: { min: 126, max: 234 },
    areaRange: { min: 48, max: 88 },
    createdAt: new Date('2025-06-15'),
    updatedAt: new Date('2025-06-29'),
  },
  {
    id: '3',
    title: '草稿房源',
    property_type: 'OFFICE',
    total_area: 200,
    status: 'draft',
    cover_image: '',
    view_count: 0,
    favorite_count: 0,
    inquiry_count: 0,
    description: '未完成的房源信息',
    type: 'rental',
    price: '面议',
    area: '200m²',
    location: '未填写地址',
    imageUrl: '',
    exposureCount: 0,
    favoriteCount: 0,
    inquiryCount: 0,
    viewCount: 0,
    tenantMatches: 0,
    isPremiumOwner: false,
    priceRange: { min: 0, max: 0 },
    areaRange: { min: 0, max: 0 },
    createdAt: new Date(),
    updatedAt: new Date(),
    draftData: { id: '3', formData: {} },
  },
];

export const PropertyListDemo: React.FC = () => {
  const [useBasicComponent, setUseBasicComponent] = useState(true);
  const [showUniversalScreen, setShowUniversalScreen] = useState(false);

  // 🔧 事件处理
  const handleItemPress = (item: any) => {
    Alert.alert('房源详情', `查看房源: ${item.title}`);
  };

  const handleEdit = (item: any) => {
    Alert.alert('编辑房源', `编辑房源: ${item.title}`);
  };

  const handleDelete = (item: any) => {
    Alert.alert('删除房源', `删除房源: ${item.title}`);
  };

  const handleStatusChange = (item: any, newStatus: string) => {
    Alert.alert('状态变更', `房源 ${item.title} 状态变更为: ${newStatus}`);
  };

  const formatDateTime = (date: Date) => {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  if (showUniversalScreen) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>通用房源列表页面演示</Text>
          <Switch
            value={showUniversalScreen}
            onValueChange={setShowUniversalScreen}
          />
        </View>
        <UniversalMyPropertiesScreen
          screenTitle="演示房源列表"
          showFilter={true}
          emptyStateConfig={{
            title: '演示页面',
            description: '这是通用房源列表组件的演示',
            actionText: '返回组件演示',
            onAction: () => setShowUniversalScreen(false),
          }}
        />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* 控制面板 */}
      <View style={styles.controlPanel}>
        <Text style={styles.title}>房源列表组件演示</Text>
        
        <View style={styles.switchContainer}>
          <Text style={styles.switchLabel}>
            使用{useBasicComponent ? '基础' : '增强'}组件
          </Text>
          <Switch
            value={useBasicComponent}
            onValueChange={setUseBasicComponent}
          />
        </View>

        <View style={styles.switchContainer}>
          <Text style={styles.switchLabel}>显示通用页面</Text>
          <Switch
            value={showUniversalScreen}
            onValueChange={setShowUniversalScreen}
          />
        </View>
      </View>

      {/* 组件演示 */}
      <View style={styles.demoSection}>
        <Text style={styles.sectionTitle}>
          {useBasicComponent ? '基础PropertyListItem' : '增强EnhancedPropertyListItem'}
        </Text>
        
        {mockProperties.map((property) => (
          <View key={property.id} style={styles.itemContainer}>
            {useBasicComponent ? (
              <PropertyListItem
                item={property}
                onPress={handleItemPress}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onStatusChange={handleStatusChange}
                onRefresh={() => console.log('刷新列表')}
              />
            ) : (
              <EnhancedPropertyListItem
                item={property}
                onPress={handleItemPress}
                onEdit={handleEdit}
                onDelete={handleDelete}
                formatDateTime={formatDateTime}
                showActions={true}
                showStats={true}
                customLayout="enhanced"
              />
            )}
          </View>
        ))}
      </View>

      {/* 使用说明 */}
      <View style={styles.instructionSection}>
        <Text style={styles.sectionTitle}>使用说明</Text>
        <Text style={styles.instructionText}>
          1. PropertyListItem: 基础通用组件，适用于简单的房源列表展示
        </Text>
        <Text style={styles.instructionText}>
          2. EnhancedPropertyListItem: 增强组件，兼容现有MyPropertiesScreen的复杂功能
        </Text>
        <Text style={styles.instructionText}>
          3. UniversalMyPropertiesScreen: 完整的房源列表页面，包含筛选、刷新等功能
        </Text>
        <Text style={styles.instructionText}>
          4. 所有组件都支持配置化使用，可以根据需要调整显示内容和行为
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  controlPanel: {
    backgroundColor: '#FFFFFF',
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  title: {
    fontSize: fontSize.xl,
    fontWeight: '700',
    color: '#333333',
    marginBottom: spacing.md,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  switchLabel: {
    fontSize: fontSize.base,
    color: '#666666',
  },
  demoSection: {
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: fontSize.lg,
    fontWeight: '600',
    color: '#333333',
    marginBottom: spacing.sm,
    paddingHorizontal: spacing.md,
  },
  itemContainer: {
    marginBottom: spacing.sm,
  },
  instructionSection: {
    backgroundColor: '#FFFFFF',
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  instructionText: {
    fontSize: fontSize.base,
    color: '#666666',
    marginBottom: spacing.sm,
    lineHeight: 20,
  },
});

export default PropertyListDemo;
