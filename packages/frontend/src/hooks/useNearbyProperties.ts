/**
 * 附近房源数据获取Hook
 * 使用地图API获取指定房源周边的其他房源
 */

import { useState, useEffect } from 'react';

interface NearbyProperty {
  id: string;
  title: string;
  distance_display: string;
  property_type: string;
  price?: number;
  area?: number;
}

interface UseNearbyPropertiesProps {
  propertyId: string;
  radiusMeters?: number;
  limit?: number;
}

interface NearbyPropertiesResult {
  properties: NearbyProperty[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useNearbyProperties = ({
  propertyId,
  radiusMeters = 1000,
  limit = 10
}: UseNearbyPropertiesProps): NearbyPropertiesResult => {
  const [properties, setProperties] = useState<NearbyProperty[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchNearbyProperties = async () => {
    if (!propertyId) return;

    try {
      setLoading(true);
      setError(null);

      // 调用房源详情地图API获取附近房源
      const response = await fetch(
        `http://8.134.250.136:8082/api/v1/map/detail/property/${propertyId}/location?radius_meters=${radiusMeters}&nearby_limit=${limit}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        console.log('[NearbyProperties] API响应数据:', data);
        
        // 使用API返回的附近房源数据
        const nearbyProps = data.nearby_properties || [];
        setProperties(nearbyProps);
      } else {
        console.error('获取附近房源失败:', response.status);
        const errorText = await response.text();
        console.error('错误详情:', errorText);
        setError('获取附近房源失败');
        setProperties([]);
      }
    } catch (err) {
      console.error('获取附近房源错误:', err);
      setError('网络错误，请稍后重试');
      setProperties([]);
    } finally {
      setLoading(false);
    }
  };

  const refetch = () => {
    fetchNearbyProperties();
  };

  useEffect(() => {
    if (propertyId) {
      fetchNearbyProperties();
    }
  }, [propertyId, radiusMeters, limit]);

  return {
    properties,
    loading,
    error,
    refetch
  };
};