/**
 * DemandForm 主业务逻辑Hook
 * @fileoverview 封装需求发布表单的核心业务逻辑
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🔒 功能保证：100%保留原有DemandFormScreen.tsx中的业务逻辑
 */

import { useCallback, useEffect, useMemo } from 'react';
import { useAuth } from '../../../../../contexts/AuthContext';
import {
  useDemandFormStore,
  useDemandFormData,
  useDemandFormUI,
  useDemandFormEditMode,
  useDemandFormTags,
  useDemandFormCache,
  useDemandFormValidation,
  useDemandFormActions,
} from '../stores/DemandFormStore';

// 导入类型
import type { UseDemandFormOptions } from '../types/demandForm.types';

/**
 * 主业务逻辑Hook - 完整保留原有DemandFormScreen.tsx的业务逻辑
 */
export const useDemandForm = (options: UseDemandFormOptions = {}) => {
  const {
    autoSave = true,
    autoSaveInterval = 30000, // 30秒
    enableValidation = true,
  } = options;

  // === 认证状态 ===
  const { state } = useAuth();
  const user = state.user;
  const isAuthenticated = !!state.userToken;

  // === Store状态（性能优化的选择器） ===
  const formData = useDemandFormData();
  const ui = useDemandFormUI();
  const editMode = useDemandFormEditMode();
  const tags = useDemandFormTags();
  const cache = useDemandFormCache();
  const validation = useDemandFormValidation();
  const actions = useDemandFormActions();

  // === 初始化用户手机号 ===
  useEffect(() => {
    if (user?.phone_number && !formData.contactInfo.phone) {
      actions.updateNestedField('contactInfo.phone', user.phone_number);
    }
  }, [user?.phone_number, formData.contactInfo.phone, actions]);

  // === 表单字段更新 - 保留原有逻辑 ===
  const handleFieldChange = useCallback(
    (field: string, value: any) => {
      actions.updateFormField(field, value);

      if (enableValidation) {
        // 清除该字段的错误
        actions.clearFieldError(field);
        // 标记字段为已触摸
        actions.markFieldTouched(field);
      }

      if (autoSave) {
        // 防抖保存
        setTimeout(() => {
          actions.saveToCache();
        }, 1000);
      }
    },
    [actions, enableValidation, autoSave]
  );

  // === 嵌套字段更新 - 保留原有逻辑 ===
  const handleNestedFieldChange = useCallback(
    (path: string, value: any) => {
      actions.updateNestedField(path, value);

      if (enableValidation) {
        actions.clearFieldError(path);
        actions.markFieldTouched(path);
      }

      if (autoSave) {
        setTimeout(() => {
          actions.saveToCache();
        }, 1000);
      }
    },
    [actions, enableValidation, autoSave]
  );

  // === 表单重置 ===
  const handleReset = useCallback(() => {
    actions.resetForm();
    actions.clearCache();
  }, [actions]);

  // === 编辑模式操作 ===
  const enterEditMode = useCallback(
    (demandId: string, demandData: any) => {
      actions.enterEditMode(demandId, demandData);

      // 🔒 保留原有编辑数据加载逻辑
      if (demandData) {
        // 设置表单数据
        actions.setFormData({
          demandType: demandData.demand_type || 'RENTAL',
          propertyType: demandData.property_types || [],
          location: {
            city: demandData.city || '南宁市',
            districts: demandData.districts || [],
            landmarks: demandData.landmarks || [],
          },
          areaRange: demandData.area_range || {
            min: 0,
            max: 0,
            unit: '平方米',
          },
          budgetRange: demandData.budget_range || {
            min: 0,
            max: 0,
            unit: '元/月',
          },
          transferFee: demandData.transfer_fee || '',
          industryType: demandData.industry_types || [],
          layoutType: demandData.layout_types || [],
          floorPreference: demandData.floor_preference || [],
          orientation: demandData.orientation || [],
          decoration: demandData.decoration || '',
          leaseTerm: demandData.lease_term || '',
          paymentMethod: demandData.payment_method || '',
          specialRequirements: demandData.special_requirements || '',
          contactInfo: {
            phone: demandData.contact_phone || user?.phone_number || '',
            surname: demandData.contact_surname || '',
            gender: demandData.contact_gender || 'male',
            wechat: demandData.contact_wechat || '',
            preferredContactTime:
              demandData.preferred_contact_time || 'ANYTIME',
            contactMethod: demandData.contact_method || 'BOTH',
            additionalContacts: demandData.additional_contacts || [],
          },
          agreeToRules: true, // 编辑模式默认已同意
        });

        // 设置标签
        if (demandData.custom_tags?.length > 0) {
          actions.setSelectedFeatureTags(demandData.custom_tags);
          actions.setShowTags(true);
          actions.setHasInitialTagsGenerated(true);
        }
      }
    },
    [actions, user?.phone_number]
  );

  const exitEditMode = useCallback(() => {
    actions.exitEditMode();
  }, [actions]);

  // === 标签管理 - 保留原有逻辑 ===
  const moveToSelected = useCallback(
    (tagValue: string) => {
      if (
        tags.selectedFeatureTags.length < 10 &&
        !tags.selectedFeatureTags.includes(tagValue)
      ) {
        actions.addFeatureTag(tagValue);
      } else if (tags.selectedFeatureTags.length >= 10) {
        // 显示最大标签数警告
        actions.setShowMaxTagsWarning(true);
        // 3秒后自动隐藏提醒
        setTimeout(() => {
          actions.setShowMaxTagsWarning(false);
        }, 3000);
      }
    },
    [tags.selectedFeatureTags, actions]
  );

  const moveToAvailable = useCallback(
    (tagValue: string) => {
      actions.removeFeatureTag(tagValue);
    },
    [actions]
  );

  // === 自动保存机制 ===
  useEffect(() => {
    if (!autoSave) return;

    const interval = setInterval(() => {
      if (formData && Object.keys(formData).length > 0) {
        actions.saveToCache();
      }
    }, autoSaveInterval);

    return () => clearInterval(interval);
  }, [autoSave, autoSaveInterval, formData, actions]);

  // === 用户登录后恢复缓存 - 保留原有逻辑 ===
  useEffect(() => {
    if (isAuthenticated && user) {
      const restored = actions.loadFromCache();
      if (restored) {
        console.log('[useDemandForm] 已恢复缓存的表单数据');
      }
    }
  }, [isAuthenticated, user, actions]);

  // === 计算表单完成度 - 保留原有逻辑 ===
  const completeness = useMemo(() => {
    const requiredFields = [
      'propertyType',
      'location.districts',
      'industryType',
      'contactInfo.phone',
      'contactInfo.surname',
      'contactInfo.gender',
    ];

    let filledFields = 0;

    requiredFields.forEach(field => {
      const value = field.includes('.')
        ? field.split('.').reduce((obj, key) => obj?.[key], formData)
        : formData[field as keyof typeof formData];

      if (value && (Array.isArray(value) ? value.length > 0 : value)) {
        filledFields++;
      }
    });

    return filledFields / requiredFields.length;
  }, [formData]);

  // === 表单验证状态 ===
  const validationState = useMemo(() => {
    const hasErrors = Object.keys(validation.formErrors).length > 0;
    const hasRequiredFields =
      formData.propertyType.length > 0 &&
      formData.location.districts.length > 0 &&
      formData.industryType.length > 0 &&
      formData.contactInfo.surname &&
      formData.contactInfo.phone;

    return {
      hasErrors,
      hasRequiredFields,
      isValid: !hasErrors && hasRequiredFields,
      completeness,
    };
  }, [validation.formErrors, formData, completeness]);

  // === 步骤导航 ===
  const nextStep = useCallback(() => {
    actions.nextStep();
  }, [actions]);

  const prevStep = useCallback(() => {
    actions.prevStep();
  }, [actions]);

  const setCurrentStep = useCallback(
    (step: number) => {
      actions.setCurrentStep(step);
    },
    [actions]
  );

  // === 返回接口 ===
  return {
    // === 状态 ===
    formData,
    ui,
    editMode,
    tags,
    cache,
    validation,

    // === 计算属性 ===
    completeness,
    validationState,
    canSubmit: validationState.isValid && !ui.loading && !ui.isSubmitting,
    isAuthenticated,
    user,

    // === 表单操作方法 ===
    handleFieldChange,
    handleNestedFieldChange,
    handleReset,

    // === 编辑模式方法 ===
    enterEditMode,
    exitEditMode,

    // === 标签管理方法 ===
    moveToSelected,
    moveToAvailable,

    // === 步骤导航方法 ===
    nextStep,
    prevStep,
    setCurrentStep,

    // === 缓存方法 ===
    saveToCache: actions.saveToCache,
    loadFromCache: actions.loadFromCache,
    clearCache: actions.clearCache,

    // === 验证方法 ===
    validateForm: actions.validateForm,
    setFieldError: actions.setFieldError,
    clearFieldError: actions.clearFieldError,

    // === UI状态方法 ===
    setLoading: actions.setLoading,
    setSubmitting: actions.setSubmitting,
    setSavingDraft: actions.setSavingDraft,
    setError: actions.setError,

    // === 直接访问actions（向后兼容） ===
    actions,
  };
};
