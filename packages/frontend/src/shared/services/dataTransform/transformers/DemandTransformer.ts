/**
 * 需求数据转换器
 * 
 * 遵循AI编码指导规范：
 * - 继承BaseTransformer基础类
 * - 处理需求相关数据转换
 * - 企业级错误处理
 * 
 * @fileoverview 处理需求相关的数据转换
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */

import { z } from 'zod';
import { BaseTransformer } from '../core/BaseTransformer';
import { 
  TransformResult, 
  TransformOptions,
  DemandFormData,
  DemandAPIRequest
} from '../types/TransformTypes';

/**
 * 需求转换器
 * 负责需求相关数据的双向转换
 */
export class DemandTransformer extends BaseTransformer<DemandFormData, DemandAPIRequest> {
  
  constructor() {
    super('DemandTransformer', '1.0.0');
  }

  /**
   * 初始化验证规则
   */
  protected initializeValidationRules(): void {
    // 需求表单数据验证
    this.addValidationRule('demandForm', {
      name: 'demandForm',
      description: '需求表单数据验证',
      schema: z.object({
        demandType: z.enum(['RENTAL', 'PURCHASE']),
        propertyType: z.array(z.string()).min(1, '请选择房源类型'),
        location: z.object({
          city: z.string(),
          districts: z.array(z.string()).min(1, '请选择区域'),
          landmarks: z.array(z.string()),
          transportRequirements: z.array(z.string())
        }),
        areaRange: z.object({
          min: z.number(),
          max: z.number(),
          unit: z.string()
        }),
        budgetRange: z.object({
          min: z.number(),
          max: z.number(),
          unit: z.string()
        }),
        industryType: z.array(z.string()).min(1, '请选择行业类型'),
        layoutType: z.array(z.string()).optional(),
        specialRequirements: z.string().optional(),
        contactInfo: z.object({
          phone: z.string().min(11, '手机号必须为11位数字'),
          surname: z.string().min(1, '请输入姓氏'),
          gender: z.enum(['male', 'female']),
          wechat: z.string().optional(),
          preferredContactTime: z.string(),
          contactMethod: z.string(),
          additionalContacts: z.array(z.object({
            phone: z.string(),
            verificationCode: z.string(),
            surname: z.string(),
            gender: z.enum(['male', 'female']),
            isVerified: z.boolean()
          })).optional()
        }),
        agreeToRules: z.boolean(),
        // 求租特有字段
        floorPreference: z.array(z.string()).optional(),
        orientation: z.array(z.string()).optional(),
        decoration: z.string().optional(),
        leaseTerm: z.string().optional(),
        // 求购特有字段
        paymentMethod: z.string().optional(),
        transferFee: z.string().optional()
      }),
      required: true
    });
  }

  /**
   * 转换为API格式
   */
  toAPI(input: DemandFormData, options?: TransformOptions): TransformResult<DemandAPIRequest> {
    return this.safeTransform(() => {
      // 🔧 草稿模式跳过严格验证
      if (options?.context !== 'draft') {
        // 验证输入数据
        const validationResult = this.validateInput(input, 'demandForm');
        if (!validationResult.success) {
          throw new Error(validationResult.error);
        }
      }

      // 转换房源类型
      const propertyTypeMap: Record<string, string> = {
        '商铺': 'SHOP',
        '写字楼': 'OFFICE',
        '厂房': 'FACTORY',
        '仓库': 'WAREHOUSE',
        '土地': 'LAND',
        '会所': 'CLUBHOUSE',
        '活动会议室': 'MEETING_ROOM'
      };

      // 构建基础API请求 - 🔧 草稿模式下允许部分字段为空
      const apiRequest: DemandAPIRequest = {
        demand_type: input.demandType || 'RENTAL',
        property_type: (input.propertyType && input.propertyType.length > 0) ? input.propertyType[0] : '',
        property_subtypes: (input.propertyType && input.propertyType.length > 1) ? input.propertyType.slice(1) : [],
        target_regions: (input.location && input.location.districts) ? input.location.districts : [],
        area_min: (input.areaRange && input.areaRange.min) ? input.areaRange.min : 0,
        area_max: (input.areaRange && input.areaRange.max) ? input.areaRange.max : 0,
        area_unit: (input.areaRange && input.areaRange.unit) ? input.areaRange.unit : '平方米',
        price_min: (input.budgetRange && input.budgetRange.min) ? input.budgetRange.min : 0,
        price_max: (input.budgetRange && input.budgetRange.max) ? input.budgetRange.max : 0,
        price_unit: (input.budgetRange && input.budgetRange.unit) ? input.budgetRange.unit : '元/月',
        contact_surname: (input.contactInfo && input.contactInfo.surname) ? input.contactInfo.surname : '',
        contact_gender: (input.contactInfo && input.contactInfo.gender) ? input.contactInfo.gender : 'male',
        contact_phone: (input.contactInfo && input.contactInfo.phone) ? input.contactInfo.phone : '',
        contact_wechat: (input.contactInfo && input.contactInfo.wechat) ? input.contactInfo.wechat : '',
        additional_contacts: (input.contactInfo && input.contactInfo.additionalContacts) ? input.contactInfo.additionalContacts : [],
        custom_tags: options?.selectedTags || [],
        description: input.specialRequirements || '',
        special_requirements: input.specialRequirements
      };

      // 添加行业类型
      if (input.industryType && input.industryType.length > 0) {
        (apiRequest as any).industry_types = input.industryType;
      }

      // 添加户型偏好
      if (input.layoutType && input.layoutType.length > 0) {
        (apiRequest as any).layout_types = input.layoutType;
      }

      // 添加求租特有字段
      if (input.demandType === 'RENTAL') {
        if (input.floorPreference && input.floorPreference.length > 0) {
          (apiRequest as any).floor_preference = input.floorPreference;
        }
        if (input.orientation && input.orientation.length > 0) {
          (apiRequest as any).orientation_preference = input.orientation;
        }
        if (input.decoration) {
          (apiRequest as any).decoration_preference = input.decoration;
        }
        if (input.leaseTerm) {
          (apiRequest as any).lease_term_preference = input.leaseTerm;
        }
      }

      // 添加求购特有字段
      if (input.demandType === 'PURCHASE') {
        if (input.paymentMethod) {
          (apiRequest as any).payment_method = input.paymentMethod;
        }
      }

      // 添加转让费范围（求租和求购都可能有）
      if (input.transferFee) {
        const [min, max] = input.transferFee.split('-').map(Number);
        if (!isNaN(min) && !isNaN(max)) {
          (apiRequest as any).transfer_fee_range = {
            min,
            max,
            unit: '万元'
          };
        }
      }

      return apiRequest;
    }, 'DemandTransformer.toAPI');
  }

  /**
   * 从API格式转换
   */
  fromAPI(input: DemandAPIRequest, options?: TransformOptions): TransformResult<DemandFormData> {
    return this.safeTransform(() => {
      // 反向转换房源类型
      const propertyTypeMap: Record<string, string> = {
        'SHOP': '商铺',
        'OFFICE': '写字楼',
        'FACTORY': '厂房',
        'WAREHOUSE': '仓库',
        'LAND': '土地',
        'CLUBHOUSE': '会所',
        'MEETING_ROOM': '活动会议室'
      };

      const formData: DemandFormData = {
        demandType: input.demand_type,
        propertyType: (input.property_subtypes || []).map(type => propertyTypeMap[type]).filter(Boolean) as any[],
        location: {
          city: '南宁市', // 默认城市
          districts: input.target_regions || [],
          landmarks: [],
          transportRequirements: []
        },
        areaRange: {
          min: input.area_min || 0,
          max: input.area_max || 0,
          unit: input.area_unit || '㎡'
        },
        budgetRange: {
          min: input.price_min || 0,
          max: input.price_max || 0,
          unit: input.price_unit || '元'
        },
        contactInfo: {
          phone: input.contact_phone || '',
          surname: input.contact_surname || '',
          gender: (input.contact_gender as 'male' | 'female') || 'male',
          wechat: input.contact_wechat,
          preferredContactTime: 'ANYTIME',
          contactMethod: 'BOTH',
          additionalContacts: input.additional_contacts || []
        },
        agreeToRules: true,
        industryType: [],
        specialRequirements: input.special_requirements
      };

      return formData;
    }, 'DemandTransformer.fromAPI');
  }
}

export default DemandTransformer;
