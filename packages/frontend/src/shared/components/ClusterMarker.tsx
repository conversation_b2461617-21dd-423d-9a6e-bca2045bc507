/**
 * 聚合标记组件
 * 职责：显示楼盘聚合数量，按照用户截图样式设计
 *
 * 设计理念：
 * 1. 蓝色标记显示租赁房源数量
 * 2. 红色标记显示出售房源数量
 * 3. 极简风格，清晰易读
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Marker } from 'react-native-amap3d';
import { wp, hp, fp } from '@shared/utils/responsive';

interface ClusterMarkerProps {
  id: string;
  latitude: number;
  longitude: number;
  rentCount: number;
  saleCount: number;
  buildingName: string;
  onPress?: () => void;
}

export const ClusterMarker: React.FC<ClusterMarkerProps> = ({
  id,
  latitude,
  longitude,
  rentCount,
  saleCount,
  buildingName,
  onPress,
}) => {
  // 自定义标记视图
  const renderCustomMarker = () => {
    return (
      <View style={styles.container}>
        {/* 租赁标记 */}
        {rentCount > 0 && (
          <View style={[styles.marker, styles.rentMarker]}>
            <Text style={styles.markerText}>
              租 {rentCount > 999 ? '999+' : rentCount}套
            </Text>
          </View>
        )}

        {/* 出售标记 */}
        {saleCount > 0 && (
          <View style={[styles.marker, styles.saleMarker]}>
            <Text style={styles.markerText}>
              售 {saleCount > 999 ? '999+' : saleCount}个
            </Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <Marker
      key={id}
      position={{ latitude, longitude }}
      title={buildingName}
      description={`租赁: ${rentCount}套, 出售: ${saleCount}个`}
      onPress={onPress}
    >
      {renderCustomMarker()}
    </Marker>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  marker: {
    paddingHorizontal: wp(8),
    paddingVertical: hp(4),
    borderRadius: 12,
    marginVertical: hp(2),
    minWidth: wp(60),
    alignItems: 'center',
    justifyContent: 'center',
    // 阴影效果
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  rentMarker: {
    backgroundColor: '#007AFF', // 蓝色
  },
  saleMarker: {
    backgroundColor: '#FF3B30', // 红色
  },
  markerText: {
    fontSize: fp(12),
    fontWeight: '600',
    color: '#fff',
    textAlign: 'center',
  },
});
