{"timestamp": "2025-07-17T13:20:26.115Z", "overallSuccess": true, "results": {"hookTests": [{"name": "useFormValidation (通用表单验证Hook)", "success": true, "errors": 0, "output": ""}, {"name": "usePropertyFormValidation (房源表单专用Hook)", "success": true, "errors": 0, "output": ""}, {"name": "Hook集成测试组件", "success": true, "errors": 1, "output": "src/screens/Publish/PropertyDetailFormScreen/test-hook-integration.tsx(7,8): error TS1259: Module '\"/data/my-real-estate-app/packages/frontend/node_modules/@types/react/index\"' can only be default-imported using the 'esModuleInterop' flag\n", "expectedErrors": 1}], "functionalityTests": [{"name": "useFormValidation导出完整性", "success": true}, {"name": "usePropertyFormValidation导出完整性", "success": true}, {"name": "<PERSON>od Schema定义正确性", "success": true}, {"name": "进度跟踪功能完整性", "success": true}, {"name": "验证规则配置完整性", "success": true}], "fileStats": [{"name": "useFormValidation (通用表单验证Hook)", "size": 9.03, "lines": 325}, {"name": "usePropertyFormValidation (房源表单专用Hook)", "size": 7.11, "lines": 245}, {"name": "Hook集成测试组件", "size": 8.46, "lines": 299}]}, "summary": {"totalTests": 8, "passedTests": 8, "criticalHooksOK": true, "totalHookLines": 869, "totalHookSize": 24.6}}