/**
 * 消息API服务
 * 处理消息中心相关的API请求
 */

import { apiClient as client } from '../../../shared/services/client';
import { 
  BusinessMessage, 
  BusinessStatistics 
} from '../types/businessMessage.types';

export interface UserRoleInfo {
  isTenantBuyer: boolean;
  isLandlord: boolean;
  tenantBuyerUnreadCount: number;
  landlordUnreadCount: number;
}

export interface MessageListResponse {
  messages: BusinessMessage[];
  total: number;
  hasMore: boolean;
}

export interface PaymentUnlockInfo {
  isUnlocked: boolean;
  unlockType: 'contact' | 'details' | 'unlimited';
  price: number;
  validDays: number;
  freeQuota: {
    used: number;
    total: number;
    resetDate: Date;
  };
  conversionText: string;
  urgencyText?: string;
  benefitsText: string[];
}

export class MessageAPI {
  /**
   * 获取商业消息列表
   */
  async getBusinessMessages(userId: string): Promise<BusinessMessage[]> {
    try {
      const response = await client.get(`/messages/business/${userId}`);
      return response.data || [];
    } catch (error) {
      console.error('获取商业消息失败:', error);
      return [];
    }
  }

  /**
   * 获取消息统计信息
   */
  async getMessageStatistics(userId: string): Promise<BusinessStatistics> {
    try {
      const response = await client.get(`/messages/statistics/${userId}`);
      return response.data || {
        role: 'tenant',
        tenantStats: {
          totalDemands: 0,
          matchedProperties: 0,
          interestedLandlords: 0,
          contactRate: 0,
          avgViewTime: 0
        },
        competitorAverage: {
          myScore: 0,
          averageScore: 0,
          ranking: 0,
          improvement: '暂无数据'
        }
      };
    } catch (error) {
      console.error('获取消息统计失败:', error);
      return {
        role: 'tenant',
        tenantStats: {
          totalDemands: 0,
          matchedProperties: 0,
          interestedLandlords: 0,
          contactRate: 0,
          avgViewTime: 0
        },
        competitorAverage: {
          myScore: 0,
          averageScore: 0,
          ranking: 0,
          improvement: '暂无数据'
        }
      };
    }
  }

  /**
   * 标记消息为已读
   */
  async markMessageAsRead(messageId: string): Promise<boolean> {
    try {
      await client.post(`/messages/${messageId}/read`);
      return true;
    } catch (error) {
      console.error('标记消息已读失败:', error);
      return false;
    }
  }

  /**
   * 获取消息互动历史
   */
  async getMessageInteractions(userId: string): Promise<any[]> {
    try {
      const response = await client.get(`/messages/interactions/${userId}`);
      return response.data || [];
    } catch (error) {
      console.error('获取消息互动历史失败:', error);
      return [];
    }
  }

  /**
   * 发送消息回复
   */
  async sendMessageReply(messageId: string, content: string): Promise<boolean> {
    try {
      await client.post(`/messages/${messageId}/reply`, {
        content
      });
      return true;
    } catch (error) {
      console.error('发送消息回复失败:', error);
      return false;
    }
  }

  /**
   * 获取用户角色信息
   */
  async getUserRoleInfo(): Promise<UserRoleInfo | null> {
    try {
      const response = await client.get('/messages/user-roles');
      return response.data || null;
    } catch (error) {
      console.error('获取用户角色信息失败:', error);
      // 🔧 API容错：返回默认角色信息
      return {
        isTenantBuyer: true,
        isLandlord: false,
        tenantBuyerUnreadCount: 0,
        landlordUnreadCount: 0
      };
    }
  }

  /**
   * 获取消息列表
   */
  async getMessageList(role: string, category: string): Promise<MessageListResponse | null> {
    try {
      const response = await client.get('/messages/list', {
        params: { role, category }
      });
      return response.data || { messages: [], total: 0, hasMore: false };
    } catch (error) {
      console.error('获取消息列表失败:', error);
      // 🔧 API容错：返回空消息列表
      return { messages: [], total: 0, hasMore: false };
    }
  }

  /**
   * 获取付费解锁信息
   */
  async getPaymentUnlockInfo(category: string): Promise<PaymentUnlockInfo | null> {
    try {
      const response = await client.get('/messages/payment-unlock', {
        params: { category }
      });
      return response.data || null;
    } catch (error) {
      console.error('获取付费解锁信息失败:', error);
      // 🔧 API容错：返回默认付费信息
      return {
        isUnlocked: false,
        unlockType: 'contact',
        price: 2990, // 29.9元，单位为分
        validDays: 30,
        freeQuota: {
          used: 0,
          total: 5,
          resetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        },
        conversionText: '解锁联系方式，提升沟通效率',
        urgencyText: '限时优惠，立即解锁',
        benefitsText: [
          '无限查看联系方式',
          '优先客服支持',
          '专业营销工具'
        ]
      };
    }
  }

  /**
   * 获取互动过的用户
   */
  async getInteractedUsers(): Promise<any[]> {
    try {
      const response = await client.get('/messages/interacted-users');
      return response.data || [];
    } catch (error) {
      console.error('获取互动用户失败:', error);
      // 🔧 API容错：返回空用户列表
      return [];
    }
  }
}

// 导出单例实例
export const messageAPI = new MessageAPI();