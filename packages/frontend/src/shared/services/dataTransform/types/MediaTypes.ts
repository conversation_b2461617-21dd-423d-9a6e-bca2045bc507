/**
 * 媒体处理抽象层类型定义
 * 
 * 基于企业级抽象层设计，定义统一的媒体处理接口类型
 * 支持图片动态处理和视频预转码的差异化策略
 * 
 * 参考文档：/data/my-real-estate-app/ALL_docs/抽象层方案/媒体处理抽象层设计.md
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月16日
 */

import { SecurityLevel, AccessLevel, MediaType, MediaCategory } from '../transformers/MediaTransformer';

// 媒体文件类型（字符串类型，用于兼容性）
export type MediaFileType = 'image' | 'video' | 'document';

// 媒体尺寸/质量
export type MediaSize = 'thumbnail' | 'medium' | 'large' | 'original';
export type VideoQuality = 'mobile' | 'web' | 'hd';

// 媒体状态
export interface MediaStatus {
  status: 'uploading' | 'processing' | 'ready' | 'failed';
  progress: number; // 0-100
  estimatedTime?: number; // 预计剩余时间（秒）
  availableQualities?: VideoQuality[]; // 可用的视频质量
  error?: string;
}

// 媒体上传结果
export interface MediaUploadResult {
  mediaId: string;
  type: MediaFileType;
  status: 'uploading' | 'processing' | 'ready' | 'failed';
  progress?: number;
  estimatedTime?: number;
  urls?: {
    thumbnail?: string;
    medium?: string;
    large?: string;
    original?: string;
  };
  error?: string;
}

// 媒体元数据
export interface MediaMetadata {
  mediaId: string;
  type: MediaFileType;
  category: MediaCategory;
  originalName: string;
  size: number;
  objectKey?: string;
  vodVideoId?: string; // 视频点播ID
  uploadTime: Date;
  processingStatus?: string;
  aspectRatio?: number; // 宽高比
  duration?: number; // 视频时长（秒）
  dimensions?: {
    width: number;
    height: number;
  };
}

// 视频信息
export interface VideoInfo {
  width: number;
  height: number;
  aspectRatio: number;
  aspectType: 'vertical' | 'horizontal' | 'square';
  recommendedRatio: string;
  duration: number;
  size: number;
}

// 视频上传结果
export interface VideoUploadResult {
  videoId: string;
  thumbnailUrl?: string;
  estimatedTime: number;
}

// 视频转码模板
export interface TranscodeTemplate {
  quality: VideoQuality;
  resolution: string;
  bitrate: string;
}

// 上传分片信息
export interface UploadPart {
  partNumber: number;
  etag: string;
}

// 水印配置
export interface WatermarkConfig {
  enabled: boolean;
  text: string;
  opacity: number;
  position: 'center' | 'bottom-right' | 'top-left' | 'top-right' | 'bottom-left';
  color: string;
  fontSize: number;
}

/**
 * 统一媒体处理器接口
 * 
 * 核心设计原则：
 * - 统一接口：图片和视频使用相同的API
 * - 差异化策略：内部根据媒体类型选择不同处理策略
 * - 异步处理：支持长时间的视频转码操作
 * - 状态跟踪：提供实时的处理状态查询
 */
export interface IMediaProcessor {
  /**
   * 上传媒体文件
   * @param file 文件对象
   * @param category 媒体分类
   * @returns 上传结果，包含mediaId和初始状态
   */
  upload(file: File, category: MediaCategory): Promise<MediaUploadResult>;
  
  /**
   * 获取媒体访问URL
   * @param mediaId 媒体ID
   * @param size 尺寸/质量
   * @returns 访问URL
   */
  getUrl(mediaId: string, size: MediaSize): Promise<string>;
  
  /**
   * 获取媒体处理状态
   * @param mediaId 媒体ID
   * @returns 处理状态信息
   */
  getStatus(mediaId: string): Promise<MediaStatus>;
  
  /**
   * 删除媒体文件
   * @param mediaId 媒体ID
   * @returns 删除是否成功
   */
  delete(mediaId: string): Promise<boolean>;
}

/**
 * 图片处理器接口
 * 
 * 策略：动态处理
 * - 只存储原图
 * - 通过OSS参数动态生成不同尺寸
 * - 支持实时水印添加
 */
export interface IImageProcessor {
  /**
   * 上传图片到OSS
   * @param file 图片文件
   * @param category 媒体分类
   * @returns 对象键
   */
  upload(file: File, category: MediaCategory): Promise<string>;
  
  /**
   * 生成图片访问URL
   * @param objectKey 对象键
   * @param size 图片尺寸
   * @returns 访问URL
   */
  generateUrl(objectKey: string, size: MediaSize): string;
  
  /**
   * 生成带水印的图片URL
   * @param objectKey 对象键
   * @param size 图片尺寸
   * @param watermark 水印配置
   * @returns 带水印的访问URL
   */
  generateWatermarkedUrl(objectKey: string, size: MediaSize, watermark: WatermarkConfig): string;
}

/**
 * 视频处理器接口
 * 
 * 策略：预处理
 * - 上传到视频点播服务
 * - 异步转码生成多个版本
 * - 支持竖屏横屏自动适配
 */
export interface IVideoProcessor {
  /**
   * 上传视频并开始处理
   * @param file 视频文件
   * @param mediaId 媒体ID
   * @param category 媒体分类
   * @returns 上传结果
   */
  uploadAndProcess(file: File, mediaId: string, category: MediaCategory): Promise<VideoUploadResult>;
  
  /**
   * 获取视频播放URL
   * @param mediaId 媒体ID
   * @param quality 视频质量
   * @returns 播放URL
   */
  getVersionUrl(mediaId: string, quality: VideoQuality): Promise<string>;
  
  /**
   * 获取视频处理状态
   * @param mediaId 媒体ID
   * @returns 处理状态
   */
  getProcessingStatus(mediaId: string): Promise<MediaStatus>;
  
  /**
   * 检测视频信息
   * @param file 视频文件
   * @returns 视频信息
   */
  detectVideoInfo(file: File): Promise<VideoInfo>;
}

/**
 * 元数据缓存接口
 */
export interface IMetadataCache {
  /**
   * 设置媒体元数据
   * @param mediaId 媒体ID
   * @param metadata 元数据
   */
  setMetadata(mediaId: string, metadata: Partial<MediaMetadata>): Promise<void>;
  
  /**
   * 获取媒体元数据
   * @param mediaId 媒体ID
   * @returns 元数据
   */
  getMetadata(mediaId: string): Promise<MediaMetadata>;
  
  /**
   * 更新媒体元数据
   * @param mediaId 媒体ID
   * @param updates 更新内容
   */
  updateMetadata(mediaId: string, updates: Partial<MediaMetadata>): Promise<void>;
  
  /**
   * 删除媒体元数据
   * @param mediaId 媒体ID
   */
  deleteMetadata(mediaId: string): Promise<void>;
}
