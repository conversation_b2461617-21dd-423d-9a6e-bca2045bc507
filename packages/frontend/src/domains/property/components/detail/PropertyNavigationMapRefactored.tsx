/**
 * 🚀 PropertyNavigationMap - 企业级重构版本
 * 遵循企业级五层架构，组件拆分 < 150行
 * 使用usePropertyNavigation Hook管理业务逻辑
 *
 * 🎯 Phase 2.2: 完善类型定义，100%类型安全
 */

import React from 'react';
import { View, StyleSheet, SafeAreaView } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// 🚀 企业级Hook层
import { usePropertyNavigation } from './hooks/usePropertyNavigation';

// 🚀 统一转换层
import { Transformers } from '../../../../shared/services/dataTransform';

// 🚀 企业级UI层 - 子组件
import {
  RouteSelectionPanel,
  NavigationControls,
  MapDisplay,
  RouteInfoDisplay,
} from './components';

// ==================== 类型定义 ====================

// 🎯 使用统一类型定义，移除any类型
import {
  PropertyNavigationParams,
  AddressReturnKey,
} from './types/navigation.types';

interface NavigationMapProps {
  route: RouteProp<
    { PropertyNavigation: PropertyNavigationParams },
    'PropertyNavigation'
  >;
}

// ==================== 主组件 ====================

export const PropertyNavigationMapRefactored: React.FC<NavigationMapProps> = ({
  route,
}) => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const { propertyLocation, selectedAddress, returnKey } = route.params;

  // 🚀 企业级Hook集成 - 地址搜索深度集成验证
  const {
    // 状态
    nativeLocation,
    customStartLocation,
    customEndLocation,
    startLocationText,
    endLocationText,
    isLocationSwapped,
    mapReady,
    routeResult,
    selectedRoute,

    // 方法
    handleAddressSearch,
    handleLocationSwap,
    calculateRoute,
    selectRoute,
    handleMapReady,
    getCurrentLocation,
    startNavigation,

    // 计算属性
    currentStartLocation,
    currentEndLocation,
    hasValidRoute,
    canNavigate,
  } = usePropertyNavigation(
    {
      propertyLocation,
      selectedAddress,
      returnKey,
    } as PropertyNavigationParams,
    navigation as any // 临时类型适配，等待AppNavigationProp完善
  );

  // ==================== 调试日志 ====================

  // 🔧 紧急修复：移除调试日志避免无限循环
  // React.useEffect(() => {
  //   console.log('🚀 [PropertyNavigationMapRefactored] 组件状态更新:', {
  //     propertyLocation,
  //     selectedAddress,
  //     returnKey,
  //     currentStartLocation,
  //     currentEndLocation,
  //     mapReady,
  //     hasValidRoute,
  //     routeResultCount: routeResult?.routes?.length || 0,
  //   });
  // }, [propertyLocation, selectedAddress, returnKey, currentStartLocation, currentEndLocation, mapReady, hasValidRoute, routeResult?.routes?.length]);

  // ==================== 渲染 ====================

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={[styles.container, { paddingTop: insets.top }]}>
        {/* 地图显示区域 */}
        <View style={styles.mapContainer}>
          <MapDisplay
            currentStartLocation={currentStartLocation}
            currentEndLocation={currentEndLocation}
            mapReady={mapReady}
            selectedRoute={selectedRoute}
            routeResult={routeResult}
            onMapReady={handleMapReady}
          />
        </View>

        {/* 控制面板区域 */}
        <View style={styles.controlsContainer}>
          {/* 导航控制 */}
          <NavigationControls
            startLocationText={startLocationText}
            endLocationText={endLocationText}
            currentStartLocation={currentStartLocation}
            currentEndLocation={currentEndLocation}
            isLocationSwapped={isLocationSwapped}
            hasValidRoute={hasValidRoute}
            canNavigate={canNavigate}
            onAddressSearch={handleAddressSearch}
            onLocationSwap={handleLocationSwap}
            onStartNavigation={startNavigation}
          />

          {/* 路线选择面板 */}
          <RouteSelectionPanel
            routeResult={routeResult}
            selectedRoute={selectedRoute}
            onRouteSelect={selectRoute}
            onCalculateRoute={calculateRoute}
          />

          {/* 路线信息显示 */}
          <RouteInfoDisplay
            routeResult={routeResult}
            selectedRoute={selectedRoute}
            startLocationText={startLocationText}
            endLocationText={endLocationText}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

// ==================== 样式定义 ====================

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  mapContainer: {
    flex: 1,
    minHeight: 300,
  },
  controlsContainer: {
    flex: 0,
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
    paddingTop: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
});

export default PropertyNavigationMapRefactored;
