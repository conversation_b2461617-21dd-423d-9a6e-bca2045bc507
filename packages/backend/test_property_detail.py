import asyncio
import aiohttp
import json

async def test_property_detail():
    """测试房源详情API"""
    
    # 获取认证token（因为要查看PENDING状态的房源，需要房东权限）
    login_data = {
        'phone_number': '18377100296',
        'password': '123456'
    }
    
    async with aiohttp.ClientSession() as session:
        # 登录
        async with session.post(
            'http://backend/api/v1/auth/login/phone',
            json=login_data,
            headers={'Content-Type': 'application/json'}
        ) as resp:
            if resp.status != 200:
                print(f'登录失败: {resp.status}')
                print(await resp.text())
                return
            
            login_result = await resp.json()
            token = login_result['access_token']
            print(f'登录成功，token: {token[:20]}...')
        
        # 测试房源详情API（使用最新发布的房源ID）
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        # 使用之前创建的房源ID
        property_id = '4c5f6fb1-8bf0-4b85-83ba-3066c1356dee'
        
        async with session.get(
            f'http://backend/api/v1/properties/{property_id}',
            headers=headers
        ) as resp:
            print(f'房源详情API响应状态: {resp.status}')
            if resp.status == 200:
                response_data = await resp.json()
                print('✅ 房源详情获取成功！')
                print(f'房源ID: {response_data.get("id")}')
                print(f'房源标题: {response_data.get("title")}')
                print(f'房源地址: {response_data.get("address")}')
                print(f'房源状态: {response_data.get("status")}')
                print(f'房源类型: {response_data.get("property_type")}')
                print(f'房源标签: {response_data.get("tags")}')
                return True
            else:
                print('❌ 房源详情获取失败')
                error_text = await resp.text()
                print(f'错误信息: {error_text}')
                return False

asyncio.run(test_property_detail())