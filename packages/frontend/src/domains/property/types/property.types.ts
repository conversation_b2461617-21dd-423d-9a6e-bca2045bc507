/**
 * 🏗️ 企业级架构：房源类型定义
 * 统一房源相关的类型定义，确保类型安全
 */

// ==================== 基础类型 ====================

/**
 * 房源状态
 */
export type PropertyStatus = 
  | 'draft'      // 草稿
  | 'published'  // 已发布
  | 'inactive'   // 已下架
  | 'sold'       // 已售出
  | 'rented'     // 已出租
  | 'expired';   // 已过期

/**
 * 房源类型
 */
export type PropertyType =
  | 'OFFICE'       // 写字楼
  | 'SHOP'         // 商铺
  | 'WAREHOUSE'    // 仓库
  | 'FACTORY'      // 厂房
  | 'APARTMENT'    // 公寓
  | 'HOUSE'        // 别墅
  | 'LAND'         // 土地
  | 'TERRACE'      // 露台
  | 'CLUBHOUSE'    // 会所
  | 'MEETING_ROOM' // 会议室
  | 'BOOTH'        // 摊位
  | 'CLUB';        // 会所（兼容性）

/**
 * 交易类型
 */
export type TransactionType = 
  | 'RENT'       // 出租
  | 'SALE';      // 出售

// ==================== API响应类型 ====================

/**
 * API房源列表项
 */
export interface APIPropertyListItem {
  id: string;
  title: string;
  property_type: PropertyType;
  sub_type?: string;
  total_area: number;
  usable_area?: number;
  building_name?: string;
  floor?: number;
  total_floors?: number;
  decoration_level?: string;
  status: PropertyStatus;
  verification_status: string;
  transaction_types: TransactionType[];
  listing_date?: string;
  created_at: string;
  updated_at?: string;
  
  // 价格信息
  rent_price?: number;
  sale_price?: number;
  
  // 位置信息
  address?: string;
  
  // 媒体信息
  cover_image?: string;
  images?: string[];
  
  // 描述信息
  description?: string;
  
  // 统计信息
  view_count?: number;
  favorite_count?: number;
  inquiry_count?: number;
  
  // 其他信息
  [key: string]: any;
}

/**
 * API房源列表响应
 */
export interface APIPropertyListResponse {
  items: APIPropertyListItem[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// ==================== UI层类型 ====================

/**
 * UI层房源列表项
 */
export interface PropertyListItem {
  id: string;
  title: string;
  price: string;
  area: string;
  location: string;
  imageUrl: string;
  tags: string[];
  aiTags?: string[];
  isVip: boolean;
  isFeatured: boolean;
  vipLabel?: string;
  status: PropertyStatus;
  type: PropertyType;
  
  // 统计信息
  viewCount: number;
  favoriteCount: number;
  inquiryCount: number;
  
  // 时间信息
  createdAt: Date;
  updatedAt?: Date;
  
  // 保留原始数据
  _rawData?: APIPropertyListItem;
}

/**
 * 房源详情
 */
export interface PropertyDetail extends PropertyListItem {
  description: string;
  features: string[];
  facilities: string[];
  transportation: string[];
  contact: {
    name: string;
    phone: string;
    wechat?: string;
  };
  
  // 详细位置信息
  detailedLocation: {
    province: string;
    city: string;
    district: string;
    address: string;
    longitude?: number;
    latitude?: number;
  };
  
  // 详细价格信息
  priceDetails: {
    rent?: {
      monthly: number;
      deposit: number;
      management?: number;
    };
    sale?: {
      total: number;
      unitPrice: number;
    };
  };
}

// ==================== 查询参数类型 ====================

/**
 * 房源查询参数
 */
export interface PropertyQueryParams {
  property_type?: PropertyType;
  sub_type?: string;
  transaction_type?: TransactionType;
  status?: PropertyStatus;
  keyword?: string;
  min_area?: number;
  max_area?: number;
  min_price?: number;
  max_price?: number;
  location?: string;
  page?: number;
  size?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

/**
 * 我的房源查询参数
 */
export interface MyPropertyQueryParams extends PropertyQueryParams {
  owner_id?: string;
}

// ==================== 表单类型 ====================

/**
 * 房源表单数据
 */
export interface PropertyFormData {
  title: string;
  property_type: PropertyType;
  sub_type?: string;
  transaction_types: TransactionType[];
  
  // 基本信息
  total_area: number;
  usable_area?: number;
  floor?: number;
  total_floors?: number;
  decoration_level?: string;
  
  // 位置信息
  province: string;
  city: string;
  district: string;
  address: string;
  building_name?: string;
  
  // 价格信息
  rent_price?: number;
  sale_price?: number;
  deposit?: number;
  management_fee?: number;
  
  // 描述信息
  description: string;
  features: string[];
  facilities: string[];
  
  // 联系信息
  contact_name: string;
  contact_phone: string;
  contact_wechat?: string;
  
  // 媒体文件
  images: string[];
  videos?: string[];
}

// ==================== 统计类型 ====================

/**
 * 房源统计信息
 */
export interface PropertyStatistics {
  total: number;
  status_breakdown: {
    [K in PropertyStatus]: number;
  };
  type_breakdown: {
    [K in PropertyType]: number;
  };
  transaction_breakdown: {
    [K in TransactionType]: number;
  };
  
  // 性能统计
  total_views: number;
  total_favorites: number;
  total_inquiries: number;
  
  // 时间统计
  this_month: number;
  last_month: number;
  growth_rate: number;
}

// ==================== 操作结果类型 ====================

/**
 * API操作结果
 */
export interface PropertyOperationResult {
  success: boolean;
  message?: string;
  data?: any;
}

/**
 * 房源状态更新结果
 */
export interface PropertyStatusUpdateResult extends PropertyOperationResult {
  data?: {
    id: string;
    status: PropertyStatus;
    updated_at: string;
  };
}

// ==================== 导出所有类型 ====================

export type {
  // 重新导出以确保类型可用
  PropertyStatus as Status,
  PropertyType as Type,
  TransactionType as Transaction,
};
