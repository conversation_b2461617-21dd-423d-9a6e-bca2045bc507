/**
 * 输入验证功能测试脚本
 * 验证房号、楼层、租金字段的输入验证逻辑
 */

console.log('=== 输入验证功能测试 ===\n');

// 模拟验证函数
const validateRoomFloorInput = (text) => {
  // 房号、楼层：只允许数字、大小写字母、横杠、小数点
  const regex = /^[a-zA-Z0-9.\-]*$/;
  return regex.test(text);
};

const validatePriceInput = (text) => {
  // 租金：只允许数字和小数点
  const regex = /^[0-9.]*$/;
  return regex.test(text);
};

// 测试数据
const roomFloorTestCases = [
  // 有效输入
  { input: '1201', expected: true, desc: '纯数字房号' },
  { input: 'A1201', expected: true, desc: '字母+数字房号' },
  { input: '1-2-1201', expected: true, desc: '带横杠房号' },
  { input: '12.5', expected: true, desc: '带小数点楼层' },
  { input: 'B1-3F', expected: true, desc: '复杂房号格式' },
  { input: '1栋1单元1201', expected: false, desc: '包含中文（应被拒绝）' },
  
  // 无效输入
  { input: '12层', expected: false, desc: '包含中文字符' },
  { input: '1201@', expected: false, desc: '包含特殊字符@' },
  { input: '1201#', expected: false, desc: '包含特殊字符#' },
  { input: '12/01', expected: false, desc: '包含斜杠' },
  { input: '12+01', expected: false, desc: '包含加号' },
  { input: '12 01', expected: false, desc: '包含空格' },
];

const priceTestCases = [
  // 有效输入
  { input: '5000', expected: true, desc: '整数租金' },
  { input: '5000.5', expected: true, desc: '带小数点租金' },
  { input: '0.5', expected: true, desc: '小于1的小数' },
  { input: '12345.67', expected: true, desc: '多位小数' },
  { input: '', expected: true, desc: '空字符串' },
  
  // 无效输入
  { input: '5000元', expected: false, desc: '包含中文单位' },
  { input: '5,000', expected: false, desc: '包含逗号分隔符' },
  { input: '5000-6000', expected: false, desc: '包含横杠范围' },
  { input: '5000+', expected: false, desc: '包含加号' },
  { input: '5000 元', expected: false, desc: '包含空格和文字' },
  { input: 'abc', expected: false, desc: '纯字母' },
];

// 执行房号/楼层验证测试
console.log('📝 房号/楼层输入验证测试:');
console.log('规则：只允许数字、大小写字母、横杠、小数点\n');

let roomFloorPassCount = 0;
roomFloorTestCases.forEach((testCase, index) => {
  const result = validateRoomFloorInput(testCase.input);
  const status = result === testCase.expected ? '✅ PASS' : '❌ FAIL';
  const inputDisplay = testCase.input === '' ? '(空字符串)' : `"${testCase.input}"`;
  
  console.log(`${index + 1}. ${status} - ${inputDisplay} - ${testCase.desc}`);
  console.log(`   预期: ${testCase.expected}, 实际: ${result}`);
  
  if (result === testCase.expected) {
    roomFloorPassCount++;
  }
  console.log('');
});

console.log(`房号/楼层验证测试结果: ${roomFloorPassCount}/${roomFloorTestCases.length} 通过\n`);

// 执行租金验证测试
console.log('💰 租金输入验证测试:');
console.log('规则：只允许数字和小数点\n');

let pricePassCount = 0;
priceTestCases.forEach((testCase, index) => {
  const result = validatePriceInput(testCase.input);
  const status = result === testCase.expected ? '✅ PASS' : '❌ FAIL';
  const inputDisplay = testCase.input === '' ? '(空字符串)' : `"${testCase.input}"`;
  
  console.log(`${index + 1}. ${status} - ${inputDisplay} - ${testCase.desc}`);
  console.log(`   预期: ${testCase.expected}, 实际: ${result}`);
  
  if (result === testCase.expected) {
    pricePassCount++;
  }
  console.log('');
});

console.log(`租金验证测试结果: ${pricePassCount}/${priceTestCases.length} 通过\n`);

// 总体测试结果
const totalTests = roomFloorTestCases.length + priceTestCases.length;
const totalPassed = roomFloorPassCount + pricePassCount;

console.log('📊 总体测试结果:');
console.log(`总测试数: ${totalTests}`);
console.log(`通过数: ${totalPassed}`);
console.log(`失败数: ${totalTests - totalPassed}`);
console.log(`通过率: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);

if (totalPassed === totalTests) {
  console.log('\n🎉 所有测试通过！输入验证功能正常工作。');
} else {
  console.log('\n⚠️  存在测试失败，需要检查验证逻辑。');
}

// 实际使用示例
console.log('\n📋 实际使用示例:');
console.log('\n房号字段示例:');
console.log('✅ 1栋1单元1201 → 输入被限制，只能输入：11201');
console.log('✅ A-12.5 → 允许输入（字母+横杠+数字+小数点）');
console.log('❌ 12层 → 输入被阻止（包含中文）');

console.log('\n楼层字段示例:');
console.log('✅ 12.5 → 允许输入（夹层）');
console.log('✅ B1 → 允许输入（地下一层）');
console.log('❌ 12层 → 输入被阻止（包含中文）');

console.log('\n租金字段示例:');
console.log('✅ 5000.5 → 允许输入');
console.log('❌ 5000元 → 输入被阻止（包含中文）');
console.log('❌ 5,000 → 输入被阻止（包含逗号）');

console.log('\n=== 测试完成 ===');