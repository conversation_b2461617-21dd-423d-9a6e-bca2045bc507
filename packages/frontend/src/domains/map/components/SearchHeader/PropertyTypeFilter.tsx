/**
 * 房源类型筛选组件
 * 职责：房源类型标签的横向滚动选择
 */

import React from 'react';
import { ScrollView, TouchableOpacity, Text, StyleSheet } from 'react-native';

interface PropertyTypeFilterProps {
  propertyTypes: string[];
  selectedType: string;
  onTypeChange: (type: string) => void;
}

export const PropertyTypeFilter: React.FC<PropertyTypeFilterProps> = ({
  propertyTypes,
  selectedType,
  onTypeChange,
}) => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.typeContainer}
    >
      {propertyTypes.map((type) => (
        <TouchableOpacity
          key={type}
          style={[
            styles.typeTag,
            selectedType === type && styles.activeTypeTag,
          ]}
          onPress={() => onTypeChange(type)}
        >
          <Text
            style={[
              styles.typeTagText,
              selectedType === type && styles.activeTypeTagText,
            ]}
          >
            {type}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  typeContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 8,
  },
  typeTag: {
    paddingVertical: 6,
    paddingHorizontal: 14,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.85)',
  },
  activeTypeTag: {
    backgroundColor: '#333',
  },
  typeTagText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  activeTypeTagText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});
