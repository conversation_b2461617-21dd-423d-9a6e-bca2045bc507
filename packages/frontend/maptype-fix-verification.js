/**
 * 🗺️ mapType错误修复验证
 * 验证 "java.lang.String cannot be cast to java.lang.Double" 错误是否已解决
 */

const fs = require('fs');
const { exec } = require('child_process');

console.log('🗺️ [MapTypeFix] 开始验证mapType修复...');

// 关键检查点
const checkPoints = [
  {
    name: 'MapType导入',
    file: '/data/my-real-estate-app/packages/frontend/src/domains/property/components/detail/components/MapDisplay.tsx',
    pattern: /import.*MapType.*from.*react-native-amap3d/,
    description: '检查是否正确导入MapType枚举',
  },
  {
    name: 'mapType使用',
    file: '/data/my-real-estate-app/packages/frontend/src/domains/property/components/detail/components/MapDisplay.tsx',
    pattern: /mapType=\{MapType\.Standard\}/,
    description: '检查是否使用枚举值而不是字符串',
  },
  {
    name: '类型定义',
    file: '/data/my-real-estate-app/packages/frontend/src/types/react-native-amap3d.d.ts',
    pattern: /export enum MapType/,
    description: '检查类型定义文件是否包含MapType枚举',
  },
];

const results = [];

console.log('\n📋 [MapTypeFix] 执行修复验证检查...');

checkPoints.forEach((check, index) => {
  console.log(`\n🔍 [Check ${index + 1}] ${check.name}...`);

  if (fs.existsSync(check.file)) {
    const content = fs.readFileSync(check.file, 'utf8');
    const isValid = check.pattern.test(content);

    if (isValid) {
      console.log(`✅ ${check.description}: 通过`);
      results.push({ check: check.name, status: 'pass' });
    } else {
      console.log(`❌ ${check.description}: 失败`);
      results.push({ check: check.name, status: 'fail' });
    }
  } else {
    console.log(`❌ 文件不存在: ${check.file}`);
    results.push({ check: check.name, status: 'file_missing' });
  }
});

// 检查是否还有字符串形式的mapType
console.log('\n🔍 [Additional Check] 检查残留的字符串mapType...');

exec(
  'grep -r "mapType=\\"" /data/my-real-estate-app/packages/frontend/src 2>/dev/null',
  (error, stdout, stderr) => {
    if (stdout) {
      console.log('❌ 发现残留的字符串mapType:');
      console.log(stdout);
      results.push({ check: 'string_mapType_check', status: 'fail' });
    } else {
      console.log('✅ 未发现残留的字符串mapType');
      results.push({ check: 'string_mapType_check', status: 'pass' });
    }

    // 生成最终报告
    setTimeout(() => {
      console.log('\n📊 [MapTypeFix] 修复验证结果:');

      const passedChecks = results.filter(r => r.status === 'pass').length;
      const totalChecks = results.length;
      const successRate = Math.round((passedChecks / totalChecks) * 100);

      console.log(
        `\n🎯 修复成功率: ${successRate}% (${passedChecks}/${totalChecks})`
      );

      results.forEach(result => {
        const status = result.status === 'pass' ? '✅' : '❌';
        console.log(`   ${status} ${result.check}`);
      });

      if (successRate === 100) {
        console.log('\n🎉 [SUCCESS] mapType错误修复完成！');
        console.log('✅ MapType枚举导入正确');
        console.log('✅ mapType属性使用枚举值');
        console.log('✅ 类型定义文件完整');
        console.log('✅ 无残留字符串mapType');

        console.log('\n🚀 [解决的问题]:');
        console.log(
          '❌ 原错误: java.lang.String cannot be cast to java.lang.Double'
        );
        console.log(
          '✅ 修复后: 使用MapType.Standard (数字0)替代"standard"字符串'
        );
        console.log('✅ 根本原因: AMapView期望数字类型，但传递了字符串类型');

        console.log('\n🔄 [建议测试]:');
        console.log('1. 重启React Native应用');
        console.log('2. 打开地图导航页面');
        console.log('3. 确认地图正常显示，不再报mapType错误');
      } else {
        console.log('\n⚠️ [PARTIAL] mapType修复不完整，需要进一步处理');
      }

      console.log('\n🔚 [MapTypeFix] mapType修复验证完成');
    }, 1000);
  }
);

// 创建测试脚本验证运行时行为
console.log('\n🔄 [MapTypeFix] 创建运行时测试...');

const testScript = `
/**
 * 运行时测试 - 验证mapType修复效果
 */
import React from 'react';
import { View } from 'react-native';
import { MapView, MapType } from 'react-native-amap3d';

const MapTypeTest = () => {
  console.log('🗺️ [Test] MapType枚举值:');
  console.log('Standard:', MapType.Standard); // 应该输出 0
  console.log('Satellite:', MapType.Satellite); // 应该输出 1
  console.log('Night:', MapType.Night); // 应该输出 2
  
  return (
    <View style={{ flex: 1 }}>
      <MapView
        style={{ flex: 1 }}
        mapType={MapType.Standard}  // 使用数字值而不是字符串
        center={{ latitude: 22.547, longitude: 114.085947 }}
        onLoad={() => console.log('✅ [Test] 地图加载成功，mapType错误已修复')}
      />
    </View>
  );
};

export default MapTypeTest;
`;

fs.writeFileSync(
  '/data/my-real-estate-app/packages/frontend/maptype-runtime-test.tsx',
  testScript
);
console.log('✅ [MapTypeFix] 运行时测试文件已创建: maptype-runtime-test.tsx');
