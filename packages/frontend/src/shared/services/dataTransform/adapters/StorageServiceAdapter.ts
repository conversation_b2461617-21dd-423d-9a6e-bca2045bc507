/**
 * 存储服务适配器 - 统一转换层集成
 * 
 * 职责：
 * 1. 将UnifiedStorageService集成到dataTransform架构中
 * 2. 提供统一的存储操作接口
 * 3. 处理数据格式转换
 * 4. 管理业务场景配置
 * 
 * 设计原则：
 * - 适配器模式：统一不同存储服务的接口
 * - 单一职责：专注于存储服务的数据转换
 * - 类型安全：完整的TypeScript类型支持
 * - 错误处理：统一的错误处理机制
 * 
 * 参考主流APP存储架构：
 * - 微信：统一存储接口，业务层无感知底层实现
 * - 抖音：智能路由，自动选择最优存储策略
 * - 淘宝：企业级权限控制，细粒度访问管理
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月16日
 */

import { z } from 'zod';
import { TransformResult, TransformOptions } from '../types/TransformTypes';
import { BaseTransformer } from '../core/BaseTransformer';
import { MediaBusinessType } from '../transformers/MediaTransformer';
import { UnifiedStorageService } from '../storage/UnifiedStorageService';
import {
  UploadOptions,
  UploadResult,
  DownloadOptions,
  DownloadResult,
  BatchUploadOptions,
  BatchUploadResult,
  BatchDeleteResult,
  STSCredentials,
  ObjectMetadata,
  StorageStats,
  Permission
} from '../types/StorageTypes';

/**
 * 存储操作选项
 */
export interface StorageUploadOptions extends TransformOptions {
  businessType: MediaBusinessType;
  userId?: string;
  metadata?: Record<string, string>;
  tags?: Record<string, string>;
  progressCallback?: (progress: number) => void;
}

/**
 * 批量存储操作选项
 */
export interface StorageBatchUploadOptions extends StorageUploadOptions {
  concurrency?: number;
  retryCount?: number;
  failFast?: boolean;
}

/**
 * 存储下载选项
 */
export interface StorageDownloadOptions extends TransformOptions {
  userId?: string;
  range?: {
    start: number;
    end: number;
  };
  progressCallback?: (progress: number) => void;
}

/**
 * STS凭证请求选项
 */
export interface STSRequestOptions extends TransformOptions {
  userId: string;
  businessType: MediaBusinessType;
  permissions: Permission[];
  expires?: number;
}

/**
 * 存储服务适配器实现
 */
export class StorageServiceAdapter extends BaseTransformer {
  private storageService: UnifiedStorageService;

  constructor() {
    super('StorageServiceAdapter', '1.0.0');
    this.storageService = new UnifiedStorageService();
  }

  /**
   * 初始化验证规则
   */
  protected initializeValidationRules(): void {
    // 文件验证规则
    this.validationRules.set('file', {
      schema: z.object({
        name: z.string().min(1, '文件名不能为空'),
        type: z.string().min(1, '文件类型不能为空'),
        size: z.number().positive('文件大小必须大于0')
      }),
      message: '文件格式验证失败'
    });

    // 路径验证规则
    this.validationRules.set('path', {
      schema: z.string().min(1, '路径不能为空').regex(/^[a-zA-Z0-9\/\-_\.]+$/, '路径格式不正确'),
      message: '路径格式验证失败'
    });

    // 用户ID验证规则
    this.validationRules.set('userId', {
      schema: z.string().min(1, '用户ID不能为空'),
      message: '用户ID验证失败'
    });

    // 对象键验证规则
    this.validationRules.set('objectKey', {
      schema: z.string().min(1, '对象键不能为空'),
      message: '对象键验证失败'
    });
  }

  /**
   * 转换为API格式（存储适配器不需要实现）
   */
  toAPI(input: any, options?: TransformOptions): TransformResult<any> {
    return {
      success: true,
      data: input,
      error: null,
      metadata: { source: 'StorageServiceAdapter.toAPI' }
    };
  }

  /**
   * 从API格式转换（存储适配器不需要实现）
   */
  fromAPI(input: any, options?: TransformOptions): TransformResult<any> {
    return {
      success: true,
      data: input,
      error: null,
      metadata: { source: 'StorageServiceAdapter.fromAPI' }
    };
  }
  
  /**
   * 上传文件 - 统一接口
   */
  async uploadFile(
    file: File, 
    path: string, 
    options: StorageUploadOptions
  ): Promise<TransformResult<UploadResult>> {
    try {
      // 验证输入
      const fileValidation = this.validateInput(file, 'file');
      if (!fileValidation.success) {
        return {
          success: false,
          error: fileValidation.error || '文件验证失败',
          data: null,
          metadata: { source: 'StorageServiceAdapter.uploadFile' }
        };
      }

      const pathValidation = this.validateInput(path, 'path');
      if (!pathValidation.success) {
        return {
          success: false,
          error: pathValidation.error || '路径验证失败',
          data: null,
          metadata: { source: 'StorageServiceAdapter.uploadFile' }
        };
      }
      
      // 设置当前用户
      if (options.userId) {
        this.storageService.setCurrentUser(options.userId);
      }
      
      // 准备上传选项
      const uploadOptions: UploadOptions = {
        contentType: file.type,
        metadata: options.metadata,
        tags: options.tags,
        progressCallback: options.progressCallback
      };
      
      // 执行上传
      const result = await this.storageService.upload(file, path, uploadOptions);
      
      return {
        success: result.success,
        data: result,
        error: result.error || null,
        metadata: {
          source: 'StorageServiceAdapter.uploadFile',
          businessType: options.businessType,
          userId: options.userId,
          fileSize: file.size,
          fileName: file.name,
          objectKey: result.objectKey
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败',
        data: null,
        metadata: { source: 'StorageServiceAdapter.uploadFile' }
      };
    }
  }
  
  /**
   * 批量上传文件
   */
  async batchUploadFiles(
    files: File[], 
    paths: string[], 
    options: StorageBatchUploadOptions
  ): Promise<TransformResult<BatchUploadResult>> {
    try {
      // 验证输入
      if (files.length !== paths.length) {
        return {
          success: false,
          error: '文件数量与路径数量不匹配',
          data: null,
          metadata: { source: 'StorageServiceAdapter.batchUploadFiles' }
        };
      }
      
      // 设置当前用户
      if (options.userId) {
        this.storageService.setCurrentUser(options.userId);
      }
      
      // 准备批量上传选项
      const batchUploadOptions: BatchUploadOptions = {
        contentType: 'application/octet-stream',
        metadata: options.metadata,
        tags: options.tags,
        concurrency: options.concurrency || 3,
        retryCount: options.retryCount || 2,
        failFast: options.failFast || false,
        progressCallback: options.progressCallback
      };
      
      // 执行批量上传
      const result = await this.storageService.batchUpload(files, paths, batchUploadOptions);
      
      return {
        success: result.success,
        data: result,
        error: result.success ? null : '部分文件上传失败',
        metadata: {
          source: 'StorageServiceAdapter.batchUploadFiles',
          businessType: options.businessType,
          userId: options.userId,
          totalFiles: files.length,
          successCount: result.successCount,
          failedCount: result.failedCount
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量上传失败',
        data: null,
        metadata: { source: 'StorageServiceAdapter.batchUploadFiles' }
      };
    }
  }
  
  /**
   * 下载文件
   */
  async downloadFile(
    objectKey: string, 
    options: StorageDownloadOptions = {}
  ): Promise<TransformResult<DownloadResult>> {
    try {
      // 设置当前用户
      if (options.userId) {
        this.storageService.setCurrentUser(options.userId);
      }
      
      // 准备下载选项
      const downloadOptions: DownloadOptions = {
        range: options.range,
        progressCallback: options.progressCallback
      };
      
      // 执行下载
      const result = await this.storageService.download(objectKey, downloadOptions);
      
      return {
        success: result.success,
        data: result,
        error: result.error || null,
        metadata: {
          source: 'StorageServiceAdapter.downloadFile',
          userId: options.userId,
          objectKey,
          fileSize: result.size
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '下载失败',
        data: null,
        metadata: { source: 'StorageServiceAdapter.downloadFile' }
      };
    }
  }
  
  /**
   * 删除文件
   */
  async deleteFile(
    objectKey: string, 
    options: TransformOptions & { userId?: string } = {}
  ): Promise<TransformResult<boolean>> {
    try {
      // 设置当前用户
      if (options.userId) {
        this.storageService.setCurrentUser(options.userId);
      }
      
      // 执行删除
      const result = await this.storageService.delete(objectKey);
      
      return {
        success: result,
        data: result,
        error: result ? null : '删除失败',
        metadata: {
          source: 'StorageServiceAdapter.deleteFile',
          userId: options.userId,
          objectKey
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除失败',
        data: false,
        metadata: { source: 'StorageServiceAdapter.deleteFile' }
      };
    }
  }
  
  /**
   * 批量删除文件
   */
  async batchDeleteFiles(
    objectKeys: string[], 
    options: TransformOptions & { userId?: string } = {}
  ): Promise<TransformResult<BatchDeleteResult>> {
    try {
      // 设置当前用户
      if (options.userId) {
        this.storageService.setCurrentUser(options.userId);
      }
      
      // 执行批量删除
      const result = await this.storageService.batchDelete(objectKeys);
      
      return {
        success: result.success,
        data: result,
        error: result.success ? null : '部分文件删除失败',
        metadata: {
          source: 'StorageServiceAdapter.batchDeleteFiles',
          userId: options.userId,
          totalFiles: objectKeys.length,
          deletedCount: result.deletedCount,
          failedCount: result.failedCount
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量删除失败',
        data: null,
        metadata: { source: 'StorageServiceAdapter.batchDeleteFiles' }
      };
    }
  }
  
  /**
   * 获取文件访问URL
   */
  async getFileUrl(
    objectKey: string, 
    options: TransformOptions & { userId?: string; expires?: number } = {}
  ): Promise<TransformResult<string>> {
    try {
      // 设置当前用户
      if (options.userId) {
        this.storageService.setCurrentUser(options.userId);
      }
      
      // 获取访问URL
      const url = await this.storageService.getViewUrl(objectKey, options.expires);
      
      return {
        success: true,
        data: url,
        error: null,
        metadata: {
          source: 'StorageServiceAdapter.getFileUrl',
          userId: options.userId,
          objectKey,
          expires: options.expires
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取URL失败',
        data: null,
        metadata: { source: 'StorageServiceAdapter.getFileUrl' }
      };
    }
  }
  
  /**
   * 获取STS临时凭证
   */
  async getSTSCredentials(
    options: STSRequestOptions
  ): Promise<TransformResult<STSCredentials>> {
    try {
      // 设置当前用户
      this.storageService.setCurrentUser(options.userId);
      
      // 生成STS凭证
      const credentials = await this.storageService.generateSTS(
        options.permissions, 
        options.expires
      );
      
      return {
        success: true,
        data: credentials,
        error: null,
        metadata: {
          source: 'StorageServiceAdapter.getSTSCredentials',
          userId: options.userId,
          businessType: options.businessType,
          permissions: options.permissions,
          expires: options.expires
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取STS凭证失败',
        data: null,
        metadata: { source: 'StorageServiceAdapter.getSTSCredentials' }
      };
    }
  }
  
  /**
   * 获取文件元数据
   */
  async getFileMetadata(
    objectKey: string, 
    options: TransformOptions & { userId?: string } = {}
  ): Promise<TransformResult<ObjectMetadata>> {
    try {
      // 设置当前用户
      if (options.userId) {
        this.storageService.setCurrentUser(options.userId);
      }
      
      // 获取元数据
      const metadata = await this.storageService.getMetadata(objectKey);
      
      return {
        success: true,
        data: metadata,
        error: null,
        metadata: {
          source: 'StorageServiceAdapter.getFileMetadata',
          userId: options.userId,
          objectKey
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取元数据失败',
        data: null,
        metadata: { source: 'StorageServiceAdapter.getFileMetadata' }
      };
    }
  }
  
  /**
   * 获取存储统计信息
   */
  async getStorageStats(
    options: TransformOptions & { userId?: string } = {}
  ): Promise<TransformResult<StorageStats>> {
    try {
      // 设置当前用户
      if (options.userId) {
        this.storageService.setCurrentUser(options.userId);
      }
      
      // 获取统计信息
      const stats = await this.storageService.getStats();
      
      return {
        success: true,
        data: stats,
        error: null,
        metadata: {
          source: 'StorageServiceAdapter.getStorageStats',
          userId: options.userId
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取统计信息失败',
        data: null,
        metadata: { source: 'StorageServiceAdapter.getStorageStats' }
      };
    }
  }
  
  /**
   * 检查文件是否存在
   */
  async fileExists(
    objectKey: string, 
    options: TransformOptions & { userId?: string } = {}
  ): Promise<TransformResult<boolean>> {
    try {
      // 设置当前用户
      if (options.userId) {
        this.storageService.setCurrentUser(options.userId);
      }
      
      // 检查文件是否存在
      const exists = await this.storageService.exists(objectKey);
      
      return {
        success: true,
        data: exists,
        error: null,
        metadata: {
          source: 'StorageServiceAdapter.fileExists',
          userId: options.userId,
          objectKey
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '检查文件存在性失败',
        data: false,
        metadata: { source: 'StorageServiceAdapter.fileExists' }
      };
    }
  }
  
  /**
   * 获取存储监控器
   */
  getStorageMonitor() {
    return this.storageService.getMonitor();
  }
}
