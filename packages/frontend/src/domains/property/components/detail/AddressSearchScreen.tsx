/**
 * 地址搜索页面 - 主搜索界面
 * 遵循企业级架构规范，仿高德地图搜索界面一比一还原
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { useAddressSearch } from './hooks/useAddressSearch';
import { AddressResult } from './types/addressSearch.types';
import { SafeAreaWrapper } from '../../../../shared/components/layout/SafeAreaWrapper';

// 搜索输入框组件
const SearchInput: React.FC<{
  value: string;
  onChangeText: (text: string) => void;
  onSubmitEditing: () => void;
  onClear: () => void;
  onBack: () => void;
  placeholder: string;
}> = ({
  value,
  onChangeText,
  onSubmitEditing,
  onClear,
  onBack,
  placeholder,
}) => {
  return (
    <View style={styles.searchHeader}>
      <TouchableOpacity style={styles.backButton} onPress={onBack}>
        <Text style={styles.backIcon}>←</Text>
      </TouchableOpacity>

      <View style={styles.searchInputContainer}>
        <TextInput
          style={styles.searchInput}
          value={value}
          onChangeText={onChangeText}
          onSubmitEditing={onSubmitEditing}
          placeholder={placeholder}
          placeholderTextColor="#999"
          returnKeyType="search"
          autoFocus
          clearButtonMode="never" // 使用自定义清除按钮
        />
        {value.length > 0 && (
          <TouchableOpacity style={styles.clearButton} onPress={onClear}>
            <Text style={styles.clearIcon}>×</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

// 搜索结果项组件
const SearchResultItem: React.FC<{
  address: AddressResult;
  onPress: (address: AddressResult) => void;
  showDistance?: boolean;
}> = ({ address, onPress, showDistance = true }) => {
  return (
    <TouchableOpacity
      style={styles.resultItem}
      onPress={() => onPress(address)}
      activeOpacity={0.7}
    >
      <View style={styles.resultItemContent}>
        <View style={styles.resultItemMain}>
          <Text style={styles.resultItemName} numberOfLines={1}>
            {(() => {
              // 🔧 修复Text错误：绝对安全的文本处理
              const safeName =
                address.name &&
                typeof address.name === 'string' &&
                address.name.trim()
                  ? address.name.trim()
                  : '未知地址';
              return safeName;
            })()}
          </Text>
          <Text style={styles.resultItemAddress} numberOfLines={2}>
            {(() => {
              // 🔧 修复Text错误：绝对安全的地址文本处理
              const safeFormattedAddress =
                address.formattedAddress &&
                typeof address.formattedAddress === 'string' &&
                address.formattedAddress.trim()
                  ? address.formattedAddress.trim()
                  : '';
              const safeAddress =
                address.address &&
                typeof address.address === 'string' &&
                address.address.trim()
                  ? address.address.trim()
                  : '';
              const finalAddress =
                safeFormattedAddress || safeAddress || '地址信息不完整';
              return finalAddress;
            })()}
          </Text>
        </View>

        {showDistance &&
          address.distance &&
          typeof address.distance === 'number' &&
          address.distance > 0 && (
            <View style={styles.resultItemDistance}>
              <Text style={styles.distanceText}>
                {address.distance > 1000
                  ? `${(address.distance / 1000).toFixed(1)}km`
                  : `${Math.round(address.distance)}m`}
              </Text>
            </View>
          )}
      </View>
    </TouchableOpacity>
  );
};

// 历史记录项组件
const HistoryItem: React.FC<{
  address: AddressResult;
  onPress: (address: AddressResult) => void;
  onRemove?: (addressId: string) => void;
}> = ({ address, onPress, onRemove }) => {
  return (
    <TouchableOpacity
      style={styles.historyItem}
      onPress={() => onPress(address)}
      activeOpacity={0.7}
    >
      <View style={styles.historyItemContent}>
        <Text style={styles.historyIcon}>🕒</Text>
        <View style={styles.historyItemMain}>
          <Text style={styles.historyItemName} numberOfLines={1}>
            {(() => {
              // 🔧 修复Text错误：历史记录名称安全处理
              const safeName =
                address.name &&
                typeof address.name === 'string' &&
                address.name.trim()
                  ? address.name.trim()
                  : '历史记录';
              return safeName;
            })()}
          </Text>
          <Text style={styles.historyItemAddress} numberOfLines={1}>
            {(() => {
              // 🔧 修复Text错误：历史记录地址安全处理
              const safeAddress =
                address.address &&
                typeof address.address === 'string' &&
                address.address.trim()
                  ? address.address.trim()
                  : '';
              const safeFormattedAddress =
                address.formattedAddress &&
                typeof address.formattedAddress === 'string' &&
                address.formattedAddress.trim()
                  ? address.formattedAddress.trim()
                  : '';
              const finalAddress =
                safeAddress || safeFormattedAddress || '地址信息不完整';
              return finalAddress;
            })()}
          </Text>
        </View>

        {onRemove && address.id && (
          <TouchableOpacity
            style={styles.removeHistoryButton}
            onPress={() => onRemove(address.id)}
          >
            <Text style={styles.removeHistoryIcon}>×</Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
};

// 快捷位置按钮组件
const QuickLocationButtons: React.FC<{
  locations: AddressResult[];
  onLocationPress: (address: AddressResult) => void;
  onCurrentLocationPress: () => void;
  currentLocation: any;
  isLoadingLocation: boolean;
}> = ({
  locations,
  onLocationPress,
  onCurrentLocationPress,
  currentLocation,
  isLoadingLocation,
}) => {
  return (
    <View style={styles.quickLocationsContainer}>
      <Text style={styles.quickLocationsTitle}>快捷位置</Text>

      <View style={styles.quickLocationsList}>
        {/* 我的位置按钮 */}
        <TouchableOpacity
          style={styles.quickLocationButton}
          onPress={onCurrentLocationPress}
          disabled={isLoadingLocation}
        >
          <View style={styles.quickLocationIcon}>
            {isLoadingLocation ? (
              <ActivityIndicator size="small" color="#007AFF" />
            ) : (
              <Text style={styles.quickLocationIconText}>📍</Text>
            )}
          </View>
          <Text style={styles.quickLocationText}>我的位置</Text>
        </TouchableOpacity>

        {/* 其他快捷位置 */}
        {locations.slice(0, 3).map(
          (
            location // 最多显示3个
          ) => (
            <TouchableOpacity
              key={location.id || 'unknown'}
              style={styles.quickLocationButton}
              onPress={() => onLocationPress(location)}
            >
              <View style={styles.quickLocationIcon}>
                <Text style={styles.quickLocationIconText}>📌</Text>
              </View>
              <Text style={styles.quickLocationText} numberOfLines={1}>
                {(() => {
                  // 🔧 修复Text错误：快捷位置文本安全处理
                  const safeName =
                    location.name &&
                    typeof location.name === 'string' &&
                    location.name.trim()
                      ? location.name.trim()
                      : '';
                  const safeFormattedAddress =
                    location.formattedAddress &&
                    typeof location.formattedAddress === 'string' &&
                    location.formattedAddress.trim()
                      ? location.formattedAddress.trim()
                      : '';
                  const safeAddress =
                    location.address &&
                    typeof location.address === 'string' &&
                    location.address.trim()
                      ? location.address.trim()
                      : '';
                  const finalText =
                    safeName || safeFormattedAddress || safeAddress || '位置';
                  return finalText;
                })()}
              </Text>
            </TouchableOpacity>
          )
        )}
      </View>
    </View>
  );
};

// 空状态组件
const EmptyState: React.FC<{
  type: 'no_results' | 'no_history';
  searchQuery?: string;
}> = ({ type, searchQuery }) => {
  return (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>
        {type === 'no_results' ? '🔍' : '📍'}
      </Text>
      <Text style={styles.emptyStateText}>
        {type === 'no_results'
          ? `未找到"${searchQuery || ''}"相关结果`
          : '暂无搜索历史'}
      </Text>
      {type === 'no_results' && (
        <Text style={styles.emptyStateSubtext}>尝试使用其他关键词搜索</Text>
      )}
    </View>
  );
};

// 主搜索页面
const AddressSearchScreen: React.FC = () => {
  const {
    // 状态
    searchQuery,
    searchResults,
    isSearching,
    searchError,
    searchHistory,
    currentLocation,
    quickLocations,

    // 计算属性
    showHistory,
    showResults,
    showEmptyState,
    searchStatusText,
    pageTitle,
    searchPlaceholder,

    // 操作方法
    setSearchQuery,
    handleSearch,
    handleAddressSelect,
    handleHistorySelect,
    handleCurrentLocationSelect,
    handleClearSearch,
    handleGoBack,
    handleLoadMore,
    removeFromHistory,
    clearHistory,
  } = useAddressSearch();

  return (
    <SafeAreaWrapper
      edges={['top', 'bottom', 'left', 'right']}
      backgroundColor="#FFFFFF"
      statusBarStyle="dark-content"
      statusBarBackgroundColor="#FFFFFF"
    >
      <View style={styles.container}>
        {/* 搜索输入栏 */}
        <SearchInput
          value={searchQuery}
          onChangeText={text => {
            setSearchQuery(text);
            // 🚀 实时搜索建议：输入1个字符以上就开始搜索
            if (text.trim().length > 0) {
              setTimeout(() => handleSearch(text), 300); // 300ms防抖
            }
          }}
          onSubmitEditing={() => handleSearch(searchQuery)}
          onClear={handleClearSearch}
          onBack={handleGoBack}
          placeholder={searchPlaceholder}
        />

        {/* 内容区域 */}
        <View style={styles.content}>
          {/* 搜索结果 */}
          {showResults && searchResults && (
            <View style={styles.resultsContainer}>
              {/* 🔧 修复Text错误：完全类型安全的状态文本渲染 */}
              {(() => {
                // 安全的状态文本处理，确保绝对不会渲染非字符串内容
                const safeStatusText =
                  searchStatusText &&
                  typeof searchStatusText === 'string' &&
                  searchStatusText.trim().length > 0
                    ? searchStatusText.trim()
                    : null;

                return safeStatusText ? (
                  <Text style={styles.statusText}>{safeStatusText}</Text>
                ) : null;
              })()}

              <FlatList
                data={searchResults}
                keyExtractor={(item, index) =>
                  item.id || `search-result-${index}`
                }
                renderItem={({ item, index }) => {
                  // 🔍 精确调试：记录每个渲染项的详细信息
                  console.log(
                    `[FlatList] 渲染项 ${index}:`,
                    JSON.stringify(item, null, 2)
                  );

                  // 🔧 修复Text错误：严格的数据类型验证
                  if (!item || typeof item !== 'object') {
                    console.warn(
                      `[FlatList] 跳过无效项 ${index}:`,
                      typeof item,
                      item
                    );
                    return null;
                  }

                  // 🔍 检查关键字段的数据类型
                  console.log(`[FlatList] 项 ${index} 字段类型:`, {
                    name: typeof item.name,
                    nameValue: item.name,
                    address: typeof item.address,
                    addressValue: item.address,
                    formattedAddress: typeof item.formattedAddress,
                    formattedAddressValue: item.formattedAddress,
                    distance: typeof item.distance,
                    distanceValue: item.distance,
                  });

                  // 🔧 更严格的数据验证和类型清理，确保所有字符串字段都是安全的
                  const validatedItem: AddressResult = {
                    ...item,
                    id:
                      typeof item.id === 'string' && item.id.trim()
                        ? item.id.trim()
                        : `item-${index}`,
                    name:
                      typeof item.name === 'string' && item.name.trim()
                        ? item.name.trim()
                        : '',
                    address:
                      typeof item.address === 'string' && item.address.trim()
                        ? item.address.trim()
                        : '',
                    formattedAddress:
                      typeof item.formattedAddress === 'string' &&
                      item.formattedAddress.trim()
                        ? item.formattedAddress.trim()
                        : undefined,
                    distance:
                      typeof item.distance === 'number' && !isNaN(item.distance)
                        ? item.distance
                        : undefined,
                    location:
                      item.location &&
                      typeof item.location.latitude === 'number' &&
                      typeof item.location.longitude === 'number'
                        ? item.location
                        : { latitude: 0, longitude: 0 },
                    district:
                      typeof item.district === 'string' && item.district.trim()
                        ? item.district.trim()
                        : '',
                    citycode:
                      typeof item.citycode === 'string' && item.citycode.trim()
                        ? item.citycode.trim()
                        : '',
                    adcode:
                      typeof item.adcode === 'string' && item.adcode.trim()
                        ? item.adcode.trim()
                        : '',
                    type:
                      typeof item.type === 'string' && item.type.trim()
                        ? item.type.trim()
                        : '',
                    typecode:
                      typeof item.typecode === 'string' && item.typecode.trim()
                        ? item.typecode.trim()
                        : '',
                  };

                  return (
                    <SearchResultItem
                      address={validatedItem}
                      onPress={handleAddressSelect}
                      showDistance={!!currentLocation}
                    />
                  );
                }}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
                showsVerticalScrollIndicator={false}
                onEndReached={handleLoadMore}
                onEndReachedThreshold={0.1}
                ListFooterComponent={
                  isSearching ? (
                    <View style={styles.loadingFooter}>
                      <ActivityIndicator size="small" color="#007AFF" />
                      <Text style={styles.loadingText}>搜索中...</Text>
                    </View>
                  ) : null
                }
              />
            </View>
          )}

          {/* 搜索历史和快捷位置 */}
          {showHistory && quickLocations && (
            <View style={styles.historyContainer}>
              {/* 快捷位置 */}
              <QuickLocationButtons
                locations={quickLocations}
                onLocationPress={handleAddressSelect}
                onCurrentLocationPress={handleCurrentLocationSelect}
                currentLocation={currentLocation}
                isLoadingLocation={false}
              />

              {/* 搜索历史 */}
              {searchHistory.length > 0 && (
                <View style={styles.historySection}>
                  <View style={styles.historySectionHeader}>
                    <Text style={styles.historySectionTitle}>搜索历史</Text>
                    <TouchableOpacity onPress={clearHistory}>
                      <Text style={styles.clearHistoryText}>清空</Text>
                    </TouchableOpacity>
                  </View>

                  <FlatList
                    data={searchHistory}
                    keyExtractor={(item, index) =>
                      item.id || `history-${index}`
                    }
                    renderItem={({ item }) => (
                      <HistoryItem
                        address={item}
                        onPress={handleHistorySelect}
                        onRemove={removeFromHistory}
                      />
                    )}
                    ItemSeparatorComponent={() => (
                      <View style={styles.separator} />
                    )}
                    showsVerticalScrollIndicator={false}
                    scrollEnabled={false} // 禁用滚动，显示所有历史记录
                  />
                </View>
              )}
            </View>
          )}

          {/* 空状态 */}
          {showEmptyState && (
            <EmptyState type="no_results" searchQuery={searchQuery} />
          )}

          {/* 错误状态 */}
          {searchError && (
            <View style={styles.errorState}>
              <Text style={styles.errorIcon}>⚠️</Text>
              <Text style={styles.errorText}>{searchError}</Text>
              <TouchableOpacity
                style={styles.retryButton}
                onPress={() => handleSearch(searchQuery)}
              >
                <Text style={styles.retryButtonText}>重试</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },

  // 搜索头部
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  backIcon: {
    fontSize: 20,
    color: '#333333',
    fontWeight: 'bold',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 40,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    paddingVertical: 0,
  },
  clearButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  clearIcon: {
    fontSize: 18,
    color: '#999999',
    fontWeight: 'bold',
  },

  // 内容区域
  content: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },

  // 搜索结果
  resultsContainer: {
    flex: 1,
  },
  statusText: {
    fontSize: 14,
    color: '#666666',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F9F9F9',
  },
  resultItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  resultItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultItemMain: {
    flex: 1,
  },
  resultItemName: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
    marginBottom: 4,
  },
  resultItemAddress: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  resultItemDistance: {
    marginLeft: 12,
  },
  distanceText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
  },

  // 历史记录
  historyContainer: {
    flex: 1,
  },
  historySection: {
    marginTop: 16,
  },
  historySectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  historySectionTitle: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
  },
  clearHistoryText: {
    fontSize: 14,
    color: '#007AFF',
  },
  historyItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  historyItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  historyIcon: {
    fontSize: 16,
    marginRight: 12,
  },
  historyItemMain: {
    flex: 1,
  },
  historyItemName: {
    fontSize: 16,
    color: '#333333',
    marginBottom: 2,
  },
  historyItemAddress: {
    fontSize: 14,
    color: '#666666',
  },
  removeHistoryButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  removeHistoryIcon: {
    fontSize: 16,
    color: '#CCCCCC',
  },

  // 快捷位置
  quickLocationsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  quickLocationsTitle: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
    marginBottom: 12,
  },
  quickLocationsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  quickLocationButton: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
    paddingHorizontal: 8,
    paddingVertical: 12,
    marginHorizontal: 8,
    marginBottom: 8,
  },
  quickLocationIcon: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 20,
    marginBottom: 6,
  },
  quickLocationIconText: {
    fontSize: 18,
  },
  quickLocationText: {
    fontSize: 12,
    color: '#333333',
    textAlign: 'center',
  },

  // 通用
  separator: {
    height: 1,
    backgroundColor: '#F0F0F0',
    marginLeft: 16,
  },
  loadingFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  loadingText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 8,
  },

  // 空状态
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
  },

  // 错误状态
  errorState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3333',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
});

export default AddressSearchScreen;
