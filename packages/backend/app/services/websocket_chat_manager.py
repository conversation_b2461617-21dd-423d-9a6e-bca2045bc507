"""
WebSocket聊天连接管理器

企业级WebSocket管理：
1. 连接池管理
2. 房间管理
3. 消息广播
4. 断线重连处理
5. 性能监控
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Set, Optional, List, Any
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID
import uuid

from ..models.chat_message import ChatRoom, ChatConnection, ChatMessage, MessageType, MessageStatus
from ..models.property_inquiry import PropertyInquiry
from ..core.database import get_async_session
from .content_filter_service import ContentFilterService
from .property_inquiry_service import PropertyInquiryService

logger = logging.getLogger(__name__)


class WebSocketChatManager:
    """
    WebSocket聊天管理器
    
    核心功能：
    - 连接池管理
    - 房间消息广播
    - 用户在线状态管理
    - 消息持久化
    """
    
    def __init__(self):
        # 活跃连接池: {connection_id: WebSocket}
        self.active_connections: Dict[str, WebSocket] = {}
        
        # 用户连接映射: {user_id: Set[connection_id]}
        self.user_connections: Dict[str, Set[str]] = {}
        
        # 房间连接映射: {room_id: Set[connection_id]}
        self.room_connections: Dict[str, Set[str]] = {}
        
        # 连接用户映射: {connection_id: user_id}
        self.connection_users: Dict[str, str] = {}
        
        # 连接房间映射: {connection_id: room_id}
        self.connection_rooms: Dict[str, str] = {}
        
        # 内容过滤服务
        self.content_filter = ContentFilterService()
        
        # 咨询服务
        self.inquiry_service = PropertyInquiryService()
    
    async def connect(
        self,
        websocket: WebSocket,
        user_id: UUID,
        inquiry_id: str,
        db: AsyncSession
    ) -> str:
        """
        建立WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
            user_id: 用户ID
            inquiry_id: 咨询记录ID
            db: 数据库会话
            
        Returns:
            connection_id: 连接ID
        """
        try:
            # 接受WebSocket连接
            await websocket.accept()
            
            # 生成连接ID
            connection_id = str(uuid.uuid4())
            
            # 验证咨询记录权限
            inquiry = await self.inquiry_service.get_inquiry_by_id(inquiry_id, db)
            if not inquiry:
                await websocket.close(code=4004, reason="咨询记录不存在")
                return None
            
            # 验证用户权限（只有咨询的双方可以加入）
            if str(user_id) not in [str(inquiry.tenant_id), str(inquiry.landlord_id)]:
                await websocket.close(code=4003, reason="无权限访问此聊天")
                return None
            
            # 获取或创建聊天房间
            room = await self._get_or_create_chat_room(inquiry, db)
            room_id = room.id
            
            # 存储连接信息
            self.active_connections[connection_id] = websocket
            self.connection_users[connection_id] = str(user_id)
            self.connection_rooms[connection_id] = room_id
            
            # 更新用户连接映射
            if str(user_id) not in self.user_connections:
                self.user_connections[str(user_id)] = set()
            self.user_connections[str(user_id)].add(connection_id)
            
            # 更新房间连接映射
            if room_id not in self.room_connections:
                self.room_connections[room_id] = set()
            self.room_connections[room_id].add(connection_id)
            
            # 数据库记录连接
            await self._save_connection_to_db(connection_id, user_id, room_id, db)
            
            # 发送连接成功消息
            await self._send_to_connection(connection_id, {
                "type": "connection_established",
                "room_id": room_id,
                "user_id": str(user_id),
                "timestamp": datetime.utcnow().isoformat()
            })
            
            # 通知房间内其他用户上线
            await self._broadcast_to_room(room_id, {
                "type": "user_online",
                "user_id": str(user_id),
                "timestamp": datetime.utcnow().isoformat()
            }, exclude_connection=connection_id)
            
            logger.info(f"用户 {user_id} 连接到房间 {room_id}，连接ID: {connection_id}")
            return connection_id
            
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            await websocket.close(code=4000, reason="连接失败")
            return None
    
    async def disconnect(self, connection_id: str, db: AsyncSession):
        """
        断开WebSocket连接
        
        Args:
            connection_id: 连接ID
            db: 数据库会话
        """
        try:
            # 获取连接信息
            user_id = self.connection_users.get(connection_id)
            room_id = self.connection_rooms.get(connection_id)
            
            # 清理连接池
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
            
            # 清理用户连接映射
            if user_id and user_id in self.user_connections:
                self.user_connections[user_id].discard(connection_id)
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
            
            # 清理房间连接映射
            if room_id and room_id in self.room_connections:
                self.room_connections[room_id].discard(connection_id)
                if not self.room_connections[room_id]:
                    del self.room_connections[room_id]
            
            # 清理连接映射
            if connection_id in self.connection_users:
                del self.connection_users[connection_id]
            if connection_id in self.connection_rooms:
                del self.connection_rooms[connection_id]
            
            # 数据库更新连接状态
            await self._update_connection_status(connection_id, False, db)
            
            # 通知房间内其他用户下线
            if room_id and user_id:
                await self._broadcast_to_room(room_id, {
                    "type": "user_offline",
                    "user_id": user_id,
                    "timestamp": datetime.utcnow().isoformat()
                }, exclude_connection=connection_id)
            
            logger.info(f"用户 {user_id} 断开连接，连接ID: {connection_id}")
            
        except Exception as e:
            logger.error(f"断开连接处理失败: {e}")
    
    async def send_message(
        self,
        connection_id: str,
        message_data: Dict[str, Any],
        db: AsyncSession
    ) -> Optional[ChatMessage]:
        """
        发送消息
        
        Args:
            connection_id: 发送者连接ID
            message_data: 消息数据
            db: 数据库会话
            
        Returns:
            ChatMessage: 保存的消息对象
        """
        try:
            # 获取发送者信息
            sender_id = self.connection_users.get(connection_id)
            room_id = self.connection_rooms.get(connection_id)
            
            if not sender_id or not room_id:
                await self._send_error(connection_id, "连接信息无效")
                return None
            
            # 获取房间信息
            room = await self._get_chat_room(room_id, db)
            if not room:
                await self._send_error(connection_id, "聊天房间不存在")
                return None
            
            # 确定接收者
            receiver_id = (
                str(room.tenant_id) if str(room.landlord_id) == sender_id 
                else str(room.landlord_id)
            )
            
            # 内容过滤检查
            content = message_data.get("content", "")
            filter_result = await self.content_filter.check_content(content)
            
            # 创建消息对象
            message = ChatMessage(
                inquiry_id=room.inquiry_id,
                sender_id=UUID(sender_id),
                receiver_id=UUID(receiver_id),
                message_type=MessageType(message_data.get("type", "text")),
                content=content,
                media_url=message_data.get("media_url"),
                media_duration=message_data.get("media_duration"),
                media_size=message_data.get("media_size"),
                contains_contact_info=filter_result.contains_contact_info,
                contact_info_detected=filter_result.detected_info,
                status=MessageStatus.BLOCKED if filter_result.should_block else MessageStatus.SENT,
                sent_at=datetime.utcnow()
            )
            
            # 保存消息到数据库
            db.add(message)
            await db.commit()
            await db.refresh(message)
            
            # 如果消息被拦截，发送付费提示
            if filter_result.should_block:
                await self._send_payment_prompt(connection_id, message, db)
                return message
            
            # 构建广播消息
            broadcast_data = {
                "type": "new_message",
                "message": {
                    "id": message.id,
                    "sender_id": str(message.sender_id),
                    "receiver_id": str(message.receiver_id),
                    "message_type": message.message_type,
                    "content": message.content,
                    "media_url": message.media_url,
                    "media_duration": message.media_duration,
                    "status": message.status,
                    "created_at": message.created_at.isoformat(),
                    "sent_at": message.sent_at.isoformat() if message.sent_at else None
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # 广播到房间
            await self._broadcast_to_room(room_id, broadcast_data)
            
            # 更新房间最后活动时间
            await self._update_room_activity(room_id, message.id, db)
            
            logger.info(f"消息发送成功: {message.id}")
            return message
            
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            await self._send_error(connection_id, "消息发送失败")
            return None
    
    async def _get_or_create_chat_room(
        self, 
        inquiry: PropertyInquiry, 
        db: AsyncSession
    ) -> ChatRoom:
        """获取或创建聊天房间"""
        # 查找现有房间
        from sqlalchemy import select
        result = await db.execute(
            select(ChatRoom).where(ChatRoom.inquiry_id == inquiry.id)
        )
        room = result.scalar_one_or_none()
        
        if not room:
            # 创建新房间
            room = ChatRoom(
                inquiry_id=inquiry.id,
                landlord_id=inquiry.landlord_id,
                tenant_id=inquiry.tenant_id
            )
            db.add(room)
            await db.commit()
            await db.refresh(room)
        
        return room
    
    async def _send_to_connection(self, connection_id: str, data: Dict[str, Any]):
        """发送消息到指定连接"""
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(data, ensure_ascii=False))
            except Exception as e:
                logger.error(f"发送消息到连接 {connection_id} 失败: {e}")
                # 连接可能已断开，清理连接
                await self._cleanup_dead_connection(connection_id)
    
    async def _broadcast_to_room(
        self, 
        room_id: str, 
        data: Dict[str, Any], 
        exclude_connection: Optional[str] = None
    ):
        """广播消息到房间内所有连接"""
        if room_id in self.room_connections:
            connections = self.room_connections[room_id].copy()
            for connection_id in connections:
                if connection_id != exclude_connection:
                    await self._send_to_connection(connection_id, data)
    
    async def _send_error(self, connection_id: str, error_message: str):
        """发送错误消息"""
        await self._send_to_connection(connection_id, {
            "type": "error",
            "message": error_message,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    async def _cleanup_dead_connection(self, connection_id: str):
        """清理死连接"""
        # 这里可以调用disconnect方法，但需要数据库会话
        # 简化处理，只清理内存中的连接
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]


# 全局WebSocket管理器实例
websocket_manager = WebSocketChatManager()
