#!/usr/bin/env python3
"""
修复SQLModel中同时使用nullable和sa_column的问题
SQLModel不支持同时传递nullable和sa_column参数
"""

import os
import re
import glob

def fix_nullable_sa_column(file_path):
    """修复单个文件中的nullable和sa_column冲突"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 匹配Field中同时包含nullable和sa_column的情况
    # 模式：Field(...nullable=True/False...sa_column=...)
    pattern = r'Field\(((?:[^)]|\([^)]*\))*nullable\s*=\s*(?:True|False)(?:[^)]|\([^)]*\))*sa_column\s*=(?:[^)]|\([^)]*\))*)\)'
    
    def replace_field(match):
        field_content = match.group(1)
        # 移除nullable参数
        field_content = re.sub(r',?\s*nullable\s*=\s*(?:True|False)\s*,?', '', field_content)
        # 清理多余的逗号
        field_content = re.sub(r',\s*,', ',', field_content)
        field_content = re.sub(r'^\s*,', '', field_content)
        field_content = re.sub(r',\s*$', '', field_content)
        return f'Field({field_content})'
    
    content = re.sub(pattern, replace_field, content, flags=re.DOTALL)
    
    # 如果内容有变化，写回文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Fixed: {file_path}")
        return True
    return False

def main():
    """主函数"""
    model_files = glob.glob('packages/backend/app/models/**/*.py', recursive=True)
    
    fixed_count = 0
    for file_path in model_files:
        if fix_nullable_sa_column(file_path):
            fixed_count += 1
    
    print(f"Total files fixed: {fixed_count}")

if __name__ == "__main__":
    main()
