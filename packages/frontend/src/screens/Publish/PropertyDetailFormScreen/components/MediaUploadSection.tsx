/**
 * 轻量级媒体上传组件
 *
 * 从PropertyDetailFormScreen中拆分出来的媒体上传功能
 * 专注于解决加载性能问题，采用懒加载和轻量化设计
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Modal,
  ScrollView,
  Dimensions,
  SafeAreaView,


} from 'react-native';
import FeedbackService from '../../../../shared/services/FeedbackService';
import { Ionicons } from '@expo/vector-icons';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';
import { MediaAsset } from '../../../../services/media/MediaPickerService';

// 导入样式
import { allStyles as styles } from '../styles';

interface MediaUploadSectionProps {
  // 基础属性
  propertyId: string | null;
  
  // 本地媒体文件管理
  localMediaFiles: any[];
  onAddLocalMediaFile: (file: any) => void;
  onRemoveLocalMediaFile: (index: number) => void;
  
  // 上传状态
  isUploadingMedia: boolean;
  uploadProgress: number;
  onUploadStart: () => void;
  onUploadComplete: (results: any[]) => void;
  onUploadError: (error: string) => void;
  
  // 图片导航
  currentImageIndex: number;
  onImageIndexChange: (index: number) => void;
}

export const MediaUploadSection: React.FC<MediaUploadSectionProps> = ({
  propertyId,
  localMediaFiles,
  onAddLocalMediaFile,
  onRemoveLocalMediaFile,
  isUploadingMedia,
  uploadProgress,
  onUploadStart,
  onUploadComplete,
  onUploadError,
  currentImageIndex,
  onImageIndexChange,
}) => {
  // 轻量化状态管理 - 减少不必要的状态
  const [isMediaPickerLoaded, setIsMediaPickerLoaded] = useState(false);
  const [mediaPickerService, setMediaPickerService] = useState<any>(null);
  
  // 简化的错误状态管理 - 移动到前面，避免Hook调用顺序问题
  const [uploadError, setUploadError] = useState<string | null>(null);

  // 懒加载媒体选择器服务
  const loadMediaPickerService = useCallback(async () => {
    if (!mediaPickerService) {
      try {
        const { mediaPickerService: service } = await import('../../../../services/media/MediaPickerService');
        setMediaPickerService(service);
        setIsMediaPickerLoaded(true);
        return service;
      } catch (error) {
        console.error('[MediaUpload] 加载媒体选择器失败:', error);
        setUploadError('加载媒体选择器失败');
        return null;
      }
    }
    return mediaPickerService;
  }, [mediaPickerService]);

  // 缓存媒体文件分类
  const mediaFilesByType = useMemo(() => {
    const images = localMediaFiles.filter(file => file.type === 'image');
    const videos = localMediaFiles.filter(file => file.type === 'video');
    return { images, videos };
  }, [localMediaFiles]);

  /**
   * 处理图片选择 - 优化版本，使用懒加载
   */
  const handleImagePicker = useCallback(async () => {
    try {
      const service = await loadMediaPickerService();
      if (!service) return;

      const result = await service.showMediaPicker({
        mediaTypes: 'all',
        allowsMultipleSelection: true,
        selectionLimit: 20,
        quality: 0.8,
      });

      if (result.success && result.assets) {
        // 添加到本地缓存，不立即上传
        result.assets.forEach((asset: MediaAsset) => {
          onAddLocalMediaFile(asset);
        });

        console.log(`[MediaUpload] 已选择 ${result.assets.length} 个媒体文件，保存到本地缓存`);
      }
    } catch (error) {
      console.error('[MediaUpload] 选择媒体文件失败:', error);
      setUploadError('选择媒体文件失败');
    }
  }, [loadMediaPickerService, onAddLocalMediaFile]);

  /**
   * 处理视频选择 - 优化版本，使用懒加载
   */
  const handleVideoUpload = useCallback(async () => {
    try {
      // 检查是否已有视频
      if (mediaFilesByType.videos.length >= 1) {
        FeedbackService.showInfo('只能选择一个视频，请先删除现有视频');
        return;
      }

      const service = await loadMediaPickerService();
      if (!service) return;

      const result = await service.showMediaPicker({
        mediaTypes: 'videos',
        allowsMultipleSelection: false,
        videoMaxDuration: 300, // 5分钟
        quality: 0.8,
      });

      if (result.success && result.assets && result.assets.length > 0) {
        const videoAsset = result.assets[0];
        onAddLocalMediaFile(videoAsset);
        console.log(`[MediaUpload] 已选择视频文件: ${videoAsset.fileName}`);
      }
    } catch (error) {
      console.error('[MediaUpload] 选择视频文件失败:', error);
      setUploadError('选择视频文件失败');
    }
  }, [mediaFilesByType.videos.length, loadMediaPickerService, onAddLocalMediaFile]);

  const clearError = useCallback(() => {
    setUploadError(null);
  }, []);

  /**
   * 图片导航处理
   */
  const handlePreviousImage = useCallback(() => {
    if (currentImageIndex > 0) {
      onImageIndexChange(currentImageIndex - 1);
    }
  }, [currentImageIndex, onImageIndexChange]);

  const handleNextImage = useCallback(() => {
    if (currentImageIndex < mediaFilesByType.images.length - 1) {
      onImageIndexChange(currentImageIndex + 1);
    }
  }, [currentImageIndex, mediaFilesByType.images.length, onImageIndexChange]);

  return (
    <View style={styles.mediaUploadContainer}>
      <Text style={styles.sectionTitle}>上传图片和视频</Text>

      {/* 上传进度显示 */}
      {isUploadingMedia && (
        <View style={styles.uploadProgressContainer}>
          <Text style={styles.uploadProgressText}>
            正在上传媒体文件... {Math.round(uploadProgress)}%
          </Text>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${uploadProgress}%` }]} />
          </View>
          <Text style={styles.uploadHint}>
            请稍候，正在将您选择的图片上传到云端...
          </Text>
        </View>
      )}

      {/* 错误显示 */}
      {uploadError && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{uploadError}</Text>
          <TouchableOpacity onPress={clearError} style={styles.clearErrorButton}>
            <Text style={styles.clearErrorText}>清除</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* 视频上传按钮 */}
      <View style={styles.mediaUploadRow}>
        <TouchableOpacity style={styles.mediaUploadButton} onPress={handleVideoUpload}>
          <Ionicons name="videocam-outline" size={wp(24)} color="#007AFF" />
          <View style={styles.uploadButtonContent}>
            <Text style={styles.mediaUploadText}>选择视频</Text>
            <Text style={styles.mediaUploadHint}>支持多选，最长5分钟，智能上传</Text>
          </View>
        </TouchableOpacity>

        {/* 视频预览 */}
        {mediaFilesByType.videos.length > 0 && (
          <View style={styles.mediaThumbnailContainer}>
            <View style={styles.videoPreview}>
              <Ionicons name="play-circle" size={wp(32)} color="#FFFFFF" style={styles.playIcon} />
              <TouchableOpacity
                style={styles.deleteMediaButton}
                onPress={() => {
                  const videoIndex = localMediaFiles.findIndex(file => file.type === 'video');
                  if (videoIndex !== -1) {
                    onRemoveLocalMediaFile(videoIndex);
                  }
                }}
              >
                <Ionicons name="close" size={wp(16)} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
            <Text style={styles.videoInfo}>视频已选择</Text>
          </View>
        )}
      </View>

      {/* 图片上传按钮 */}
      <View style={styles.mediaUploadRow}>
        <TouchableOpacity style={styles.mediaUploadButton} onPress={handleImagePicker}>
          <Ionicons name="camera-outline" size={wp(24)} color="#007AFF" />
          <View style={styles.uploadButtonContent}>
            <Text style={styles.mediaUploadText}>选择图片</Text>
            <Text style={styles.mediaUploadHint}>支持多选，最多20张，智能上传</Text>
          </View>
        </TouchableOpacity>

        {/* 图片预览 */}
        {mediaFilesByType.images.length > 0 && (
          <View style={styles.mediaThumbnailContainer}>
            <View style={styles.imagePreview}>
              <Image
                source={{ uri: mediaFilesByType.images[currentImageIndex]?.uri }}
                style={styles.thumbnailImage}
                resizeMode="cover"
              />
              <TouchableOpacity
                style={styles.deleteMediaButton}
                onPress={() => {
                  const imageFiles = localMediaFiles.filter(file => file.type === 'image');
                  const targetFile = imageFiles[currentImageIndex];
                  const targetIndex = localMediaFiles.findIndex(file => file === targetFile);
                  if (targetIndex !== -1) {
                    onRemoveLocalMediaFile(targetIndex);
                    // 调整当前图片索引
                    if (currentImageIndex >= imageFiles.length - 1) {
                      onImageIndexChange(Math.max(0, imageFiles.length - 2));
                    }
                  }
                }}
              >
                <Ionicons name="close" size={wp(16)} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
            <Text style={styles.imageInfo}>{mediaFilesByType.images.length} 张图片已选择</Text>
            
            {/* 图片导航 */}
            {mediaFilesByType.images.length > 1 && (
              <View style={styles.imageNavigation}>
                <TouchableOpacity
                  style={[styles.navButton, currentImageIndex === 0 && styles.navButtonDisabled]}
                  onPress={handlePreviousImage}
                  disabled={currentImageIndex === 0}
                >
                  <Ionicons name="chevron-back" size={wp(16)} color="#FFFFFF" />
                </TouchableOpacity>
                
                <Text style={styles.imageCounter}>
                  {currentImageIndex + 1} / {mediaFilesByType.images.length}
                </Text>
                
                <TouchableOpacity
                  style={[styles.navButton, currentImageIndex === mediaFilesByType.images.length - 1 && styles.navButtonDisabled]}
                  onPress={handleNextImage}
                  disabled={currentImageIndex === mediaFilesByType.images.length - 1}
                >
                  <Ionicons name="chevron-forward" size={wp(16)} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

export default MediaUploadSection;
