/**
 * API配置测试脚本
 * 用于验证前端API路径配置是否正确
 */

// 模拟前端配置
const API_CONFIG = {
  BASE_URL: 'http://8.134.250.136:8082/api/v1', // 修复后的配置
  API_VERSION: '', // 已包含在BASE_URL中
};

// 模拟getApiEndpoint函数
const getApiEndpoint = (path) => {
  const baseUrl = API_CONFIG.BASE_URL;
  return `${baseUrl}${path.startsWith('/') ? path : '/' + path}`;
};

// 测试各个API端点
console.log('=== API路径配置测试 ===\n');

const testEndpoints = [
  '/messages/list',
  '/messages/user-roles', 
  '/messages/interacted-users',
  '/messages/payment-unlock',
  '/sms/account'
];

console.log('修复后的URL配置:');
testEndpoints.forEach(endpoint => {
  const fullUrl = getApiEndpoint(endpoint);
  console.log(`${endpoint.padEnd(25)} -> ${fullUrl}`);
});

console.log('\n=== 对比修复前后 ===\n');

// 修复前的配置（错误的）
const OLD_CONFIG = {
  BASE_URL: 'http://8.134.250.136:8082',
  API_VERSION: '/api/v1',
};

const oldGetApiEndpoint = (path) => {
  const baseUrl = OLD_CONFIG.BASE_URL + OLD_CONFIG.API_VERSION;
  return `${baseUrl}/api${path}`; // 前端API调用中还有/api前缀，导致重复
};

console.log('修复前（错误）:');
testEndpoints.forEach(endpoint => {
  const oldUrl = oldGetApiEndpoint(endpoint);
  const newUrl = getApiEndpoint(endpoint);
  console.log(`${endpoint.padEnd(25)}:`);
  console.log(`  错误: ${oldUrl}`);
  console.log(`  正确: ${newUrl}`);
  console.log('');
});

console.log('=== 验证结果 ===');
console.log('✅ 修复后的URL不再包含重复的/api路径');
console.log('✅ 所有端点都指向正确的后端API');
console.log('✅ 前端需要重新构建以应用配置更改');
