/**
 * 消息系统统一样式文件
 */
import { StyleSheet, Dimensions } from 'react-native';

// 响应式设计 - 获取屏幕尺寸
const { width: screenWidth } = Dimensions.get('window');

export const messageStyles = StyleSheet.create({
  // 容器样式
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },

  // 头部样式
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },

  // 角色切换样式
  roleSwitch: {
    flexDirection: 'row',
    backgroundColor: '#F8F8F8',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    padding: 4,
  },
  roleOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeRoleOption: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  roleText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  activeRoleText: {
    color: '#FF6B35',
    fontWeight: '600',
  },

  // 探索按钮样式
  exploreButton: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 20,
  },
  exploreButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },

  // 沉浸式角色切换样式（恢复原始样式）
  roleSwitchContainerImmersive: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingBottom: 30, // 增加底部间距，避免内容覆盖
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  roleSwitchContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 30, // 角色选项之间的间距
  },
  settingsButton: {
    padding: 8,
  },
  settingsIcon: {
    fontSize: 20,
    color: '#666666',
  },
  roleOption: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  roleText: {
    fontSize: 16,
    color: '#999999',
    fontWeight: '500',
  },
  activeRoleText: {
    fontSize: 26, // 🔧 修复：与未登录时保持一致
    color: '#FF6B35',
    fontWeight: '600',
  },

  // 欢迎区域样式
  welcomeSection: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  welcomeIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  welcomeTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },

  // 功能预览区域样式
  featurePreviewSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  featureListCompact: {
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    padding: 16,
    borderRadius: 12,
  },
  featureIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
  },

  // 底部间距样式（与未登录时保持一致）
  bottomSpacing: {
    height: 20,
  },
  
  // 底部间距
  bottomSpacing: {
    height: 20,
  },

  // 空状态样式
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },

  // 消息列表容器
  messageListContainer: {
    backgroundColor: '#FFFFFF',
    marginTop: 2,
  },

  // 未读徽章样式
  unreadBadge: {
    backgroundColor: '#FF3333',
    borderRadius: 10,
    minWidth: 18,
    height: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
    paddingHorizontal: 4,
  },
  unreadText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },

  // 分类标签样式
  categoryContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingTop: 4,
    paddingBottom: 4,
    marginTop: -10,
  },
  categoryTabs: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  categoryTab: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
  },
  activeCategoryTab: {
    backgroundColor: '#FF6B35',
  },
  categoryText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  activeCategoryText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  categorySeparator: {
    fontSize: 16,
    color: '#CCCCCC',
    marginHorizontal: 8,
  },
});
