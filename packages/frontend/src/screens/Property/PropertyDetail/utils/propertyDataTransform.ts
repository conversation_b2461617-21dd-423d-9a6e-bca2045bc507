/**
 * 房源详情页数据转换工具
 * 
 * 遵循企业级前端五层架构的数据转换层规范
 * 专门处理房源详情页的数据转换逻辑
 */

import type { PropertyDetailData } from '@property/types';
import type { PublishedPropertyData } from '../types/propertyDetail.types';

/**
 * 发布数据合并到房源详情数据（企业级数据转换层）
 * 遵循单一事实来源原则：publishedData 优先级高于 baseData
 * 
 * @param baseData 基础房源数据（来自API）
 * @param publishedData 发布时的数据（来自发布页面）
 * @returns 合并后的房源数据
 */
export const mergePublishedData = (
  baseData: PropertyDetailData,
  publishedData: PublishedPropertyData
): PropertyDetailData => {
  if (!publishedData) {
    return baseData;
  }

  // 基础合并：发布数据优先
  const mergedData: PropertyDetailData = {
    ...baseData,
    // 基础信息：发布数据优先
    id: String(publishedData.propertyId || publishedData.id || baseData.id),
    title: publishedData.title || baseData.title,
    // description不是PropertyDetailData的字段，移除此行
    
    // 房源类型和交易类型
    ...(publishedData.propertyType && { propertyType: publishedData.propertyType }),
    ...(publishedData.transactionType && { transactionType: publishedData.transactionType }),
    
    // 基础属性
    ...(publishedData.area && { 
      keyInfo: {
        ...baseData.keyInfo,
        area: publishedData.area
      }
    }),
    // 价格信息：根据交易类型更新对应的价格字段
    ...(publishedData.price && {
      keyInfo: {
        ...baseData.keyInfo,
        // 根据交易类型决定更新租赁价格还是销售价格
        ...(publishedData.transactionType === 'SALE' ? {
          sale: {
            ...baseData.keyInfo?.sale,
            price: publishedData.price,
            unit: baseData.keyInfo?.sale?.unit || '万'
          }
        } : {
          rent: {
            ...baseData.keyInfo?.rent,
            price: publishedData.price,
            term: baseData.keyInfo?.rent?.term || '月'
          }
        })
      }
    }),
    // 位置信息需要更新到propertyDetails中
    ...(publishedData.location || publishedData.address) && {
      propertyDetails: {
        ...baseData.propertyDetails,
        location: publishedData.location || publishedData.address || baseData.propertyDetails.location
      }
    },
    
    // 联系信息（PropertyDetailData中没有landlord字段，需要通过其他方式处理）
    // 可以添加到propertyDetails中或创建自定义字段
    
    // 发布状态和时间戳（企业级状态管理）
    ...(publishedData.status && { status: publishedData.status }),
    ...(publishedData.publishedAt && { publishedAt: publishedData.publishedAt }),
    
    // 房东ID
    ...(publishedData.landlordId && { landlordId: publishedData.landlordId }),
    
    // 合并AI标签到房源数据中
    aiTags: publishedData.aiTags || [],
  };

  // 媒体信息智能合并
  if (publishedData.images || publishedData.video) {
    const publishedImages = publishedData.images?.map((url, index) => ({
      id: `published-${index}`,
      type: 'image' as const,
      url,
      title: `房源图片${index + 1}`,
      description: '房东发布的图片',
      isPublished: true, // 标记为发布数据
    })) || [];

    const publishedVideos = publishedData.video ? [{
      id: 'published-video',
      type: 'video' as const,
      url: publishedData.video,
      title: '房源视频',
      description: '房东发布的视频',
      isPublished: true, // 标记为发布数据
    }] : [];

    mergedData.media = {
      ...baseData.media,
      // 发布的图片优先显示
      images: publishedImages.length > 0 ? 
        [...publishedImages, ...baseData.media.images] : 
        baseData.media.images,
      // 发布的视频优先显示
      videos: publishedVideos.length > 0 ? 
        [...publishedVideos, ...(baseData.media.videos || [])] : 
        baseData.media.videos || [],
      // 更新总数
      totalCount: (publishedImages.length || 0) + (publishedVideos.length || 0) + (baseData.media.totalCount || 0)
    };
  }

  // 标签智能合并和去重
  const allTags = [
    ...(publishedData.tags || []),
    ...(publishedData.aiTags || []),
    ...(baseData.propertyDetails?.tags || [])
  ];
  
  // 去重并限制数量，同时更新到propertyDetails中
  const uniqueTags = Array.from(new Set(allTags)).slice(0, 10);
  mergedData.propertyDetails = {
    ...baseData.propertyDetails,
    tags: uniqueTags
  };

  return mergedData;
};

/**
 * 验证房源数据完整性
 * 
 * @param data 房源数据
 * @returns 验证结果
 */
export const validatePropertyData = (data: PropertyDetailData | null): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (!data) {
    errors.push('房源数据为空');
    return { isValid: false, errors };
  }

  if (!data.id) {
    errors.push('房源ID缺失');
  }

  if (!data.title) {
    errors.push('房源标题缺失');
  }

  if (!data.propertyDetails) {
    errors.push('房源详情缺失');
  }

  if (!data.media) {
    errors.push('媒体数据缺失');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 格式化房源价格显示
 * 
 * @param propertyData 房源数据
 * @returns 格式化的价格字符串
 */
export const formatPropertyPrice = (propertyData: PropertyDetailData): string => {
  const { keyInfo } = propertyData;
  
  if (!keyInfo) {
    return '价格面议';
  }

  // 租赁价格
  if (keyInfo.rent?.price) {
    return `${keyInfo.rent.price} / ${keyInfo.rent.term || '月'}`;
  }

  // 销售价格
  if (keyInfo.sale?.price) {
    return `${keyInfo.sale.price}${keyInfo.sale.unit || ''}`;
  }

  return '价格面议';
};

/**
 * 格式化房源面积显示
 * 
 * @param propertyData 房源数据
 * @returns 格式化的面积字符串
 */
export const formatPropertyArea = (propertyData: PropertyDetailData): string => {
  const { keyInfo } = propertyData;
  
  if (!keyInfo?.area) {
    return '面积待定';
  }

  return `${keyInfo.area}m²`;
};

/**
 * 获取房源类型显示名称
 * 
 * @param propertyType 房源类型
 * @returns 显示名称
 */
export const getPropertyTypeDisplayName = (propertyType: string): string => {
  const typeMap: Record<string, string> = {
    'OFFICE': '写字楼',
    'SHOP': '商铺',
    'FACTORY': '厂房',
    'CLUBHOUSE': '会所',
    'MEETING_ROOM': '会议室',
    'LAND': '土地',
    'TERRACE': '露台',
  };

  return typeMap[propertyType] || propertyType;
};

/**
 * 获取房源状态显示名称
 * 
 * @param status 房源状态
 * @returns 显示名称
 */
export const getPropertyStatusDisplayName = (status: string): string => {
  const statusMap: Record<string, string> = {
    'ACTIVE': '可租售',
    'PENDING': '审核中',
    'RENTED': '已出租',
    'SOLD': '已售出',
    'SUSPENDED': '暂停',
    'EXPIRED': '已过期',
  };

  return statusMap[status] || status;
};

/**
 * 计算房源标签优先级
 * 
 * @param tags 标签数组
 * @returns 按优先级排序的标签
 */
export const sortTagsByPriority = (tags: string[]): string[] => {
  const priorityMap: Record<string, number> = {
    '地铁口': 10,
    '精装修': 9,
    '商务区': 8,
    '停车位': 7,
    '电梯': 6,
    '空调': 5,
    '独立卫生间': 4,
    '采光好': 3,
  };

  return [...tags].sort((a, b) => {
    const priorityA = priorityMap[a] || 0;
    const priorityB = priorityMap[b] || 0;
    return priorityB - priorityA;
  });
};

/**
 * 生成分享链接
 * 
 * @param propertyId 房源ID
 * @returns 分享链接
 */
export const generateShareLink = (propertyId: string): string => {
  return `https://app.example.com/property/${propertyId}`;
};

/**
 * 计算媒体总数
 * 
 * @param media 媒体数据
 * @returns 总数
 */
export const calculateMediaCount = (media: PropertyDetailData['media']): number => {
  if (!media) {
    return 0;
  }

  const imageCount = media.images?.length || 0;
  const videoCount = media.videos?.length || 0;
  
  return imageCount + videoCount;
};

/**
 * 检查是否有媒体数据
 * 
 * @param media 媒体数据
 * @returns 是否有媒体
 */
export const hasMediaData = (media: PropertyDetailData['media']): boolean => {
  return calculateMediaCount(media) > 0;
};

/**
 * 获取默认图片URL
 * 
 * @param propertyType 房源类型
 * @returns 默认图片URL
 */
export const getDefaultImageUrl = (propertyType: string): string => {
  const defaultImages: Record<string, string> = {
    'OFFICE': 'https://via.placeholder.com/400x300/f0f0f0/666?text=写字楼',
    'SHOP': 'https://via.placeholder.com/400x300/f0f0f0/666?text=商铺',
    'FACTORY': 'https://via.placeholder.com/400x300/f0f0f0/666?text=厂房',
    'CLUBHOUSE': 'https://via.placeholder.com/400x300/f0f0f0/666?text=会所',
    'MEETING_ROOM': 'https://via.placeholder.com/400x300/f0f0f0/666?text=会议室',
    'LAND': 'https://via.placeholder.com/400x300/f0f0f0/666?text=土地',
    'TERRACE': 'https://via.placeholder.com/400x300/f0f0f0/666?text=露台',
  };

  return defaultImages[propertyType] || 'https://via.placeholder.com/400x300/f0f0f0/666?text=房源';
};

/**
 * 从发布数据创建房源详情数据（用于PENDING状态房源）
 * 当API无法访问时，使用发布数据构建详情页所需的数据结构
 * 
 * @param publishedData 发布时的数据
 * @returns 房源详情页数据结构
 */
export const fromPublishedData = (publishedData: PublishedPropertyData): PropertyDetailData => {
  return {
    id: String(publishedData.id || publishedData.propertyId || ''),
    title: publishedData.title || '新发布房源',
    
    // 媒体数据结构
    media: {
      displayTab: '图片' as const,
      currentIndex: 0,
      images: publishedData.images?.map((img, index) => ({
        id: String(index),
        type: 'image' as const,
        url: img,
        title: `房源图片${index + 1}`,
        description: '',
      })) || [],
      videos: [],
      totalCount: publishedData.images?.length || 0,
    },
    
    // 关键信息
    keyInfo: {
      area: String(publishedData.area || publishedData.total_area || 0),
      transferFee: publishedData.transfer_price ? `${publishedData.transfer_price}万` : '面议',
      rent: {
        price: String(publishedData.rent_price || 0),
        term: '月'
      },
      sale: {
        price: String(publishedData.sale_price || 0),
        unit: '万'
      }
    },
    
    // 房源详情
    propertyDetails: {
      location: publishedData.address || '地址信息',
      type: publishedData.property_type || '商业房源',
      floor: publishedData.floor ? `${publishedData.floor}层` : '楼层信息待定',
      industry: publishedData.industry || '多种经营',
      status: publishedData.status || '可租售',
      specs: `面积${publishedData.area || publishedData.total_area || 0}㎡`,
      paymentTerm: '付款方式面议',
      leaseRemaining: '租期面议',
      customerFlow: '客流信息待补充',
      tags: publishedData.tags || [],
      discountLink: '优惠咨询>',
    },
    
    // 描述标签页
    descriptionTabs: {
      tabs: ['房源详情', '楼盘情况'],
      content: publishedData.description || '暂无详细描述'
    },
    
    // 操作按钮
    actionButtons: {
      favorite: { icon: 'heart', text: '收藏' },
      track: { icon: 'eye', text: '追踪' },
      contact: { text: '立即沟通', style: 'primary' as const },
      reserve: { text: '预约看房', subText: '系统推荐', style: 'secondary' as const }
    }
  };
};

/**
 * 数据转换工具集合
 */
export const PropertyDetailTransformer = {
  mergePublishedData,
  fromPublishedData,
  validatePropertyData,
  formatPropertyPrice,
  formatPropertyArea,
  getPropertyTypeDisplayName,
  getPropertyStatusDisplayName,
  sortTagsByPriority,
  generateShareLink,
  calculateMediaCount,
  hasMediaData,
  getDefaultImageUrl,
} as const;