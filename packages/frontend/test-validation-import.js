// 测试在React Native环境中动态导入validationSchema
// 这个测试模拟了usePropertyFormValidation hook中的动态导入逻辑

console.log('开始测试validationSchema的动态导入...');

async function testDynamicImport() {
  try {
    console.log('1. 尝试动态导入 validationSchema.ts...');
    
    // 模拟真实的动态导入路径
    const importPath = './src/screens/Publish/PropertyDetailFormScreen/hooks/validationSchema.ts';
    
    // 这个模拟了react-native环境中的动态导入行为
    const moduleImport = await import(importPath);
    
    console.log('2. 动态导入成功');
    console.log('3. 导入的模块keys:', Object.keys(moduleImport));
    
    // 检查具体的导出
    const { propertyFormSchema, defaultFormValues, PropertyFormData } = moduleImport;
    
    console.log('4. 检查导出的内容:');
    console.log('   - propertyFormSchema:', typeof propertyFormSchema);
    console.log('   - defaultFormValues:', typeof defaultFormValues);
    console.log('   - PropertyFormData:', typeof PropertyFormData);
    
    // 检查schema的基本功能
    if (propertyFormSchema) {
      console.log('5. 测试schema的基本功能...');
      
      // 简单的schema验证测试
      const testData = {
        title: 'Test Property',
        property_certificate_address: 'Test Address',
        sub_type: 'SINGLE_ROOM',
        area: '50',
        floor: '5',
        total_floors: '10',
        orientation: 'SOUTH',
        decoration_level: 'SIMPLE',
        transaction_types: ['RENT'],
        description: 'This is a test property description with enough characters.',
        rent_price: '2000'
      };
      
      try {
        const result = propertyFormSchema.safeParse(testData);
        console.log('   - schema.safeParse() 测试:', result.success ? '通过' : '失败');
        if (!result.success) {
          console.log('   - 错误:', result.error.issues.slice(0, 3)); // 只显示前3个错误
        }
      } catch (schemaError) {
        console.log('   - schema测试失败:', schemaError.message);
      }
    }
    
    // 检查defaultFormValues
    if (defaultFormValues) {
      console.log('6. 检查defaultFormValues结构:');
      console.log('   - 字段数量:', Object.keys(defaultFormValues).length);
      console.log('   - 包含title:', 'title' in defaultFormValues);
      console.log('   - 包含area:', 'area' in defaultFormValues);
      console.log('   - 包含transaction_types:', 'transaction_types' in defaultFormValues);
    }
    
    return { success: true, moduleImport };
    
  } catch (error) {
    console.error('动态导入失败:', error);
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    
    if (error.stack) {
      console.error('错误堆栈:', error.stack.split('\n').slice(0, 5).join('\n'));
    }
    
    return { success: false, error };
  }
}

// 运行测试
testDynamicImport().then(result => {
  console.log('\n=== 测试结果 ===');
  if (result.success) {
    console.log('✅ 动态导入测试成功');
    console.log('文件可以正常被动态导入，导出的内容可以正常使用');
  } else {
    console.log('❌ 动态导入测试失败');
    console.log('这可能是问题的根源');
  }
}).catch(error => {
  console.error('❌ 测试脚本本身失败:', error);
});