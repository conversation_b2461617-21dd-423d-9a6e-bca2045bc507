/**
 * FormHeader 样式定义
 * @fileoverview 表单头部组件的样式
 * <AUTHOR> Assistant
 * @since 2025-08-01
 */

import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../../../shared/utils/responsiveUtils';

export const styles = StyleSheet.create({
  // === 头部导航样式 ===
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(20),
    paddingVertical: hp(15),
    backgroundColor: 'transparent',
  },

  backButton: {
    width: wp(40),
    height: wp(40),
    borderRadius: wp(20),
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  headerTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
    flex: 1,
  },

  headerRight: {
    width: wp(40),
    height: wp(40),
    alignItems: 'center',
    justifyContent: 'center',
  },

  // === 标题区域样式 ===
  titleSection: {
    paddingHorizontal: wp(20),
    paddingBottom: hp(20),
    alignItems: 'center',
  },

  subtitle: {
    fontSize: fp(16),
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: '500',
    opacity: 0.9,
  },

  // === 进度指示器样式 ===
  progressContainer: {
    marginTop: hp(15),
    alignItems: 'center',
    width: '100%',
  },

  progressBar: {
    width: '80%',
    height: hp(4),
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: hp(2),
    overflow: 'hidden',
  },

  progressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: hp(2),
  },

  progressText: {
    fontSize: fp(12),
    color: '#FFFFFF',
    marginTop: hp(5),
    opacity: 0.8,
  },
});
