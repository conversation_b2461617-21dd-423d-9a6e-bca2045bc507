/**
 * BasicInfoSection 样式定义
 * @fileoverview 基础信息区块组件的样式
 * <AUTHOR> Assistant
 * @since 2025-08-01
 */

import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../../../shared/utils/responsiveUtils';

export const styles = StyleSheet.create({
  // === 主容器样式 ===
  container: {
    marginBottom: hp(30),
  },

  // === 区块标题样式 ===
  sectionTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#333333',
    marginBottom: hp(15),
    marginTop: hp(20),
  },

  // === 需求类型选择样式 ===
  demandTypeContainer: {
    flexDirection: 'row',
    marginBottom: hp(20),
    backgroundColor: '#F8F9FA',
    borderRadius: wp(12),
    padding: wp(4),
  },

  demandTypeButton: {
    flex: 1,
    paddingVertical: hp(12),
    paddingHorizontal: wp(20),
    borderRadius: wp(8),
    alignItems: 'center',
    justifyContent: 'center',
  },

  demandTypeButtonSelected: {
    backgroundColor: '#FF6B35',
    shadowColor: '#FF6B35',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },

  demandTypeText: {
    fontSize: fp(16),
    fontWeight: '500',
    color: '#666666',
  },

  demandTypeTextSelected: {
    color: '#FFFFFF',
    fontWeight: '600',
  },

  // === 字段容器样式 ===
  fieldContainer: {
    marginBottom: hp(20),
  },

  fieldLabel: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#333333',
    marginBottom: hp(8),
  },

  requiredMark: {
    color: '#FF4444',
  },
});
