/**
 * CacheInvalidationService - 缓存失效服务
 * @fileoverview 智能管理React Query缓存失效
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🎯 功能：
 * - 根据实体类型自动失效相关缓存
 * - 支持批量缓存失效
 * - 避免过度失效影响性能
 */

import { QueryClient } from '@tanstack/react-query';

// 实体类型定义
export type EntityType = 'demand' | 'property' | 'user';

// 缓存失效配置
export interface InvalidationConfig {
  entityType: EntityType;
  entityId?: string;
  action: 'create' | 'update' | 'delete' | 'statusChange';
  customQueries?: string[];
  skipQueries?: string[];
}

// 缓存键模式定义
interface CacheKeyPatterns {
  [entityType: string]: {
    list: string[];
    detail: string[];
    related: string[];
  };
}

/**
 * 缓存失效服务类 - 单例模式
 */
class CacheInvalidationServiceClass {
  private static instance: CacheInvalidationServiceClass | null = null;
  private queryClient: QueryClient | null = null;
  private isInitialized = false;

  private constructor() {
    // 私有构造函数，防止外部实例化
  }

  /**
   * 获取单例实例（按需初始化）
   */
  public static getInstance(
    queryClient?: QueryClient
  ): CacheInvalidationServiceClass {
    if (!CacheInvalidationServiceClass.instance) {
      CacheInvalidationServiceClass.instance =
        new CacheInvalidationServiceClass();
    }

    // 如果提供了queryClient且还未初始化，则进行初始化
    if (queryClient && !CacheInvalidationServiceClass.instance.isInitialized) {
      CacheInvalidationServiceClass.instance.initialize(queryClient);
    }

    return CacheInvalidationServiceClass.instance;
  }

  // 缓存键模式配置
  private cacheKeyPatterns: CacheKeyPatterns = {
    demand: {
      list: [
        'demands', // 需求列表（复数形式）
        'demand', // 需求列表（单数形式，UniversalTabList使用）
        'myDemands', // 我的需求
        'demandStats', // 需求统计
        'demandSearch', // 需求搜索
        'detailed-demand-stats', // 详细统计
        'demand-status-counts', // 需求状态计数（MyDemandsScreen使用）
        'demand-statistics', // 需求统计数据（新Hook系统使用）
      ],
      detail: [
        'demandDetail', // 需求详情
        'demandMatches', // 需求匹配
      ],
      related: [
        'userProfile', // 用户资料（包含需求统计）
        'notifications', // 通知（可能包含需求相关）
        'recentActivity', // 最近活动
        'user-roles', // 用户角色信息
        'business-messages', // 业务消息
        'favorites', // 收藏列表（需求状态变化可能影响收藏）
      ],
    },
    property: {
      list: [
        'properties', // 房源列表
        'property', // 房源列表（单数形式，UniversalTabList使用）
        'myProperties', // 我的房源
        'propertyStats', // 房源统计
        'propertySearch', // 房源搜索
        'detailed-property-stats', // 详细统计（MyPropertiesScreen使用）
        'property-status-counts', // 房源状态计数（MyPropertiesScreen使用）
        'property-statistics', // 房源统计数据（新Hook系统使用）
      ],
      detail: [
        'propertyDetail', // 房源详情
        'propertyMatches', // 房源匹配
        'propertyImages', // 房源图片
      ],
      related: [
        'userProfile', // 用户资料（包含房源统计）
        'notifications', // 通知（可能包含房源相关）
        'recentActivity', // 最近活动
        'user-roles', // 用户角色信息
        'business-messages', // 业务消息
        'favorites', // 收藏列表（房源状态变化可能影响收藏）
        'sdui-config', // SDUI配置（房源更新可能影响显示配置）
        '房源配置', // 房源UI配置
      ],
    },
    user: {
      list: [
        'userList', // 用户列表（管理员）
      ],
      detail: [
        'userProfile', // 用户详情
        'userStats', // 用户统计
      ],
      related: [
        'myDemands', // 我的需求
        'myProperties', // 我的房源
        'notifications', // 通知
      ],
    },
  };

  /**
   * 初始化服务
   */
  private initialize(queryClient: QueryClient): void {
    if (this.isInitialized) return;

    this.queryClient = queryClient;
    this.isInitialized = true;
    console.log('🗄️ [CacheInvalidation] 按需初始化缓存失效服务');
  }

  /**
   * 根据实体更新失效相关缓存
   */
  async invalidateByEntity(
    entityType: EntityType,
    entityId: string,
    action: 'create' | 'update' | 'delete' | 'statusChange' = 'update'
  ): Promise<void> {
    if (!this.queryClient) {
      console.warn('🗄️ [CacheInvalidation] QueryClient未初始化');
      return;
    }

    console.log(
      `🗄️ [CacheInvalidation] 失效缓存: ${entityType}/${entityId} (${action})`
    );

    const patterns = this.cacheKeyPatterns[entityType];
    if (!patterns) {
      console.warn(`🗄️ [CacheInvalidation] 未知实体类型: ${entityType}`);
      return;
    }

    const invalidationPromises: Promise<void>[] = [];

    // 1. 失效列表缓存（总是需要）
    patterns.list.forEach(queryKey => {
      // 失效所有以该键开头的查询（包括带状态参数的查询）
      invalidationPromises.push(
        this.queryClient!.invalidateQueries({
          predicate: query => {
            const key = query.queryKey;
            const matches =
              Array.isArray(key) && key.length > 0 && key[0] === queryKey;
            if (matches) {
              console.log(
                `🗄️ [CacheInvalidation] 匹配到查询键: ${JSON.stringify(key)}`
              );
            }
            return matches;
          },
        })
      );
    });

    // 2. 失效详情缓存（更新和删除时需要）
    if (
      action === 'update' ||
      action === 'delete' ||
      action === 'statusChange'
    ) {
      patterns.detail.forEach(queryKey => {
        invalidationPromises.push(
          this.queryClient!.invalidateQueries({
            queryKey: [queryKey, entityId],
          })
        );
      });
    }

    // 3. 失效相关缓存（根据操作类型决定）
    if (
      action === 'create' ||
      action === 'delete' ||
      action === 'statusChange'
    ) {
      patterns.related.forEach(queryKey => {
        invalidationPromises.push(
          this.queryClient!.invalidateQueries({ queryKey: [queryKey] })
        );
      });
    }

    // 4. 特殊处理：状态变更时的额外失效
    if (action === 'statusChange') {
      await this.handleStatusChangeInvalidation(entityType, entityId);
    }

    // 执行所有失效操作
    await Promise.allSettled(invalidationPromises);

    console.log(
      `🗄️ [CacheInvalidation] 缓存失效完成: ${invalidationPromises.length}个查询`
    );
  }

  /**
   * 批量失效缓存
   */
  async invalidateBatch(configs: InvalidationConfig[]): Promise<void> {
    if (!this.queryClient) {
      console.warn('🗄️ [CacheInvalidation] QueryClient未初始化');
      return;
    }

    console.log(`🗄️ [CacheInvalidation] 批量失效缓存: ${configs.length}个配置`);

    const invalidationPromises = configs.map(config =>
      this.invalidateByEntity(
        config.entityType,
        config.entityId || '',
        config.action
      )
    );

    await Promise.allSettled(invalidationPromises);
  }

  /**
   * 自定义缓存失效
   */
  async invalidateCustom(queryKeys: string[][]): Promise<void> {
    if (!this.queryClient) {
      console.warn('🗄️ [CacheInvalidation] QueryClient未初始化');
      return;
    }

    console.log(`🗄️ [CacheInvalidation] 自定义失效缓存:`, queryKeys);

    const invalidationPromises = queryKeys.map(queryKey =>
      this.queryClient!.invalidateQueries({ queryKey })
    );

    await Promise.allSettled(invalidationPromises);
  }

  /**
   * 失效所有缓存（慎用）
   */
  async invalidateAll(): Promise<void> {
    if (!this.queryClient) {
      console.warn('🗄️ [CacheInvalidation] QueryClient未初始化');
      return;
    }

    console.log('🗄️ [CacheInvalidation] 失效所有缓存');
    await this.queryClient.invalidateQueries();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): any {
    if (!this.queryClient) {
      return null;
    }

    const cache = this.queryClient.getQueryCache();
    const queries = cache.getAll();

    return {
      totalQueries: queries.length,
      staleQueries: queries.filter(q => q.isStale()).length,
      fetchingQueries: queries.filter(q => q.isFetching()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
    };
  }

  // === 私有方法 ===

  /**
   * 处理状态变更时的特殊缓存失效
   */
  private async handleStatusChangeInvalidation(
    entityType: EntityType,
    entityId: string
  ): Promise<void> {
    if (!this.queryClient) return;

    // 状态变更会影响分类列表，需要失效所有相关的分类查询
    const statusRelatedQueries = [
      // 按状态分类的列表
      [`${entityType}s`, 'active'],
      [`${entityType}s`, 'inactive'],
      [`${entityType}s`, 'draft'],
      [`${entityType}s`, 'expired'],
      // 我的内容按状态分类
      [
        `my${entityType.charAt(0).toUpperCase() + entityType.slice(1)}s`,
        'active',
      ],
      [
        `my${entityType.charAt(0).toUpperCase() + entityType.slice(1)}s`,
        'inactive',
      ],
      [
        `my${entityType.charAt(0).toUpperCase() + entityType.slice(1)}s`,
        'draft',
      ],
      // 统计数据
      [`${entityType}Stats`],
      ['userStats'],
      // 🔧 添加详细统计数据（MyPropertiesScreen和MyDemandsScreen使用）
      [`detailed-${entityType}-stats`],
      // 🔧 添加收藏相关（状态变更可能影响收藏显示）
      ['favorites'],
    ];

    const promises = statusRelatedQueries.map(queryKey =>
      this.queryClient!.invalidateQueries({ queryKey })
    );

    await Promise.allSettled(promises);
  }
}

// 导出单例访问器
export const CacheInvalidationService = {
  getInstance: (queryClient?: QueryClient) =>
    CacheInvalidationServiceClass.getInstance(queryClient),
  // 兼容性方法，直接代理到实例
  invalidateByEntity: (...args: any[]) =>
    CacheInvalidationServiceClass.getInstance().invalidateByEntity(...args),
  invalidateBatch: (...args: any[]) =>
    CacheInvalidationServiceClass.getInstance().invalidateBatch(...args),
  invalidateCustom: (...args: any[]) =>
    CacheInvalidationServiceClass.getInstance().invalidateCustom(...args),
  invalidateAll: () =>
    CacheInvalidationServiceClass.getInstance().invalidateAll(),
  getCacheStats: () =>
    CacheInvalidationServiceClass.getInstance().getCacheStats(),
};

// React Hook 用于在组件中使用缓存失效
import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';

/**
 * React Hook：使用缓存失效服务
 */
export const useCacheInvalidation = () => {
  const queryClient = useQueryClient();

  // 按需获取服务实例
  const serviceInstance = useMemo(() => {
    return CacheInvalidationServiceClass.getInstance(queryClient);
  }, [queryClient]);

  const invalidateEntity = useCallback(
    (
      entityType: EntityType,
      entityId: string,
      action: 'create' | 'update' | 'delete' | 'statusChange' = 'update'
    ) => {
      return serviceInstance.invalidateByEntity(entityType, entityId, action);
    },
    [serviceInstance]
  );

  const invalidateCustom = useCallback(
    (queryKeys: string[][]) => {
      return serviceInstance.invalidateCustom(queryKeys);
    },
    [serviceInstance]
  );

  return {
    invalidateEntity,
    invalidateCustom,
    invalidateAll: serviceInstance.invalidateAll.bind(serviceInstance),
    getCacheStats: serviceInstance.getCacheStats.bind(serviceInstance),
  };
};
