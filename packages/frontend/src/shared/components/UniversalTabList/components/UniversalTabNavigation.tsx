/**
 * 🏗️ 企业级架构：通用Tab导航组件
 * 遵循前端企业级架构规范 - UI层
 * 参考主流APP设计：微信、支付宝、淘宝的Tab导航
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { TabConfig, TabStatus, TabListTheme, defaultTheme } from '../types';
import { spacing, fontSize, borderRadius } from '../../../../shared/utils/responsiveUtils';

interface UniversalTabNavigationProps {
  tabs: TabConfig[];
  activeTab: TabStatus;
  onTabPress: (tab: TabStatus) => void;
  onSearchPress: () => void;
  theme?: TabListTheme;
  showSearch?: boolean;
}

export const UniversalTabNavigation: React.FC<UniversalTabNavigationProps> = ({
  tabs,
  activeTab,
  onTabPress,
  onSearchPress,
  theme = defaultTheme,
  showSearch = true,
}) => {
  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      {/* Tab列表 */}
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tab,
            activeTab === tab.key && styles.activeTab
          ]}
          onPress={() => onTabPress(tab.key)}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            activeTab === tab.key && styles.activeTabText
          ]}>
            {tab.title}
          </Text>
          <Text style={[
            styles.tabCount,
            activeTab === tab.key && styles.activeTabCount
          ]}>
            {tab.count}
          </Text>
        </TouchableOpacity>
      ))}

      {/* 搜索按钮 */}
      {showSearch && (
        <TouchableOpacity
          style={styles.searchButton}
          onPress={onSearchPress}
          activeOpacity={0.7}
        >
          <Ionicons 
            name="search" 
            size={20} 
            color={theme.search.iconColor} 
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const createStyles = (theme: TabListTheme) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: theme.tab.activeBackground,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs, // 🔧 企业级响应式：减少垂直间距
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    alignItems: 'center',
    minHeight: 50, // 🔧 企业级响应式：确保Tab导航有合适高度
  },
  
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  
  activeTab: {
    borderBottomColor: theme.primary,
  },
  
  tabText: {
    fontSize: fontSize.base,
    color: theme.text.secondary,
    fontWeight: '500',
  },
  
  activeTabText: {
    color: theme.text.active,
    fontWeight: '600',
  },
  
  tabCount: {
    fontSize: fontSize.sm,
    color: theme.secondary,
    fontWeight: '400',
  },
  
  activeTabCount: {
    color: theme.primary,
    fontWeight: '600',
  },
  
  searchButton: {
    padding: spacing.sm,
    borderRadius: borderRadius.md,
    backgroundColor: theme.search.background,
    marginLeft: spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 40,
    minHeight: 40,
  },
});
