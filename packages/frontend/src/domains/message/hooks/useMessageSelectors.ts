/**
 * 消息Store选择器Hook
 * 提供优化的选择器，避免不必要的重渲染，符合企业级架构规范
 */
// 移除useCallback导入，避免选择器循环问题
import { useMessageStore } from '../stores/MessageStore';
import type { MessageCategory } from '../types/businessMessage.types';

/**
 * 基础状态选择器
 */
export const useMessageBasicState = () => {
  return useMessageStore((state) => ({
    activeRole: state.activeRole,
    activeCategory: state.activeCategory,
    isDataEnabled: state.isDataEnabled,
  }));
};

/**
 * 数据状态选择器
 */
export const useMessageData = () => {
  return useMessageStore((state) => ({
    userRoles: state.userRoles,
    messageData: state.messageData,
    paymentInfo: state.paymentInfo,
    interactedUsers: state.interactedUsers,
    smsAccount: state.smsAccount,
  }));
};

/**
 * 加载状态选择器
 */
export const useMessageLoadingState = () => {
  return useMessageStore((state) => ({
    isLoadingUserRoles: state.isLoadingUserRoles,
    isLoadingMessages: state.isLoadingMessages,
    isLoadingPayment: state.isLoadingPayment,
    isLoadingUsers: state.isLoadingUsers,
    isLoadingSMS: state.isLoadingSMS,
    // 修复：在选择器中计算isLoading，而不是访问不存在的字段
    isLoading: state.isLoadingUserRoles || state.isLoadingMessages ||
               state.isLoadingPayment || state.isLoadingUsers || state.isLoadingSMS,
  }));
};

/**
 * 错误状态选择器
 */
export const useMessageErrorState = () => {
  return useMessageStore((state) => ({
    userRolesError: state.userRolesError,
    messagesError: state.messagesError,
    paymentError: state.paymentError,
    usersError: state.usersError,
    smsError: state.smsError,
    hasError: state.hasError,
  }));
};

/**
 * UI状态选择器
 */
export const useMessageUIState = () => {
  return useMessageStore((state) => ({
    refreshing: state.refreshing,
    showPaymentModal: state.showPaymentModal,
  }));
};

/**
 * 计算属性选择器 - 修复：在选择器中计算，避免Store循环
 */
export const useMessageComputedState = () => {
  return useMessageStore((state) => {
    // 在选择器中计算，避免Store内部循环
    const hasMessages = state.messageData && state.messageData.length > 0;
    const totalUnreadCount = state.messageData
      ? state.messageData.filter((msg: any) => !msg.isRead).length
      : 0;
    const isLoading = state.isLoadingUserRoles || state.isLoadingMessages ||
                     state.isLoadingPayment || state.isLoadingUsers || state.isLoadingSMS;
    const hasError = !!(state.userRolesError || state.messagesError ||
                       state.paymentError || state.usersError || state.smsError);

    return {
      hasMessages,
      totalUnreadCount,
      isLoading,
      hasError,
    };
  });
};

/**
 * 操作方法选择器
 * 修复：移除useCallback，使用稳定引用（参考7.23成功案例）
 */
export const useMessageActions = () => {
  return useMessageStore((state) => ({
    // 基础操作
    setActiveRole: state.setActiveRole,
    setActiveCategory: state.setActiveCategory,
    enableDataLoading: state.enableDataLoading,
    disableDataLoading: state.disableDataLoading,

    // 数据操作
    loadUserRoles: state.loadUserRoles,
    loadMessages: state.loadMessages,
    loadPaymentInfo: state.loadPaymentInfo,
    loadInteractedUsers: state.loadInteractedUsers,
    loadSMSAccount: state.loadSMSAccount,
    refreshAllData: state.refreshAllData,

    // UI操作
    setRefreshing: state.setRefreshing,
    setShowPaymentModal: state.setShowPaymentModal,

    // 重置操作
    resetErrors: state.resetErrors,
    resetData: state.resetData,
    resetAll: state.resetAll,
  }));
};

/**
 * 角色相关选择器
 */
export const useMessageRoleState = () => {
  return useMessageStore((state) => ({
    activeRole: state.activeRole,
    isTenantBuyerActive: state.activeRole === 'tenant_buyer',
    isLandlordActive: state.activeRole === 'landlord',
    roleDisplayName: state.activeRole === 'tenant_buyer' ? '租买消息' : '业主消息',
  }));
};

/**
 * 分类相关选择器
 */
export const useMessageCategoryState = () => {
  return useMessageStore((state) => ({
    activeCategory: state.activeCategory,
    isAllCategory: state.activeCategory === 'all',
    isInquiryCategory: state.activeCategory === 'inquiry',
    isAppointmentCategory: state.activeCategory === 'appointment',
  }));
};

/**
 * 当前角色消息选择器
 */
export const useCurrentRoleMessages = () => {
  return useMessageStore((state) => {
    if (!state.messageData) return [];

    return state.messageData.filter((msg: any) => {
      if (state.activeCategory === 'all') return true;
      return msg.category === state.activeCategory;
    });
  });
};

/**
 * 消息统计选择器
 */
export const useMessageStats = () => {
  return useMessageStore((state) => {
    const messageData = state.messageData || [];

    return {
      totalCount: messageData.length,
      unreadCount: messageData.filter((msg: any) => !msg.isRead).length,
      inquiryCount: messageData.filter((msg: any) => msg.category === 'inquiry').length,
      appointmentCount: messageData.filter((msg: any) => msg.category === 'appointment').length,
    };
  });
};

/**
 * 组合选择器 - 用于主屏幕
 */
export const useMessageScreenState = () => {
  return useMessageStore((state) => ({
    // 基础状态
    activeRole: state.activeRole,
    activeCategory: state.activeCategory,

    // 数据状态
    messageData: state.messageData,

    // 加载状态（从实际存在的字段计算）
    isLoading: state.isLoadingUserRoles || state.isLoadingMessages ||
               state.isLoadingPayment || state.isLoadingUsers || state.isLoadingSMS,
    refreshing: state.refreshing,

    // 计算属性（在选择器中计算，避免Store循环）
    hasMessages: state.messageData && state.messageData.length > 0,
    hasError: !!(state.userRolesError || state.messagesError ||
                state.paymentError || state.usersError || state.smsError),

    // UI状态
    showPaymentModal: state.showPaymentModal,
    
    // 统计信息
    totalUnreadCount: state.messageData
      ? state.messageData.filter((msg: any) => !msg.isRead).length
      : 0,
  }));
};

/**
 * 组合操作Hook - 用于主屏幕
 * 修复：使用稳定的引用，避免每次返回新对象（参考7.23成功案例）
 */
export const useMessageScreenActions = () => {
  return useMessageStore((state) => ({
    setActiveRole: state.setActiveRole,
    setActiveCategory: state.setActiveCategory,
    enableDataLoading: state.enableDataLoading,
    disableDataLoading: state.disableDataLoading,
    refreshAllData: state.refreshAllData,
    setShowPaymentModal: state.setShowPaymentModal,
    resetErrors: state.resetErrors,
  }));
};
