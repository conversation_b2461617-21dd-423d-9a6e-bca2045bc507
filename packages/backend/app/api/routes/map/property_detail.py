"""
房源详情页地图API
企业级架构 - 专门为房源详情页提供地图相关功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
from typing import List, Optional
from uuid import UUID
import logging

from app.core.db import get_async_session
from app.services.map_service import MapService
from app.core.exceptions import ValidationError, BusinessError

router = APIRouter()
logger = logging.getLogger(__name__)


class PropertyLocationInfo(BaseModel):
    """房源位置信息"""
    id: str
    title: str
    latitude: float
    longitude: float
    address: Optional[str] = None
    property_type: str
    area: Optional[float] = None
    rent_price: Optional[float] = None
    sale_price: Optional[float] = None


class NearbyProperty(BaseModel):
    """附近房源信息"""
    id: str
    title: str
    latitude: float
    longitude: float
    property_type: str
    area: Optional[float] = None
    price: Optional[float] = None
    distance_meters: float
    distance_display: str
    address: Optional[str] = None


class PropertyDetailMapResponse(BaseModel):
    """房源详情页地图响应"""
    target_property: PropertyLocationInfo
    nearby_properties: List[NearbyProperty]
    search_radius: int
    total_nearby: int
    map_center: dict
    
    class Config:
        json_schema_extra = {
            "example": {
                "target_property": {
                    "id": "123e4567-e89b-12d3-a456-426614174000",
                    "title": "CBD核心商铺出租",
                    "latitude": 22.547,
                    "longitude": 114.085,
                    "address": "深圳市福田区中心大道123号",
                    "property_type": "SHOP",
                    "area": 120.5,
                    "rent_price": 15000.0,
                    "sale_price": None
                },
                "nearby_properties": [
                    {
                        "id": "456e7890-e89b-12d3-a456-426614174001",
                        "title": "附近写字楼",
                        "latitude": 22.548,
                        "longitude": 114.086,
                        "property_type": "OFFICE",
                        "area": 200.0,
                        "price": 20000.0,
                        "distance_meters": 150.5,
                        "distance_display": "151米",
                        "address": "深圳市福田区中心大道456号"
                    }
                ],
                "search_radius": 1000,
                "total_nearby": 1,
                "map_center": {
                    "lat": 22.547,
                    "lng": 114.085
                }
            }
        }


@router.get(
    "/property/{property_id}/location",
    response_model=PropertyDetailMapResponse,
    summary="获取房源详情页地图信息",
    description="""
    获取房源详情页的地图信息，包括：
    
    **功能特点**：
    - 目标房源的精确位置信息
    - 周边同类型房源推荐
    - 距离计算和友好显示
    - 地图中心点自动计算
    
    **使用场景**：
    - 房源详情页地图展示
    - 周边房源推荐
    - 位置便利性展示
    """
)
async def get_property_detail_map(
    property_id: UUID,
    radius_meters: int = Query(1000, ge=100, le=5000, description="搜索周边房源的半径（米）"),
    nearby_limit: int = Query(10, ge=1, le=50, description="周边房源数量限制"),
    same_type_only: bool = Query(False, description="是否只显示同类型房源"),
    session: AsyncSession = Depends(get_async_session)
) -> PropertyDetailMapResponse:
    """
    获取房源详情页地图信息
    """
    try:
        logger.info(f"获取房源{property_id}的详情页地图信息")
        
        # 创建地图服务
        map_service = MapService(session)
        
        # 首先获取目标房源信息
        # 这里需要调用房源服务获取房源详情
        # 暂时使用模拟数据，后续需要集成真实的房源服务
        target_property = PropertyLocationInfo(
            id=str(property_id),
            title="目标房源标题",
            latitude=22.547,
            longitude=114.085,
            address="深圳市福田区中心大道123号",
            property_type="SHOP",
            area=120.5,
            rent_price=15000.0,
            sale_price=None
        )
        
        # 搜索周边房源
        nearby_result = await map_service.search_properties_by_location(
            center_lat=target_property.latitude,
            center_lng=target_property.longitude,
            radius_meters=radius_meters,
            limit=nearby_limit + 1,  # +1 因为结果可能包含目标房源本身
            include_distances=True
        )
        
        # 过滤掉目标房源本身，转换为附近房源格式
        nearby_properties = []
        for prop in nearby_result["properties"]:
            if prop["id"] != str(property_id):
                # 根据参数决定是否只显示同类型房源
                if same_type_only and prop["property_type"] != target_property.property_type:
                    continue
                    
                nearby_properties.append(NearbyProperty(
                    id=prop["id"],
                    title=prop["title"],
                    latitude=prop["location"]["lat"],
                    longitude=prop["location"]["lng"],
                    property_type=prop["property_type"],
                    area=prop["area"],
                    price=prop["rent_price"] or prop["sale_price"],
                    distance_meters=prop.get("distance_meters", 0),
                    distance_display=_format_distance_display(prop.get("distance_meters", 0)),
                    address=prop["address"]
                ))
        
        # 限制返回数量
        nearby_properties = nearby_properties[:nearby_limit]
        
        response = PropertyDetailMapResponse(
            target_property=target_property,
            nearby_properties=nearby_properties,
            search_radius=radius_meters,
            total_nearby=len(nearby_properties),
            map_center={
                "lat": target_property.latitude,
                "lng": target_property.longitude
            }
        )
        
        logger.info(f"房源{property_id}地图信息获取成功，找到{len(nearby_properties)}个周边房源")
        return response
        
    except ValidationError as e:
        logger.warning(f"参数验证失败: {str(e)}")
        raise HTTPException(status_code=422, detail=str(e))
    except BusinessError as e:
        logger.error(f"业务逻辑错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"获取房源地图信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get(
    "/property/{property_id}/distance",
    summary="计算房源到指定点的距离",
    description="""
    计算房源到指定坐标点的距离，用于通勤分析。

    **功能特点**：
    - 高精度PostGIS距离计算
    - 支持多种距离单位
    - 友好的距离显示格式

    **使用场景**：
    - 上班通勤距离计算
    - 房源位置便利性分析
    """
)
async def calculate_property_distance(
    property_id: UUID,
    target_lat: float = Query(..., ge=-90, le=90, description="目标点纬度"),
    target_lng: float = Query(..., ge=-180, le=180, description="目标点经度"),
    session: AsyncSession = Depends(get_async_session)
):
    """
    计算房源到指定点的距离
    """
    try:
        logger.info(f"计算房源{property_id}到目标点({target_lat}, {target_lng})的距离")

        # 创建地图服务
        map_service = MapService(session)

        # 计算距离
        distance_info = await map_service.calculate_property_distance(
            property_id=property_id,
            target_lat=target_lat,
            target_lng=target_lng
        )

        logger.info(f"距离计算完成: {distance_info['distance_display']}")
        return distance_info

    except ValidationError as e:
        logger.warning(f"参数验证失败: {str(e)}")
        raise HTTPException(status_code=422, detail=str(e))
    except BusinessError as e:
        logger.error(f"业务逻辑错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"距离计算失败: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get(
    "/property/{property_id}/nearby",
    summary="获取房源周边信息",
    description="""
    获取指定房源周边的其他房源信息。

    **功能特点**：
    - 支持按距离排序
    - 支持房源类型筛选
    - 支持价格范围筛选
    - 提供距离友好显示

    **使用场景**：
    - 房源对比功能
    - 周边房源推荐
    - 市场分析
    """
)
async def get_nearby_properties(
    property_id: UUID,
    radius_meters: int = Query(2000, ge=100, le=10000, description="搜索半径（米）"),
    property_type: Optional[str] = Query(None, description="筛选房源类型"),
    min_price: Optional[float] = Query(None, ge=0, description="最低价格"),
    max_price: Optional[float] = Query(None, ge=0, description="最高价格"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    session: AsyncSession = Depends(get_async_session)
):
    """
    获取房源周边信息（简化版）
    """
    try:
        logger.info(f"获取房源{property_id}周边信息，半径{radius_meters}米")
        
        # 这里需要先获取目标房源的位置信息
        # 暂时使用模拟坐标
        target_lat, target_lng = 22.547, 114.085
        
        # 创建地图服务
        map_service = MapService(session)
        
        # 搜索周边房源
        result = await map_service.search_properties_by_location(
            center_lat=target_lat,
            center_lng=target_lng,
            radius_meters=radius_meters,
            limit=limit + 1,  # +1 因为可能包含目标房源
            include_distances=True
        )
        
        # 过滤和处理结果
        nearby_properties = []
        for prop in result["properties"]:
            if prop["id"] != str(property_id):
                # 应用筛选条件
                if property_type and prop["property_type"] != property_type:
                    continue
                
                price = prop["rent_price"] or prop["sale_price"] or 0
                if min_price and price < min_price:
                    continue
                if max_price and price > max_price:
                    continue
                
                nearby_properties.append({
                    "id": prop["id"],
                    "title": prop["title"],
                    "location": prop["location"],
                    "property_type": prop["property_type"],
                    "area": prop["area"],
                    "price": price,
                    "distance_meters": prop.get("distance_meters", 0),
                    "distance_display": _format_distance_display(prop.get("distance_meters", 0)),
                    "address": prop["address"]
                })
        
        # 限制返回数量
        nearby_properties = nearby_properties[:limit]
        
        return {
            "nearby_properties": nearby_properties,
            "total_found": len(nearby_properties),
            "search_params": {
                "property_id": str(property_id),
                "radius_meters": radius_meters,
                "property_type": property_type,
                "price_range": {"min": min_price, "max": max_price}
            }
        }
        
    except Exception as e:
        logger.error(f"获取周边房源失败: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


def _format_distance_display(distance_meters: float) -> str:
    """格式化距离显示"""
    if distance_meters < 1000:
        return f"{int(distance_meters)}米"
    else:
        return f"{distance_meters/1000:.1f}公里"
