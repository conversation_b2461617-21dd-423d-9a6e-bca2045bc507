/**
 * 地址搜索API服务
 * 封装高德地图API调用，提供统一的数据转换和错误处理
 */

import { ApiResponse } from '../../../../../shared/types/api.types';
import {
  AddressResult,
  AddressSearchResult,
  SearchParams,
  GeocodeParams,
  ReverseGeocodeParams,
  POIResult,
} from '../types/addressSearch.types';

export class AddressSearchAPI {
  // 通过后端代理调用高德地图API，避免密钥暴露
  private static readonly BASE_URL =
    process.env.EXPO_PUBLIC_FRONTEND_API_URL || 'http://localhost:8082/api/v1';

  // 缓存配置
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟
  private static cache = new Map<string, { data: any; timestamp: number }>();

  /**
   * POI搜索 - 根据关键词搜索地点
   */
  static async searchAddress(
    params: SearchParams
  ): Promise<ApiResponse<AddressSearchResult>> {
    const cacheKey = `search_${JSON.stringify(params)}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return { success: true, data: cached };
    }

    try {
      // 使用后端POI搜索API
      const poiParams = {
        keywords: params.keywords,
        city: params.city || '南宁',
        location: params.location,
        radius: params.radius || 3000,
        page: params.page || 1,
        offset: params.offset || 20,
      };

      console.log(
        '[AddressSearchAPI.searchAddress] 使用后端POI搜索API:',
        poiParams
      );

      const response = await fetch(`${this.BASE_URL}/map/geocode/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(poiParams),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.pois) {
        // POI搜索成功，转换结果格式
        const results: AddressResult[] = data.pois.map((poi: any) => {
          const location = poi.location ? poi.location.split(',') : ['0', '0'];
          return {
            id: poi.id || `poi_${Date.now()}_${Math.random()}`,
            name: poi.name,
            address: poi.address || poi.pname + poi.cityname + poi.adname,
            formattedAddress:
              poi.address ||
              `${poi.pname || ''}${poi.cityname || ''}${poi.adname || ''}${poi.address || ''}`,
            location: {
              longitude: parseFloat(location[0]),
              latitude: parseFloat(location[1]),
            },
            district: poi.adname || '',
            citycode: poi.citycode || '',
            adcode: poi.adcode || '',
            type: poi.type || 'poi',
            typecode: poi.typecode || 'poi',
            distance: poi.distance ? parseInt(poi.distance) : 0,
          };
        });

        const result: AddressSearchResult = {
          results,
          totalCount: parseInt(data.count || '0'),
          hasMore: results.length >= (params.offset || 20),
        };

        this.setCachedData(cacheKey, result);

        return {
          success: true,
          data: result,
          message: `找到${results.length}个搜索结果`,
        };
      } else {
        throw new Error(data.message || 'POI搜索失败');
      }
    } catch (error) {
      console.error('[AddressSearchAPI.searchAddress] POI搜索失败:', error);

      return {
        success: false,
        message: error instanceof Error ? error.message : 'POI搜索失败',
        code: 'POI_SEARCH_FAILED',
        data: {
          results: [],
          totalCount: 0,
          hasMore: false,
        },
      };
    }
  }

  /**
   * 逆地理编码 - 坐标转地址
   */
  static async reverseGeocode(
    params: ReverseGeocodeParams
  ): Promise<ApiResponse<AddressResult>> {
    const cacheKey = `reverse_${JSON.stringify(params)}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return { success: true, data: cached };
    }

    try {
      const [longitude, latitude] = params.location.split(',').map(parseFloat);

      const reverseParams = {
        longitude,
        latitude,
      };

      console.log(
        '[AddressSearchAPI.reverseGeocode] 使用后端逆地理编码API:',
        reverseParams
      );

      const response = await fetch(`${this.BASE_URL}/map/geocode/reverse`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reverseParams),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.address) {
        const result: AddressResult = {
          id: `reverse_${Date.now()}`,
          name: data.address,
          address: data.address,
          location: { latitude: latitude, longitude: longitude },
          district: data.district || '',
          citycode: data.city || '',
          adcode: '',
          type: '逆地理编码结果',
          typecode: 'reverse',
          formattedAddress: data.address,
        };

        this.setCachedData(cacheKey, result);

        return {
          success: true,
          data: result,
          message: '逆地理编码成功',
        };
      } else {
        throw new Error(data.message || '逆地理编码失败');
      }
    } catch (error) {
      console.error('[AddressSearchAPI.reverseGeocode] 逆地理编码失败:', error);

      return {
        success: false,
        message: error instanceof Error ? error.message : '逆地理编码失败',
        code: 'REVERSE_GEOCODE_FAILED',
      };
    }
  }

  /**
   * 地理编码 - 地址转坐标
   */
  static async geocode(
    params: GeocodeParams
  ): Promise<ApiResponse<AddressResult>> {
    const cacheKey = `geocode_${JSON.stringify(params)}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return { success: true, data: cached };
    }

    try {
      const geocodeParams = {
        address: params.address,
        city: params.city || '南宁',
      };

      console.log(
        '[AddressSearchAPI.geocode] 使用后端地理编码API:',
        geocodeParams
      );

      const response = await fetch(`${this.BASE_URL}/map/geocode/address`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(geocodeParams),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.longitude && data.latitude) {
        const result: AddressResult = {
          id: `geocode_${Date.now()}`,
          name: data.formatted_address || params.address,
          address: data.formatted_address || params.address,
          location: {
            latitude: data.latitude,
            longitude: data.longitude,
          },
          district: data.district || '',
          citycode: data.city || '',
          adcode: '',
          type: '地理编码结果',
          typecode: 'geocode',
          formattedAddress: data.formatted_address || params.address,
        };

        this.setCachedData(cacheKey, result);

        return {
          success: true,
          data: result,
          message: '地理编码成功',
        };
      } else {
        throw new Error(data.message || '地理编码失败');
      }
    } catch (error) {
      console.error('[AddressSearchAPI.geocode] 地理编码失败:', error);

      return {
        success: false,
        message: error instanceof Error ? error.message : '地理编码失败',
        code: 'GEOCODE_FAILED',
      };
    }
  }

  /**
   * 获取附近POI
   */
  static async getNearbyPOIs(
    location: string,
    radius: number = 1000
  ): Promise<ApiResponse<POIResult[]>> {
    const cacheKey = `nearby_${location}_${radius}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return { success: true, data: cached };
    }

    try {
      // 🔧 基于官方文档修复：为避免后端空关键词检查，使用通用关键词配合types参数
      const poiParams = {
        keywords: location ? '周边' : '附近', // 使用通用关键词避免后端空值检查
        types: '050000|150000|060000|070000', // 明确指定POI类型：餐饮|交通|购物|生活服务
        location: location,
        radius: radius,
        page: 1,
        offset: 20,
      };

      console.log(
        '[AddressSearchAPI.getNearbyPOIs] 使用后端POI搜索API获取附近POI:',
        poiParams
      );

      const response = await fetch(`${this.BASE_URL}/map/geocode/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(poiParams),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.pois) {
        const results: POIResult[] = data.pois.map((poi: any) => {
          const location = poi.location ? poi.location.split(',') : ['0', '0'];
          return {
            id: poi.id,
            name: poi.name,
            address: poi.address,
            location: {
              latitude: parseFloat(location[1]),
              longitude: parseFloat(location[0]),
            },
            category: poi.type || '未知',
            distance: parseInt(poi.distance) || 0,
            rating: poi.biz_ext?.rating
              ? parseFloat(poi.biz_ext.rating)
              : undefined,
          };
        });

        this.setCachedData(cacheKey, results);

        return {
          success: true,
          data: results,
          message: `获取到${results.length}个附近POI`,
        };
      } else {
        throw new Error(data.message || '获取附近POI失败');
      }
    } catch (error) {
      console.error('[AddressSearchAPI.getNearbyPOIs] 获取附近POI失败:', error);

      return {
        success: false,
        message: error instanceof Error ? error.message : '获取附近POI失败',
        code: 'GET_NEARBY_POIS_FAILED',
        data: [],
      };
    }
  }

  /**
   * 输入提示 - 实时搜索建议
   */
  static async getInputTips(params: {
    keywords: string;
    location?: string;
    city?: string;
  }): Promise<ApiResponse<AddressResult[]>> {
    if (!params.keywords.trim()) {
      return { success: true, data: [] };
    }

    const cacheKey = `tips_${JSON.stringify(params)}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return { success: true, data: cached };
    }

    try {
      // 使用后端输入提示API
      const tipsParams = {
        keywords: params.keywords,
        city: params.city || '南宁',
        location: params.location,
        datatype: 'all',
      };

      console.log(
        '[AddressSearchAPI.getInputTips] 使用后端输入提示API:',
        tipsParams
      );

      const response = await fetch(`${this.BASE_URL}/map/geocode/tips`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tipsParams),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.tips) {
        const results: AddressResult[] = data.tips.map((tip: any) => {
          const location = tip.location ? tip.location.split(',') : ['0', '0'];
          return {
            id: tip.id || `tip_${Date.now()}_${Math.random()}`,
            name: tip.name,
            address: tip.address || tip.district,
            formattedAddress:
              tip.address || `${tip.district || ''}${tip.name || ''}`,
            location: {
              longitude: parseFloat(location[0]) || 0,
              latitude: parseFloat(location[1]) || 0,
            },
            district: tip.district || '',
            citycode: tip.citycode || '',
            adcode: tip.adcode || '',
            type: tip.typecode || 'tip',
            typecode: tip.typecode || 'tip',
          };
        });

        this.setCachedData(cacheKey, results, 2 * 60 * 1000); // 输入提示缓存2分钟

        return {
          success: true,
          data: results,
          message: `获取到${results.length}个输入提示`,
        };
      } else {
        throw new Error(data.message || '获取输入提示失败');
      }
    } catch (error) {
      console.error('[AddressSearchAPI.getInputTips] 获取输入提示失败:', error);

      return {
        success: false,
        message: error instanceof Error ? error.message : '获取输入提示失败',
        code: 'GET_INPUT_TIPS_FAILED',
        data: [],
      };
    }
  }

  /**
   * 缓存管理
   */
  private static getCachedData(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  private static setCachedData(
    key: string,
    data: any,
    duration?: number
  ): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });

    // 定期清理过期缓存
    if (this.cache.size > 100) {
      // 限制缓存大小
      const now = Date.now();
      const expireDuration = duration || this.CACHE_DURATION;

      Array.from(this.cache.entries()).forEach(([cacheKey, value]) => {
        if (now - value.timestamp > expireDuration) {
          this.cache.delete(cacheKey);
        }
      });
    }
  }

  /**
   * 清除缓存
   */
  static clearCache(): void {
    this.cache.clear();
  }
}
