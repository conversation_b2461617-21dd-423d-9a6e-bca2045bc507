import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../../shared/theme/designSystem';

interface PropertyStatsBarProps {
  totalCount: number;
  matchRate?: number;
  averagePrice?: number;
  averageArea?: number;
  onFilterPress?: () => void;
  onSortPress?: () => void;
}

export const PropertyStatsBar: React.FC<PropertyStatsBarProps> = ({
  totalCount,
  matchRate = 98,
  averagePrice = 5000,
  averageArea = 100,
  onFilterPress,
  onSortPress,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.statsContainer}>
        <View style={styles.leftStats}>
          <Text style={styles.countText}>
            求租需求：{totalCount}套次
          </Text>
          <Text style={styles.matchText}>
            {matchRate}%匹配
          </Text>
        </View>
        
        <View style={styles.rightStats}>
          <Text style={styles.priceText}>
            均价{averagePrice}元/月 {averageArea}m²
          </Text>
        </View>
      </View>
      
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={onSortPress}
        >
          <Ionicons name="swap-vertical" size={16} color={Colors.neutral[700]} />
          <Text style={styles.actionText}>排序</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.filterButton}
          onPress={onFilterPress}
        >
          <Text style={styles.filterText}>筛选功能-秒速匹配店铺</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background.primary,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral[200],
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  leftStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  countText: {
    fontSize: 14,
    color: Colors.text.primary,
    fontWeight: '500',
  },
  matchText: {
    fontSize: 12,
    color: Colors.semantic.success,
    fontWeight: '600',
  },
  rightStats: {
    alignItems: 'flex-end',
  },
  priceText: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingVertical: 4,
  },
  actionText: {
    fontSize: 12,
    color: Colors.text.primary,
  },
  filterButton: {
    flex: 1,
    backgroundColor: Colors.semantic.warning,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: 'center',
  },
  filterText: {
    fontSize: 14,
    color: Colors.background.primary,
    fontWeight: '600',
  },
});
