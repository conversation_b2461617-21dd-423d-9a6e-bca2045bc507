#!/usr/bin/env node

/**
 * 简单修复导入问题
 * 移除错误插入的FeedbackService导入，然后在正确位置重新添加
 */

const fs = require('fs');
const { execSync } = require('child_process');

console.log('🔧 开始简单修复导入问题...\n');

// 获取所有有语法错误的文件
let errorFiles = [];
try {
  execSync('npx tsc --noEmit --skipLibCheck 2>&1', { 
    cwd: '/data/my-real-estate-app/packages/frontend',
    encoding: 'utf8' 
  });
} catch (error) {
  const output = error.stdout || error.message;
  const lines = output.split('\n');
  
  lines.forEach(line => {
    const match = line.match(/^([^(]+)\(\d+,\d+\):/);
    if (match) {
      const filePath = match[1];
      if (filePath.startsWith('src/') && !errorFiles.includes(filePath)) {
        errorFiles.push(filePath);
      }
    }
  });
}

console.log(`发现 ${errorFiles.length} 个有错误的文件`);

let fixedCount = 0;

errorFiles.forEach(filePath => {
  try {
    if (!fs.existsSync(filePath)) {
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否有错误插入的FeedbackService导入
    if (content.includes('import {\nimport FeedbackService') || 
        content.includes('import FeedbackService') && content.includes('} from \'react-native\';')) {
      
      console.log(`🔧 修复文件: ${filePath}`);
      
      // 移除所有FeedbackService导入
      content = content.replace(/import\s+FeedbackService\s+from\s+['"'][^'"]+['"];?\s*\n/g, '');
      
      // 修复被破坏的import语句
      content = content.replace(/import\s+{\s*\n\s*([A-Za-z_$][A-Za-z0-9_$,\s\n]*)\s*}\s*from/g, 'import {\n  $1\n} from');
      
      // 计算正确的FeedbackService导入路径
      const pathParts = filePath.split('/');
      const depth = pathParts.length - 2; // 减去src和文件名
      const relativePath = '../'.repeat(depth) + 'shared/services/FeedbackService';
      
      // 在react-native导入后添加FeedbackService导入
      const reactNativeImportMatch = content.match(/(import\s+{[^}]*}\s+from\s+['"]react-native['"];?\s*\n)/);
      if (reactNativeImportMatch) {
        const insertPos = content.indexOf(reactNativeImportMatch[0]) + reactNativeImportMatch[0].length;
        const importStatement = `import FeedbackService from '${relativePath}';\n`;
        content = content.slice(0, insertPos) + importStatement + content.slice(insertPos);
      }
      
      // 写回文件
      fs.writeFileSync(filePath, content, 'utf8');
      fixedCount++;
      console.log(`✅ ${filePath}: 已修复`);
    }

  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
  }
});

console.log(`\n🎉 修复完成!`);
console.log(`📊 统计信息:`);
console.log(`   - 修复文件: ${fixedCount} 个`);
