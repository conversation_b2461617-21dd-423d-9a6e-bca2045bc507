#!/usr/bin/env python3
"""
成功的API测试脚本 - 正确的TestClient使用方式
"""

import sys
import traceback
import asyncio

sys.path.append('/app')

def test_app_creation():
    """测试应用创建"""
    try:
        print("🔍 测试应用创建...")
        from app.main import app
        print("✅ 应用创建成功")
        return app
    except Exception as e:
        print(f"❌ 应用创建失败: {e}")
        traceback.print_exc()
        return None

def test_basic_endpoints(app):
    """测试基础端点"""
    if not app:
        return False
    
    try:
        print("\n🔍 测试基础端点...")
        from fastapi.testclient import TestClient
        
        # 正确的TestClient使用方式 - app作为第一个位置参数
        client = TestClient(app)
        
        # 测试健康检查
        print("测试健康检查...")
        response = client.get("/health")
        print(f"健康检查状态: {response.status_code}")
        if response.status_code == 200:
            print(f"健康检查响应: {response.json()}")
        else:
            print(f"健康检查失败: {response.text}")
        
        # 测试房源列表API
        print("\n测试房源列表API...")
        response = client.get("/api/v1/properties/")
        print(f"房源列表状态: {response.status_code}")
        if response.status_code != 200:
            print(f"房源列表错误: {response.text}")
            # 打印详细错误信息
            print("详细错误信息:")
            try:
                error_detail = response.json()
                print(f"错误详情: {error_detail}")
            except:
                print(f"原始错误文本: {response.text}")
            return False
        else:
            print("✅ 房源列表API正常")
            data = response.json()
            print(f"返回数据类型: {type(data)}")
            if isinstance(data, dict):
                print(f"返回数据键: {list(data.keys())}")
                if 'items' in data:
                    print(f"房源数量: {len(data['items'])}")
                elif 'data' in data:
                    print(f"房源数量: {len(data['data'])}")
            elif isinstance(data, list):
                print(f"房源数量: {len(data)}")
            return True
            
    except Exception as e:
        print(f"❌ 端点测试失败: {e}")
        traceback.print_exc()
        return False

async def test_database_connection():
    """测试数据库连接"""
    try:
        print("\n🔍 测试数据库连接...")
        from app.core.db import get_async_session
        from app.models.property.property import Property
        from sqlmodel import select
        
        # 正确使用异步生成器
        async_session_gen = get_async_session()
        session = await async_session_gen.__anext__()
        
        try:
            print("✅ 数据库连接成功")
            
            # 测试简单查询
            result = await session.exec(select(Property).limit(1))
            properties = result.all()
            print(f"✅ 查询到 {len(properties)} 个房源")
            
            if properties:
                prop = properties[0]
                print(f"房源示例: ID={prop.id}, 标题={prop.title}")
            
            return True
        finally:
            await session.close()
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        traceback.print_exc()
        return False

def test_property_creation(app):
    """测试房源创建API"""
    if not app:
        return False
    
    try:
        print("\n🔍 测试房源创建API...")
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # 测试数据
        test_property = {
            "title": "API测试房源",
            "property_type": "OFFICE",
            "total_area": 100,
            "address": "测试地址",
            "description": "这是一个API测试房源"
        }
        
        # 不使用认证先测试
        response = client.post("/api/v1/properties", json=test_property)
        print(f"房源创建状态: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ 认证保护正常工作")
            return True
        elif response.status_code == 201:
            print("✅ 房源创建成功")
            data = response.json()
            print(f"创建的房源ID: {data.get('id')}")
            return True
        else:
            print(f"房源创建错误: {response.text}")
            try:
                error_detail = response.json()
                print(f"错误详情: {error_detail}")
            except:
                pass
            return False
            
    except Exception as e:
        print(f"❌ 房源创建测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主调试函数"""
    print("🚀 开始完整API调试...")
    
    # 1. 测试应用创建
    app = test_app_creation()
    
    # 2. 测试数据库连接
    db_ok = asyncio.run(test_database_connection())
    
    # 3. 测试基础端点
    api_ok = False
    create_ok = False
    
    if app:
        api_ok = test_basic_endpoints(app)
        create_ok = test_property_creation(app)
    
    # 总结
    print(f"\n📊 测试结果总结:")
    print(f"✅ 应用创建: {'成功' if app else '失败'}")
    print(f"✅ 数据库连接: {'成功' if db_ok else '失败'}")
    print(f"✅ API端点: {'成功' if api_ok else '失败'}")
    print(f"✅ 房源创建: {'成功' if create_ok else '失败'}")
    
    if app and db_ok and api_ok:
        print("\n🎉 核心功能测试通过！系统运行正常！")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
