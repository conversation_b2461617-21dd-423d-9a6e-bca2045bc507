#!/usr/bin/env node

/**
 * PropertyDetailScreen样式调试脚本
 * 用于验证样式修改是否正确应用
 */

const fs = require('fs');
const path = require('path');

const PROPERTY_DETAIL_FILE = path.join(__dirname, 'src/screens/Property/PropertyDetailScreen.tsx');

console.log('🔍 PropertyDetailScreen样式调试检查');
console.log('=====================================');

try {
  const content = fs.readFileSync(PROPERTY_DETAIL_FILE, 'utf8');
  
  // 检查关键样式修改
  const checks = [
    {
      name: '租金数字左右边距减半',
      pattern: /paddingHorizontal:\s*wp\(8\)/,
      description: '检查keyMetricsRowStandalone是否使用wp(8)'
    },
    {
      name: '租金左对齐样式',
      pattern: /alignItems:\s*['"]flex-start['"]/,
      description: '检查租金是否左对齐'
    },
    {
      name: '面积右对齐样式', 
      pattern: /alignItems:\s*['"]flex-end['"]/,
      description: '检查面积是否右对齐'
    },
    {
      name: '底部操作栏左右边距',
      pattern: /paddingHorizontal:\s*wp\(16\)/,
      description: '检查底部操作栏左右边距'
    },
    {
      name: '底部距离2倍',
      pattern: /paddingBottom:\s*wp\(48\)/,
      description: '检查底部距离是否为2倍'
    },
    {
      name: '按钮内部边距减半',
      pattern: /paddingHorizontal:\s*wp\(6\)/,
      description: '检查按钮内部边距是否减半'
    },
    {
      name: '强制样式应用',
      pattern: /\{\s*paddingHorizontal:\s*wp\(6\)\s*\}/,
      description: '检查是否有强制样式应用'
    }
  ];

  console.log('✅ 样式检查结果:');
  console.log('------------------');
  
  let allPassed = true;
  
  checks.forEach((check, index) => {
    const found = check.pattern.test(content);
    const status = found ? '✅ 通过' : '❌ 失败';
    console.log(`${index + 1}. ${check.name}: ${status}`);
    console.log(`   ${check.description}`);
    
    if (!found) {
      allPassed = false;
      // 尝试找到相关代码段
      const lines = content.split('\n');
      const relevantLines = lines.filter(line => 
        line.includes('paddingHorizontal') || 
        line.includes('alignItems') || 
        line.includes('paddingBottom')
      );
      
      if (relevantLines.length > 0) {
        console.log('   相关代码:');
        relevantLines.slice(0, 3).forEach(line => {
          console.log(`   ${line.trim()}`);
        });
      }
    }
    console.log('');
  });

  // 检查响应式函数使用
  console.log('📱 响应式函数使用检查:');
  console.log('------------------------');
  
  const responsiveFunctions = ['wp(', 'hp(', 'fp('];
  responsiveFunctions.forEach(func => {
    const matches = content.match(new RegExp(func.replace('(', '\\('), 'g'));
    const count = matches ? matches.length : 0;
    console.log(`${func}: ${count} 次使用`);
  });

  console.log('');
  console.log('🎯 修改建议:');
  console.log('-------------');
  
  if (!allPassed) {
    console.log('❌ 部分样式检查失败，建议:');
    console.log('1. 检查是否正确使用了wp()响应式函数');
    console.log('2. 确认样式对象中的属性名拼写正确');
    console.log('3. 验证是否有其他样式覆盖了修改的样式');
    console.log('4. 尝试重启Metro bundler: npx expo start --clear');
  } else {
    console.log('✅ 所有样式检查通过！');
    console.log('如果界面仍未更新，请尝试:');
    console.log('1. 重启Expo开发服务器: npx expo start --clear');
    console.log('2. 在设备上强制刷新应用');
    console.log('3. 检查是否有缓存问题');
  }

  // 生成样式摘要
  console.log('');
  console.log('📋 当前样式配置摘要:');
  console.log('----------------------');
  
  const styleMatches = content.match(/(\w+):\s*{[^}]+}/g);
  if (styleMatches) {
    const relevantStyles = styleMatches.filter(style => 
      style.includes('bottomActionBar') || 
      style.includes('keyMetrics') || 
      style.includes('ActionButton')
    );
    
    relevantStyles.forEach(style => {
      const styleName = style.match(/(\w+):/)[1];
      console.log(`- ${styleName}`);
    });
  }

} catch (error) {
  console.error('❌ 读取文件失败:', error.message);
  console.log('请确认文件路径正确:', PROPERTY_DETAIL_FILE);
}

console.log('');
console.log('🔧 快速修复命令:');
console.log('------------------');
console.log('cd packages/frontend && npx expo start --clear');
console.log('');
