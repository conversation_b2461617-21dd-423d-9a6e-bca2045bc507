/**
 * 活动类型定义
 * 统一管理用户行为活动的类型定义
 * 
 * @fileoverview 解决ActivityType枚举缺失问题
 * <AUTHOR> Assistant
 * @since 2025-07-09
 */

/**
 * 用户活动类型枚举
 */
export enum ActivityType {
  // 房源相关活动
  PROPERTY_VIEWED = 'property_viewed',
  PROPERTY_FAVORITED = 'property_favorited',
  PROPERTY_SHARED = 'property_shared',
  PROPERTY_INQUIRY = 'property_inquiry',
  
  // 搜索相关活动
  SEARCH_PERFORMED = 'search_performed',
  SEARCH_SAVED = 'search_saved',
  FILTER_APPLIED = 'filter_applied',
  
  // 需求相关活动
  DEMAND_CREATED = 'demand_created',
  DEMAND_UPDATED = 'demand_updated',
  DEMAND_PUBLISHED = 'demand_published',
  DEMAND_OFFLINE = 'demand_offline',
  DEMAND_COMPLETED = 'demand_completed',
  
  // 咨询相关活动
  INQUIRY_SENT = 'inquiry_sent',
  INQUIRY_RECEIVED = 'inquiry_received',
  INQUIRY_REPLIED = 'inquiry_replied',
  
  // 预约相关活动
  APPOINTMENT_BOOKED = 'appointment_booked',
  APPOINTMENT_CONFIRMED = 'appointment_confirmed',
  APPOINTMENT_CANCELLED = 'appointment_cancelled',
  APPOINTMENT_COMPLETED = 'appointment_completed',
  
  // 评价相关活动
  REVIEW_POSTED = 'review_posted',
  REVIEW_REPLIED = 'review_replied',
  RATING_GIVEN = 'rating_given',
  
  // 用户相关活动
  PROFILE_UPDATED = 'profile_updated',
  PREFERENCES_UPDATED = 'preferences_updated',
  NOTIFICATION_SETTINGS_UPDATED = 'notification_settings_updated',
  
  // 登录相关活动
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  USER_REGISTERED = 'user_registered',
  
  // 系统相关活动
  SYSTEM_NOTIFICATION = 'system_notification',
  PROMOTION_VIEWED = 'promotion_viewed',
  FEEDBACK_SUBMITTED = 'feedback_submitted',
}

/**
 * 活动状态枚举
 */
export enum ActivityStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
}

/**
 * 活动优先级枚举
 */
export enum ActivityPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

/**
 * 活动记录接口
 */
export interface ActivityRecord {
  id: string;
  userId: string;
  activityType: ActivityType;
  status: ActivityStatus;
  priority: ActivityPriority;
  title: string;
  description?: string;
  metadata?: Record<string, any>;
  relatedEntityId?: string;
  relatedEntityType?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

/**
 * 活动统计接口
 */
export interface ActivityStatistics {
  totalActivities: number;
  completedActivities: number;
  pendingActivities: number;
  failedActivities: number;
  typeBreakdown: Record<ActivityType, number>;
  statusBreakdown: Record<ActivityStatus, number>;
  priorityBreakdown: Record<ActivityPriority, number>;
}

/**
 * 活动查询参数
 */
export interface ActivityQueryParams {
  userId?: string;
  activityType?: ActivityType;
  status?: ActivityStatus;
  priority?: ActivityPriority;
  startDate?: string;
  endDate?: string;
  relatedEntityId?: string;
  relatedEntityType?: string;
  page?: number;
  size?: number;
}

/**
 * 活动创建请求
 */
export interface CreateActivityRequest {
  userId: string;
  activityType: ActivityType;
  title: string;
  description?: string;
  priority?: ActivityPriority;
  metadata?: Record<string, any>;
  relatedEntityId?: string;
  relatedEntityType?: string;
}

/**
 * 活动更新请求
 */
export interface UpdateActivityRequest {
  status?: ActivityStatus;
  priority?: ActivityPriority;
  title?: string;
  description?: string;
  metadata?: Record<string, any>;
}