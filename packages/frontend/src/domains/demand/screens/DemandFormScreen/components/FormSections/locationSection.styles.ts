/**
 * LocationSection 样式定义
 * @fileoverview 位置需求区块组件的样式
 * <AUTHOR> Assistant
 * @since 2025-08-01
 */

import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../../../shared/utils/responsiveUtils';

export const styles = StyleSheet.create({
  // === 主容器样式 ===
  container: {
    marginBottom: hp(30),
  },

  // === 区块标题样式 ===
  sectionTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#333333',
    marginBottom: hp(15),
    marginTop: hp(20),
  },

  // === 字段容器样式 ===
  fieldContainer: {
    marginBottom: hp(20),
  },
});
