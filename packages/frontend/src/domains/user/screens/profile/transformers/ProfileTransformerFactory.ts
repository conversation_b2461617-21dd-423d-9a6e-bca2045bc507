/**
 * 用户中心转换器工厂
 * 提供统一的转换器实例管理，符合企业级架构规范
 * 目标：60行
 */
import {
  UserInfoTransformer,
  TenantBuyerStatsTransformer,
  LandlordStatsTransformer,
  OrderCountsTransformer,
} from './ProfileTransformer';

/**
 * 转换器工厂类
 * 单例模式，确保转换器实例的唯一性和性能优化
 */
export class ProfileTransformerFactory {
  private static instance: ProfileTransformerFactory;
  
  // 转换器实例缓存
  private userInfoTransformer?: UserInfoTransformer;
  private tenantBuyerStatsTransformer?: TenantBuyerStatsTransformer;
  private landlordStatsTransformer?: LandlordStatsTransformer;
  private orderCountsTransformer?: OrderCountsTransformer;

  private constructor() {
    // 私有构造函数，确保单例
  }

  /**
   * 获取工厂实例
   */
  public static getInstance(): ProfileTransformerFactory {
    if (!ProfileTransformerFactory.instance) {
      ProfileTransformerFactory.instance = new ProfileTransformerFactory();
    }
    return ProfileTransformerFactory.instance;
  }

  /**
   * 获取用户信息转换器
   */
  public getUserInfoTransformer(): UserInfoTransformer {
    if (!this.userInfoTransformer) {
      this.userInfoTransformer = new UserInfoTransformer();
      console.log('[ProfileTransformerFactory] 创建UserInfoTransformer实例');
    }
    return this.userInfoTransformer;
  }

  /**
   * 获取租买统计转换器
   */
  public getTenantBuyerStatsTransformer(): TenantBuyerStatsTransformer {
    if (!this.tenantBuyerStatsTransformer) {
      this.tenantBuyerStatsTransformer = new TenantBuyerStatsTransformer();
      console.log('[ProfileTransformerFactory] 创建TenantBuyerStatsTransformer实例');
    }
    return this.tenantBuyerStatsTransformer;
  }

  /**
   * 获取业主统计转换器
   */
  public getLandlordStatsTransformer(): LandlordStatsTransformer {
    if (!this.landlordStatsTransformer) {
      this.landlordStatsTransformer = new LandlordStatsTransformer();
      console.log('[ProfileTransformerFactory] 创建LandlordStatsTransformer实例');
    }
    return this.landlordStatsTransformer;
  }

  /**
   * 获取订单统计转换器
   */
  public getOrderCountsTransformer(): OrderCountsTransformer {
    if (!this.orderCountsTransformer) {
      this.orderCountsTransformer = new OrderCountsTransformer();
      console.log('[ProfileTransformerFactory] 创建OrderCountsTransformer实例');
    }
    return this.orderCountsTransformer;
  }

  /**
   * 重置所有转换器实例（用于测试或重新初始化）
   */
  public resetTransformers(): void {
    console.log('[ProfileTransformerFactory] 重置所有转换器实例');
    this.userInfoTransformer = undefined;
    this.tenantBuyerStatsTransformer = undefined;
    this.landlordStatsTransformer = undefined;
    this.orderCountsTransformer = undefined;
  }

  /**
   * 获取所有转换器的状态信息
   */
  public getTransformerStatus(): Record<string, boolean> {
    return {
      userInfoTransformer: !!this.userInfoTransformer,
      tenantBuyerStatsTransformer: !!this.tenantBuyerStatsTransformer,
      landlordStatsTransformer: !!this.landlordStatsTransformer,
      orderCountsTransformer: !!this.orderCountsTransformer,
    };
  }
}

/**
 * 便捷的转换器获取函数
 */
export const getProfileTransformers = () => {
  const factory = ProfileTransformerFactory.getInstance();
  
  return {
    userInfo: factory.getUserInfoTransformer(),
    tenantBuyerStats: factory.getTenantBuyerStatsTransformer(),
    landlordStats: factory.getLandlordStatsTransformer(),
    orderCounts: factory.getOrderCountsTransformer(),
  };
};

/**
 * 转换器类型映射
 */
export type ProfileTransformers = ReturnType<typeof getProfileTransformers>;

/**
 * 导出单例实例
 */
export const profileTransformerFactory = ProfileTransformerFactory.getInstance();
