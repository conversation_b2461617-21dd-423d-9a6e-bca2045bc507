"""
房源Schema定义

定义房源相关的Pydantic Schema，用于API请求和响应的数据验证和序列化。
包含创建、更新、查询和响应的完整Schema定义。
"""

from datetime import datetime, date
from typing import List, Optional, Dict, Any, Union
import uuid
from pydantic import BaseModel, Field, ConfigDict, model_validator

from app.core.constants import (
    PropertyType, PropertyStatus, VerificationStatus,
    TransactionType, DecorationLevel, DeliveryStatus,
    validate_sub_type, get_sub_type_values
)



# --- 基础Schema ---

class PropertyBase(BaseModel):
    """房源基础Schema"""
    title: str = Field(..., min_length=1, max_length=100, description="房源标题")
    property_type: PropertyType = Field(..., description="房源类型")
    sub_type: Optional[str] = Field(default=None, description="房源子类型")
    total_area: float = Field(..., gt=0, description="总面积（平方米）")
    usable_area: Optional[float] = Field(default=None, gt=0, description="可使用面积（平方米）")

    @model_validator(mode='before')
    @classmethod
    def validate_sub_type_value(cls, values):
        """验证子类型是否与房源类型匹配"""
        if isinstance(values, dict) and 'property_type' in values and 'sub_type' in values:
            property_type = values['property_type']
            sub_type = values['sub_type']
            if sub_type is not None and not validate_sub_type(property_type, sub_type):
                valid_types = get_sub_type_values(property_type)
                raise ValueError(f"子类型 '{sub_type}' 与房源类型 '{property_type}' 不匹配。有效的子类型: {valid_types}")
        return values


class PropertyCreate(PropertyBase):
    """创建房源Schema"""
    # 地址信息
    address: Optional[str] = Field(default=None, max_length=200, description="房源地址")
    
    # 楼层信息
    floor: Optional[int] = Field(default=None, description="所在楼层")
    total_floors: Optional[int] = Field(default=None, gt=0, description="总楼层数")
    is_basement: bool = Field(default=False, description="是否为地下室")

    # 建筑信息
    building_name: Optional[str] = Field(default=None, max_length=100, description="建筑名称")
    building_age: Optional[int] = Field(default=None, ge=0, description="建筑年代")
    building_structure: Optional[str] = Field(default=None, max_length=50, description="建筑结构")
    building_purpose: Optional[str] = Field(default=None, max_length=100, description="建筑用途")

    # 空间特性
    orientation: Optional[str] = Field(default=None, max_length=50, description="朝向")

    # 装修与设施
    decoration_level: Optional[DecorationLevel] = Field(default=None, description="装修等级")
    decoration_year: Optional[int] = Field(default=None, ge=1900, description="装修年份")
    decoration_style: Optional[str] = Field(default=None, max_length=50, description="装修风格")
    can_redecorate: bool = Field(default=True, description="是否可重新装修")

    # 交付状态
    delivery_status: DeliveryStatus = Field(default=DeliveryStatus.IMMEDIATE, description="交付状态")
    delivery_date: Optional[date] = Field(default=None, description="预计交付日期")
    can_enter_early: bool = Field(default=False, description="是否可提前进场装修")

    # 权属信息
    property_right_type: Optional[str] = Field(default=None, max_length=50, description="产权性质")
    property_right_years: Optional[int] = Field(default=None, gt=0, description="产权年限")
    property_right_owner_type: Optional[str] = Field(default=None, max_length=50, description="产权所有人类型")
    is_shared_property: bool = Field(default=False, description="是否共有产权")

    # 物业管理
    property_company: Optional[str] = Field(default=None, max_length=100, description="物业公司名称")
    property_fee: Optional[float] = Field(default=None, ge=0, description="物业费（元/平米/月）")
    property_services: Optional[str] = Field(default=None, max_length=500, description="物业服务内容")

    # 产业政策
    policy_name: Optional[str] = Field(default=None, max_length=100, description="政策名称")
    policy_content: Optional[str] = Field(default=None, max_length=1000, description="政策内容")
    policy_documents: List[str] = Field(default=[], description="政策文件列表")
    policy_expiry_date: Optional[date] = Field(default=None, description="政策有效期")

    # 描述信息
    description: Optional[str] = Field(default=None, max_length=2000, description="详细描述")
    highlights: Optional[str] = Field(default=None, max_length=500, description="房源亮点")
    suitable_industries: Optional[str] = Field(default=None, max_length=500, description="适合行业")

    # 交易信息
    transaction_types: List[TransactionType] = Field(default=[], description="支持的交易类型")
    
    # 房源特性 (匹配前端PropertyPublishAPIRequest)
    features: Optional[Dict[str, Any]] = Field(default=None, description="房源特性数据")
    
    # 价格信息 (匹配前端PropertyPublishAPIRequest)
    prices: Optional[List[Dict[str, Any]]] = Field(default=None, description="价格信息数据")
    
    # 标签信息 (匹配前端PropertyPublishAPIRequest)
    tags: Optional[List[str]] = Field(default=None, description="房源标签")



class PropertyUpdate(BaseModel):
    """更新房源Schema"""
    title: Optional[str] = Field(default=None, min_length=1, max_length=100, description="房源标题")
    sub_type: Optional[str] = Field(default=None, description="房源子类型")
    address: Optional[str] = Field(default=None, max_length=200, description="房源地址")
    total_area: Optional[float] = Field(default=None, gt=0, description="总面积（平方米）")
    usable_area: Optional[float] = Field(default=None, gt=0, description="可使用面积（平方米）")
    floor: Optional[int] = Field(default=None, description="所在楼层")
    total_floors: Optional[int] = Field(default=None, gt=0, description="总楼层数")
    is_basement: Optional[bool] = Field(default=None, description="是否为地下室")
    building_name: Optional[str] = Field(default=None, max_length=100, description="建筑名称")
    building_age: Optional[int] = Field(default=None, ge=0, description="建筑年代")
    building_structure: Optional[str] = Field(default=None, max_length=50, description="建筑结构")
    building_purpose: Optional[str] = Field(default=None, max_length=100, description="建筑用途")
    orientation: Optional[str] = Field(default=None, max_length=50, description="朝向")
    decoration_level: Optional[DecorationLevel] = Field(default=None, description="装修等级")
    decoration_year: Optional[int] = Field(default=None, ge=1900, description="装修年份")
    decoration_style: Optional[str] = Field(default=None, max_length=50, description="装修风格")
    can_redecorate: Optional[bool] = Field(default=None, description="是否可重新装修")
    delivery_status: Optional[DeliveryStatus] = Field(default=None, description="交付状态")
    delivery_date: Optional[date] = Field(default=None, description="预计交付日期")
    can_enter_early: Optional[bool] = Field(default=None, description="是否可提前进场装修")
    property_right_type: Optional[str] = Field(default=None, max_length=50, description="产权性质")
    property_right_years: Optional[int] = Field(default=None, gt=0, description="产权年限")
    property_right_owner_type: Optional[str] = Field(default=None, max_length=50, description="产权所有人类型")
    is_shared_property: Optional[bool] = Field(default=None, description="是否共有产权")
    property_company: Optional[str] = Field(default=None, max_length=100, description="物业公司名称")
    property_fee: Optional[float] = Field(default=None, ge=0, description="物业费（元/平米/月）")
    property_services: Optional[str] = Field(default=None, max_length=500, description="物业服务内容")
    policy_name: Optional[str] = Field(default=None, max_length=100, description="政策名称")
    policy_content: Optional[str] = Field(default=None, max_length=1000, description="政策内容")
    policy_documents: Optional[List[str]] = Field(default=None, description="政策文件列表")
    policy_expiry_date: Optional[date] = Field(default=None, description="政策有效期")
    description: Optional[str] = Field(default=None, max_length=2000, description="详细描述")
    highlights: Optional[str] = Field(default=None, max_length=500, description="房源亮点")
    suitable_industries: Optional[str] = Field(default=None, max_length=500, description="适合行业")
    transaction_types: Optional[List[TransactionType]] = Field(default=None, description="支持的交易类型")


class PropertyStatusUpdate(BaseModel):
    """房源状态更新Schema"""
    status: PropertyStatus = Field(..., description="房源状态")
    verification_status: Optional[VerificationStatus] = Field(default=None, description="审核状态")
    reason: Optional[str] = Field(default=None, max_length=500, description="状态变更原因")


# --- 查询Schema ---

class PropertyQuery(BaseModel):
    """房源查询Schema"""
    property_type: Optional[PropertyType] = Field(default=None, description="房源类型")
    sub_type: Optional[str] = Field(default=None, description="房源子类型")
    status: Optional[PropertyStatus] = Field(default=None, description="房源状态")
    verification_status: Optional[VerificationStatus] = Field(default=None, description="审核状态")
    transaction_types: Optional[List[TransactionType]] = Field(default=None, description="交易类型")
    min_area: Optional[float] = Field(default=None, gt=0, description="最小面积")
    max_area: Optional[float] = Field(default=None, gt=0, description="最大面积")
    min_floor: Optional[int] = Field(default=None, description="最小楼层")
    max_floor: Optional[int] = Field(default=None, description="最大楼层")
    decoration_level: Optional[DecorationLevel] = Field(default=None, description="装修等级")
    delivery_status: Optional[DeliveryStatus] = Field(default=None, description="交付状态")
    owner_id: Optional[uuid.UUID] = Field(default=None, description="房东ID")
    keyword: Optional[str] = Field(default=None, max_length=100, description="关键词搜索")

    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")

    # 排序参数
    sort_by: Optional[str] = Field(default="created_at", description="排序字段")
    sort_order: Optional[str] = Field(default="desc", pattern="^(asc|desc)$", description="排序方向")



# --- 响应Schema ---

class PropertyResponse(PropertyBase):
    """房源响应Schema"""
    id: int = Field(..., description="房源ID")
    floor: Optional[int] = Field(default=None, description="所在楼层")
    total_floors: Optional[int] = Field(default=None, description="总楼层数")
    is_basement: bool = Field(..., description="是否为地下室")
    building_name: Optional[str] = Field(default=None, description="建筑名称")
    building_age: Optional[int] = Field(default=None, description="建筑年代")
    building_structure: Optional[str] = Field(default=None, description="建筑结构")
    building_purpose: Optional[str] = Field(default=None, description="建筑用途")
    orientation: Optional[str] = Field(default=None, description="朝向")
    decoration_level: Optional[DecorationLevel] = Field(default=None, description="装修等级")
    decoration_year: Optional[int] = Field(default=None, description="装修年份")
    decoration_style: Optional[str] = Field(default=None, description="装修风格")
    can_redecorate: bool = Field(..., description="是否可重新装修")
    delivery_status: DeliveryStatus = Field(..., description="交付状态")
    delivery_date: Optional[date] = Field(default=None, description="预计交付日期")
    can_enter_early: bool = Field(..., description="是否可提前进场装修")
    property_right_type: Optional[str] = Field(default=None, description="产权性质")
    property_right_years: Optional[int] = Field(default=None, description="产权年限")
    property_right_owner_type: Optional[str] = Field(default=None, description="产权所有人类型")
    is_shared_property: bool = Field(..., description="是否共有产权")
    property_company: Optional[str] = Field(default=None, description="物业公司名称")
    property_fee: Optional[float] = Field(default=None, description="物业费（元/平米/月）")
    property_services: Optional[str] = Field(default=None, description="物业服务内容")
    policy_name: Optional[str] = Field(default=None, description="政策名称")
    policy_content: Optional[str] = Field(default=None, description="政策内容")
    policy_documents: List[str] = Field(..., description="政策文件列表")
    policy_expiry_date: Optional[date] = Field(default=None, description="政策有效期")
    description: Optional[str] = Field(default=None, description="详细描述")
    highlights: Optional[str] = Field(default=None, description="房源亮点")
    suitable_industries: Optional[str] = Field(default=None, description="适合行业")
    status: PropertyStatus = Field(..., description="房源状态")
    verification_status: VerificationStatus = Field(..., description="审核状态")
    listing_date: Optional[datetime] = Field(default=None, description="挂牌日期")
    last_active_date: Optional[datetime] = Field(default=None, description="最后活跃日期")
    transaction_types: List[TransactionType] = Field(..., description="支持的交易类型")
    owner_id: uuid.UUID = Field(..., description="房东用户ID")
    agent_id: Optional[uuid.UUID] = Field(default=None, description="经纪人用户ID")
    property_manager_id: Optional[uuid.UUID] = Field(default=None, description="物业管理员用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)


class PropertyListItem(BaseModel):
    """房源列表项Schema"""
    id: int = Field(..., description="房源ID")
    title: str = Field(..., description="房源标题")
    property_type: PropertyType = Field(..., description="房源类型")
    sub_type: Optional[str] = Field(default=None, description="房源子类型")
    total_area: float = Field(..., description="总面积（平方米）")
    usable_area: Optional[float] = Field(default=None, description="可使用面积（平方米）")
    building_name: Optional[str] = Field(default=None, description="建筑名称")
    floor: Optional[int] = Field(default=None, description="所在楼层")
    total_floors: Optional[int] = Field(default=None, description="总楼层数")
    decoration_level: Optional[DecorationLevel] = Field(default=None, description="装修等级")
    status: PropertyStatus = Field(..., description="房源状态")
    verification_status: VerificationStatus = Field(..., description="审核状态")
    transaction_types: List[TransactionType] = Field(..., description="支持的交易类型")
    listing_date: Optional[datetime] = Field(default=None, description="挂牌日期")
    created_at: datetime = Field(..., description="创建时间")

    model_config = ConfigDict(from_attributes=True)


class PropertyListResponse(BaseModel):
    """房源列表响应Schema"""
    items: List[PropertyListItem] = Field(..., description="房源列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    pages: int = Field(..., description="总页数")

    model_config = ConfigDict(from_attributes=True)
