/**
 * 空状态组件
 * 显示登录提示或业务触发提示
 */
import React from 'react';
import {
  View,
  Text,
  Dimensions,
} from 'react-native';

// 响应式设计 - 获取屏幕尺寸
const { width: screenWidth } = Dimensions.get('window');

interface EmptyStateProps {
  activeRole: 'tenant_buyer' | 'landlord';
  isAuthenticated: boolean;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  activeRole,
  isAuthenticated,
}) => {
  if (!isAuthenticated) {
    // 未登录时显示登录提示
    return (
      <View style={styles.welcomeSection}>
        <Text style={styles.welcomeIcon}>📨</Text>
        <Text style={styles.welcomeTitle}>
          {activeRole === 'tenant_buyer' ? '租买消息中心' : '业主消息中心'}
        </Text>
        <Text style={styles.welcomeSubtitle}>
          登录后查看您的消息通知
        </Text>
      </View>
    );
  }

  // 登录后显示业务触发提示
  return (
    <View style={styles.welcomeSection}>
      <Text style={styles.welcomeIcon}>📨</Text>
      <Text style={styles.welcomeTitle}>
        {activeRole === 'tenant_buyer' ? '租买消息中心' : '业主消息中心'}
      </Text>
      <Text style={styles.welcomeSubtitle}>
        {activeRole === 'tenant_buyer'
          ? '发布求租求购信息后，消息将在这里显示'
          : '发布房源信息后，消息将在这里显示'
        }
      </Text>
    </View>
  );
};

const styles = {
  welcomeSection: {
    alignItems: 'center' as const,
    paddingVertical: screenWidth * 0.04,      // 响应式垂直内边距
    paddingHorizontal: screenWidth * 0.05,   // 响应式水平内边距
    backgroundColor: '#ffffff',
    marginBottom: screenWidth * 0.015,       // 响应式下边距，更紧凑
  },
  welcomeIcon: {
    fontSize: screenWidth * 0.08,             // 响应式图标大小
    marginBottom: screenWidth * 0.025,       // 响应式下边距，更紧凑
  },
  welcomeTitle: {
    fontSize: screenWidth * 0.045,           // 响应式标题字体
    fontWeight: '600' as const,
    color: '#333333',
    marginBottom: screenWidth * 0.012,      // 响应式下边距，更紧凑
  },
  welcomeSubtitle: {
    fontSize: screenWidth * 0.032,          // 响应式副标题字体
    color: '#666666',
    textAlign: 'center' as const,
    lineHeight: screenWidth * 0.045,        // 响应式行高
  },
};
