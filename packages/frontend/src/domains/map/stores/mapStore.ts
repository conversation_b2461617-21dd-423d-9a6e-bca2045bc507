/**
 * 地图状态管理Store - 企业级Zustand架构
 * 
 * 参考主流APP最佳实践：
 * 1. 单一数据源 (Single Source of Truth)
 * 2. 不可变状态更新 (Immutable State Updates)
 * 3. 类型安全 (Type Safety)
 * 4. 可预测的状态变更 (Predictable State Changes)
 * 5. 优秀的开发者体验 (Developer Experience)
 * 
 * 遵循项目统一状态管理规范
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { devtools } from 'zustand/middleware';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

// ===== 类型定义 =====

/**
 * 地图中心坐标
 */
export interface MapCenter {
  latitude: number;
  longitude: number;
}

/**
 * 地图标记数据
 */
export interface MapMarker {
  id: string;
  title: string;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  price: string;
  type: string;
  distance?: string;
  address?: string;
  district?: string;
  status: 'available' | 'rented' | 'sold';
}

/**
 * 搜索状态
 */
export interface SearchState {
  query: string;
  selectedPropertyType: string;
  priceRange: {
    min?: number;
    max?: number;
  };
  searchRadius: number;
  isSearching: boolean;
  lastSearchTime: number | null;
}

/**
 * 地图UI状态
 */
export interface MapUIState {
  isGridExpanded: boolean;
  showSearchCircle: boolean;
  searchCircleRadius: number;
  selectedMarkerId: string | null;
  showPropertyDetails: boolean;
  mapType: 'standard' | 'satellite' | 'hybrid';
}

/**
 * 地图状态接口
 */
export interface MapState {
  // 地图核心状态
  center: MapCenter;
  zoomLevel: number;
  userLocation: MapCenter | null;
  
  // 房源数据
  markers: MapMarker[];
  totalCount: number;
  
  // 搜索状态
  search: SearchState;
  
  // UI状态
  ui: MapUIState;
  
  // 加载状态
  isLoading: boolean;
  error: string | null;
  
  // 缓存状态
  lastUpdateTime: number | null;
  cacheExpiry: number;
}

/**
 * 地图操作接口
 */
export interface MapActions {
  // 地图操作
  setCenter: (center: MapCenter) => void;
  setZoomLevel: (level: number) => void;
  setUserLocation: (location: MapCenter | null) => void;
  
  // 搜索操作
  updateSearchQuery: (query: string) => void;
  setSelectedPropertyType: (type: string) => void;
  setPriceRange: (range: { min?: number; max?: number }) => void;
  setSearchRadius: (radius: number) => void;
  performSearch: () => Promise<void>;
  clearSearch: () => void;
  
  // 房源数据操作
  setMarkers: (markers: MapMarker[]) => void;
  addMarker: (marker: MapMarker) => void;
  removeMarker: (markerId: string) => void;
  updateMarker: (markerId: string, updates: Partial<MapMarker>) => void;
  
  // UI操作
  toggleGrid: () => void;
  setSelectedMarker: (markerId: string | null) => void;
  togglePropertyDetails: () => void;
  setMapType: (type: 'standard' | 'satellite' | 'hybrid') => void;
  
  // 状态管理
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  resetState: () => void;
  
  // 缓存管理
  invalidateCache: () => void;
  isCacheValid: () => boolean;
}

// ===== 初始状态 =====

const initialState: MapState = {
  // 地图核心状态 - 默认南宁市中心
  center: {
    latitude: 22.8167,
    longitude: 108.3669,
  },
  zoomLevel: 15,
  userLocation: null,
  
  // 房源数据
  markers: [],
  totalCount: 0,
  
  // 搜索状态
  search: {
    query: '',
    selectedPropertyType: '商铺',
    priceRange: {},
    searchRadius: 5000, // 5公里
    isSearching: false,
    lastSearchTime: null,
  },
  
  // UI状态
  ui: {
    isGridExpanded: false,
    showSearchCircle: false,
    searchCircleRadius: 5000,
    selectedMarkerId: null,
    showPropertyDetails: false,
    mapType: 'standard',
  },
  
  // 加载状态
  isLoading: false,
  error: null,
  
  // 缓存状态
  lastUpdateTime: null,
  cacheExpiry: 5 * 60 * 1000, // 5分钟缓存
};

/**
 * 企业级地图状态管理Store
 * 
 * 使用Zustand的多个中间件实现企业级功能：
 * - immer: 不可变状态更新
 * - subscribeWithSelector: 细粒度订阅
 * - devtools: 开发者工具集成
 * - persist: 选择性持久化
 */
export const useMapStore = create<MapState & MapActions>()(
  devtools(
    subscribeWithSelector(
      persist(
        immer((set, get) => ({
          // 初始状态
          ...initialState,
          
          // ===== 地图操作 =====
          setCenter: (center) => {
            set((state) => {
              state.center = center;
            });
          },
          
          setZoomLevel: (level) => {
            set((state) => {
              state.zoomLevel = Math.max(1, Math.min(20, level));
            });
          },
          
          setUserLocation: (location) => {
            set((state) => {
              state.userLocation = location;
            });
          },
          
          // ===== 搜索操作 =====
          updateSearchQuery: (query) => {
            set((state) => {
              state.search.query = query;
            });
          },
          
          setSelectedPropertyType: (type) => {
            set((state) => {
              state.search.selectedPropertyType = type;
            });
          },
          
          setPriceRange: (range) => {
            set((state) => {
              state.search.priceRange = range;
            });
          },
          
          setSearchRadius: (radius) => {
            set((state) => {
              state.search.searchRadius = Math.max(100, Math.min(50000, radius));
              state.ui.searchCircleRadius = radius;
            });
          },
          
          performSearch: async () => {
            const state = get();
            
            set((draft) => {
              draft.search.isSearching = true;
              draft.isLoading = true;
              draft.error = null;
            });
            
            try {
              // 这里将集成统一转换层和API调用
              // 暂时使用模拟数据
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              set((draft) => {
                draft.search.isSearching = false;
                draft.search.lastSearchTime = Date.now();
                draft.isLoading = false;
                draft.lastUpdateTime = Date.now();
              });
              
            } catch (error) {
              set((draft) => {
                draft.search.isSearching = false;
                draft.isLoading = false;
                draft.error = error instanceof Error ? error.message : '搜索失败';
              });
            }
          },
          
          clearSearch: () => {
            set((state) => {
              state.search.query = '';
              state.search.priceRange = {};
              state.markers = [];
              state.totalCount = 0;
            });
          },
          
          // ===== 房源数据操作 =====
          setMarkers: (markers) => {
            set((state) => {
              state.markers = markers;
              state.totalCount = markers.length;
              state.lastUpdateTime = Date.now();
            });
          },
          
          addMarker: (marker) => {
            set((state) => {
              state.markers.push(marker);
              state.totalCount = state.markers.length;
            });
          },
          
          removeMarker: (markerId: string) => {
            set((state) => {
              state.markers = state.markers.filter((m: any) => m.id !== markerId);
              state.totalCount = state.markers.length;
            });
          },

          updateMarker: (markerId: string, updates: any) => {
            set((state) => {
              const index = state.markers.findIndex((m: any) => m.id === markerId);
              if (index !== -1) {
                Object.assign(state.markers[index], updates);
              }
            });
          },
          
          // ===== UI操作 =====
          toggleGrid: () => {
            set((state) => {
              state.ui.isGridExpanded = !state.ui.isGridExpanded;
            });
          },
          
          setSelectedMarker: (markerId) => {
            set((state) => {
              state.ui.selectedMarkerId = markerId;
            });
          },
          
          togglePropertyDetails: () => {
            set((state) => {
              state.ui.showPropertyDetails = !state.ui.showPropertyDetails;
            });
          },
          
          setMapType: (type) => {
            set((state) => {
              state.ui.mapType = type;
            });
          },
          
          // ===== 状态管理 =====
          setLoading: (loading) => {
            set((state) => {
              state.isLoading = loading;
            });
          },
          
          setError: (error) => {
            set((state) => {
              state.error = error;
            });
          },
          
          clearError: () => {
            set((state) => {
              state.error = null;
            });
          },
          
          resetState: () => {
            set(initialState);
          },
          
          // ===== 缓存管理 =====
          invalidateCache: () => {
            set((state) => {
              state.lastUpdateTime = null;
            });
          },
          
          isCacheValid: () => {
            const state = get();
            if (!state.lastUpdateTime) return false;
            return Date.now() - state.lastUpdateTime < state.cacheExpiry;
          },
        })),
        {
          name: 'map-store',
          storage: createJSONStorage(() => AsyncStorage),
          // 选择性持久化：只持久化用户偏好设置
          partialize: (state) => ({
            search: {
              selectedPropertyType: state.search.selectedPropertyType,
              searchRadius: state.search.searchRadius,
            },
            ui: {
              mapType: state.ui.mapType,
            },
          }),
        }
      )
    ),
    {
      name: 'map-store',
    }
  )
);
