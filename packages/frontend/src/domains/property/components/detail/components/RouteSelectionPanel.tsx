/**
 * 🚀 路线选择面板组件
 * 企业级组件拆分 - 从PropertyNavigationMap中提取
 * 职责：路线模式选择和备选路线显示
 */

import React, { useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';

// ==================== 类型定义 ====================

// 🔄 使用统一类型定义
import {
  RouteMode,
  RouteInfo,
  RouteResult,
  SelectedRoute,
} from '../types/navigation.types';

// 移除重复类型定义，使用统一类型系统

export interface RouteSelectionPanelProps {
  routeResult: RouteResult;
  selectedRoute: SelectedRoute;
  onRouteSelect: (mode: RouteMode, alternativeIndex?: number) => void;
  onCalculateRoute: (
    mode: RouteMode,
    requestMultiRoute?: boolean
  ) => Promise<void>;
}

// ==================== 组件实现 ====================

export const RouteSelectionPanel: React.FC<RouteSelectionPanelProps> = ({
  routeResult,
  selectedRoute,
  onRouteSelect,
  onCalculateRoute,
}) => {
  // ==================== 路线模式配置 ====================

  const routeModes: Array<{ mode: RouteMode; label: string; icon: string }> = [
    { mode: 'driving', label: '驾车', icon: '🚗' },
    { mode: 'taxi', label: '打车', icon: '🚕' },
    { mode: 'transit', label: '公交', icon: '🚌' },
    { mode: 'walking', label: '步行', icon: '🚶' },
    { mode: 'cycling', label: '骑行', icon: '🚴' },
  ];

  // ==================== 事件处理 ====================

  const handleModePress = useCallback(
    async (mode: RouteMode) => {
      console.log(`🎯 [RouteSelectionPanel] 选择路线模式: ${mode}`);

      // 如果是驾车模式，请求多路线
      const requestMultiRoute = mode === 'driving';

      // 更新选中状态
      onRouteSelect(mode, 0);

      // 重新计算路线
      try {
        await onCalculateRoute(mode, requestMultiRoute);
      } catch (error) {
        console.error(`❌ [RouteSelectionPanel] 计算${mode}路线失败:`, error);
      }
    },
    [onRouteSelect, onCalculateRoute]
  );

  const handleAlternativeSelect = useCallback(
    (alternativeIndex: number) => {
      console.log(`🎯 [RouteSelectionPanel] 选择备选路线: ${alternativeIndex}`);
      onRouteSelect(selectedRoute.mode, alternativeIndex);
    },
    [onRouteSelect, selectedRoute.mode]
  );

  // ==================== 渲染方法 ====================

  const renderRouteMode = useCallback(
    (modeConfig: { mode: RouteMode; label: string; icon: string }) => {
      const { mode, label, icon } = modeConfig;
      const isSelected = selectedRoute.mode === mode;
      const currentRoute = routeResult?.routes?.find(r => r.mode === mode);
      const isLoading = routeResult?.isLoading && isSelected;

      return (
        <TouchableOpacity
          key={mode}
          style={[
            styles.routeModeButton,
            isSelected && styles.selectedModeButton,
          ]}
          onPress={() => handleModePress(mode)}
          disabled={isLoading}
        >
          <Text style={styles.modeIcon}>{icon}</Text>
          <Text
            style={[styles.modeLabel, isSelected && styles.selectedModeLabel]}
          >
            {label}
          </Text>

          {isLoading && <Text style={styles.loadingText}>计算中...</Text>}

          {currentRoute && !isLoading && (
            <>
              <Text style={styles.routeDistance}>{currentRoute.distance}</Text>
              <Text style={styles.routeDuration}>{currentRoute.duration}</Text>
              {currentRoute.cost && (
                <Text style={styles.routeCost}>{currentRoute.cost}</Text>
              )}
            </>
          )}
        </TouchableOpacity>
      );
    },
    [selectedRoute.mode, routeResult, handleModePress]
  );

  const renderDrivingAlternatives = useCallback(() => {
    if (
      selectedRoute.mode !== 'driving' ||
      !routeResult?.drivingAlternatives ||
      routeResult.drivingAlternatives.length === 0
    ) {
      return null;
    }

    return (
      <View style={styles.alternativesSection}>
        <Text style={styles.alternativesTitle}>驾车路线选择</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {routeResult.drivingAlternatives.map((alternative, index) => {
            const isSelected = selectedRoute.alternativeIndex === index;

            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.alternativeButton,
                  isSelected && styles.selectedAlternativeButton,
                ]}
                onPress={() => handleAlternativeSelect(index)}
              >
                <Text
                  style={[
                    styles.alternativeIndex,
                    isSelected && styles.selectedAlternativeIndex,
                  ]}
                >
                  路线{index + 1}
                </Text>
                <Text style={styles.alternativeDistance}>
                  {alternative.distance}
                </Text>
                <Text style={styles.alternativeDuration}>
                  {alternative.duration}
                </Text>
                {alternative.description && (
                  <Text style={styles.alternativeDescription} numberOfLines={1}>
                    {alternative.description}
                  </Text>
                )}
                {alternative.trafficLights && (
                  <Text style={styles.trafficLights}>
                    🚦 {alternative.trafficLights}个
                  </Text>
                )}
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>
    );
  }, [selectedRoute, routeResult.drivingAlternatives, handleAlternativeSelect]);

  const renderErrorState = useCallback(() => {
    if (!routeResult?.error) return null;

    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          ❌ {routeResult?.error || '未知错误'}
        </Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => handleModePress(selectedRoute.mode)}
        >
          <Text style={styles.retryButtonText}>重试</Text>
        </TouchableOpacity>
      </View>
    );
  }, [routeResult.error, selectedRoute.mode, handleModePress]);

  // ==================== 主渲染 ====================

  return (
    <View style={styles.container}>
      {/* 路线模式选择 */}
      <View style={styles.modesContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.modesScrollContent}
        >
          {routeModes.map(renderRouteMode)}
        </ScrollView>
      </View>

      {/* 错误状态 */}
      {renderErrorState()}

      {/* 驾车备选路线 */}
      {renderDrivingAlternatives()}
    </View>
  );
};

// ==================== 样式定义 ====================

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },

  // 路线模式样式
  modesContainer: {
    paddingVertical: 12,
  },
  modesScrollContent: {
    paddingHorizontal: 16,
  },
  routeModeButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 12,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    minWidth: 80,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  selectedModeButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  modeIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  modeLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 2,
  },
  selectedModeLabel: {
    color: '#ffffff',
  },
  loadingText: {
    fontSize: 10,
    color: '#666666',
    marginTop: 2,
  },
  routeDistance: {
    fontSize: 10,
    color: '#666666',
    fontWeight: '600',
  },
  routeDuration: {
    fontSize: 9,
    color: '#888888',
  },
  routeCost: {
    fontSize: 9,
    color: '#FF6B00',
    fontWeight: '600',
  },

  // 备选路线样式
  alternativesSection: {
    paddingHorizontal: 16,
    paddingBottom: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  alternativesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    marginTop: 8,
  },
  alternativeButton: {
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e8e8e8',
    minWidth: 100,
  },
  selectedAlternativeButton: {
    backgroundColor: '#E3F2FD',
    borderColor: '#2196F3',
  },
  alternativeIndex: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666666',
    marginBottom: 2,
  },
  selectedAlternativeIndex: {
    color: '#2196F3',
  },
  alternativeDistance: {
    fontSize: 11,
    color: '#333333',
    fontWeight: '500',
  },
  alternativeDuration: {
    fontSize: 10,
    color: '#666666',
  },
  alternativeDescription: {
    fontSize: 9,
    color: '#888888',
    marginTop: 2,
    textAlign: 'center',
  },
  trafficLights: {
    fontSize: 9,
    color: '#FF9800',
    marginTop: 1,
  },

  // 错误状态样式
  errorContainer: {
    backgroundColor: '#ffebee',
    margin: 16,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 12,
    color: '#d32f2f',
    textAlign: 'center',
    marginBottom: 8,
  },
  retryButton: {
    backgroundColor: '#f44336',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 16,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '500',
  },
});

export default RouteSelectionPanel;
