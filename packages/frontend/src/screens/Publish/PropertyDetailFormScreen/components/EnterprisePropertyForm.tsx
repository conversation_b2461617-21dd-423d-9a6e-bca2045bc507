/**
 * 企业级房源发布表单组件
 * 
 * 使用新的企业级表单系统：
 * 1. 统一的表单验证和错误处理
 * 2. 键盘响应式布局
 * 3. 自动滚动到错误字段
 * 4. 红色错误提示
 * 5. 防抖验证优化
 * 6. 企业级用户体验
 */

import React, { useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';

// 企业级表单组件
import {
  EnterpriseFormContainer,
  EnterpriseFormInput,
  useEnterpriseFormValidation,
  type EnterpriseFormConfig,
  type FormFieldError,
} from '../../../../shared/components/Form';

// 原有组件和类型
import { PriceInfoSection } from './PriceInfoSection';
import { MediaUploadSection } from './MediaUploadSection';
import { PropertyFormProgress } from '../../components/PropertyFormProgress';
import type { PropertyFormData } from '../hooks/validationSchema';

// 配置和常量
import {
  TRANSACTION_TYPE_CONFIG,
  DECORATION_LEVEL_CONFIG,
  ORIENTATION_OPTIONS,
  SUB_TYPE_CONFIG,
} from '../../../../domains/publish/constants';

// 服务
import FeedbackService from '../../../../shared/services/FeedbackService';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';

export interface EnterprisePropertyFormProps {
  propertyType: string;
  onSubmit: (data: PropertyFormData) => Promise<void>;
  initialData?: Partial<PropertyFormData>;
}

export const EnterprisePropertyForm: React.FC<EnterprisePropertyFormProps> = ({
  propertyType,
  onSubmit,
  initialData = {},
}) => {
  const navigation = useNavigation();
  
  // 表单验证配置
  const formConfig: EnterpriseFormConfig<PropertyFormData> = useMemo(() => ({
    fields: {
      title: {
        required: true,
        minLength: 8, // 修改为8个字符
        maxLength: 100,
      },
      property_certificate_address: {
        required: true,
        minLength: 5,
      },
      area: {
        required: true,
        min: 0.1,
        custom: (value: any) => {
          const numValue = typeof value === 'number' ? value : parseFloat(value);
          if (isNaN(numValue) || numValue <= 0) {
            return { valid: false, message: '请输入有效的面积' };
          }
          return { valid: true };
        }
      },
      sub_type: {
        required: true,
      },
      floor: {
        required: true,
      },
      orientation: {
        required: true,
      },
      decoration_level: {
        required: true,
      },
      transaction_types: {
        required: true,
        custom: (value: string[]) => {
          if (!Array.isArray(value) || value.length === 0) {
            return { valid: false, message: '请至少选择一种交易类型' };
          }
          return { valid: true };
        }
      },
      description: {
        required: true,
        minLength: 20,
        maxLength: 2000,
      },
      rent_payment_method: {
        custom: (value: string, allValues?: PropertyFormData) => {
          // 只有在包含租赁交易时才验证支付方式
          if (allValues?.transaction_types?.includes('RENT') && !value) {
            return { valid: false, message: '请选择租金支付方式' };
          }
          return { valid: true };
        }
      }
    },
    defaultValues: {
      title: '',
      property_certificate_address: '',
      sub_type: '',
      area: '',
      floor: '',
      total_floors: '',
      orientation: '',
      decoration_level: '',
      transaction_types: [],
      rent_price: '',
      sale_price: '',
      transfer_price: '',
      rent_deposit_months: '',
      rent_payment_method: 'QUARTERLY',
      property_fee: '',
      description: '',
      ...initialData,
    },
    realTimeValidation: true,
    validateOnBlur: true,
    showWarnings: true,
  }), [initialData]);
  
  // 表单验证Hook
  const {
    values,
    errors,
    warnings,
    isFormValid,
    setValue,
    validateAllFields,
    getFieldErrors,
    markFieldTouched,
    resetForm,
    submitForm,
  } = useEnterpriseFormValidation<PropertyFormData>(formConfig);
  
  // 处理交易类型切换
  const handleTransactionTypeToggle = useCallback((type: string) => {
    const currentTypes = values.transaction_types || [];
    const newTypes = currentTypes.includes(type as any)
      ? currentTypes.filter(t => t !== type)
      : [...currentTypes, type as any];
    setValue('transaction_types', newTypes);
  }, [values.transaction_types, setValue]);
  
  // 表单验证函数 - 返回错误列表用于滚动
  const validateForm = useCallback((): FormFieldError[] => {
    const fieldErrors = getFieldErrors();
    
    // 添加自定义业务验证
    const customErrors: FormFieldError[] = [];
    
    // 验证交易类型和对应价格
    if (values.transaction_types.includes('RENT') && !values.rent_price) {
      customErrors.push({
        fieldName: 'rent_price',
        message: '请填写租金',
        priority: 1,
      });
    }
    
    if (values.transaction_types.includes('SALE') && !values.sale_price) {
      customErrors.push({
        fieldName: 'sale_price', 
        message: '请填写售价',
        priority: 1,
      });
    }
    
    if (values.transaction_types.includes('TRANSFER') && !values.transfer_price) {
      customErrors.push({
        fieldName: 'transfer_price',
        message: '请填写转让费', 
        priority: 1,
      });
    }
    
    return [...fieldErrors, ...customErrors];
  }, [values, getFieldErrors]);
  
  // 处理表单提交
  const handleSubmit = useCallback(async () => {
    try {
      console.log('[EnterprisePropertyForm] 开始提交表单...');
      
      const success = await submitForm(async (formData) => {
        console.log('[EnterprisePropertyForm] 验证通过，提交数据:', formData);
        await onSubmit(formData);
      });
      
      if (success) {
        console.log('[EnterprisePropertyForm] 表单提交成功');
        FeedbackService.showSuccess('房源发布成功！');
        
        // 跳转到成功页面
        navigation.navigate('PublishSuccess' as any, {
          publishedData: {
            title: values.title || '房源',
            propertyType: values.sub_type || propertyType,
            area: values.area || '未知',
          }
        });
      } else {
        console.log('[EnterprisePropertyForm] 表单提交失败');
      }
    } catch (error) {
      console.error('[EnterprisePropertyForm] 提交异常:', error);
      FeedbackService.showError('提交失败，请稍后重试');
    }
  }, [submitForm, onSubmit, values, propertyType, navigation]);
  
  // 处理验证错误
  const handleValidationError = useCallback((fieldErrors: FormFieldError[]) => {
    console.log('[EnterprisePropertyForm] 表单验证失败:', fieldErrors);
    
    if (fieldErrors.length > 0) {
      const firstError = fieldErrors[0];
      FeedbackService.showError(`请完善${firstError.message}`);
    }
  }, []);
  
  return (
    <EnterpriseFormContainer
      onValidate={validateForm}
      onSubmit={handleSubmit}
      onValidationError={handleValidationError}
      enableAutoValidation={true}
      keyboardOptions={{
        extraOffset: 30,
        enableAutoScroll: true,
        scrollAnimationDuration: 300,
      }}
    >
      {/* 进度条 */}
      <View style={styles.progressContainer}>
        <PropertyFormProgress
          currentStep={1}
          totalSteps={4}
          stepNames={['基础信息', '媒体上传', '价格设置', '完成发布']}
        />
      </View>
      
      {/* 基础信息区块 */}
      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>基础信息</Text>
        <Text style={styles.sectionSubtitle}>来自房产证识别的信息，可手动修改</Text>
        
        <EnterpriseFormInput
          fieldName="property_certificate_address"
          label="地址"
          required={true}
          value={values.property_certificate_address}
          onValueChange={(value) => setValue('property_certificate_address', value)}
          error={errors.property_certificate_address}
          warning={warnings.property_certificate_address}
          placeholder="请输入详细地址"
          multiline={true}
          numberOfLines={2}
          hint="请填写完整的房产证地址"
        />
        
        <EnterpriseFormInput
          fieldName="area"
          label="建筑面积"
          required={true}
          value={values.area?.toString()}
          onValueChange={(value) => setValue('area', parseFloat(value) || 0)}
          error={errors.area}
          warning={warnings.area}
          placeholder="房产证上的建筑面积"
          keyboardType="numeric"
          hint="单位：平方米"
        />
        
        <EnterpriseFormInput
          fieldName="floor"
          label="楼层"
          required={true}
          value={values.floor}
          onValueChange={(value) => setValue('floor', value)}
          error={errors.floor}
          placeholder="所在楼层"
          keyboardType="numeric"
        />
        
        <EnterpriseFormInput
          fieldName="total_floors"
          label="总楼层"
          value={values.total_floors}
          onValueChange={(value) => setValue('total_floors', value)}
          placeholder="总楼层数"
          keyboardType="numeric"
        />
      </View>
      
      {/* 价格信息区块 */}
      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>价格信息</Text>
        
        <PriceInfoSection
          transactionTypes={values.transaction_types}
          rentPrice={values.rent_price || ''}
          salePrice={values.sale_price || ''}
          transferPrice={values.transfer_price || ''}
          rentDepositMonths={values.rent_deposit_months || ''}
          rentPaymentMethod={values.rent_payment_method || 'QUARTERLY'}
          propertyFee={values.property_fee || ''}
          onTransactionTypeToggle={handleTransactionTypeToggle}
          onRentPriceChange={(value) => setValue('rent_price', value)}
          onSalePriceChange={(value) => setValue('sale_price', value)}
          onTransferPriceChange={(value) => setValue('transfer_price', value)}
          onRentDepositMonthsChange={(value) => setValue('rent_deposit_months', value)}
          onRentPaymentMethodChange={(value) => setValue('rent_payment_method', value)}
          onPropertyFeeChange={(value) => setValue('property_fee', value)}
          onFieldLayout={() => {}} // 企业级表单容器会自动处理
          errors={errors}
          transactionTypeConfig={TRANSACTION_TYPE_CONFIG}
        />
      </View>
      
      {/* 详细描述区块 */}
      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>详细描述</Text>
        
        <EnterpriseFormInput
          fieldName="title"
          label="房源标题"
          required={true}
          value={values.title}
          onValueChange={(value) => setValue('title', value)}
          error={errors.title}
          warning={warnings.title}
          placeholder="请输入房源标题"
          maxLength={100}
          hint="标题至少需要8个字符，用于吸引客户注意"
        />
        
        <EnterpriseFormInput
          fieldName="description"
          label="房源描述"
          required={true}
          value={values.description}
          onValueChange={(value) => setValue('description', value)}
          error={errors.description}
          warning={warnings.description}
          placeholder="请详细描述房源特色、周边配套、交通情况等..."
          multiline={true}
          numberOfLines={6}
          maxLength={2000}
          hint="详细描述有助于提高房源吸引力"
        />
      </View>
      
      {/* 底部提交按钮区域由EnterpriseFormContainer自动处理 */}
    </EnterpriseFormContainer>
  );
};

const styles = StyleSheet.create({
  progressContainer: {
    marginBottom: hp(16),
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: wp(12),
    padding: wp(16),
    marginBottom: hp(16),
    
    // 阴影效果
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#000000',
    marginBottom: hp(8),
    lineHeight: fp(24),
  },
  sectionSubtitle: {
    fontSize: fp(12),
    color: '#8E8E93',
    marginBottom: hp(16),
    lineHeight: fp(18),
  },
});

export default EnterprisePropertyForm;