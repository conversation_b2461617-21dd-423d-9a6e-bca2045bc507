#!/usr/bin/env python3
"""
测试房源发布中面积字段的验证机制
验证前端和后端是否正确阻止面积为0或无效值的数据
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'packages/backend'))

from pydantic import ValidationError
from app.schemas.property.property_sqlmodel import PropertyCreate
from app.schemas.property.base_sqlmodel import PropertyBase

def test_backend_area_validation():
    """测试后端面积验证规则"""
    print("🔍 测试后端面积验证规则...")
    
    # 测试用例1: 面积为0
    print("\n测试用例1: total_area = 0")
    try:
        invalid_property = PropertyCreate(
            title="测试房源面积为零问题",
            property_type="OFFICE",
            sub_type="GRADE_A", 
            total_area=0,  # 无效值: 0
            transaction_types=["RENT"]
        )
        print("❌ 面积为0的数据通过了验证 - 这是一个问题!")
    except ValidationError as e:
        print(f"✅ 面积为0被正确拒绝: {e.errors()[0]['msg'] if e.errors() else str(e)}")
    
    # 测试用例2: 面积为负数
    print("\n测试用例2: total_area = -50")
    try:
        invalid_property = PropertyCreate(
            title="测试房源负面积问题",
            property_type="OFFICE", 
            sub_type="GRADE_A",
            total_area=-50,  # 无效值: 负数
            transaction_types=["RENT"]
        )
        print("❌ 负面积的数据通过了验证 - 这是一个问题!")
    except ValidationError as e:
        print(f"✅ 负面积被正确拒绝: {e.errors()[0]['msg'] if e.errors() else str(e)}")
    
    # 测试用例3: 有效面积
    print("\n测试用例3: total_area = 100.5")
    try:
        valid_property = PropertyCreate(
            title="测试房源正常面积数值",
            property_type="OFFICE",
            sub_type="GRADE_A", 
            total_area=100.5,  # 有效值
            transaction_types=["RENT"]
        )
        print("✅ 有效面积数据通过验证")
    except ValidationError as e:
        print(f"❌ 有效面积被错误拒绝: {e.errors()}")

def test_property_base_validation():
    """测试PropertyBase的面积验证"""
    print("\n\n🔍 测试PropertyBase面积验证规则...")
    
    # 测试用例1: 面积为0
    print("\n测试用例1: PropertyBase total_area = 0")
    try:
        invalid_base = PropertyBase(
            title="测试基础房源面积为零",
            property_type="OFFICE",
            sub_type="GRADE_A",
            total_area=0,  # 无效值: 0
            transaction_types=["RENT"]
        )
        print("❌ PropertyBase面积为0的数据通过了验证 - 这是一个问题!")
    except ValidationError as e:
        print(f"✅ PropertyBase面积为0被正确拒绝: {e.errors()[0]['msg'] if e.errors() else str(e)}")

def analyze_validation_rules():
    """分析当前的验证规则设置"""
    print("\n\n📋 分析当前验证规则...")
    
    # 获取PropertyBase的字段信息
    fields = PropertyBase.__fields__ if hasattr(PropertyBase, '__fields__') else {}
    
    for field_name, field_info in fields.items():
        if 'area' in field_name.lower():
            print(f"字段: {field_name}")
            print(f"  类型: {field_info.annotation if hasattr(field_info, 'annotation') else 'unknown'}")
            if hasattr(field_info, 'field_info'):
                constraints = field_info.field_info.constraints if hasattr(field_info.field_info, 'constraints') else {}
                print(f"  约束: {constraints}")
    
    print("\n🔍 PropertyCreate模型字段检查:")
    if hasattr(PropertyCreate, 'model_fields'):
        fields = PropertyCreate.model_fields
        for field_name, field_info in fields.items():
            if 'area' in field_name.lower():
                print(f"字段: {field_name} = {field_info}")

def main():
    """主测试函数"""
    print("🚀 开始测试房源面积验证机制")
    print("=" * 60)
    
    try:
        test_backend_area_validation()
        test_property_base_validation() 
        analyze_validation_rules()
        
        print("\n" + "=" * 60)
        print("📊 测试总结:")
        print("1. 检查后端Pydantic模型是否正确使用 gt=0 约束")
        print("2. 检查前端TypeScript验证是否正确限制面积输入")
        print("3. 检查数据转换层是否有额外的验证逻辑")
        print("4. 建议检查实际API调用中的数据流")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()