/**
 * 地图操作按钮组件
 * 职责：地图相关的操作按钮（定位、画圈筛选、快捷功能等）
 */

import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LocationButton } from './LocationButton';

interface MapActionsProps {
  onLocationPress: () => void;
  isLocating?: boolean;
  onRentFilterPress?: () => void;
  onSaleFilterPress?: () => void;
  selectedDealType?: 'rent' | 'sale' | null;
}

export const MapActions: React.FC<MapActionsProps> = ({
  onLocationPress,
  isLocating = false,
  onRentFilterPress,
  onSaleFilterPress,
  selectedDealType,
}) => {
  return (
    <View style={styles.mapActionsContainer}>
      {/* 定位按钮 */}
      <LocationButton onPress={onLocationPress} isLocating={isLocating} />

      {/* 租赁筛选按钮 */}
      <TouchableOpacity
        style={[
          styles.filterButton,
          selectedDealType === 'rent' && styles.filterButtonActive,
        ]}
        onPress={onRentFilterPress}
        activeOpacity={0.8}
      >
        <Ionicons
          name="filter-outline"
          size={20}
          color={selectedDealType === 'rent' ? '#fff' : '#007AFF'}
        />
        <Text
          style={[
            styles.filterButtonText,
            selectedDealType === 'rent' && styles.filterButtonTextActive,
          ]}
        >
          租赁筛选
        </Text>
      </TouchableOpacity>

      {/* 买卖筛选按钮 */}
      <TouchableOpacity
        style={[
          styles.filterButton,
          selectedDealType === 'sale' && styles.filterButtonActive,
        ]}
        onPress={onSaleFilterPress}
        activeOpacity={0.8}
      >
        <Ionicons
          name="filter-outline"
          size={20}
          color={selectedDealType === 'sale' ? '#fff' : '#007AFF'}
        />
        <Text
          style={[
            styles.filterButtonText,
            selectedDealType === 'sale' && styles.filterButtonTextActive,
          ]}
        >
          买卖筛选
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  mapActionsContainer: {
    position: 'absolute',
    top: '25%',
    left: 16,
    gap: 12,
    alignItems: 'flex-start',
    zIndex: 999,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    gap: 6,
  },
  filterButtonActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
});
