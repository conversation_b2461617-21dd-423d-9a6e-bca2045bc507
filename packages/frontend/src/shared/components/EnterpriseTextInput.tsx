/**
 * 企业级智能输入框组件
 * 
 * 基于微信、支付宝等主流APP的输入框设计
 * 提供完整的键盘响应、自动滚动、错误处理功能
 */

import React, { useCallback, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  TextInput,
  TextInputProps,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { UseKeyboardAwareFormReturn } from '../hooks/useKeyboardAwareForm';

export interface EnterpriseTextInputProps extends Omit<TextInputProps, 'style'> {
  /**
   * 字段名称，用于键盘响应和错误定位
   */
  fieldName: string;
  
  /**
   * 表单控制器
   */
  formControl: UseKeyboardAwareFormReturn;
  
  /**
   * 字段标签
   */
  label?: string;
  
  /**
   * 是否为必填字段
   */
  required?: boolean;
  
  /**
   * 错误信息优先级（数字越小优先级越高）
   */
  errorPriority?: number;
  
  /**
   * 输入框样式
   */
  inputStyle?: TextStyle;
  
  /**
   * 容器样式
   */
  containerStyle?: ViewStyle;
  
  /**
   * 标签样式
   */
  labelStyle?: TextStyle;
  
  /**
   * 错误信息样式
   */
  errorStyle?: TextStyle;
  
  /**
   * 是否显示密码切换按钮
   */
  showPasswordToggle?: boolean;
  
  /**
   * 右侧图标
   */
  rightIcon?: React.ReactNode;
  
  /**
   * 左侧图标
   */
  leftIcon?: React.ReactNode;
  
  /**
   * 验证规则
   */
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: string) => string | null;
  };
  
  /**
   * 实时验证（输入时验证）
   */
  validateOnChange?: boolean;
  
  /**
   * 失焦时验证
   */
  validateOnBlur?: boolean;
}

export interface EnterpriseTextInputRef {
  /**
   * 聚焦输入框
   */
  focus: () => void;
  
  /**
   * 失焦输入框
   */
  blur: () => void;
  
  /**
   * 获取输入框值
   */
  getValue: () => string;
  
  /**
   * 设置输入框值
   */
  setValue: (value: string) => void;
  
  /**
   * 验证字段
   */
  validate: () => string | null;
  
  /**
   * 清除错误状态
   */
  clearError: () => void;
}

export const EnterpriseTextInput = forwardRef<EnterpriseTextInputRef, EnterpriseTextInputProps>(
  ({
    fieldName,
    formControl,
    label,
    required = false,
    errorPriority = 1,
    inputStyle,
    containerStyle,
    labelStyle,
    errorStyle,
    showPasswordToggle = false,
    rightIcon,
    leftIcon,
    validation,
    validateOnChange = false,
    validateOnBlur = true,
    secureTextEntry: initialSecureTextEntry = false,
    value,
    onChangeText,
    onFocus,
    onBlur,
    ...textInputProps
  }, ref) => {
    const inputRef = useRef<TextInput>(null);
    const [localValue, setLocalValue] = React.useState(value || '');
    const [isSecure, setIsSecure] = React.useState(initialSecureTextEntry);
    const [isFocused, setIsFocused] = React.useState(false);
    const errorOpacity = useRef(new Animated.Value(0)).current;
    
    // 获取当前字段的错误信息
    const fieldErrors = formControl.getFieldErrors();
    const currentError = fieldErrors[fieldName];
    const hasError = !!currentError;

    // 注册字段到表单控制器
    useEffect(() => {
      formControl.registerField(fieldName, inputRef);
      return () => formControl.unregisterField(fieldName);
    }, [fieldName, formControl]);

    // 错误状态动画
    useEffect(() => {
      Animated.timing(errorOpacity, {
        toValue: hasError ? 1 : 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }, [hasError, errorOpacity]);

    // 字段验证函数
    const validateField = useCallback((valueToValidate: string): string | null => {
      if (!validation) return null;

      // 必填验证
      if (validation.required && !valueToValidate.trim()) {
        return `${label || fieldName}不能为空`;
      }

      // 最小长度验证
      if (validation.minLength && valueToValidate.length < validation.minLength) {
        return `${label || fieldName}至少需要${validation.minLength}位字符`;
      }

      // 最大长度验证
      if (validation.maxLength && valueToValidate.length > validation.maxLength) {
        return `${label || fieldName}不能超过${validation.maxLength}位字符`;
      }

      // 正则验证
      if (validation.pattern && !validation.pattern.test(valueToValidate)) {
        return `${label || fieldName}格式不正确`;
      }

      // 自定义验证
      if (validation.custom) {
        return validation.custom(valueToValidate);
      }

      return null;
    }, [validation, label, fieldName]);

    // 处理布局变化
    const handleLayout = useCallback((event: any) => {
      const { y, height } = event.nativeEvent.layout;
      formControl.updateFieldLayout(fieldName, { y, height });
    }, [fieldName, formControl]);

    // 处理输入框聚焦
    const handleFocus = useCallback((event: any) => {
      setIsFocused(true);
      formControl.handleFieldFocus(fieldName);
      onFocus?.(event);
    }, [fieldName, formControl, onFocus]);

    // 处理输入框失焦
    const handleBlur = useCallback((event: any) => {
      setIsFocused(false);
      formControl.handleFieldBlur(fieldName);
      
      // 失焦时验证
      if (validateOnBlur) {
        const error = validateField(localValue);
        if (error) {
          formControl.showFieldErrors([{
            fieldName,
            message: error,
            priority: errorPriority,
          }]);
        } else {
          formControl.clearFieldErrors([fieldName]);
        }
      }
      
      onBlur?.(event);
    }, [fieldName, formControl, onBlur, validateOnBlur, validateField, localValue, errorPriority]);

    // 处理文本变化
    const handleChangeText = useCallback((text: string) => {
      setLocalValue(text);
      onChangeText?.(text);
      
      // 实时验证
      if (validateOnChange) {
        const error = validateField(text);
        if (error) {
          formControl.showFieldErrors([{
            fieldName,
            message: error,
            priority: errorPriority,
          }]);
        } else {
          formControl.clearFieldErrors([fieldName]);
        }
      }
    }, [onChangeText, validateOnChange, validateField, fieldName, formControl, errorPriority]);

    // 切换密码显示
    const togglePasswordVisibility = useCallback(() => {
      setIsSecure(!isSecure);
    }, [isSecure]);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      focus: () => inputRef.current?.focus(),
      blur: () => inputRef.current?.blur(),
      getValue: () => localValue,
      setValue: (newValue: string) => {
        setLocalValue(newValue);
        onChangeText?.(newValue);
      },
      validate: () => validateField(localValue),
      clearError: () => formControl.clearFieldErrors([fieldName]),
    }), [localValue, onChangeText, validateField, formControl, fieldName]);

    return (
      <View style={[styles.container, containerStyle]} onLayout={handleLayout}>
        {/* 标签 */}
        {label && (
          <Text style={[styles.label, labelStyle]}>
            {label}
            {required && <Text style={styles.required}> *</Text>}
          </Text>
        )}
        
        {/* 输入框容器 */}
        <View style={[
          styles.inputContainer,
          isFocused && styles.inputContainerFocused,
          hasError && styles.inputContainerError,
        ]}>
          {/* 左侧图标 */}
          {leftIcon && (
            <View style={styles.leftIconContainer}>
              {leftIcon}
            </View>
          )}
          
          {/* 输入框 */}
          <TextInput
            ref={inputRef}
            style={[styles.textInput, inputStyle]}
            value={localValue}
            onChangeText={handleChangeText}
            onFocus={handleFocus}
            onBlur={handleBlur}
            secureTextEntry={showPasswordToggle ? isSecure : initialSecureTextEntry}
            placeholderTextColor="#CCCCCC"
            {...textInputProps}
          />
          
          {/* 密码切换按钮 */}
          {showPasswordToggle && (
            <TouchableOpacity
              style={styles.rightIconContainer}
              onPress={togglePasswordVisibility}
            >
              <Ionicons
                name={isSecure ? 'eye-off-outline' : 'eye-outline'}
                size={20}
                color="#666666"
              />
            </TouchableOpacity>
          )}
          
          {/* 右侧图标 */}
          {rightIcon && !showPasswordToggle && (
            <View style={styles.rightIconContainer}>
              {rightIcon}
            </View>
          )}
        </View>
        
        {/* 错误信息 */}
        {hasError && (
          <Animated.View style={[styles.errorContainer, { opacity: errorOpacity }]}>
            <Ionicons name="alert-circle" size={14} color="#FF4444" />
            <Text style={[styles.errorText, errorStyle]}>{currentError}</Text>
          </Animated.View>
        )}
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 8,
  },
  required: {
    color: '#FF4444',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
    minHeight: 48,
  },
  inputContainerFocused: {
    borderColor: '#007AFF',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  inputContainerError: {
    borderColor: '#FF4444',
  },
  leftIconContainer: {
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    paddingVertical: 12,
  },
  rightIconContainer: {
    marginLeft: 8,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
  },
  errorText: {
    fontSize: 12,
    color: '#FF4444',
    marginLeft: 4,
    flex: 1,
  },
});

export default EnterpriseTextInput;