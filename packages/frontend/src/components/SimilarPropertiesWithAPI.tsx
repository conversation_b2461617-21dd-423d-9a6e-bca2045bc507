/**
 * 集成API的相似房源组件包装器
 */

import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { SimilarProperties } from '@property/components/detail';
import { useSimilarProperties } from '@hooks/useSimilarProperties';
import type { PropertyDetailData } from '@property/types';

interface SimilarPropertiesWithAPIProps {
  propertyId: string;
  propertyData: PropertyDetailData | null;
  onPropertyPress: (propertyId: string) => void;
}

const SimilarPropertiesWithAPI: React.FC<SimilarPropertiesWithAPIProps> = ({
  propertyId,
  propertyData,
  onPropertyPress
}) => {
  const { properties, loading, error } = useSimilarProperties({
    propertyId,
    propertyData: propertyData || undefined,
    limit: 6
  });

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color="#FF6B35" />
        <Text style={styles.loadingText}>正在加载相似房源...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>暂无相似房源推荐</Text>
      </View>
    );
  }

  if (properties.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>暂无相似房源</Text>
      </View>
    );
  }

  return (
    <SimilarProperties
      properties={properties}
      onPropertyPress={onPropertyPress}
    />
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  errorText: {
    fontSize: 14,
    color: '#999',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',  
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
  },
});

export default SimilarPropertiesWithAPI;