import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';

export const formStyles = StyleSheet.create({
  // 表单容器样式
  formSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: wp(16),
    marginBottom: hp(6) // 缩小到二分之一间距
  },
  sectionTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#000000',
    paddingTop: hp(16),
    paddingBottom: hp(12)
  },
  
  // 表单行样式
  formRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#C6C6C8',
    minHeight: 44
  },
  rowLabel: {
    fontSize: 17,
    color: '#000000',
    width: 60,
    fontWeight: '400'
  },
  rowInput: {
    flex: 1,
    fontSize: 17,
    color: '#000000',
    textAlign: 'right',
    paddingLeft: 16
  },
  rowRight: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end'
  },
  rowValue: {
    fontSize: 17,
    color: '#000000',
    marginRight: 8
  },
  placeholder: {
    color: '#C7C7CC'
  },
  unit: {
    fontSize: 17,
    color: '#8E8E93',
    marginLeft: 8
  },
  
  // 交易类型样式
  transactionTypes: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end'
  },
  transactionType: {
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    borderRadius: wp(16),
    marginLeft: wp(8),
    borderWidth: 1
  },
  transactionTypeActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF'
  },
  transactionTypeInactive: {
    backgroundColor: 'transparent',
    borderColor: '#C7C7CC'
  },
  transactionTypeText: {
    fontSize: fp(14),
    fontWeight: '500'
  },
  transactionTypeTextActive: {
    color: '#FFFFFF'
  },
  transactionTypeTextInactive: {
    color: '#8E8E93'
  },
  
  // 输入框样式
  textInput: {
    flex: 1,
    fontSize: fp(16),
    color: '#000000',
    textAlign: 'right',
    paddingVertical: hp(8),
    paddingHorizontal: wp(12),
    borderWidth: 1,
    borderColor: '#E5E5EA',
    borderRadius: wp(8),
    backgroundColor: '#FFFFFF'
  },
  textInputFocused: {
    borderColor: '#007AFF',
    backgroundColor: '#F8F9FA'
  },
  textInputError: {
    borderColor: '#FF3B30',
    backgroundColor: '#FFF5F5'
  },
  
  // 多行文本输入框
  textArea: {
    minHeight: hp(80),
    textAlignVertical: 'top',
    paddingTop: hp(12)
  },
  
  // 选择器样式
  selector: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingVertical: hp(8)
  },
  selectorText: {
    fontSize: fp(16),
    color: '#000000',
    marginRight: wp(8)
  },
  selectorPlaceholder: {
    fontSize: fp(16),
    color: '#C7C7CC',
    marginRight: wp(8)
  },
  selectorArrow: {
    fontSize: fp(16),
    color: '#C7C7CC'
  },
  
  // 复选框样式
  checkbox: {
    width: wp(20),
    height: wp(20),
    borderWidth: 1,
    borderColor: '#C7C7CC',
    borderRadius: wp(4),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: wp(8)
  },
  checkboxChecked: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF'
  },
  checkboxLabel: {
    fontSize: fp(16),
    color: '#000000',
    flex: 1
  },
  
  // 标签样式
  tag: {
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    borderRadius: wp(16),
    marginRight: wp(8),
    marginBottom: hp(8),
    borderWidth: 1
  },
  tagActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF'
  },
  tagInactive: {
    backgroundColor: 'transparent',
    borderColor: '#C7C7CC'
  },
  tagText: {
    fontSize: fp(14),
    fontWeight: '500'
  },
  tagTextActive: {
    color: '#FFFFFF'
  },
  tagTextInactive: {
    color: '#8E8E93'
  },
  
  // 表单验证样式
  validationContainer: {
    paddingHorizontal: wp(16),
    paddingTop: hp(4)
  },
  validationText: {
    fontSize: fp(12),
    color: '#FF3B30'
  },
  validationSuccess: {
    color: '#34C759'
  },
  
  // 表单提示样式
  hintText: {
    fontSize: fp(12),
    color: '#8E8E93',
    marginTop: hp(4),
    paddingHorizontal: wp(16)
  },
  requiredMark: {
    color: '#FF3B30',
    fontSize: fp(16),
    marginLeft: wp(2)
  },

  // 补充缺失的样式
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(8),
  },
  inputGroup: {
    marginBottom: hp(16),
  },
  inputLabel: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#000000',
    marginBottom: hp(8),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputUnit: {
    fontSize: fp(16),
    color: '#8E8E93',
    marginLeft: wp(8),
  },
  checkboxGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },

  // 补充更多缺失的表单样式
  floorRowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(16),
  },
  floorInputContainer: {
    flex: 1,
    marginHorizontal: wp(4),
  },
  floorInputLabel: {
    fontSize: fp(14),
    fontWeight: '500',
    color: '#000000',
    marginBottom: hp(8),
  },
  aiSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    backgroundColor: '#F8F9FA',
  },
  aiSectionTitle: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#000000',
  },
  aiStatusBadge: {
    paddingHorizontal: wp(8),
    paddingVertical: wp(4),
    borderRadius: wp(12),
    backgroundColor: '#34C759',
  },
  aiStatusText: {
    fontSize: fp(12),
    color: '#FFFFFF',
    fontWeight: '500',
  },

  // 补充交易类型相关样式
  transactionTypesContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    flexWrap: 'wrap',
  },
  transactionChip: {
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    borderRadius: wp(16),
    marginLeft: wp(8),
    marginBottom: hp(4),
    borderWidth: 1,
    borderColor: '#C7C7CC',
    backgroundColor: 'transparent',
  },
  transactionChipSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  transactionChipText: {
    fontSize: fp(14),
    fontWeight: '500',
    color: '#8E8E93',
  },
  transactionChipTextSelected: {
    color: '#FFFFFF',
  },

  // 补充标题和描述相关样式
  titleFormRow: {
    marginBottom: hp(16),
  },
  labelWithAI: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(8),
  },
  aiButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(8),
    paddingVertical: hp(4),
    backgroundColor: '#34C759',
    borderRadius: wp(4),
  },
  aiButtonText: {
    fontSize: fp(12),
    color: '#FFFFFF',
    fontWeight: '500',
    marginLeft: wp(4),
  },
  titleUnderlineInput: {
    fontSize: fp(17),
    color: '#000000',
    paddingVertical: hp(12),
    paddingHorizontal: wp(16),
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
    backgroundColor: '#FFFFFF',
  },

  // 补充描述相关样式
  reducedMargin: {
    marginBottom: hp(3),
  },
  sectionHeaderWithAI: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(8),
  },
  aiHint: {
    fontSize: fp(12),
    color: '#8E8E93',
    marginBottom: hp(8),
    fontStyle: 'italic',
  },
  descriptionInput: {
    fontSize: fp(16),
    color: '#000000',
    paddingVertical: hp(12),
    paddingHorizontal: wp(16),
    borderWidth: 1,
    borderColor: '#E5E5EA',
    borderRadius: wp(8),
    backgroundColor: '#FFFFFF',
    minHeight: hp(100),
    textAlignVertical: 'top',
  },

  // 补充标签相关样式
  sectionHeaderWithHint: {
    marginBottom: hp(12),
  },
  selectedTagsContainer: {
    marginBottom: hp(16),
  },
  selectedTagsTitle: {
    fontSize: fp(14),
    fontWeight: '600',
    color: '#000000',
    marginBottom: hp(8),
  },
  selectedTagsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  selectedTagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    backgroundColor: '#007AFF',
    borderRadius: wp(16),
    marginRight: wp(8),
    marginBottom: hp(8),
  },
  selectedTagText: {
    fontSize: fp(14),
    color: '#FFFFFF',
    fontWeight: '500',
  },
  deleteTagButton: {
    marginLeft: wp(4),
    padding: wp(2),
  },
  newTagsSection: {
    marginTop: hp(16),
  },
  newTagsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(8),
  },
  newTagsTitle: {
    fontSize: fp(14),
    fontWeight: '600',
    color: '#000000',
  },
  tagActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  refreshText: {
    fontSize: fp(14),
    color: '#007AFF',
    fontWeight: '500',
  },
  refreshTextDisabled: {
    color: '#C7C7CC',
  },
  maxTagsWarning: {
    fontSize: fp(12),
    color: '#FF9500',
    marginBottom: hp(8),
    fontStyle: 'italic',
  },
  newTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  newTagItem: {
    paddingHorizontal: wp(12),
    paddingVertical: hp(6),
    backgroundColor: '#F2F2F7',
    borderRadius: wp(16),
    marginRight: wp(8),
    marginBottom: hp(8),
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  newTagText: {
    fontSize: fp(14),
    color: '#000000',
    fontWeight: '500',
  },

  // 错误文本样式
  errorText: {
    fontSize: fp(14),
    color: '#FF3B30',
    fontWeight: '500',
  },
});
