/**
 * 房源详情页企业级业务逻辑Hook
 *
 * 企业级Hook层实现，遵循前端五层架构规范
 * 集成Zustand状态管理和数据转换层
 *
 * 🔧 修复版本：解决无限循环问题
 * 备份时间：2025-01-08
 */

import { useCallback, useMemo, useState } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';
import * as Sharing from 'expo-sharing';
import FeedbackService from '@shared/services/FeedbackService';
import { useAuth } from '../../../../contexts/AuthContext';
import { usePropertyDetail as usePropertyDetailAPI } from '@property/services/useProperties';
import { useSimplifiedSDUI } from '@utils/sdui/SimplifiedSDUI';

// 导入数据转换工具
import { PropertyDetailTransformer } from '../utils/propertyDataTransform';
// 导入类型定义
import type {
  PublishedPropertyData,
} from '../types/propertyDetail.types';

export const usePropertyDetailLogic = () => {
  // 导航和路由
  const navigation = useNavigation();
  const route = useRoute();
  const { propertyId, publishedData } = route.params as {
    propertyId: string;
    source?: string;
    publishedData?: PublishedPropertyData;
  };

  // 🔧 彻底修复：使用本地状态，完全移除store依赖
  const [isImageViewerVisible, setImageViewerVisible] = useState(false);
  const [imageViewerIndex, setImageViewerIndex] = useState(0);
  const [isFavorited, setFavorited] = useState(false);

  // 数据获取 - API层
  const {
    data: apiPropertyData,
    isLoading: apiLoading,
    error: apiError,
    refetch: apiRefetch,
  } = usePropertyDetailAPI(propertyId);

  // 使用SimplifiedSDUI获取配置
  const {
    config: apiSduiConfig,
    isLoading: sduiConfigLoading,
    error: sduiConfigError,
  } = useSimplifiedSDUI(propertyId);

  // 数据转换层 - 合并发布数据（企业级转换）
  const currentData = useMemo(() => {
    // 🔧 修复：如果没有API数据但有发布数据，使用发布数据
    if (!apiPropertyData) {
      if (publishedData) {
        console.log('[PropertyDetail] 使用发布数据（API不可见）:', {
          publishedDataKeys: Object.keys(publishedData),
          publishedId: publishedData.id || publishedData.propertyId,
          status: publishedData.status,
        });

        // 将发布数据转换为详情页格式
        return PropertyDetailTransformer.fromPublishedData(publishedData);
      }
      return null;
    }

    // 如果有发布数据，执行企业级合并转换
    if (publishedData) {
      console.log('[PropertyDetail] 执行发布数据合并转换:', {
        publishedDataKeys: Object.keys(publishedData),
        baseDataId: apiPropertyData.id,
        publishedId: publishedData.id || publishedData.propertyId,
      });

      const mergedData = PropertyDetailTransformer.mergePublishedData(
        apiPropertyData,
        publishedData
      );

      // 验证合并后的数据完整性
      const validation =
        PropertyDetailTransformer.validatePropertyData(mergedData);
      if (!validation.isValid) {
        console.warn('[PropertyDetail] 合并数据验证失败:', validation.errors);
      }

      return mergedData;
    }

    return apiPropertyData;
  }, [apiPropertyData, publishedData]);

  // SimplifiedSDUI准备状态检查 - 🔧 修复：直接使用API数据
  const isSDUIReady = useMemo(() => {
    return !sduiConfigLoading && !sduiConfigError && !!apiSduiConfig;
  }, [sduiConfigLoading, sduiConfigError, apiSduiConfig]);

  // 获取认证状态
  const { state: authState } = useAuth();
  const { userToken, user } = authState;
  const isAuthenticated = !!userToken;

  // 🔧 修复无限循环：完全移除状态同步
  // 原因：这些useEffect导致了无限循环，因为对象引用比较总是不相等
  // 解决方案：直接使用API数据，不需要同步到store

  // 注释：移除了所有导致无限循环的useEffect
  // 现在直接在return中使用API数据，避免了状态同步的复杂性

  // 业务逻辑方法

  // 处理媒体切换
  const handleMediaChange = useCallback((index: number) => {
    // TODO: 实现媒体切换逻辑
    console.log('媒体切换:', index);
  }, []);

  // 处理标签切换
  const handleTabChange = useCallback((tab: '图片' | '视频') => {
    // TODO: 实现标签切换逻辑
    console.log('标签切换:', tab);
  }, []);

  // 处理图片点击
  const handleImagePress = useCallback(
    (index: number) => {
      setImageViewerVisible(true);
      setImageViewerIndex(index);
    },
    [] // 🔧 修复：移除setter依赖，useState的setter是稳定的
  );

  // 处理收藏
  const handleFavorite = useCallback(async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setFavorited(prev => !prev); // 🔧 修复：使用函数式更新，移除isFavorited依赖
    // TODO: 调用收藏API
  }, []); // 🔧 修复：移除所有依赖

  // 处理消息查看
  const handleMessageView = useCallback(async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    FeedbackService.showInfo('此功能正在开发中...');
  }, []);

  // 处理分享
  const handleShare = useCallback(async () => {
    try {
      const shareLink = PropertyDetailTransformer.generateShareLink(propertyId);
      await Sharing.shareAsync(shareLink, {
        mimeType: 'text/plain',
        dialogTitle: '分享房源信息',
      });
    } catch (error) {
      console.error('分享失败:', error);
    }
  }, [propertyId]);

  // 处理返回
  const handleGoBack = useCallback(async () => {
    navigation.goBack();
  }, [navigation]);

  // 处理联系 - 跳转到聊天界面或登录页面
  const handleContact = useCallback(async () => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // 检查登录状态
      if (!isAuthenticated || !user) {
        console.log('[PropertyDetail] 用户未登录，跳转到登录页面');
        (navigation as any).navigate('SmsLogin');
        return;
      }

      console.log('[PropertyDetail] 正在连接房东...');

      // 调用API初始化聊天会话
      const { PropertyInquiryAPI } = await import(
        '@property/services/propertyInquiryAPI'
      );
      const chatInfo = await PropertyInquiryAPI.initializeChat(
        propertyId,
        '您好！我对您的房源很感兴趣，想了解更多详情。'
      );

      // 构建聊天消息对象
      const chatMessage = {
        id: `msg_${Date.now()}`,
        userName: chatInfo.landlordInfo.name,
        userAvatar:
          chatInfo.landlordInfo.avatar || 'https://via.placeholder.com/50',
        lastMessage: '您好！我对您的房源很感兴趣',
        timestamp: new Date(),
        unreadCount: 0,
        propertyInfo: {
          title: chatInfo.propertyInfo.title,
          location: chatInfo.propertyInfo.location,
          price: chatInfo.propertyInfo.price,
          area: chatInfo.propertyInfo.area,
          propertyType: chatInfo.propertyInfo.propertyType,
          industry: '便利店',
          status: '经营中',
          rent: '面议0.0%，房租6.0%',
          payment: '预付3个月，押金2个月',
          period: '意向租期6个月',
          target: '办公、学生、居民',
        },
      };

      console.log('[PropertyDetail] 房东连接成功');

      // 跳转到聊天详情页
      (navigation as any).navigate('ChatDetail', {
        message: chatMessage,
        userRole: 'tenant',
        inquiryId: chatInfo.inquiryId,
      });
    } catch (error) {
      console.error('初始化聊天失败:', error);
      FeedbackService.showInfo('无法连接到房东，请稍后重试');
    }
  }, [propertyId, navigation, isAuthenticated, user]);

  // 处理预约
  const handleReserve = useCallback(() => {
    FeedbackService.showInfo('此功能正在开发中...');
  }, []);

  // 重新获取数据
  const refetch = useCallback(async () => {
    await apiRefetch();
  }, [apiRefetch]);

  return {
    // 数据
    propertyId,
    currentData,
    publishedData, // 导出发布数据
    sduiConfig: apiSduiConfig, // 🔧 修复：直接使用API数据
    isSDUIReady,

    // 状态 - 🔧 修复：直接使用API状态和分离的UI状态
    isLoading: apiLoading || sduiConfigLoading,
    error: apiError || sduiConfigError,
    isImageViewerVisible,
    imageViewerIndex,
    isFavorited,
    activeDescriptionTab: 0, // TODO: 从store获取

    // 操作方法
    handleMediaChange,
    handleTabChange,
    handleImagePress,
    handleFavorite,
    handleMessageView,
    handleShare,
    handleGoBack,
    handleContact,
    handleReserve,
    refetch,

    // 图片查看器控制
    setIsImageViewerVisible: setImageViewerVisible,
    setImageViewerIndex,
  };
};
