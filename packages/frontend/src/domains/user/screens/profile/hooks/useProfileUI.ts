/**
 * 用户中心UI状态管理Hook - 企业级重新设计
 * 功能：管理所有UI状态和交互反馈，完整保留原始功能
 * 架构师重新设计：遵循Zustand最佳实践，最小化选择器使用
 * 核心原则：只使用必要的选择器，直接访问Store，保持功能完整性
 */
import { useCallback } from 'react';
import { useProfileStore } from '../stores/ProfileStore';

interface UseProfileUIOptions {
  initialShowLoginModal?: boolean;
  initialShowLogoutModal?: boolean;
  initialRefreshing?: boolean;
}

interface UseProfileUIReturn {
  // UI状态
  showLoginModal: boolean;
  showLogoutModal: boolean;
  refreshing: boolean;
  
  // UI操作方法
  setShowLoginModal: (show: boolean) => void;
  setShowLogoutModal: (show: boolean) => void;
  setRefreshing: (refreshing: boolean) => void;
  
  // 便捷操作方法
  openLoginModal: () => void;
  closeLoginModal: () => void;
  openLogoutModal: () => void;
  closeLogoutModal: () => void;
  startRefreshing: () => void;
  stopRefreshing: () => void;
  
  // 事件处理方法
  handleRefresh: (refreshAction: () => Promise<void>) => Promise<void>;
  handleLoginModalToggle: () => void;
  handleLogoutModalToggle: () => void;
}

export const useProfileUI = ({
  initialShowLoginModal = false,
  initialShowLogoutModal = false,
  initialRefreshing = false,
}: UseProfileUIOptions = {}): UseProfileUIReturn => {

  // 紧急修复：完全移除选择器，直接使用Store，确保功能不丢失
  const showLoginModal = useProfileStore((state) => state.showLoginModal);
  const showLogoutModal = useProfileStore((state) => state.showLogoutModal);
  const refreshing = useProfileStore((state) => state.refreshing);

  // 企业级架构师重新设计：直接使用Store方法，避免依赖问题
  const openLoginModal = useCallback(() => {
    console.log('[useProfileUI] 打开登录弹窗');
    useProfileStore.getState().setShowLoginModal(true);
  }, []);

  const closeLoginModal = useCallback(() => {
    console.log('[useProfileUI] 关闭登录弹窗');
    useProfileStore.getState().setShowLoginModal(false);
  }, []);

  const openLogoutModal = useCallback(() => {
    console.log('[useProfileUI] 打开退出弹窗');
    useProfileStore.getState().setShowLogoutModal(true);
  }, []);

  const closeLogoutModal = useCallback(() => {
    console.log('[useProfileUI] 关闭退出弹窗');
    useProfileStore.getState().setShowLogoutModal(false);
  }, []);

  const startRefreshing = useCallback(() => {
    console.log('[useProfileUI] 开始刷新');
    useProfileStore.getState().setRefreshing(true);
  }, []);

  const stopRefreshing = useCallback(() => {
    console.log('[useProfileUI] 停止刷新');
    useProfileStore.getState().setRefreshing(false);
  }, []);

  // 下拉刷新处理 - 完全保留原始逻辑
  const handleRefresh = useCallback(async (refreshAction: () => Promise<void>) => {
    console.log('[useProfileUI] 处理下拉刷新');
    startRefreshing();
    
    try {
      await refreshAction();
      console.log('[useProfileUI] 刷新完成');
    } catch (error) {
      console.error('[useProfileUI] 刷新失败:', error);
    } finally {
      stopRefreshing();
    }
  }, [startRefreshing, stopRefreshing]);

  // 弹窗切换处理 - 企业级修复：使用Store状态
  const handleLoginModalToggle = useCallback(() => {
    const currentState = useProfileStore.getState().showLoginModal;
    useProfileStore.getState().setShowLoginModal(!currentState);
  }, []);

  const handleLogoutModalToggle = useCallback(() => {
    const currentState = useProfileStore.getState().showLogoutModal;
    useProfileStore.getState().setShowLogoutModal(!currentState);
  }, []);

  return {
    // UI状态（直接从Store获取，确保功能完整性）
    showLoginModal,
    showLogoutModal,
    refreshing,

    // UI操作方法 - 紧急修复：使用useCallback包装Store方法
    setShowLoginModal: useCallback((show: boolean) => {
      useProfileStore.getState().setShowLoginModal(show);
    }, []),
    setShowLogoutModal: useCallback((show: boolean) => {
      useProfileStore.getState().setShowLogoutModal(show);
    }, []),
    setRefreshing: useCallback((refreshing: boolean) => {
      useProfileStore.getState().setRefreshing(refreshing);
    }, []),
    
    // 便捷操作方法
    openLoginModal,
    closeLoginModal,
    openLogoutModal,
    closeLogoutModal,
    startRefreshing,
    stopRefreshing,
    
    // 事件处理方法
    handleRefresh,
    handleLoginModalToggle,
    handleLogoutModalToggle,
  };
};
