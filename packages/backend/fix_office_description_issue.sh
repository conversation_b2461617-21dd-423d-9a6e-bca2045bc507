#!/bin/bash
"""
修复写字楼描述内页验证问题的脚本
"""

set -e  # 遇到错误时退出

echo "=== 修复写字楼描述内页验证问题 ==="
echo

# 1. 检查当前目录
if [[ ! -f "alembic.ini" ]]; then
    echo "❌ 错误: 请在backend目录下运行此脚本"
    exit 1
fi

# 2. 显示当前迁移状态
echo "📋 当前数据库迁移状态:"
alembic current
echo

# 3. 运行所有挂起的迁移
echo "🔄 运行数据库迁移..."
alembic upgrade head
echo "✅ 数据库迁移完成"
echo

# 4. 运行验证脚本
echo "🔍 运行验证和清理脚本..."
python scripts/clean_office_description_validation.py
echo

# 5. 检查是否有语法错误
echo "🔍 检查Python语法..."
python -m py_compile app/models/property/property.py
python -m py_compile app/schemas/property/property_sqlmodel.py
echo "✅ Python语法检查通过"
echo

# 6. 提供后续建议
echo "=== 修复完成 ==="
echo "✅ 已删除office_description相关验证逻辑"
echo "✅ 已清理测试代码中的引用"
echo "✅ 已确保数据库迁移正确执行"
echo
echo "📋 后续操作建议:"
echo "1. 重启后端服务器"
echo "2. 测试创建写字楼类型房源"
echo "3. 如果仍有问题，检查前端验证逻辑"
echo
echo "🔧 如果问题仍然存在，可能的原因:"
echo "   - 前端还有验证逻辑引用office_description"
echo "   - 缓存未清理"
echo "   - 数据库连接使用了旧的schema"