/**
 * 🚀 路线信息显示组件
 * 企业级组件拆分 - 从PropertyNavigationMap中提取
 * 职责：路线详情显示、时间距离信息、交通信息
 */

import React, { useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

// ==================== 类型定义 ====================

export interface RouteInfo {
  mode: string;
  distance: string;
  duration: string;
  cost?: string;
  coordinates?: Array<{ latitude: number; longitude: number }>;
}

export interface RouteResult {
  routes: RouteInfo[];
  isLoading: boolean;
  error: string | null;
  drivingAlternatives?: Array<{
    distance: string;
    duration: string;
    description: string;
    cost?: string;
    coordinates: Array<{ latitude: number; longitude: number }>;
    trafficLights?: number;
  }>;
}

export interface SelectedRoute {
  mode: string;
  alternativeIndex?: number;
}

export interface RouteInfoDisplayProps {
  routeResult: RouteResult;
  selectedRoute: SelectedRoute;
  startLocationText: string;
  endLocationText: string;
}

// ==================== 组件实现 ====================

export const RouteInfoDisplay: React.FC<RouteInfoDisplayProps> = ({
  routeResult,
  selectedRoute,
  startLocationText,
  endLocationText,
}) => {
  // ==================== 计算属性 ====================

  const currentRoute = useMemo(() => {
    return routeResult?.routes?.find(r => r.mode === selectedRoute.mode);
  }, [routeResult?.routes, selectedRoute.mode]);

  const currentAlternative = useMemo(() => {
    if (
      selectedRoute.mode !== 'driving' ||
      !routeResult?.drivingAlternatives ||
      routeResult.drivingAlternatives.length === 0
    ) {
      return null;
    }

    const index = selectedRoute.alternativeIndex || 0;
    return routeResult.drivingAlternatives[index];
  }, [selectedRoute, routeResult?.drivingAlternatives]);

  const routeModeConfig = useMemo(() => {
    const configs = {
      driving: { icon: '🚗', label: '驾车', color: '#1E90FF' },
      taxi: { icon: '🚕', label: '打车', color: '#FFD700' },
      transit: { icon: '🚌', label: '公交', color: '#32CD32' },
      walking: { icon: '🚶', label: '步行', color: '#FF6347' },
      cycling: { icon: '🚴', label: '骑行', color: '#8A2BE2' },
    };

    return (
      configs[selectedRoute.mode as keyof typeof configs] || configs.driving
    );
  }, [selectedRoute.mode]);

  // ==================== 渲染方法 ====================

  const renderLoadingState = useCallback(() => {
    if (!routeResult.isLoading) return null;

    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingIcon}>🔄</Text>
        <Text style={styles.loadingText}>
          正在计算{routeModeConfig.label}路线...
        </Text>
      </View>
    );
  }, [routeResult.isLoading, routeModeConfig.label]);

  const renderErrorState = useCallback(() => {
    if (!routeResult.error) return null;

    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorIcon}>❌</Text>
        <Text style={styles.errorText}>{routeResult.error}</Text>
      </View>
    );
  }, [routeResult.error]);

  const renderRouteHeader = useCallback(() => {
    return (
      <View style={styles.routeHeader}>
        <View style={styles.routeMode}>
          <Text style={styles.routeModeIcon}>{routeModeConfig.icon}</Text>
          <Text
            style={[styles.routeModeLabel, { color: routeModeConfig.color }]}
          >
            {routeModeConfig.label}路线
          </Text>
        </View>

        <View style={styles.routeEndpoints}>
          <View style={styles.endpointRow}>
            <Text style={styles.endpointIcon}>📍</Text>
            <Text style={styles.endpointText} numberOfLines={1}>
              {startLocationText}
            </Text>
          </View>
          <View style={styles.routeArrow}>
            <Text style={styles.arrowText}>↓</Text>
          </View>
          <View style={styles.endpointRow}>
            <Text style={styles.endpointIcon}>🎯</Text>
            <Text style={styles.endpointText} numberOfLines={1}>
              {endLocationText}
            </Text>
          </View>
        </View>
      </View>
    );
  }, [routeModeConfig, startLocationText, endLocationText]);

  const renderRouteInfo = useCallback(() => {
    if (!currentRoute && !currentAlternative) return null;

    const displayRoute = currentAlternative || currentRoute;
    if (!displayRoute) return null;

    return (
      <View style={styles.routeInfoContainer}>
        <View style={styles.routeStats}>
          <View style={styles.statItem}>
            <Text style={styles.statIcon}>📏</Text>
            <Text style={styles.statLabel}>距离</Text>
            <Text style={styles.statValue}>{displayRoute.distance}</Text>
          </View>

          <View style={styles.statDivider} />

          <View style={styles.statItem}>
            <Text style={styles.statIcon}>⏱️</Text>
            <Text style={styles.statLabel}>时间</Text>
            <Text style={styles.statValue}>{displayRoute.duration}</Text>
          </View>

          {displayRoute.cost && (
            <>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statIcon}>💰</Text>
                <Text style={styles.statLabel}>费用</Text>
                <Text style={[styles.statValue, styles.costValue]}>
                  {displayRoute.cost}
                </Text>
              </View>
            </>
          )}
        </View>

        {currentAlternative?.description && (
          <View style={styles.routeDescription}>
            <Text style={styles.descriptionText}>
              {currentAlternative.description}
            </Text>
          </View>
        )}

        {currentAlternative?.trafficLights && (
          <View style={styles.trafficInfo}>
            <Text style={styles.trafficIcon}>🚦</Text>
            <Text style={styles.trafficText}>
              途经 {currentAlternative.trafficLights} 个红绿灯
            </Text>
          </View>
        )}
      </View>
    );
  }, [currentRoute, currentAlternative]);

  const renderAlternativeInfo = useCallback(() => {
    if (selectedRoute.mode !== 'driving' || !routeResult.drivingAlternatives) {
      return null;
    }

    const totalAlternatives = routeResult.drivingAlternatives.length;
    const currentIndex = selectedRoute.alternativeIndex || 0;

    if (totalAlternatives <= 1) return null;

    return (
      <View style={styles.alternativeInfo}>
        <Text style={styles.alternativeText}>
          路线 {currentIndex + 1} / {totalAlternatives}
        </Text>
        <Text style={styles.alternativeHint}>左右滑动查看更多路线选择</Text>
      </View>
    );
  }, [selectedRoute, routeResult.drivingAlternatives]);

  const renderEmptyState = useCallback(() => {
    if (
      routeResult.isLoading ||
      routeResult.error ||
      currentRoute ||
      currentAlternative
    ) {
      return null;
    }

    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyIcon}>🗺️</Text>
        <Text style={styles.emptyText}>选择出行方式开始计算路线</Text>
      </View>
    );
  }, [routeResult, currentRoute, currentAlternative]);

  // ==================== 主渲染 ====================

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* 路线头部信息 */}
        {renderRouteHeader()}

        {/* 加载状态 */}
        {renderLoadingState()}

        {/* 错误状态 */}
        {renderErrorState()}

        {/* 路线信息 */}
        {renderRouteInfo()}

        {/* 备选路线信息 */}
        {renderAlternativeInfo()}

        {/* 空状态 */}
        {renderEmptyState()}
      </ScrollView>
    </View>
  );
};

// ==================== 样式定义 ====================

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    maxHeight: 200,
  },
  scrollContent: {
    padding: 16,
  },

  // 路线头部
  routeHeader: {
    marginBottom: 16,
  },
  routeMode: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  routeModeIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  routeModeLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  routeEndpoints: {
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    padding: 12,
  },
  endpointRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  endpointIcon: {
    fontSize: 14,
    marginRight: 8,
  },
  endpointText: {
    flex: 1,
    fontSize: 14,
    color: '#333333',
  },
  routeArrow: {
    alignItems: 'center',
    marginVertical: 4,
  },
  arrowText: {
    fontSize: 16,
    color: '#666666',
  },

  // 路线统计
  routeInfoContainer: {
    marginTop: 8,
  },
  routeStats: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statIcon: {
    fontSize: 18,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 2,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  costValue: {
    color: '#FF6B00',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 12,
  },

  // 路线描述
  routeDescription: {
    backgroundColor: '#fff3cd',
    borderRadius: 6,
    padding: 8,
    marginBottom: 8,
  },
  descriptionText: {
    fontSize: 12,
    color: '#856404',
    lineHeight: 18,
  },

  // 交通信息
  trafficInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    padding: 8,
  },
  trafficIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  trafficText: {
    fontSize: 12,
    color: '#6c757d',
  },

  // 备选路线信息
  alternativeInfo: {
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  alternativeText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#007AFF',
    marginBottom: 4,
  },
  alternativeHint: {
    fontSize: 12,
    color: '#888888',
  },

  // 状态样式
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  loadingText: {
    fontSize: 14,
    color: '#666666',
  },

  errorContainer: {
    alignItems: 'center',
    paddingVertical: 20,
    backgroundColor: '#ffebee',
    borderRadius: 8,
    marginVertical: 8,
  },
  errorIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#d32f2f',
    textAlign: 'center',
  },

  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  emptyIcon: {
    fontSize: 32,
    marginBottom: 8,
    opacity: 0.5,
  },
  emptyText: {
    fontSize: 14,
    color: '#999999',
  },
});

export default RouteInfoDisplay;
