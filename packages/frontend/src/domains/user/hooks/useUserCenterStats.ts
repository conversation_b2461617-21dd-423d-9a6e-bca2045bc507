/**
 * 用户中心统计数据Hook
 * 整合房源、需求、订单等所有统计数据
 * 遵循企业级架构规范 - Hook层
 */
import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import { usePropertyStatusCounts } from '../../../shared/hooks/entity/useEntityStatusCounts';
import { useDemandStatusCounts } from '../../../shared/hooks/entity/useEntityStatusCounts';
import {
  userCenterTransformer,
  type UserCenterStats,
} from '../services/transform/UserCenterTransformer';

// 🏗️ 企业级架构：用户中心统计Hook接口
export interface UseUserCenterStatsReturn {
  // 统计数据
  stats: UserCenterStats | null;

  // 分类统计
  propertyStats: {
    active: number;
    draft: number;
    inactive: number;
    total: number;
  };

  demandStats: {
    active: number;
    draft: number;
    inactive: number;
    total: number;
  };

  orderStats: {
    favoriteCount: number;
    appointmentCount: number;
    orderCount: number;
    tenantResourceCount: number;
    total: number;
  };

  // 状态
  isLoading: boolean;
  error: Error | null;

  // 操作
  refresh: () => void;
}

/**
 * 用户中心统计数据Hook
 */
export const useUserCenterStats = (): UseUserCenterStatsReturn => {
  // 🔧 获取房源状态计数
  const {
    statusCounts: propertyStatusCounts,
    isLoading: propertyLoading,
    error: propertyError,
    refetch: refetchProperty,
  } = usePropertyStatusCounts();

  // 🔧 获取需求状态计数
  const {
    statusCounts: demandStatusCounts,
    isLoading: demandLoading,
    error: demandError,
    refetch: refetchDemand,
  } = useDemandStatusCounts();

  // 🔧 获取订单统计数据
  const {
    data: orderStatsData,
    isLoading: orderLoading,
    error: orderError,
    refetch: refetchOrder,
  } = useQuery({
    queryKey: ['user-center-order-stats'],
    queryFn: async () => {
      // TODO: 实现订单统计API调用
      // 暂时返回模拟数据
      return {
        favorite_count: 12,
        appointment_count: 5,
        order_count: 3,
        tenant_resource_count: 8,
        completed_orders: 2,
        pending_orders: 1,
      };
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });

  // 🔧 获取用户信息
  const {
    data: userInfoData,
    isLoading: userLoading,
    error: userError,
  } = useQuery({
    queryKey: ['user-center-info'],
    queryFn: async () => {
      // TODO: 实现用户信息API调用
      // 暂时返回模拟数据
      return {
        id: 'user-123',
        phone_number: '138****8888',
        user_name: '用户',
        avatar: null,
        is_verified: true,
        user_type: 'tenant_buyer',
        member_level: 'basic',
        created_at: new Date().toISOString(),
      };
    },
    staleTime: 10 * 60 * 1000, // 10分钟缓存
  });

  // 🔧 计算综合统计数据
  const stats = useMemo((): UserCenterStats | null => {
    if (!orderStatsData || !userInfoData) return null;

    // 使用转换器处理API数据
    const apiData = {
      property_stats: {
        active_count: propertyStatusCounts.active,
        draft_count: propertyStatusCounts.draft,
        inactive_count: propertyStatusCounts.inactive,
        total_views: propertyStatusCounts.active * 25, // 模拟数据
        total_inquiries: propertyStatusCounts.active * 8, // 模拟数据
      },
      demand_stats: {
        active_count: demandStatusCounts.active,
        draft_count: demandStatusCounts.draft,
        inactive_count: demandStatusCounts.inactive,
        total_matches: demandStatusCounts.active * 3, // 模拟数据
        total_interested: demandStatusCounts.active * 5, // 模拟数据
      },
      order_stats: orderStatsData,
      user_info: userInfoData,
    };

    const transformResult = userCenterTransformer.fromAPI(apiData);
    return transformResult.success ? transformResult.data! : null;
  }, [propertyStatusCounts, demandStatusCounts, orderStatsData, userInfoData]);

  // 🔧 分类统计数据
  const propertyStats = useMemo(
    () => ({
      active: propertyStatusCounts.active,
      draft: propertyStatusCounts.draft,
      inactive: propertyStatusCounts.inactive,
      total:
        propertyStatusCounts.active +
        propertyStatusCounts.draft +
        propertyStatusCounts.inactive,
    }),
    [propertyStatusCounts]
  );

  const demandStats = useMemo(
    () => ({
      active: demandStatusCounts.active,
      draft: demandStatusCounts.draft,
      inactive: demandStatusCounts.inactive,
      total:
        demandStatusCounts.active +
        demandStatusCounts.draft +
        demandStatusCounts.inactive,
    }),
    [demandStatusCounts]
  );

  const orderStats = useMemo(() => {
    if (!orderStatsData) {
      return {
        favoriteCount: 0,
        appointmentCount: 0,
        orderCount: 0,
        tenantResourceCount: 0,
        total: 0,
      };
    }

    return {
      favoriteCount: orderStatsData.favorite_count,
      appointmentCount: orderStatsData.appointment_count,
      orderCount: orderStatsData.order_count,
      tenantResourceCount: orderStatsData.tenant_resource_count,
      total:
        orderStatsData.favorite_count +
        orderStatsData.appointment_count +
        orderStatsData.order_count +
        orderStatsData.tenant_resource_count,
    };
  }, [orderStatsData]);

  // 🔧 状态计算
  const isLoading =
    propertyLoading || demandLoading || orderLoading || userLoading;
  const error = propertyError || demandError || orderError || userError;

  // 🔧 刷新操作
  const refresh = () => {
    refetchProperty();
    refetchDemand();
    refetchOrder();
  };

  return {
    // 统计数据
    stats,

    // 分类统计
    propertyStats,
    demandStats,
    orderStats,

    // 状态
    isLoading,
    error,

    // 操作
    refresh,
  };
};

export default useUserCenterStats;
