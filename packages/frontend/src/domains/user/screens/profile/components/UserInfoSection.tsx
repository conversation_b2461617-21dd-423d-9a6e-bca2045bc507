/**
 * 用户信息区域组件
 * 功能：用户头像、姓名、类型、手机号展示，设置和客服按钮
 * 完整保留原始样式，确保UI完全一致，目标：60行
 */
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { UserAvatar } from '../../../components/UserAvatar';
import { wp } from '@shared/utils/responsiveUtils';
import { styles } from './styles';

interface UserInfoSectionProps {
  user: any | null;
  isLoggedIn: boolean;
  onSettingsPress: () => void;
  onCustomerServicePress: () => void;
}

export const UserInfoSection: React.FC<UserInfoSectionProps> = ({
  user,
  isLoggedIn,
  onSettingsPress,
  onCustomerServicePress,
}) => {
  return (
    <View style={styles.userInfoContainer}>
      {/* 用户头像和信息 - 完全保留原始布局 */}
      <View style={styles.userProfile}>
        <View style={styles.avatarContainer}>
          <UserAvatar
            size={wp(60)}
            source={user?.avatar}
            style={styles.avatar}
          />
        </View>
        <View style={styles.userDetails}>
          <Text style={styles.userName}>
            {isLoggedIn ? `用户${user?.phone_number?.slice(-4) || '****'}` : '访客用户'}
          </Text>
          <Text style={styles.userType}>
            {isLoggedIn ? '商业地产租户' : '点击登录享受更多服务'}
          </Text>
          <Text style={styles.userPhone}>
            手机：
            {isLoggedIn && user?.phone_number
              ? `${user.phone_number.slice(0, 3)}****${user.phone_number.slice(-4)}`
              : '未绑定'}
          </Text>
        </View>
      </View>

      {/* 右上角操作按钮 - 完全保留原始样式 */}
      <View style={styles.headerActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={onSettingsPress}
        >
          <Ionicons name="settings-outline" size={wp(24)} color="#666666" />
          <Text style={styles.actionText}>设置</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={onCustomerServicePress}
        >
          <Ionicons name="headset-outline" size={wp(24)} color="#666666" />
          <Text style={styles.actionText}>客服</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};
