/**
 * 验证相关类型定义
 * 
 * 遵循AI编码指导规范：
 * - 完整的TypeScript类型覆盖
 * - 企业级验证机制
 * 
 * @fileoverview 验证引擎相关类型
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */

import { z } from 'zod';

/**
 * 验证结果
 */
export interface ValidationResult {
  valid: boolean;
  data?: any;
  errors?: string[];
  warnings?: string[];
}

/**
 * 验证上下文
 */
export interface ValidationContext {
  strict?: boolean;
  skipOptional?: boolean;
  customRules?: Record<string, (value: any) => boolean>;
}

/**
 * 验证规则
 */
export interface ValidationRule {
  name: string;
  description?: string;
  schema: z.ZodSchema<any>;
  required?: boolean;
}

/**
 * 验证引擎统计信息
 */
export interface ValidationStats {
  totalRules: number;
  requiredRules: number;
  optionalRules: number;
  engineVersion: string;
}

/**
 * 验证引擎健康检查结果
 */
export interface ValidationHealthCheck {
  healthy: boolean;
  issues: string[];
}

// 注意：这些类型已经在上面定义，这里不需要重复导出
