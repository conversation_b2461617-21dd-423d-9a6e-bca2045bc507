/**
 * 通用下拉选择器组件
 * @fileoverview 统一的下拉选择器，支持多种样式主题
 * <AUTHOR> Assistant
 * @since 2025-07-12
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  TextInput
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { wp, hp, fp } from '../../utils/responsiveUtils';

export interface DropdownOption {
  label: string;
  value: string;
  desc?: string;
  disabled?: boolean;
}

export interface UniversalDropdownProps {
  label: string;
  value: string | string[];
  options: DropdownOption[];
  onSelect: (value: string | string[]) => void;
  placeholder: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  searchable?: boolean;
  multiSelect?: boolean;
  theme?: 'default' | 'demand' | 'property'; // 主题样式
  style?: 'bottom' | 'center'; // 弹出样式
}

/**
 * 通用下拉选择器组件
 * 支持多种主题和样式，可在不同页面复用
 */
export const UniversalDropdown: React.FC<UniversalDropdownProps> = ({
  label,
  value,
  options,
  onSelect,
  placeholder,
  required = false,
  disabled = false,
  error,
  searchable = false,
  multiSelect = false,
  theme = 'default',
  style = 'bottom'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchText, setSearchText] = useState('');

  // 过滤选项
  const filteredOptions = searchable && searchText
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchText.toLowerCase())
      )
    : options;

  // 获取显示文本
  const getDisplayText = () => {
    if (Array.isArray(value)) {
      if (value.length === 0) return placeholder;
      if (value.length === 1) {
        const option = options.find(opt => opt.value === value[0]);
        return option?.label || placeholder;
      }
      return `已选择 ${value.length} 项`;
    } else {
      const option = options.find(opt => opt.value === value);
      return option?.label || placeholder;
    }
  };

  // 检查是否选中
  const isSelected = (optionValue: string) => {
    if (Array.isArray(value)) {
      return value.includes(optionValue);
    }
    return value === optionValue;
  };

  // 处理选择
  const handleSelect = (optionValue: string) => {
    if (multiSelect) {
      const currentValues = Array.isArray(value) ? value : [];
      if (currentValues.includes(optionValue)) {
        onSelect(currentValues.filter(v => v !== optionValue));
      } else {
        onSelect([...currentValues, optionValue]);
      }
    } else {
      onSelect(optionValue);
      setIsOpen(false);
    }
  };

  // 获取主题样式
  const getThemeStyles = () => {
    switch (theme) {
      case 'demand':
        return {
          container: styles.demandContainer,
          input: styles.demandInput,
          inputText: styles.demandInputText,
          modal: styles.demandModal,
          optionItem: styles.demandOptionItem,
          optionItemSelected: styles.demandOptionItemSelected,
          optionText: styles.demandOptionText,
          optionTextSelected: styles.demandOptionTextSelected
        };
      case 'property':
        return {
          container: styles.propertyContainer,
          input: styles.propertyInput,
          inputText: styles.propertyInputText,
          modal: styles.propertyModal,
          optionItem: styles.propertyOptionItem,
          optionItemSelected: styles.propertyOptionItemSelected,
          optionText: styles.propertyOptionText,
          optionTextSelected: styles.propertyOptionTextSelected
        };
      default:
        return {
          container: styles.defaultContainer,
          input: styles.defaultInput,
          inputText: styles.defaultInputText,
          modal: styles.defaultModal,
          optionItem: styles.defaultOptionItem,
          optionItemSelected: styles.defaultOptionItemSelected,
          optionText: styles.defaultOptionText,
          optionTextSelected: styles.defaultOptionTextSelected
        };
    }
  };

  const themeStyles = getThemeStyles();

  return (
    <View style={themeStyles.container}>
      {/* 标签 */}
      <View style={styles.labelContainer}>
        <Text style={styles.label}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      </View>

      {/* 输入框 */}
      <TouchableOpacity
        style={[
          themeStyles.input,
          ...(error ? [styles.inputError] : [])
        ]}
        onPress={() => !disabled && setIsOpen(true)}
        disabled={disabled}
      >
        <Text style={[
          themeStyles.inputText,
          ...(!value || (Array.isArray(value) && value.length === 0) ? [styles.placeholderText] : [])
        ]}>
          {getDisplayText()}
        </Text>
        <Ionicons 
          name="chevron-down" 
          size={wp(16)} 
          color={disabled ? "#CCCCCC" : "#8E8E93"} 
        />
      </TouchableOpacity>

      {/* 错误提示 */}
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      {/* 下拉模态框 */}
      <Modal
        visible={isOpen}
        transparent={true}
        animationType={style === 'bottom' ? 'slide' : 'fade'}
        onRequestClose={() => setIsOpen(false)}
      >
        <TouchableOpacity
          style={style === 'bottom' ? styles.bottomModalOverlay : styles.centerModalOverlay}
          activeOpacity={1}
          onPress={() => setIsOpen(false)}
        >
          <View style={[
            style === 'bottom' ? styles.bottomModal : styles.centerModal,
            themeStyles.modal
          ]}>
            {/* 标题栏 */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{label}</Text>
              <TouchableOpacity onPress={() => setIsOpen(false)}>
                <Ionicons name="close" size={wp(24)} color="#666666" />
              </TouchableOpacity>
            </View>

            {/* 搜索框 */}
            {searchable && (
              <View style={styles.searchContainer}>
                <Ionicons name="search" size={wp(16)} color="#8E8E93" />
                <TextInput
                  style={styles.searchInput}
                  placeholder="搜索选项..."
                  value={searchText}
                  onChangeText={setSearchText}
                />
              </View>
            )}

            {/* 选项列表 */}
            <ScrollView style={styles.optionsList} showsVerticalScrollIndicator={false}>
              {filteredOptions.map((option, index) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    themeStyles.optionItem,
                    ...(isSelected(option.value) ? [themeStyles.optionItemSelected] : []),
                    ...(index === filteredOptions.length - 1 ? [styles.lastOption] : [])
                  ]}
                  onPress={() => handleSelect(option.value)}
                  disabled={option.disabled}
                >
                  <View style={styles.optionContent}>
                    <Text style={[
                      themeStyles.optionText,
                      ...(isSelected(option.value) ? [themeStyles.optionTextSelected] : []),
                      ...(option.disabled ? [styles.optionTextDisabled] : [])
                    ]}>
                      {option.label}
                    </Text>
                    {option.desc && (
                      <Text style={styles.optionDesc}>{option.desc}</Text>
                    )}
                  </View>
                  {isSelected(option.value) && (
                    <Ionicons 
                      name="checkmark" 
                      size={wp(20)} 
                      color={theme === 'demand' ? '#FF6B35' : '#007AFF'} 
                    />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* 多选确认按钮 */}
            {multiSelect && (
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={() => setIsOpen(false)}
                >
                  <Text style={styles.confirmButtonText}>确定</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  // 通用样式
  labelContainer: {
    marginBottom: hp(8)
  },
  label: {
    fontSize: fp(16),
    fontWeight: '600',
    color: '#000000'
  },
  required: {
    color: '#FF3B30'
  },
  placeholderText: {
    color: '#C7C7CC'
  },
  inputError: {
    borderColor: '#FF3B30'
  },
  errorText: {
    fontSize: fp(12),
    color: '#FF3B30',
    marginTop: hp(4)
  },

  // 默认主题样式
  defaultContainer: {
    marginVertical: hp(8)
  },
  defaultInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    borderWidth: 1,
    borderColor: '#E5E5EA',
    borderRadius: wp(8),
    backgroundColor: '#FFFFFF'
  },
  defaultInputText: {
    flex: 1,
    fontSize: fp(16),
    color: '#000000'
  },
  defaultModal: {
    // 默认模态框样式
  },
  defaultOptionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(20),
    paddingVertical: hp(16),
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0'
  },
  defaultOptionItemSelected: {
    backgroundColor: '#F0F8FF'
  },
  defaultOptionText: {
    fontSize: fp(16),
    color: '#000000'
  },
  defaultOptionTextSelected: {
    color: '#007AFF',
    fontWeight: '600'
  },

  // 求租求购主题样式
  demandContainer: {
    marginVertical: hp(12)
  },
  demandInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(16),
    paddingVertical: hp(14),
    borderWidth: 1,
    borderColor: '#E5E5EA',
    borderRadius: wp(12),
    backgroundColor: '#FFFFFF'
  },
  demandInputText: {
    flex: 1,
    fontSize: fp(16),
    color: '#000000',
    fontWeight: '500'
  },
  demandModal: {
    // 求租求购模态框样式
  },
  demandOptionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(20),
    paddingVertical: hp(16),
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0'
  },
  demandOptionItemSelected: {
    backgroundColor: '#FFF3E0',
    borderLeftWidth: 3,
    borderLeftColor: '#FF6B35'
  },
  demandOptionText: {
    fontSize: fp(16),
    color: '#333333'
  },
  demandOptionTextSelected: {
    color: '#FF6B35',
    fontWeight: '600'
  },

  // 房源发布主题样式
  propertyContainer: {
    marginVertical: hp(8)
  },
  propertyInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    borderWidth: 1,
    borderColor: '#E5E5EA',
    borderRadius: wp(8),
    backgroundColor: '#FFFFFF'
  },
  propertyInputText: {
    flex: 1,
    fontSize: fp(16),
    color: '#000000'
  },
  propertyModal: {
    // 房源发布模态框样式
  },
  propertyOptionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(20),
    paddingVertical: hp(16),
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0'
  },
  propertyOptionItemSelected: {
    backgroundColor: '#F2F2F7'
  },
  propertyOptionText: {
    fontSize: fp(16),
    color: '#000000'
  },
  propertyOptionTextSelected: {
    color: '#007AFF',
    fontWeight: '600'
  },

  // 模态框样式
  bottomModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end'
  },
  centerModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(20)
  },
  bottomModal: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: wp(20),
    borderTopRightRadius: wp(20),
    maxHeight: hp(500),
    paddingBottom: hp(34)
  },
  centerModal: {
    backgroundColor: '#FFFFFF',
    borderRadius: wp(16),
    width: '100%',
    maxHeight: hp(400)
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: wp(20),
    paddingVertical: hp(16),
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0'
  },
  modalTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#000000'
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(20),
    paddingVertical: hp(12),
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0'
  },
  searchInput: {
    flex: 1,
    fontSize: fp(16),
    color: '#000000',
    marginLeft: wp(8)
  },
  optionsList: {
    maxHeight: hp(300)
  },
  optionContent: {
    flex: 1
  },
  optionDesc: {
    fontSize: fp(12),
    color: '#666666',
    marginTop: hp(4)
  },
  optionTextDisabled: {
    color: '#CCCCCC'
  },
  lastOption: {
    borderBottomWidth: 0
  },
  buttonContainer: {
    paddingHorizontal: wp(20),
    paddingVertical: hp(16),
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0'
  },
  confirmButton: {
    backgroundColor: '#007AFF',
    borderRadius: wp(8),
    paddingVertical: hp(12),
    alignItems: 'center'
  },
  confirmButtonText: {
    fontSize: fp(16),
    color: '#FFFFFF',
    fontWeight: '600'
  }
});

export default UniversalDropdown;
