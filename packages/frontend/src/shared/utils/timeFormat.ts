/**
 * 智能时间格式化工具
 * 参考主流APP的时间显示逻辑
 * 
 * @fileoverview 企业级时间显示工具
 * <AUTHOR> Assistant
 * @since 2025-08-05
 */

/**
 * 智能时间格式化
 * 根据时间距离现在的长短，显示不同格式的时间
 * 
 * 规则：
 * - 当天：只显示时分（如：14:30）
 * - 昨天：显示"昨天 14:30"
 * - 本周：显示星期（如：周三 14:30）
 * - 本年：显示月日（如：8月5日）
 * - 跨年：显示年月日（如：2024年12月1日）
 * 
 * @param dateString ISO时间字符串或Date对象
 * @returns 格式化后的时间字符串
 */
export const formatSmartTime = (dateString: string | Date): string => {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    const now = new Date();
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '时间无效';
    }
    
    // 获取各种时间组件
    const dateYear = date.getFullYear();
    const dateMonth = date.getMonth();
    const dateDay = date.getDate();
    const dateHours = date.getHours();
    const dateMinutes = date.getMinutes();
    
    const nowYear = now.getFullYear();
    const nowMonth = now.getMonth();
    const nowDay = now.getDate();
    
    // 格式化时分
    const timeStr = `${dateHours.toString().padStart(2, '0')}:${dateMinutes.toString().padStart(2, '0')}`;
    
    // 判断是否为同一天
    const isSameDay = dateYear === nowYear && dateMonth === nowMonth && dateDay === nowDay;
    
    if (isSameDay) {
      // 当天：只显示时分
      return timeStr;
    }
    
    // 判断是否为昨天
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    const isYesterday = dateYear === yesterday.getFullYear() && 
                       dateMonth === yesterday.getMonth() && 
                       dateDay === yesterday.getDate();
    
    if (isYesterday) {
      // 昨天：显示"昨天 时分"
      return `昨天 ${timeStr}`;
    }
    
    // 判断是否为本周（过去7天内）
    const daysDiff = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff >= 0 && daysDiff <= 6) {
      // 本周：显示星期
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const weekday = weekdays[date.getDay()];
      return `${weekday} ${timeStr}`;
    }
    
    // 判断是否为同一年
    if (dateYear === nowYear) {
      // 本年：显示月日
      const month = dateMonth + 1;
      return `${month}月${dateDay}日`;
    }
    
    // 跨年：显示年月日
    const month = dateMonth + 1;
    return `${dateYear}年${month}月${dateDay}日`;
    
  } catch (error) {
    console.error('[TimeFormat] 时间格式化失败:', error);
    return '时间错误';
  }
};

/**
 * 格式化完整时间（用于详情页等需要完整时间的场景）
 * 
 * @param dateString ISO时间字符串或Date对象
 * @returns 完整的时间字符串（如：2025年8月5日 14:30）
 */
export const formatFullTime = (dateString: string | Date): string => {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    
    if (isNaN(date.getTime())) {
      return '时间无效';
    }
    
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    
    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    
    return `${year}年${month}月${day}日 ${timeStr}`;
    
  } catch (error) {
    console.error('[TimeFormat] 完整时间格式化失败:', error);
    return '时间错误';
  }
};

/**
 * 格式化相对时间（如：3分钟前、2小时前）
 * 用于实时性要求较高的场景
 * 
 * @param dateString ISO时间字符串或Date对象
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (dateString: string | Date): string => {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    const now = new Date();
    
    if (isNaN(date.getTime())) {
      return '时间无效';
    }
    
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffMinutes < 1) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      // 超过一周，使用智能时间格式
      return formatSmartTime(date);
    }
    
  } catch (error) {
    console.error('[TimeFormat] 相对时间格式化失败:', error);
    return '时间错误';
  }
};

/**
 * 检查时间是否为今天
 * 
 * @param dateString ISO时间字符串或Date对象
 * @returns 是否为今天
 */
export const isToday = (dateString: string | Date): boolean => {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    const now = new Date();
    
    return date.getFullYear() === now.getFullYear() &&
           date.getMonth() === now.getMonth() &&
           date.getDate() === now.getDate();
  } catch {
    return false;
  }
};

/**
 * 检查时间是否为昨天
 * 
 * @param dateString ISO时间字符串或Date对象
 * @returns 是否为昨天
 */
export const isYesterday = (dateString: string | Date): boolean => {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    return date.getFullYear() === yesterday.getFullYear() &&
           date.getMonth() === yesterday.getMonth() &&
           date.getDate() === yesterday.getDate();
  } catch {
    return false;
  }
};
