#!/usr/bin/env node

/**
 * 无限循环修复验证脚本
 * 检查AuthContext的useEffect依赖问题
 */

const fs = require('fs');

console.log('🔄 无限循环修复验证');
console.log('==================');

// 检查AuthContext的useEffect依赖
const checkAuthContextDependencies = () => {
  console.log('\n📋 检查AuthContext useEffect依赖...');
  
  const authContextFile = 'packages/frontend/src/contexts/AuthContext.tsx';
  
  if (fs.existsSync(authContextFile)) {
    const content = fs.readFileSync(authContextFile, 'utf8');
    
    // 查找useEffect的依赖数组
    const useEffectMatches = content.match(/useEffect\([\s\S]*?\}, \[(.*?)\]/g);
    
    if (useEffectMatches) {
      console.log(`  找到 ${useEffectMatches.length} 个useEffect:`);
      
      useEffectMatches.forEach((match, index) => {
        const dependencyMatch = match.match(/\[(.*?)\]/);
        const dependencies = dependencyMatch ? dependencyMatch[1].trim() : '';
        
        console.log(`    useEffect ${index + 1}: [${dependencies}]`);
        
        // 检查是否有问题的依赖
        if (dependencies.includes('authStore')) {
          console.log(`      ❌ 包含authStore依赖，可能导致无限循环`);
        } else if (dependencies === '') {
          console.log(`      ✅ 空依赖数组，只执行一次`);
        } else {
          console.log(`      ⚠️  其他依赖: ${dependencies}`);
        }
      });
    } else {
      console.log('  ❌ 没有找到useEffect');
    }
    
    // 检查是否有修复注释
    const hasFixComment = content.includes('修复无限循环');
    console.log(`  修复注释: ${hasFixComment ? '✅' : '❌'}`);
    
    return !content.includes('}, [authStore]');
  } else {
    console.log('  ❌ AuthContext文件不存在');
    return false;
  }
};

// 检查其他可能的无限循环问题
const checkOtherInfiniteLoops = () => {
  console.log('\n🔍 检查其他潜在的无限循环...');
  
  const filesToCheck = [
    'packages/frontend/src/contexts/OrderContext.tsx',
    'packages/frontend/src/contexts/MessageContext.tsx',
    'packages/frontend/src/navigation/AppNavigator.tsx'
  ];
  
  let allGood = true;
  
  filesToCheck.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查可能导致无限循环的模式
      const hasProblematicPatterns = [
        content.includes('setState') && content.includes('useEffect') && !content.includes('[]'),
        content.includes('dispatch') && content.includes('useEffect') && content.match(/useEffect\([^}]+\}, \[[^[\]]*[a-zA-Z][^[\]]*\]/),
      ].some(Boolean);
      
      console.log(`  ${filePath}: ${hasProblematicPatterns ? '⚠️  可能有问题' : '✅ 看起来正常'}`);
      
      if (hasProblematicPatterns) allGood = false;
    }
  });
  
  return allGood;
};

// 生成修复报告
const generateFixReport = () => {
  console.log('\n📊 无限循环修复报告:');
  console.log('======================');
  
  const authContextFixed = checkAuthContextDependencies();
  const otherFilesOk = checkOtherInfiniteLoops();
  
  console.log('\n🎯 修复结果:');
  console.log(`✅ AuthContext无限循环修复: ${authContextFixed ? '通过' : '失败'}`);
  console.log(`✅ 其他文件检查: ${otherFilesOk ? '通过' : '需要注意'}`);
  
  if (authContextFixed && otherFilesOk) {
    console.log('\n🎉 无限循环问题已修复！');
    console.log('💡 应用应该能正常启动了');
    
    console.log('\n📋 现在请测试:');
    console.log('1. 启动应用，确认没有"Maximum update depth exceeded"错误');
    console.log('2. 观察日志不再无限重复');
    console.log('3. 应用能正常进入主界面');
    console.log('4. 认证状态检查只执行一次');
  } else {
    console.log('\n⚠️  仍有问题需要解决');
    
    if (!authContextFixed) {
      console.log('❌ AuthContext的useEffect依赖仍有问题');
    }
    
    if (!otherFilesOk) {
      console.log('❌ 其他文件可能也有无限循环问题');
    }
  }
  
  console.log('\n✅ 检查完成！');
};

// 执行检查
generateFixReport();
