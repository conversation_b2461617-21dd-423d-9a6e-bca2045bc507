import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { Colors } from '../../../shared/theme/designSystem';

export interface PropertyType {
  id: string;
  label: string;
  value: string;
}

interface PropertyTypeFilterProps {
  types: PropertyType[];
  selectedTypes?: string[];
  onSelectionChange?: (selectedTypes: string[]) => void;
  multiSelect?: boolean;
}

const DEFAULT_TYPES: PropertyType[] = [
  { id: '1', label: '写字楼', value: 'office' },
  { id: '2', label: '娱乐', value: 'entertainment' },
  { id: '3', label: '土地厂房', value: 'industrial' },
  { id: '4', label: '共享办公', value: 'coworking' },
  { id: '5', label: '活动会场', value: 'venue' },
];

export const PropertyTypeFilter: React.FC<PropertyTypeFilterProps> = ({
  types = DEFAULT_TYPES,
  selectedTypes = [],
  onSelectionChange,
  multiSelect = true,
}) => {
  const [localSelectedTypes, setLocalSelectedTypes] = useState<string[]>([]);
  const currentSelectedTypes = selectedTypes.length > 0 ? selectedTypes : localSelectedTypes;

  const handleTypePress = (typeValue: string) => {
    let newSelection: string[];

    if (multiSelect) {
      if (currentSelectedTypes.includes(typeValue)) {
        newSelection = currentSelectedTypes.filter(t => t !== typeValue);
      } else {
        newSelection = [...currentSelectedTypes, typeValue];
      }
    } else {
      newSelection = currentSelectedTypes.includes(typeValue) ? [] : [typeValue];
    }

    if (selectedTypes.length === 0) {
      setLocalSelectedTypes(newSelection);
    }
    
    onSelectionChange?.(newSelection);
  };

  const isSelected = (typeValue: string) => {
    return currentSelectedTypes.includes(typeValue);
  };

  return (
    <View style={styles.container}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {types.map((type) => (
          <TouchableOpacity
            key={type.id}
            style={[
              styles.typeButton,
              isSelected(type.value) && styles.typeButtonSelected,
            ]}
            onPress={() => handleTypePress(type.value)}
          >
            <Text
              style={[
                styles.typeText,
                isSelected(type.value) && styles.typeTextSelected,
              ]}
            >
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    backgroundColor: Colors.background.primary,
  },
  scrollContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  typeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.neutral[200],
    minWidth: 60,
    alignItems: 'center',
  },
  typeButtonSelected: {
    backgroundColor: Colors.primary[500],
    borderColor: Colors.primary[500],
  },
  typeText: {
    fontSize: 14,
    color: Colors.text.primary,
    fontWeight: '500',
  },
  typeTextSelected: {
    color: Colors.background.primary,
  },
});
