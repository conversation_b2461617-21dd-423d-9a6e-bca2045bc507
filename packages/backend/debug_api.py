#!/usr/bin/env python3
"""
API调试脚本 - 遵循AI编码指导文件的调试方法论
"""

import sys
import traceback
import asyncio
from fastapi.testclient import TestClient

sys.path.append('/app')

def test_app_creation():
    """测试应用创建"""
    try:
        print("🔍 测试应用创建...")
        from app.main import app
        print("✅ 应用创建成功")
        return app
    except Exception as e:
        print(f"❌ 应用创建失败: {e}")
        traceback.print_exc()
        return None

def test_basic_endpoints(app):
    """测试基础端点"""
    if not app:
        return False
    
    try:
        print("\n🔍 测试基础端点...")
        client = TestClient(app)
        
        # 测试健康检查
        print("测试健康检查...")
        response = client.get("/health")
        print(f"健康检查状态: {response.status_code}")
        if response.status_code == 200:
            print(f"健康检查响应: {response.json()}")
        else:
            print(f"健康检查失败: {response.text}")
        
        # 测试房源列表API
        print("\n测试房源列表API...")
        response = client.get("/api/v1/properties/")
        print(f"房源列表状态: {response.status_code}")
        if response.status_code != 200:
            print(f"房源列表错误: {response.text}")
            return False
        else:
            print("✅ 房源列表API正常")
            return True
            
    except Exception as e:
        print(f"❌ 端点测试失败: {e}")
        traceback.print_exc()
        return False

async def test_database_connection():
    """测试数据库连接"""
    try:
        print("\n🔍 测试数据库连接...")
        from app.core.db import get_async_session
        from app.models.property.property import Property
        from sqlmodel import select
        
        async with get_async_session() as session:
            print("✅ 数据库连接成功")
            
            # 测试简单查询
            result = await session.exec(select(Property).limit(1))
            properties = result.all()
            print(f"✅ 查询到 {len(properties)} 个房源")
            return True
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主调试函数"""
    print("🚀 开始API调试...")
    
    # 1. 测试应用创建
    app = test_app_creation()
    
    # 2. 测试数据库连接
    db_ok = asyncio.run(test_database_connection())
    
    # 3. 测试基础端点
    if app and db_ok:
        api_ok = test_basic_endpoints(app)
        if api_ok:
            print("\n🎉 所有测试通过！")
        else:
            print("\n❌ API测试失败")
    else:
        print("\n❌ 基础组件测试失败")

if __name__ == "__main__":
    main()
