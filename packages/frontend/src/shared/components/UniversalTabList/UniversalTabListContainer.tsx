/**
 * 🏗️ 企业级架构：通用Tab列表容器组件
 * 遵循前端企业级架构规范 - 完整五层架构
 * 参考主流APP架构：微信、支付宝、淘宝的列表页面
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import PagerView from 'react-native-pager-view';
import { useQuery } from '@tanstack/react-query';

import { UniversalTabListProps, TabListTheme, defaultTheme } from './types';
import { useTabListLogic } from './hooks/useTabListLogic';
import { useUniversalSearch } from './hooks/useUniversalSearch';
import { UniversalTabNavigation } from './components/UniversalTabNavigation';
import { UniversalSearchModal } from './components/UniversalSearchModal';
import { UniversalListScene } from './components/UniversalListScene';
import { spacing } from '../../../shared/utils/responsiveUtils';

export const UniversalTabListContainer = <T extends any>({
  type,
  tabs,
  searchConfig,
  onSearch,
  dataFetcher,
  renderItem,
  keyExtractor,
  headerComponent: HeaderComponent,
  emptyComponent: EmptyComponent,
  loadingComponent: LoadingComponent,
  navigation,
  initialTab,
  onTabChange,
  onItemPress,
  onRefresh,
}: UniversalTabListProps<T>) => {
  // 🏗️ 企业级架构：状态管理层
  const [showSearchModal, setShowSearchModal] = useState(false);
  const theme = defaultTheme;

  // 🔧 Hook层：Tab列表逻辑
  const {
    activeTab,
    currentPage,
    pagerRef,
    handleTabPress,
    handlePageSelected,
    tabsWithIndex,
  } = useTabListLogic({
    type,
    tabs,
    initialTab,
    onTabChange,
  });

  // 🔧 Hook层：通用搜索逻辑
  const {
    searchText,
    isSearching,
    searchResults,
    recentSearches,
    hotSearches,
    searchState,
    handleSearchTextChange,
    handleSearchSubmit,
    selectRecentSearch,
    clearSearch,
    loadRecentSearches,
  } = useUniversalSearch({
    type,
    searchConfig,
    dataFetcher,
    onSearchResults: (results, text) => {
      console.log(`[UniversalTabListContainer] ${type} 搜索结果:`, results.length);
    },
  });

  // 🔧 生命周期：加载历史搜索
  useEffect(() => {
    loadRecentSearches();
  }, [loadRecentSearches]);

  // 🔧 事件处理：搜索按钮点击
  const handleSearchPress = () => {
    setShowSearchModal(true);
  };

  // 🔧 事件处理：搜索模态框关闭
  const handleSearchModalClose = () => {
    setShowSearchModal(false);
    // 清除搜索状态，回到正常Tab模式
    clearSearch();
  };

  // 🔧 事件处理：搜索模态框搜索
  const handleModalSearch = (text: string) => {
    console.log(`[UniversalTabListContainer] 🔍 模态框搜索: "${text}"`);

    // 🔧 企业级UX：不关闭模态框，在模态框内显示搜索结果
    // 开始搜索
    handleSearchSubmit(text, activeTab);
    onSearch(text, activeTab);

    console.log(`[UniversalTabListContainer] ✅ 开始在模态框内显示搜索结果`);
  };

  // 🔧 事件处理：Tab切换
  const handleTabPressWithSearch = (tab: typeof activeTab) => {
    handleTabPress(tab);
    
    // 如果有搜索文本，重新搜索新Tab的数据
    if (searchState.hasSearchText) {
      handleSearchTextChange(searchText, tab);
    }
  };

  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      {/* 🎨 UI层：头部组件 */}
      {HeaderComponent && <HeaderComponent />}

      {/* 🎨 UI层：Tab导航 */}
      <UniversalTabNavigation
        tabs={tabs}
        activeTab={activeTab}
        onTabPress={handleTabPressWithSearch}
        onSearchPress={handleSearchPress}
        theme={theme}
      />

      {/* 🎨 UI层：内容区域 */}
      <View style={styles.content}>
        {searchState.isActive ? (
          // 搜索结果显示
          <UniversalListScene
            type={type}
            status={activeTab}
            data={searchResults}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            isLoading={isSearching}
            isEmpty={searchState.isEmpty}
            emptyComponent={EmptyComponent}
            loadingComponent={LoadingComponent}
            onItemPress={onItemPress}
            searchMode={true}
            searchText={searchText}
            externalLoading={isSearching}
            externalIsEmpty={searchState.isEmpty}
          />
        ) : (
          // 正常Tab切换显示
          <PagerView
            ref={pagerRef}
            style={styles.pagerView}
            initialPage={currentPage}
            onPageSelected={handlePageSelected}
          >
            {tabsWithIndex.map((tab) => (
              <View key={tab.key} style={styles.pageContainer}>
                <UniversalListScene
                  type={type}
                  status={tab.key}
                  dataFetcher={dataFetcher}
                  renderItem={renderItem}
                  keyExtractor={keyExtractor}
                  emptyComponent={EmptyComponent}
                  loadingComponent={LoadingComponent}
                  onItemPress={onItemPress}
                  onRefresh={onRefresh}
                />
              </View>
            ))}
          </PagerView>
        )}
      </View>

      {/* 🎨 UI层：搜索模态框 */}
      <UniversalSearchModal
        visible={showSearchModal}
        onClose={handleSearchModalClose}
        type={type}
        searchConfig={searchConfig}
        onSearch={handleModalSearch}
        recentSearches={recentSearches}
        hotSearches={hotSearches}
        searchResults={searchResults}
        isSearching={isSearching}
        hasSearched={searchState.hasSearchText}
        searchText={searchText}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        onItemPress={onItemPress}
      />
    </View>
  );
};

const createStyles = (theme: TabListTheme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  
  content: {
    flex: 1,
  },
  
  pagerView: {
    flex: 1,
  },
  
  pageContainer: {
    flex: 1,
  },
});
