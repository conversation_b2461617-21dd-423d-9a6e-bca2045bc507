/**
 * 内存元数据缓存实现
 * 
 * 功能：
 * 1. 在内存中缓存媒体元数据
 * 2. 支持基本的CRUD操作
 * 3. 提供TTL过期机制
 * 4. 线程安全的操作
 * 
 * 注意：
 * - 这是一个简单的内存实现，适用于开发和测试
 * - 生产环境建议使用Redis等持久化缓存
 * - 应用重启后数据会丢失
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月16日
 */

import { IMetadataCache, MediaMetadata } from '../types/MediaTypes';

interface CacheEntry {
  data: MediaMetadata;
  timestamp: number;
  ttl?: number; // 生存时间（毫秒）
}

/**
 * 内存元数据缓存实现
 */
export class MemoryMetadataCache implements IMetadataCache {
  private cache: Map<string, CacheEntry> = new Map();
  private defaultTTL: number = 24 * 60 * 60 * 1000; // 24小时
  private cleanupInterval: NodeJS.Timeout;
  
  constructor(defaultTTL?: number) {
    if (defaultTTL) {
      this.defaultTTL = defaultTTL;
    }
    
    // 启动定期清理过期数据
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000); // 每5分钟清理一次
  }
  
  /**
   * 设置媒体元数据
   */
  async setMetadata(mediaId: string, metadata: Partial<MediaMetadata>): Promise<void> {
    try {
      const now = Date.now();
      
      // 如果已存在，合并数据
      const existing = this.cache.get(mediaId);
      const fullMetadata: MediaMetadata = existing ? 
        { ...existing.data, ...metadata } : 
        {
          mediaId,
          type: metadata.type || 'image',
          businessType: metadata.businessType!,
          originalName: metadata.originalName || '',
          size: metadata.size || 0,
          uploadTime: metadata.uploadTime || new Date(),
          ...metadata
        };
      
      this.cache.set(mediaId, {
        data: fullMetadata,
        timestamp: now,
        ttl: this.defaultTTL
      });
      
    } catch (error) {
      console.error('Failed to set metadata:', error);
      throw error;
    }
  }
  
  /**
   * 获取媒体元数据
   */
  async getMetadata(mediaId: string): Promise<MediaMetadata> {
    try {
      const entry = this.cache.get(mediaId);
      
      if (!entry) {
        throw new Error(`Metadata not found for mediaId: ${mediaId}`);
      }
      
      // 检查是否过期
      if (this.isExpired(entry)) {
        this.cache.delete(mediaId);
        throw new Error(`Metadata expired for mediaId: ${mediaId}`);
      }
      
      return entry.data;
      
    } catch (error) {
      console.error('Failed to get metadata:', error);
      throw error;
    }
  }
  
  /**
   * 更新媒体元数据
   */
  async updateMetadata(mediaId: string, updates: Partial<MediaMetadata>): Promise<void> {
    try {
      const existing = await this.getMetadata(mediaId);
      const updatedMetadata = { ...existing, ...updates };
      
      await this.setMetadata(mediaId, updatedMetadata);
      
    } catch (error) {
      console.error('Failed to update metadata:', error);
      throw error;
    }
  }
  
  /**
   * 删除媒体元数据
   */
  async deleteMetadata(mediaId: string): Promise<void> {
    try {
      const deleted = this.cache.delete(mediaId);
      
      if (!deleted) {
        console.warn(`Metadata not found for deletion: ${mediaId}`);
      }
      
    } catch (error) {
      console.error('Failed to delete metadata:', error);
      throw error;
    }
  }
  
  /**
   * 检查缓存项是否过期
   */
  private isExpired(entry: CacheEntry): boolean {
    if (!entry.ttl) {
      return false;
    }
    
    const now = Date.now();
    return (now - entry.timestamp) > entry.ttl;
  }
  
  /**
   * 清理过期的缓存项
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [mediaId, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(mediaId);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`Cleaned up ${cleanedCount} expired metadata entries`);
    }
  }
  
  /**
   * 获取缓存统计信息
   */
  getStats(): {
    totalEntries: number;
    memoryUsage: string;
  } {
    return {
      totalEntries: this.cache.size,
      memoryUsage: `${Math.round(JSON.stringify([...this.cache.values()]).length / 1024)}KB`
    };
  }
  
  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
    console.log('Metadata cache cleared');
  }
  
  /**
   * 销毁缓存实例
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}
