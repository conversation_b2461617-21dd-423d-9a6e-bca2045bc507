/**
 * 相似房源数据获取Hook
 * 使用房源搜索API来实现相似房源推荐功能
 */

import { useState, useEffect } from 'react';
import type { PropertyDetailData } from '@property/types';

interface UseSimilarPropertiesProps {
  propertyId: string;
  propertyData?: PropertyDetailData;
  limit?: number;
}

interface SimilarPropertiesResult {
  properties: PropertyDetailData[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useSimilarProperties = ({
  propertyId,
  propertyData,
  limit = 10
}: UseSimilarPropertiesProps): SimilarPropertiesResult => {
  const [properties, setProperties] = useState<PropertyDetailData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSimilarProperties = async () => {
    if (!propertyData) return;

    try {
      setLoading(true);
      setError(null);

      // 使用房源搜索API来获取相似房源
      // 基于当前房源的类型、价格范围等属性进行搜索
      const params = new URLSearchParams({
        // 基础筛选条件
        limit: limit.toString(),
        skip: '0',
        // 排除当前房源
        exclude_property_id: propertyId,
        // 按照创建时间排序，获取最新的相似房源
        sort_by: 'created_at',
        sort_order: 'desc'
      });

      // 如果有房源类型，添加筛选条件
      if (propertyData.propertyDetails?.type) {
        // 这里需要将前端的房源类型映射到后端的枚举值
        // 暂时使用搜索关键词的方式
        params.append('search', propertyData.propertyDetails.type);
      }

      // 如果有价格范围，计算相似价格区间
      if (propertyData.keyInfo?.rent?.price) {
        const priceText = propertyData.keyInfo.rent.price;
        const priceMatch = priceText.match(/(\d+)/);
        if (priceMatch) {
          const price = parseInt(priceMatch[1]);
          // 设置±30%的价格区间
          const minPrice = Math.floor(price * 0.7);
          const maxPrice = Math.floor(price * 1.3);
          params.append('min_price', minPrice.toString());
          params.append('max_price', maxPrice.toString());
        }
      }

      const response = await fetch(
        `http://8.134.250.136:8082/api/v1/properties/?${params.toString()}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        // 转换后端数据格式为前端PropertyDetailData格式
        const transformedProperties = transformBackendToFrontend(data.items || []);
        setProperties(transformedProperties);
      } else {
        console.error('获取相似房源失败:', response.status);
        setError('获取相似房源失败');
        setProperties([]);
      }
    } catch (err) {
      console.error('获取相似房源错误:', err);
      setError('网络错误，请稍后重试');
      setProperties([]);
    } finally {
      setLoading(false);
    }
  };

  // 将后端房源数据转换为前端PropertyDetailData格式
  const transformBackendToFrontend = (backendProperties: any[]): PropertyDetailData[] => {
    return backendProperties.map((prop, index) => ({
      id: prop.id || `similar-${index}`,
      title: prop.title || '房源标题',
      media: {
        displayTab: '图片' as const,
        currentIndex: 0,
        totalCount: 1,
        images: [
          {
            id: `${prop.id}-image-1`,
            type: 'image' as const,
            url: '/api/placeholder/400/300',
            title: '房源图片',
            description: '房源展示图'
          }
        ],
        videos: []
      },
      keyInfo: {
        area: `${prop.total_area || 0}m²`,
        transferFee: prop.transfer_price ? `${prop.transfer_price}万元` : '面议',
        rent: {
          price: prop.rent_price ? `${prop.rent_price}元/月` : '面议',
          term: '押二付三'
        }
      },
      propertyDetails: {
        location: prop.address || '位置信息',
        type: prop.property_type || '商业房源',
        floor: `${prop.floor || 1}层/共${prop.total_floors || 1}层`,
        industry: '多种经营',
        status: prop.status === 'ACTIVE' ? '可租赁' : '待审核',
        specs: `面积${prop.total_area || 0}平方米`,
        paymentTerm: '押二付三',
        leaseRemaining: '租期面议',
        customerFlow: '客流信息待补充',
        tags: prop.tags || [],
        discountLink: '优惠咨询>'
      },
      descriptionTabs: {
        tabs: ['房源详情', '楼盘情况'],
        content: prop.description || '暂无描述'
      },
      actionButtons: {
        favorite: { icon: 'heart', text: '收藏' },
        track: { icon: 'eye', text: '追踪' },
        contact: { text: '立即沟通', style: 'primary' as const },
        reserve: { text: '预约看房', subText: '系统推荐', style: 'secondary' as const }
      }
    }));
  };

  const refetch = () => {
    fetchSimilarProperties();
  };

  useEffect(() => {
    if (propertyData && propertyId) {
      fetchSimilarProperties();
    }
  }, [propertyId, propertyData]);

  return {
    properties,
    loading,
    error,
    refetch
  };
};