# 🚀 前端项目专用npm配置 - 构建加速优化

# 国内镜像配置
registry=https://registry.npmmirror.com/

# React Native和Expo相关包镜像
@react-native:registry=https://registry.npmmirror.com/
@expo:registry=https://registry.npmmirror.com/
@react-navigation:registry=https://registry.npmmirror.com/

# 二进制文件镜像 - 大幅提升安装速度
electron_mirror=https://npmmirror.com/mirrors/electron/
node_sqlite3_binary_host_mirror=https://npmmirror.com/mirrors/sqlite3
chromedriver_cdnurl=https://npmmirror.com/mirrors/chromedriver
phantomjs_cdnurl=https://npmmirror.com/mirrors/phantomjs

# React Native相关
react_native_mirror=https://npmmirror.com/mirrors/react-native/

# 性能优化配置
strict-peer-dependencies=false
auto-install-peers=true
legacy-peer-deps=true

# 缓存配置
cache-max=86400000
prefer-offline=true
