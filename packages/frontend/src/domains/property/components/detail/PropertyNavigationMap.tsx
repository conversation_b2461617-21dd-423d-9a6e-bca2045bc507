/**
 * 🚀 PropertyNavigationMap - Git版本完整恢复 + 企业级组件集成
 * 基于git版本3f4052b00完全恢复，使用现有的RouteSelectionPanel和RouteInfoDisplay组件
 * 功能：
 * 1. 实时获取用户GPS位置作为起点
 * 2. 房源地址作为终点
 * 3. 完整的红绿灯数量显示（🚦 X个，途经 X 个红绿灯）
 * 4. 1-4条驾车路线选择
 * 5. 使用现有企业级组件确保功能完整
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import MapView, { Marker } from 'react-native-amap3d';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  getRouteByMode,
  type RouteRequest,
} from '../../services/amapRouteService';

// 🚀 使用现有的企业级组件
import { RouteSelectionPanel } from './components/RouteSelectionPanel';
import { RouteInfoDisplay } from './components/RouteInfoDisplay';

// 🚀 使用统一的类型定义
import {
  RouteMode,
  RouteResult,
  SelectedRoute,
} from './types/navigation.types';

interface NavigationMapProps {
  route: {
    params: {
      propertyLocation: {
        latitude: number | null;
        longitude: number | null;
        address: string;
      };
    };
  };
}

const PropertyNavigationMapComponent: React.FC<NavigationMapProps> = ({
  route,
}) => {
  const { propertyLocation } = route.params;

  // 🚀 高德原生定位状态
  const [nativeLocation, setNativeLocation] = useState<{
    latitude: number;
    longitude: number;
    accuracy: number;
  } | null>(null);

  // 地图状态
  const [mapReady, setMapReady] = useState(false);

  // 🚀 使用企业级组件需要的状态结构
  const [routeResult, setRouteResult] = useState<RouteResult>({
    routes: [],
    isLoading: false,
    error: null,
    drivingAlternatives: [], // 🚗 多路线支持
  });

  const [selectedRoute, setSelectedRoute] = useState<SelectedRoute>({
    mode: RouteMode.DRIVING,
    alternativeIndex: 0,
  });

  // 🎯 地址文本
  const startLocationText = '我的位置';
  const endLocationText = propertyLocation?.address || '目标地址';

  // 🔧 初始化逻辑
  useEffect(() => {
    let isMounted = true;

    const initializeMap = async () => {
      if (!isMounted) return;

      console.log('🚀 [PropertyNavigationMap] 初始化开始');

      try {
        await AsyncStorage.setItem('amap_privacy_agreed_v1', 'true');

        if (!isMounted) return;

        console.log('⏳ 等待MapView加载...');
      } catch (error) {
        console.error('❌ [PropertyNavigationMap] 初始化失败:', error);
      }
    };

    initializeMap();

    return () => {
      isMounted = false;
    };
  }, []);

  // 🚀 获取当前起点位置
  const getCurrentStartLocation = useCallback(() => {
    if (nativeLocation) {
      return {
        latitude: nativeLocation.latitude,
        longitude: nativeLocation.longitude,
      };
    }
    // 备用：南宁测试位置
    return {
      latitude: 22.807413,
      longitude: 108.421136,
    };
  }, [nativeLocation]);

  // 🚀 获取当前终点位置
  const getCurrentEndLocation = useCallback(() => {
    if (propertyLocation?.latitude && propertyLocation?.longitude) {
      return {
        latitude: propertyLocation.latitude,
        longitude: propertyLocation.longitude,
      };
    }
    return null;
  }, [propertyLocation]);

  // 🚀 路线规划API调用（支持企业级组件数据结构）
  const calculateRoute = useCallback(
    async (mode: RouteMode, requestMultiRoute?: boolean) => {
      const currentStart = getCurrentStartLocation();
      const currentEnd = getCurrentEndLocation();

      // 检查是否有必要的位置数据
      if (!currentStart || !currentEnd) {
        console.log('⚠️ [路线规划] 缺少位置数据，无法计算路线');
        return;
      }

      console.log('🚀 [路线规划] 开始计算路线:', {
        mode,
        requestMultiRoute,
        start: currentStart,
        end: currentEnd,
      });

      // 设置加载状态
      setRouteResult(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      try {
        const request: RouteRequest = {
          origin: {
            latitude: currentStart.latitude,
            longitude: currentStart.longitude,
          },
          destination: {
            latitude: currentEnd.latitude,
            longitude: currentEnd.longitude,
          },
        };

        const routeResponse = await getRouteByMode(mode, request);

        // 🚗 处理驾车多路线响应（符合企业级组件数据结构）
        if (
          mode === RouteMode.DRIVING &&
          requestMultiRoute &&
          routeResponse.routes
        ) {
          const drivingAlternatives = routeResponse.routes.map(
            (route: any, index: number) => {
              const descriptions = ['最快', '距离最短', '备选1', '备选2'];
              return {
                distance: route.distance || '未知距离',
                duration: route.duration || '未知时间',
                description: descriptions[index] || `路线${index + 1}`,
                coordinates: route.coordinates || [],
                trafficLights: route.trafficLights || 0, // 🚦 红绿灯数量
                cost: route.cost,
              };
            }
          );

          setRouteResult({
            routes: [
              {
                mode,
                distance: drivingAlternatives[0]?.distance || '未知',
                duration: drivingAlternatives[0]?.duration || '未知',
                cost: drivingAlternatives[0]?.cost,
                coordinates: drivingAlternatives[0]?.coordinates,
              },
            ],
            isLoading: false,
            error: null,
            drivingAlternatives, // 🚗 提供给企业级组件的多路线数据
          });

          // 重置为第一条路线
          setSelectedRoute({ mode, alternativeIndex: 0 });

          console.log('✅ [路线规划] 驾车多路线计算成功:', {
            routeCount: drivingAlternatives.length,
            firstRouteTrafficLights: drivingAlternatives[0]?.trafficLights,
          });
        } else {
          // 🚶🚌🚴 其他交通方式单路线
          setRouteResult({
            routes: [
              {
                mode,
                distance: routeResponse.distance || '未知距离',
                duration: routeResponse.duration || '未知时间',
                cost: routeResponse.cost,
                coordinates: routeResponse.coordinates,
              },
            ],
            isLoading: false,
            error: null,
            drivingAlternatives: [], // 非驾车模式清空多路线
          });

          setSelectedRoute({ mode, alternativeIndex: 0 });

          console.log('✅ [路线规划] 单路线计算成功:', { mode });
        }
      } catch (error) {
        console.error('❌ [路线规划] 路线计算失败:', error);
        setRouteResult({
          routes: [],
          isLoading: false,
          error: error instanceof Error ? error.message : '路线计算失败',
          drivingAlternatives: [],
        });
      }
    },
    [getCurrentStartLocation, getCurrentEndLocation]
  );

  // 🚀 路线选择处理器（给企业级组件使用）
  const handleRouteSelect = useCallback(
    (mode: RouteMode, alternativeIndex?: number) => {
      console.log(`🎯 [路线选择] 选择: ${mode}, 备选: ${alternativeIndex}`);
      setSelectedRoute({ mode, alternativeIndex: alternativeIndex || 0 });
    },
    []
  );

  // 🚀 获取当前路线坐标（用于地图显示）
  const getCurrentRouteCoordinates = useCallback(() => {
    if (
      selectedRoute.mode === RouteMode.DRIVING &&
      routeResult.drivingAlternatives &&
      routeResult.drivingAlternatives.length > 0
    ) {
      const altIndex = selectedRoute.alternativeIndex || 0;
      return routeResult.drivingAlternatives[altIndex]?.coordinates;
    }

    // 非驾车模式使用主路线坐标
    return routeResult.routes[0]?.coordinates;
  }, [selectedRoute, routeResult]);

  // 🚀 定位或地址更新后自动计算默认路线
  useEffect(() => {
    const currentStart = getCurrentStartLocation();
    const currentEnd = getCurrentEndLocation();

    if (
      currentStart &&
      currentEnd &&
      routeResult.routes.length === 0 &&
      !routeResult.isLoading
    ) {
      console.log('🚀 [自动路线] 检测到位置更新，自动计算驾车多路线');
      calculateRoute(RouteMode.DRIVING, true); // 默认请求多路线
    }
  }, [
    getCurrentStartLocation,
    getCurrentEndLocation,
    routeResult.routes.length,
    routeResult.isLoading,
    calculateRoute,
  ]);

  // 🧪 测试位置备用逻辑
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!nativeLocation && mapReady) {
        console.log('🧪 [测试] 5秒后无定位，使用测试位置');
        setNativeLocation({
          latitude: 22.807413,
          longitude: 108.421136,
          accuracy: 20,
        });
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [nativeLocation, mapReady]);

  return (
    <View style={styles.container}>
      {/* 🗺️ 地图显示区域 */}
      <View style={styles.mapContainer}>
        <MapView
          style={styles.map}
          initialCameraPosition={{
            target: {
              latitude: getCurrentEndLocation()?.latitude || 22.816,
              longitude: getCurrentEndLocation()?.longitude || 108.376,
            },
            zoom: 14,
          }}
          myLocationEnabled={true}
          onLocation={event => {
            const coords = event.nativeEvent?.coords;
            if (coords?.latitude && coords?.longitude) {
              // 防抖更新定位
              setNativeLocation(prev => {
                const shouldLog =
                  !prev ||
                  Math.abs(prev.latitude - coords.latitude) > 0.0001 ||
                  Math.abs(prev.longitude - coords.longitude) > 0.0001;

                if (shouldLog) {
                  console.log('📍 [高德原生定位] 位置更新:', {
                    latitude: coords.latitude.toFixed(6),
                    longitude: coords.longitude.toFixed(6),
                    accuracy: coords.accuracy?.toFixed(0) + 'm' || '未知',
                    timestamp: new Date().toLocaleTimeString(),
                  });
                }

                return {
                  latitude: coords.latitude,
                  longitude: coords.longitude,
                  accuracy: coords.accuracy || 0,
                };
              });
            }
          }}
          onLoad={() => {
            console.log('🎉 [SUCCESS] MapView加载完成！');
            setMapReady(true);
          }}
        >
          {/* 🏠 终点标记（红色房源标记） */}
          {(() => {
            const endLocation = getCurrentEndLocation();
            if (endLocation) {
              return (
                <Marker
                  coordinate={{
                    latitude: endLocation.latitude,
                    longitude: endLocation.longitude,
                  }}
                  title="房源位置"
                  description={endLocationText}
                />
              );
            }
            return null;
          })()}

          {/* 📍 起点标记（蓝色用户位置） */}
          {nativeLocation && (
            <Marker
              coordinate={{
                latitude: nativeLocation.latitude,
                longitude: nativeLocation.longitude,
              }}
              title="我的位置"
              description="当前位置"
            />
          )}

          {/* 🔗 当前选中路线显示 */}
          {(() => {
            const coordinates = getCurrentRouteCoordinates();
            if (coordinates && coordinates.length > 0) {
              const routeMode = selectedRoute.mode;

              // 根据交通方式设置不同颜色
              const getRouteColor = (mode: string) => {
                switch (mode) {
                  case RouteMode.DRIVING:
                    return '#007AFF'; // 蓝色 - 驾车
                  case RouteMode.TAXI:
                    return '#FFD700'; // 金色 - 出租车
                  case RouteMode.TRANSIT:
                    return '#32CD32'; // 绿色 - 公交
                  case RouteMode.WALKING:
                    return '#FF6B35'; // 橙色 - 步行
                  case RouteMode.CYCLING:
                    return '#8A2BE2'; // 紫色 - 骑行
                  default:
                    return '#666666'; // 灰色 - 默认
                }
              };

              // 🚀 使用多个Marker显示路线（react-native-amap3d不直接支持Polyline）
              return (
                <>
                  {coordinates.map((coord, index) => {
                    // 只显示起点、终点和几个关键点
                    if (
                      index === 0 ||
                      index === coordinates.length - 1 ||
                      index % 10 === 0
                    ) {
                      return (
                        <Marker
                          key={`route-point-${index}`}
                          coordinate={{
                            latitude: coord.latitude,
                            longitude: coord.longitude,
                          }}
                        >
                          <View
                            style={{
                              width: 6,
                              height: 6,
                              borderRadius: 3,
                              backgroundColor: getRouteColor(routeMode),
                            }}
                          />
                        </Marker>
                      );
                    }
                    return null;
                  })}
                </>
              );
            }

            return null;
          })()}
        </MapView>
      </View>

      {/* 🚀 使用现有的企业级组件 - 路线选择面板 */}
      <RouteSelectionPanel
        routeResult={routeResult}
        selectedRoute={selectedRoute}
        onRouteSelect={handleRouteSelect}
        onCalculateRoute={calculateRoute}
      />

      {/* 🚀 使用现有的企业级组件 - 路线信息显示 */}
      <RouteInfoDisplay
        routeResult={routeResult}
        selectedRoute={selectedRoute}
        startLocationText={startLocationText}
        endLocationText={endLocationText}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },

  // 🗺️ 地图容器（git版本样式）
  mapContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#E0E0E0',
  },
  map: {
    flex: 1,
    minHeight: 300,
  },
});

export default PropertyNavigationMapComponent;
