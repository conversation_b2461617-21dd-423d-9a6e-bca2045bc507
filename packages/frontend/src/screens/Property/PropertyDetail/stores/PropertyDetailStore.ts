/**
 * 房源详情页企业级状态管理
 * 
 * 基于Zustand的企业级状态管理系统
 * 遵循AI编码指导文件的前端五层架构规范
 */

import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import type { PropertyDetailData } from '@property/types';

/**
 * 房源详情状态接口
 */
interface PropertyDetailState {
  // 数据状态
  propertyData: PropertyDetailData | null;
  sduiConfig: any;
  loading: boolean;
  error: string | null;
  
  // UI状态
  activeTab: 'info' | 'photos' | 'videos';
  isImageViewerVisible: boolean;
  imageViewerIndex: number;
  isFavorited: boolean;
  activeDescriptionTab: number;
  
  // 显示控制
  showSimilarProperties: boolean;
  showNearbyProperties: boolean;
  showMapSection: boolean;
}

/**
 * 房源详情操作接口
 */
interface PropertyDetailActions {
  // 数据操作
  setPropertyData: (data: PropertyDetailData | null) => void;
  setSduiConfig: (config: any) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // UI操作
  setActiveTab: (tab: 'info' | 'photos' | 'videos') => void;
  setImageViewerVisible: (visible: boolean) => void;
  setImageViewerIndex: (index: number) => void;
  setFavorited: (favorited: boolean) => void;
  setActiveDescriptionTab: (tab: number) => void;
  
  // 显示控制
  toggleSimilarProperties: () => void;
  toggleNearbyProperties: () => void;
  toggleMapSection: () => void;
  
  // 复合操作
  openImageViewer: (index: number) => void;
  closeImageViewer: () => void;
  reset: () => void;
  
  // 业务操作
  initializeDetail: (propertyId: string) => Promise<void>;
  refreshData: () => Promise<void>;
}

/**
 * 完整的房源详情Store类型
 */
export type PropertyDetailStore = PropertyDetailState & PropertyDetailActions;

/**
 * 初始状态
 */
const initialState: PropertyDetailState = {
  // 数据状态
  propertyData: null,
  sduiConfig: null,
  loading: false,
  error: null,
  
  // UI状态
  activeTab: 'info',
  isImageViewerVisible: false,
  imageViewerIndex: 0,
  isFavorited: false,
  activeDescriptionTab: 0,
  
  // 显示控制
  showSimilarProperties: true,
  showNearbyProperties: true,
  showMapSection: true,
};

/**
 * 房源详情页企业级状态管理Store
 * 
 * 特性：
 * - devtools: 开发工具支持
 * - persist: 部分状态持久化
 * - subscribeWithSelector: 选择性订阅
 */
export const usePropertyDetailStore = create<PropertyDetailStore>()(
  devtools(
    persist(
      subscribeWithSelector((set, get) => ({
        ...initialState,
        
        // 数据操作
        setPropertyData: (data) => {
          set({ propertyData: data }, false, 'setPropertyData');
        },
        
        setSduiConfig: (config) => {
          set({ sduiConfig: config }, false, 'setSduiConfig');
        },
        
        setLoading: (loading) => {
          set({ loading }, false, 'setLoading');
        },
        
        setError: (error) => {
          set({ error }, false, 'setError');
        },
        
        // UI操作
        setActiveTab: (tab) => {
          set({ activeTab: tab }, false, 'setActiveTab');
        },
        
        setImageViewerVisible: (visible) => {
          set({ isImageViewerVisible: visible }, false, 'setImageViewerVisible');
        },
        
        setImageViewerIndex: (index) => {
          set({ imageViewerIndex: index }, false, 'setImageViewerIndex');
        },
        
        setFavorited: (favorited) => {
          set({ isFavorited: favorited }, false, 'setFavorited');
        },
        
        setActiveDescriptionTab: (tab) => {
          set({ activeDescriptionTab: tab }, false, 'setActiveDescriptionTab');
        },
        
        // 显示控制
        toggleSimilarProperties: () => {
          set(
            (state) => ({ showSimilarProperties: !state.showSimilarProperties }),
            false,
            'toggleSimilarProperties'
          );
        },
        
        toggleNearbyProperties: () => {
          set(
            (state) => ({ showNearbyProperties: !state.showNearbyProperties }),
            false,
            'toggleNearbyProperties'
          );
        },
        
        toggleMapSection: () => {
          set(
            (state) => ({ showMapSection: !state.showMapSection }),
            false,
            'toggleMapSection'
          );
        },
        
        // 复合操作
        openImageViewer: (index) => {
          set(
            { isImageViewerVisible: true, imageViewerIndex: index },
            false,
            'openImageViewer'
          );
        },
        
        closeImageViewer: () => {
          set(
            { isImageViewerVisible: false, imageViewerIndex: 0 },
            false,
            'closeImageViewer'
          );
        },
        
        reset: () => {
          set(initialState, false, 'reset');
        },
        
        // 业务操作
        initializeDetail: async (propertyId: string) => {
          set({ loading: true, error: null }, false, 'initializeDetail:start');
          
          try {
            // 这里将在后续集成实际的API调用
            console.log('初始化房源详情:', propertyId);
            
            // 模拟数据加载
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            set({ loading: false }, false, 'initializeDetail:success');
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '加载失败';
            set(
              { loading: false, error: errorMessage },
              false,
              'initializeDetail:error'
            );
          }
        },
        
        refreshData: async () => {
          const { propertyData } = get();
          if (propertyData?.id) {
            await get().initializeDetail(propertyData.id);
          }
        },
      })),
      {
        name: 'property-detail-store',
        // 只持久化UI偏好设置，不持久化数据
        partialize: (state) => ({
          activeTab: state.activeTab,
          showSimilarProperties: state.showSimilarProperties,
          showNearbyProperties: state.showNearbyProperties,
          showMapSection: state.showMapSection,
        }),
      }
    ),
    {
      name: 'PropertyDetailStore',
    }
  )
);

/**
 * 房源详情状态选择器
 * 用于优化组件订阅，避免不必要的重渲染
 */
export const propertyDetailSelectors = {
  // 数据选择器
  propertyData: (state: PropertyDetailStore) => state.propertyData,
  sduiConfig: (state: PropertyDetailStore) => state.sduiConfig,
  loading: (state: PropertyDetailStore) => state.loading,
  error: (state: PropertyDetailStore) => state.error,
  
  // UI选择器
  activeTab: (state: PropertyDetailStore) => state.activeTab,
  imageViewer: (state: PropertyDetailStore) => ({
    isVisible: state.isImageViewerVisible,
    index: state.imageViewerIndex,
  }),
  isFavorited: (state: PropertyDetailStore) => state.isFavorited,
  
  // 显示控制选择器
  displaySettings: (state: PropertyDetailStore) => ({
    showSimilarProperties: state.showSimilarProperties,
    showNearbyProperties: state.showNearbyProperties,
    showMapSection: state.showMapSection,
  }),
  
  // 复合选择器
  isReady: (state: PropertyDetailStore) => 
    !state.loading && !state.error && !!state.propertyData,
};

/**
 * 房源详情操作选择器
 */
export const propertyDetailActions = {
  // 数据操作
  data: (state: PropertyDetailStore) => ({
    setPropertyData: state.setPropertyData,
    setSduiConfig: state.setSduiConfig,
    setLoading: state.setLoading,
    setError: state.setError,
  }),
  
  // UI操作
  ui: (state: PropertyDetailStore) => ({
    setActiveTab: state.setActiveTab,
    setImageViewerVisible: state.setImageViewerVisible,
    setImageViewerIndex: state.setImageViewerIndex,
    setFavorited: state.setFavorited,
    openImageViewer: state.openImageViewer,
    closeImageViewer: state.closeImageViewer,
  }),
  
  // 业务操作
  business: (state: PropertyDetailStore) => ({
    initializeDetail: state.initializeDetail,
    refreshData: state.refreshData,
    reset: state.reset,
  }),
};