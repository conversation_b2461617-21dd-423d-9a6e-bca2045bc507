/**
 * 地址搜索相关类型定义
 * 遵循企业级TypeScript类型系统规范
 */

export interface AddressResult {
  id: string;
  name: string;
  address: string;
  location: {
    latitude: number;
    longitude: number;
  };
  district: string;
  citycode: string;
  adcode: string;
  type: string;
  typecode: string;
  distance?: number;
  formattedAddress?: string; // 格式化后的完整地址
}

// 🔧 序列化安全的地址类型，用于React Navigation参数传递
export interface SerializableAddress {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  formattedAddress?: string;
  district?: string;
  citycode?: string;
  adcode?: string;
  type?: string;
  typecode?: string;
}

// 🔧 地址转换工具函数：将AddressResult转换为序列化安全的格式
export const convertToSerializableAddress = (
  address: AddressResult
): SerializableAddress => {
  try {
    return {
      id: typeof address.id === 'string' ? address.id : '',
      name: typeof address.name === 'string' ? address.name : '',
      address: typeof address.address === 'string' ? address.address : '',
      latitude:
        typeof address.location?.latitude === 'number'
          ? address.location.latitude
          : 0,
      longitude:
        typeof address.location?.longitude === 'number'
          ? address.location.longitude
          : 0,
      formattedAddress:
        typeof address.formattedAddress === 'string'
          ? address.formattedAddress
          : undefined,
      district:
        typeof address.district === 'string' ? address.district : undefined,
      citycode:
        typeof address.citycode === 'string' ? address.citycode : undefined,
      adcode: typeof address.adcode === 'string' ? address.adcode : undefined,
      type: typeof address.type === 'string' ? address.type : undefined,
      typecode:
        typeof address.typecode === 'string' ? address.typecode : undefined,
    };
  } catch (error) {
    console.error('[convertToSerializableAddress] 转换失败:', error);
    // 返回最小安全格式
    return {
      id: 'fallback_id',
      name: '未知地址',
      address: '地址信息不完整',
      latitude: 0,
      longitude: 0,
    };
  }
};

export interface AddressSearchResult {
  results: AddressResult[];
  totalCount: number;
  hasMore: boolean;
  nextPage?: number;
}

export interface SearchHistoryItem extends AddressResult {
  searchTime: string;
  searchCount: number;
  lastUsed: string;
}

export interface LocationResult {
  latitude: number;
  longitude: number;
  address: string;
  accuracy: number;
  timestamp: string;
}

export interface POIResult {
  id: string;
  name: string;
  address: string;
  location: {
    latitude: number;
    longitude: number;
  };
  category: string;
  distance: number;
  rating?: number;
}

// 搜索参数类型
export interface SearchParams {
  keywords: string;
  city?: string;
  location?: string; // "经度,纬度"
  radius?: number;
  page?: number;
  offset?: number;
}

// 地理编码参数
export interface GeocodeParams {
  address: string;
  city?: string;
}

// 逆地理编码参数
export interface ReverseGeocodeParams {
  location: string; // "经度,纬度"
  radius?: number;
  extensions?: 'base' | 'all';
}

// API响应类型
export interface AddressSearchAPIResponse {
  status: string;
  count: string;
  info: string;
  infocode: string;
  pois?: any[];
  suggestion?: {
    keywords: any[];
    cities: any[];
  };
}

export interface GeocodeAPIResponse {
  status: string;
  info: string;
  infocode: string;
  geocodes?: any[];
}

export interface ReverseGeocodeAPIResponse {
  status: string;
  info: string;
  infocode: string;
  regeocode?: {
    addressComponent: any;
    formatted_address: string;
    pois: any[];
  };
}

// Store状态类型
export interface AddressSearchState {
  // 搜索状态
  searchQuery: string;
  searchResults: AddressResult[];
  isSearching: boolean;
  searchError: string | null;
  hasMore: boolean;
  currentPage: number;

  // 历史记录
  searchHistory: SearchHistoryItem[];

  // UI状态
  showHistory: boolean;
  selectedIndex: number;
  isHistoryExpanded: boolean;

  // 位置状态
  currentLocation: LocationResult | null;
  nearbyPOIs: POIResult[];
  isLoadingLocation: boolean;
  locationError: string | null;

  // 快捷位置
  quickLocations: AddressResult[];
}

// Store操作类型
export interface AddressSearchActions {
  // 搜索操作
  setSearchQuery: (query: string) => void;
  performSearch: (query: string, page?: number) => Promise<void>;
  loadMoreResults: () => Promise<void>;
  clearSearch: () => void;
  setSearchError: (error: string | null) => void;

  // 历史记录操作
  addToHistory: (address: AddressResult) => void;
  removeFromHistory: (addressId: string) => void;
  clearHistory: () => void;
  toggleHistoryExpanded: () => void;

  // 选择操作
  selectAddress: (address: AddressResult) => void;
  setSelectedIndex: (index: number) => void;

  // 位置操作
  getCurrentLocation: () => Promise<void>;
  getNearbyPOIs: (location?: string) => Promise<void>;
  setCurrentLocation: (location: LocationResult | null) => void;

  // 快捷位置操作
  addQuickLocation: (address: AddressResult) => void;
  removeQuickLocation: (addressId: string) => void;

  // UI操作
  setShowHistory: (show: boolean) => void;
  reset: () => void;
}

// 组件Props类型
export interface AddressSearchScreenProps {
  route: {
    params: {
      searchType: 'origin' | 'destination';
      currentAddress?: string;
      onAddressSelect: (address: AddressResult) => void;
    };
  };
}

export interface SearchInputProps {
  placeholder?: string;
  autoFocus?: boolean;
  onSearch?: (query: string) => void;
  onClear?: () => void;
}

export interface SearchResultListProps {
  results: AddressResult[];
  loading: boolean;
  onItemPress: (address: AddressResult) => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

export interface SearchResultItemProps {
  address: AddressResult;
  onPress: (address: AddressResult) => void;
  showDistance?: boolean;
  highlighted?: boolean;
}

export interface HistoryListProps {
  history: SearchHistoryItem[];
  onItemPress: (address: AddressResult) => void;
  onItemRemove?: (addressId: string) => void;
  onClearAll?: () => void;
  expanded?: boolean;
  onToggleExpanded?: () => void;
}

export interface QuickLocationButtonsProps {
  locations: AddressResult[];
  onLocationPress: (address: AddressResult) => void;
  currentLocation?: LocationResult | null;
  onCurrentLocationPress?: () => void;
  loadingLocation?: boolean;
}

// 错误类型
export type AddressSearchError =
  | 'NETWORK_ERROR'
  | 'API_ERROR'
  | 'LOCATION_ERROR'
  | 'PERMISSION_ERROR'
  | 'TIMEOUT_ERROR'
  | 'UNKNOWN_ERROR';

export interface SearchErrorInfo {
  type: AddressSearchError;
  message: string;
  code?: string;
  retryable: boolean;
}
