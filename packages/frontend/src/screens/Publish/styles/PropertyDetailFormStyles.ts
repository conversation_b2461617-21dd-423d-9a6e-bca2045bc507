import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../shared/utils/responsiveUtils';

export const propertyDetailFormStyles = StyleSheet.create({
  errorText: {
    color: '#FF3B30',
    fontSize: fp(12),
    marginTop: hp(4),
    marginLeft: wp(4),
  },
  warningText: {
    color: '#FF9500',
    fontSize: fp(12),
    marginTop: hp(4),
    marginLeft: wp(4),
  },
  inputError: {
    borderColor: '#FF3B30',
    borderWidth: 1,
  },
  // 4步骤进度条样式
  stepProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
  },
  percentageText: {
    fontSize: fp(16),
    fontWeight: 'bold',
    color: '#FF6B35',
    marginRight: wp(12),
    minWidth: wp(45), // 固定宽度，避免跳动
  },
  stepProgressBar: {
    flex: 1,
    flexDirection: 'row',
    height: hp(6),
    marginRight: wp(12),
  },
  stepSegment: {
    flex: 1,
    height: '100%',
    marginRight: wp(2),
    borderRadius: wp(3),
  },
  warmTip: {
    fontSize: fp(12),
    color: '#FF6B35',
    fontWeight: '500',
    flex: 2,
  },
  completeTip: {
    fontSize: fp(12),
    color: '#34C759',
    fontWeight: '600',
    flex: 2,
  },
});
