/**
 * 位置权限管理工具
 * 根据高德地图官方文档实现Android 6.0+动态权限请求
 */

import { Platform, PermissionsAndroid, Alert } from 'react-native';

export interface PermissionResult {
  granted: boolean;
  shouldShowRationale?: boolean;
  error?: string;
}

export class LocationPermissions {
  /**
   * 请求基本定位权限（前台定位）
   */
  static async requestBasicLocationPermissions(): Promise<PermissionResult> {
    if (Platform.OS !== 'android') {
      return { granted: true };
    }

    try {
      console.log('[LocationPermissions] 🔐 开始请求基本定位权限...');

      // Android 12+ 需要同时请求精确和模糊定位权限
      const permissions = [
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
      ];

      const granted = await PermissionsAndroid.requestMultiple(permissions);
      
      console.log('[LocationPermissions] 🔐 权限请求结果:', granted);

      // 检查是否至少获得了一个定位权限
      const hasFineLocation = granted[PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED;
      const hasCoarseLocation = granted[PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED;
      
      const hasLocationPermission = hasFineLocation || hasCoarseLocation;

      if (hasLocationPermission) {
        console.log('[LocationPermissions] ✅ 定位权限获取成功');
        return { granted: true };
      } else {
        console.log('[LocationPermissions] ❌ 定位权限被拒绝');
        return { 
          granted: false, 
          error: '需要位置权限才能使用地图定位功能' 
        };
      }

    } catch (error) {
      console.error('[LocationPermissions] ❌ 请求权限失败:', error);
      return { 
        granted: false, 
        error: '权限请求失败' 
      };
    }
  }

  /**
   * 检查当前定位权限状态
   */
  static async checkLocationPermissions(): Promise<PermissionResult> {
    if (Platform.OS !== 'android') {
      return { granted: true };
    }

    try {
      const fineLocationGranted = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      );
      
      const coarseLocationGranted = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION
      );

      const hasPermission = fineLocationGranted || coarseLocationGranted;
      
      console.log('[LocationPermissions] 🔍 当前权限状态:', {
        fine: fineLocationGranted,
        coarse: coarseLocationGranted,
        hasPermission
      });

      return { granted: hasPermission };

    } catch (error) {
      console.error('[LocationPermissions] ❌ 检查权限失败:', error);
      return { granted: false, error: '检查权限失败' };
    }
  }

  /**
   * 智能权限请求 - 只请求前台定位权限（地图找房功能足够）
   */
  static async smartRequestPermissions(): Promise<PermissionResult> {
    console.log('[LocationPermissions] 🧠 开始智能权限请求...');

    // 先检查当前权限状态
    const currentStatus = await this.checkLocationPermissions();
    
    if (currentStatus.granted) {
      console.log('[LocationPermissions] ✅ 权限已存在，无需重复请求');
      return currentStatus;
    }

    // 权限未授予，请求基本定位权限（足够地图找房使用）
    console.log('[LocationPermissions] 🔐 权限未授予，开始请求前台定位权限...');
    return await this.requestBasicLocationPermissions();
  }

  /**
   * 显示权限被拒绝的说明对话框
   */
  static showPermissionDeniedDialog(): void {
    Alert.alert(
      '需要位置权限',
      '地图功能需要访问您的位置信息。请在系统设置中开启位置权限。',
      [
        { text: '稍后设置', style: 'cancel' },
        { 
          text: '去设置', 
          onPress: () => {
            // 可以使用Linking.openSettings()引导用户到设置页面
            console.log('[LocationPermissions] 引导用户到设置页面');
          }
        }
      ]
    );
  }

}