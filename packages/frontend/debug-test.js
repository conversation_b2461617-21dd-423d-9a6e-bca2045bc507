/**
 * 调试测试脚本
 * 用于验证筛选功能的问题
 */

// 模拟筛选选项数据
const priceRanges = [
  { label: '不限', value: [0, 999999] },
  { label: '1000以下', value: [0, 1000] },
  { label: '1000-3000', value: [1000, 3000] },
  { label: '3000-5000', value: [3000, 5000] },
  { label: '5000-8000', value: [5000, 8000] },
  { label: '8000-12000', value: [8000, 12000] },
  { label: '12000以上', value: [12000, 999999] },
];

const propertyTypes = [
  '写字楼',
  '商铺',
  '厂房',
  '仓库',
  '土地',
  '车位',
  '其他',
];

// 测试渲染逻辑
function testRenderTagOptions() {
  console.log('🔍 [测试] 开始测试筛选选项渲染逻辑');

  // 测试价格区间选项
  console.log('📊 [测试] 价格区间选项数量:', priceRanges.length);
  priceRanges.forEach((option, index) => {
    console.log(
      `  ${index}: ${option.label} -> ${JSON.stringify(option.value)}`
    );
  });

  // 测试房源类型选项
  console.log('🏢 [测试] 房源类型选项数量:', propertyTypes.length);
  propertyTypes.forEach((option, index) => {
    console.log(`  ${index}: ${option}`);
  });

  // 测试选中状态逻辑
  const selectedPriceRange = [1000, 3000];
  const selectedPropertyTypes = ['写字楼', '商铺'];

  console.log('✅ [测试] 选中状态测试:');
  console.log('  选中价格区间:', JSON.stringify(selectedPriceRange));
  console.log('  选中房源类型:', selectedPropertyTypes);

  // 测试价格区间匹配
  priceRanges.forEach((option, index) => {
    const isSelected =
      JSON.stringify(selectedPriceRange) === JSON.stringify(option.value);
    console.log(
      `  价格区间 ${option.label}: ${isSelected ? '✅选中' : '❌未选中'}`
    );
  });

  // 测试房源类型匹配
  propertyTypes.forEach((option, index) => {
    const isSelected = selectedPropertyTypes.includes(option);
    console.log(`  房源类型 ${option}: ${isSelected ? '✅选中' : '❌未选中'}`);
  });
}

// 运行测试
testRenderTagOptions();

console.log('🎯 [测试] 筛选选项渲染逻辑测试完成');
