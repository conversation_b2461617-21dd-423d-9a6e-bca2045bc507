import asyncio
from app.core.db import get_async_session
from sqlalchemy import text
from passlib.context import CryptContext

# 密码加密工具
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def reset_user_password():
    async for session in get_async_session():
        # 生成简单密码 "123456" 的哈希
        hashed_password = pwd_context.hash("123456")
        
        # 更新用户密码
        result = await session.execute(
            text('''
                UPDATE users 
                SET hashed_password = :password
                WHERE phone_number = :phone
                RETURNING id, email, phone_number
            '''),
            {
                'password': hashed_password,
                'phone': '18377100296'
            }
        )
        
        user = result.fetchone()
        if user:
            await session.commit()
            print(f'✅ 密码重置成功: {user.email} ({user.phone_number})')
            print('新密码: 123456')
        else:
            print('❌ 未找到用户')
        break

asyncio.run(reset_user_password())