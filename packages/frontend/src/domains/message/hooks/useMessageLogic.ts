/**
 * 消息业务逻辑Hook
 * 封装消息系统的核心业务逻辑，符合企业级架构规范
 * 重构版本：使用Zustand Store管理状态
 */
import { useCallback } from 'react';
import { useMessageStore } from '../stores/MessageStore';
import type { MessageCategory } from '../types/businessMessage.types';

/**
 * 消息业务逻辑Hook接口
 */
interface UseMessageLogicProps {
  initialRole?: 'tenant_buyer' | 'landlord';
  initialCategory?: MessageCategory;
}

/**
 * 消息业务逻辑Hook返回值
 */
interface UseMessageLogicReturn {
  // 状态
  activeRole: 'tenant_buyer' | 'landlord';
  activeCategory: MessageCategory;
  isDataEnabled: boolean;
  
  // 数据（来自useMessageData）
  userRoles: any;
  messageData: any;
  paymentInfo: any;
  interactedUsers: any;
  smsAccount: any;
  
  // 加载状态
  isLoading: boolean;
  hasError: boolean;
  
  // 业务逻辑方法
  handleRoleSwitch: (role: 'tenant_buyer' | 'landlord') => void;
  handleCategorySwitch: (category: MessageCategory) => void;
  enableDataLoading: () => void;
  disableDataLoading: () => void;
  refreshAllData: () => void;
  
  // 计算属性
  hasMessages: boolean;
  totalUnreadCount: number;
  currentRoleMessages: any[];
}

/**
 * 消息业务逻辑Hook
 *
 * @param props - Hook配置参数
 * @returns 业务逻辑状态和方法
 */
export const useMessageLogic = ({
  initialRole = 'tenant_buyer',
  initialCategory = 'all',
}: UseMessageLogicProps = {}): UseMessageLogicReturn => {

  // 企业级根本解决方案：直接使用Store，避免选择器的引用不稳定问题
  // 按照专家建议，避免使用会返回新对象的选择器
  const activeRole = useMessageStore((state) => state.activeRole);
  console.log('[useMessageLogic] ✅ 直接获取activeRole:', activeRole);

  // 企业级解决方案：完全移除useEffect初始化
  // Store应该有合理的默认值，不需要在Hook中强制设置
  // 如果需要初始化，应该在Store创建时设置，而不是在组件中

  // 企业级根本解决方案：直接使用Store方法，避免选择器引用问题
  const handleRoleSwitch = useCallback((role: 'tenant_buyer' | 'landlord') => {
    console.log(`[useMessageLogic] 切换到角色: ${role}`);
    useMessageStore.getState().setActiveRole(role);
  }, []); // 直接使用Store，无需依赖

  // 分类切换处理
  const handleCategorySwitch = useCallback((category: MessageCategory) => {
    console.log(`[useMessageLogic] 切换到分类: ${category}`);
    useMessageStore.getState().setActiveCategory(category);
  }, []); // 直接使用Store，无需依赖

  // 启用数据加载
  const enableDataLoading = useCallback(() => {
    console.log('[useMessageLogic] 启用数据加载');
    useMessageStore.getState().enableDataLoading();
  }, []); // 直接使用Store，无需依赖

  // 禁用数据加载
  const disableDataLoading = useCallback(() => {
    console.log('[useMessageLogic] 禁用数据加载');
    useMessageStore.getState().disableDataLoading();
  }, []); // 直接使用Store，无需依赖

  // 刷新所有数据
  const refreshAllData = useCallback(() => {
    console.log('[useMessageLogic] 刷新所有数据');
    useMessageStore.getState().refreshAllData();
  }, []); // 直接使用Store，无需依赖

  // 所有计算属性现在都从Store中获取，不需要本地计算

  return {
    // 企业级修复：只返回必要的稳定数据，避免引用问题
    activeRole, // 直接从Store获取的稳定值

    // 操作方法（稳定的useCallback，无依赖）
    handleRoleSwitch,
  };
};
