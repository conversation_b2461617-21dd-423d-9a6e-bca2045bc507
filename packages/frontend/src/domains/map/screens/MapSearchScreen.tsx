/**
 * 地图搜索页面 - 企业级架构重构版本
 *
 * 重构目标：
 * 1. 保留原有UI设计和交互逻辑
 * 2. 业务逻辑分离到Hook层
 * 3. UI组件化，职责单一
 * 4. 符合企业级开发标准
 */

import React, { useEffect } from 'react';
import { View, StyleSheet, StatusBar } from 'react-native';
import { useMapScreenState } from '../hooks/useMapScreenState';
import { MapContainer } from '../../../shared/components/MapContainer';
import { SearchHeader } from '../components/SearchHeader/SearchHeader';
import { MapActions } from '../components/MapActions/MapActions';
import { PropertyStats } from '../components/PropertyStats/PropertyStats';
import { RentFilterModal } from '../components/FilterModal/RentFilterModal';
import { SaleFilterModal } from '../components/FilterModal/SaleFilterModal';
import { PropertyTypeSelector } from '../components/PropertyTypeSelector/PropertyTypeSelector';

interface MapSearchScreenProps {
  navigation?: any;
}

export const MapSearchScreen: React.FC<MapSearchScreenProps> = () => {
  // 🔧 基本的组件生命周期监控
  useEffect(() => {
    console.log('[MapSearchScreen] 🚀 组件挂载 - 开始初始化');
    return () => {
      console.log('[MapSearchScreen] 🧹 组件卸载 - 开始清理');
    };
  }, []);

  // 使用屏幕级状态管理Hook
  const {
    mapProps,
    searchProps,
    actionProps,
    filterModalProps,
    selectedPropertyType,
    handlePropertyTypeChange,
  } = useMapScreenState();

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="transparent"
        translucent
      />

      {/* 地图容器 */}
      <MapContainer {...mapProps} />

      {/* 搜索头部 */}
      <SearchHeader {...searchProps} />

      {/* 房源类型选择器 */}
      <PropertyTypeSelector
        selectedType={selectedPropertyType}
        onTypeChange={handlePropertyTypeChange}
      />

      {/* 地图操作按钮 */}
      <MapActions {...actionProps} />

      {/* 底部统计和功能区 - 简化版，只保留轮动文字和解锁按钮 */}
      <PropertyStats />

      {/* 筛选弹窗 - 简化版，删除租售切换 */}
      <RentFilterModal
        visible={filterModalProps.rentModalVisible}
        onClose={filterModalProps.onRentModalClose}
        onApply={filterModalProps.onRentFiltersApply}
      />

      <SaleFilterModal
        visible={filterModalProps.saleModalVisible}
        onClose={filterModalProps.onSaleModalClose}
        onApply={filterModalProps.onSaleFiltersApply}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});
