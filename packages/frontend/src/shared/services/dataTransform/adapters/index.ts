/**
 * 适配器模块导出
 * 
 * 统一导出所有适配器，用于将不同的服务集成到统一转换层架构中
 */

export { MediaServiceAdapter, mediaServiceAdapter } from './MediaServiceAdapter';
export type { 
  UnifiedUploadOptions, 
  UnifiedUploadResult, 
  ViewUrlOptions 
} from './MediaServiceAdapter';

// 重新导出媒体相关类型
export { 
  MediaBusinessType,
  type CompressionConfig,
  type WatermarkConfig,
  type MediaProcessingConfig
} from '../transformers/MediaTransformer';
