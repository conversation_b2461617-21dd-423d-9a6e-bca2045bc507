/**
 * 用户中心类型定义
 * 支持统一转换层，完整保留原始功能
 * 符合企业级架构规范，目标：80行
 */
import { z } from 'zod';

/**
 * 前端用户信息类型
 */
export interface UserInfo {
  id: string;
  phoneNumber: string;
  userName?: string;
  avatar?: string;
  isVerified: boolean;
  userType: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 后端API用户信息类型
 */
export interface UserInfoAPI {
  user_id: string;
  phone_number: string;
  user_name?: string;
  user_avatar?: string;
  is_verified: boolean;
  user_type: string;
  created_at: string;
  updated_at: string;
}

/**
 * 前端租买统计类型
 */
export interface TenantBuyerStats {
  rentalDemands: number;
  purchaseDemands: number;
  interestedProperties: number;
  totalExposure: number;
  totalMatches: number;
}

/**
 * 后端API租买统计类型
 */
export interface TenantBuyerStatsAPI {
  rental_demands: number;
  purchase_demands: number;
  interested_properties: number;
  total_exposure: number;
  total_matches: number;
}

/**
 * 前端业主统计类型
 */
export interface LandlordStats {
  rentalProperties: number;
  saleProperties: number;
  totalFavorites: number;
  totalInquiries: number;
  totalViews: number;
  averageRating: number;
}

/**
 * 后端API业主统计类型
 */
export interface LandlordStatsAPI {
  rental_properties: number;
  sale_properties: number;
  total_favorites: number;
  total_inquiries: number;
  total_views: number;
  average_rating: number;
}

/**
 * 前端订单统计类型
 */
export interface OrderCounts {
  favoriteCount: number;
  appointmentCount: number;
  orderCount: number;
  tenantResourceCount: number;
  completedOrders: number;
  pendingOrders: number;
}

/**
 * 后端API订单统计类型
 */
export interface OrderCountsAPI {
  favorite_count: number;
  appointment_count: number;
  order_count: number;
  tenant_resource_count: number;
  completed_orders: number;
  pending_orders: number;
}

/**
 * Zod验证规则
 */
export const UserInfoSchema = z.object({
  id: z.string(),
  phoneNumber: z.string(),
  userName: z.string().optional(),
  avatar: z.string().optional(),
  isVerified: z.boolean(),
  userType: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const TenantBuyerStatsSchema = z.object({
  rentalDemands: z.number().min(0),
  purchaseDemands: z.number().min(0),
  interestedProperties: z.number().min(0),
  totalExposure: z.number().min(0),
  totalMatches: z.number().min(0),
});

export const LandlordStatsSchema = z.object({
  rentalProperties: z.number().min(0),
  saleProperties: z.number().min(0),
  totalFavorites: z.number().min(0),
  totalInquiries: z.number().min(0),
  totalViews: z.number().min(0),
  averageRating: z.number().min(0).max(5),
});

export const OrderCountsSchema = z.object({
  favoriteCount: z.number().min(0),
  appointmentCount: z.number().min(0),
  orderCount: z.number().min(0),
  tenantResourceCount: z.number().min(0),
  completedOrders: z.number().min(0),
  pendingOrders: z.number().min(0),
});

/**
 * 转换结果类型
 */
export interface TransformResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  validationErrors?: string[];
}

/**
 * 转换选项类型
 */
export interface TransformOptions {
  validateInput?: boolean;
  validateOutput?: boolean;
  throwOnError?: boolean;
  logErrors?: boolean;
}
