/**
 * 🧪 Phase 2.1.2验证：MapNavigationStore集成测试
 * 验证Hook与Store的集成状态
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 [StoreIntegrationTest] 开始验证Store集成...');

// 测试文件路径
const HOOK_FILE =
  '/data/my-real-estate-app/packages/frontend/src/domains/property/components/detail/hooks/usePropertyNavigation.ts';
const STORE_FILE =
  '/data/my-real-estate-app/packages/frontend/src/domains/property/components/detail/stores/MapNavigationStore.ts';

const integrationTests = {
  storeCreation: [],
  hookIntegration: [],
  interfaceCompatibility: [],
};

// 1. Store文件验证
console.log('\n📁 [Test 1] MapNavigationStore文件验证...');

if (fs.existsSync(STORE_FILE)) {
  const storeContent = fs.readFileSync(STORE_FILE, 'utf8');

  const storeChecks = [
    {
      name: 'Zustand三中间件',
      pattern: /devtools.*persist.*subscribeWithSelector/,
      required: true,
    },
    {
      name: '状态接口定义',
      pattern: /interface MapNavigationState/,
      required: true,
    },
    {
      name: '操作接口定义',
      pattern: /interface MapNavigationActions/,
      required: true,
    },
    {
      name: '选择器Hook',
      pattern: /export const use.*= \(\) => useMapNavigationStore/,
      required: true,
    },
    { name: '持久化配置', pattern: /partialize:.*state.*=>/, required: true },
    {
      name: 'swapLocations复合操作',
      pattern: /swapLocations:.*=>.*set/,
      required: true,
    },
  ];

  storeChecks.forEach(({ name, pattern, required }) => {
    const found = pattern.test(storeContent);
    integrationTests.storeCreation.push({
      name,
      status: found ? 'PASS' : 'FAIL',
      required,
    });

    console.log(
      `${found ? '✅' : '❌'} ${name}: ${found ? 'FOUND' : 'MISSING'}`
    );
  });
} else {
  console.log('❌ MapNavigationStore.ts: FILE_NOT_FOUND');
  integrationTests.storeCreation.push({
    name: 'Store文件存在',
    status: 'FAIL',
    required: true,
  });
}

// 2. Hook集成验证
console.log('\n🔧 [Test 2] usePropertyNavigation Hook集成验证...');

if (fs.existsSync(HOOK_FILE)) {
  const hookContent = fs.readFileSync(HOOK_FILE, 'utf8');

  const hookChecks = [
    {
      name: 'Store导入',
      pattern:
        /import.*{[\s\S]*useMapNavigationStore[\s\S]*}.*from.*stores\/MapNavigationStore/,
      required: true,
    },
    {
      name: '选择器Hook使用',
      pattern: /useNativeLocation\(\)/,
      required: true,
    },
    {
      name: 'Actions使用',
      pattern: /useMapNavigationActions\(\)/,
      required: true,
    },
    {
      name: '移除useState',
      pattern: /const \[.*useState/,
      expected: false,
      required: true,
    },
    { name: 'swapLocations使用', pattern: /swapLocations\(\)/, required: true },
    { name: 'Store版本日志', pattern: /Store版本/, required: false },
    {
      name: '向后兼容接口',
      pattern: /return \{[\s\S]*nativeLocation,[\s\S]*\}/,
      required: true,
    },
  ];

  hookChecks.forEach(({ name, pattern, expected = true, required }) => {
    const found = pattern.test(hookContent);
    const passed = expected ? found : !found;

    integrationTests.hookIntegration.push({
      name,
      status: passed ? 'PASS' : 'FAIL',
      required,
    });

    const symbol = passed ? '✅' : '❌';
    const result = expected
      ? found
        ? 'INTEGRATED'
        : 'NOT_INTEGRATED'
      : found
        ? 'STILL_PRESENT'
        : 'REMOVED';

    console.log(`${symbol} ${name}: ${result}`);
  });
} else {
  console.log('❌ usePropertyNavigation.ts: FILE_NOT_FOUND');
  integrationTests.hookIntegration.push({
    name: 'Hook文件存在',
    status: 'FAIL',
    required: true,
  });
}

// 3. 接口兼容性验证
console.log('\n🔗 [Test 3] 接口兼容性验证...');

if (fs.existsSync(HOOK_FILE) && fs.existsSync(STORE_FILE)) {
  const hookContent = fs.readFileSync(HOOK_FILE, 'utf8');
  const storeContent = fs.readFileSync(STORE_FILE, 'utf8');

  const compatibilityChecks = [
    {
      name: 'LocationData类型重用',
      hookPattern: /export type \{ LocationData \}.*stores\/MapNavigationStore/,
      storePattern: /export interface LocationData/,
    },
    {
      name: 'RouteMode类型兼容',
      hookPattern: /export type RouteMode = 'driving'/,
      storePattern: /export type RouteMode = 'driving'/,
    },
    {
      name: '返回接口完整性',
      hookPattern:
        /return \{[\s\S]*nativeLocation,[\s\S]*customStartLocation,[\s\S]*handleAddressSearch,[\s\S]*calculateRoute,[\s\S]*currentStartLocation,[\s\S]*hasValidRoute,[\s\S]*\}/,
      storePattern: null, // 不需要Store验证
    },
  ];

  compatibilityChecks.forEach(({ name, hookPattern, storePattern }) => {
    const hookMatch = hookPattern.test(hookContent);
    const storeMatch = storePattern ? storePattern.test(storeContent) : true;
    const compatible = hookMatch && storeMatch;

    integrationTests.interfaceCompatibility.push({
      name,
      status: compatible ? 'PASS' : 'FAIL',
      required: true,
    });

    console.log(
      `${compatible ? '✅' : '❌'} ${name}: ${compatible ? 'COMPATIBLE' : 'INCOMPATIBLE'}`
    );
  });
}

// 4. 生成集成报告
console.log('\n📊 [Integration Report] Store集成验证汇总:');

const generateCategoryReport = (tests, categoryName) => {
  const total = tests.length;
  const passed = tests.filter(t => t.status === 'PASS').length;
  const required = tests.filter(t => t.required).length;
  const requiredPassed = tests.filter(
    t => t.required && t.status === 'PASS'
  ).length;
  const percentage = Math.round((passed / total) * 100);

  console.log(`\n${categoryName}:`);
  console.log(`  ✅ 通过: ${passed}/${total} (${percentage}%)`);
  console.log(
    `  🔥 必需项: ${requiredPassed}/${required} (${Math.round((requiredPassed / required) * 100)}%)`
  );

  if (passed < total) {
    console.log(`  ❌ 失败项目:`);
    tests
      .filter(t => t.status === 'FAIL')
      .forEach(test => {
        console.log(`    - ${test.name}${test.required ? ' (必需)' : ''}`);
      });
  }

  return percentage;
};

const storeScore = generateCategoryReport(
  integrationTests.storeCreation,
  '🏪 Store创建'
);
const hookScore = generateCategoryReport(
  integrationTests.hookIntegration,
  '🔧 Hook集成'
);
const compatScore = generateCategoryReport(
  integrationTests.interfaceCompatibility,
  '🔗 接口兼容性'
);

const overallScore = Math.round((storeScore + hookScore + compatScore) / 3);

console.log(
  `\n🎯 [OVERALL INTEGRATION SCORE] Store集成质量评分: ${overallScore}% 🎯`
);

// 5. 集成状态评估
if (overallScore >= 95) {
  console.log('🚀 [STATUS] Store集成完美完成！Hook已成功使用Store管理状态！');
} else if (overallScore >= 85) {
  console.log('✅ [STATUS] Store集成基本完成，少量问题需要修复');
} else if (overallScore >= 70) {
  console.log('⚠️ [STATUS] Store集成部分完成，存在重要问题');
} else {
  console.log('❌ [STATUS] Store集成失败，需要重新检查实现');
}

// 6. 下一步行动建议
console.log('\n📋 [Next Actions] 建议的下一步行动:');

if (overallScore < 100) {
  console.log('1. 🔧 修复剩余的集成问题');
  console.log('2. 🧪 运行应用测试Store状态管理功能');
  console.log('3. 🔍 验证所有Hook方法正常工作');
  console.log('4. 📝 更新Phase 2.1.2完成状态');
}

if (overallScore >= 85) {
  console.log('1. ✅ 验证应用运行状态');
  console.log('2. 🎯 继续Phase 2.2任务 (完善类型定义)');
  console.log('3. 📊 运行性能基准测试');
  console.log('4. 🏁 准备Phase 2.3深度集成验证');
}

console.log('\n🔚 [StoreIntegrationTest] Store集成验证测试完成！');
