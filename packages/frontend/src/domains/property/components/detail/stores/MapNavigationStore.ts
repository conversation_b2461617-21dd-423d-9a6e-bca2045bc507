/**
 * 🏪 MapNavigationStore - 企业级地图导航状态管理
 * 遵循Zustand三中间件模式，管理地图导航的全局状态
 *
 * 🎯 Phase 2.2: 完善类型定义，100%类型安全
 * 架构层次：Store层 (五层架构中的第三层)
 * 依赖：无 (最底层状态管理)
 * 被依赖：Hook层 (usePropertyNavigation)
 */

import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';

// ==================== 类型定义 ====================

// 🎯 使用统一类型定义，确保类型一致性
import {
  RouteMode,
  GPSLocation,
  LocationData,
  RouteInfo,
  RouteResult,
  SelectedRoute,
  DrivingAlternative,
} from '../types/navigation.types';

// 重新导出类型供外部使用
export type {
  RouteMode,
  GPSLocation,
  LocationData,
  RouteInfo,
  RouteResult,
  SelectedRoute,
  DrivingAlternative,
} from '../types/navigation.types';

// ==================== 状态接口定义 ====================

export interface MapNavigationState {
  // 位置状态
  nativeLocation: GPSLocation | null;
  customStartLocation: LocationData | null;
  customEndLocation: LocationData | null;

  // UI状态
  startLocationText: string;
  endLocationText: string;
  isLocationSwapped: boolean;
  mapReady: boolean;

  // 路线状态
  routeResult: RouteResult;
  selectedRoute: SelectedRoute;

  // 初始化状态
  initialized: boolean;
}

// ==================== 操作接口定义 ====================

export interface MapNavigationActions {
  // 位置操作
  setNativeLocation: (location: GPSLocation | null) => void;
  setCustomStartLocation: (location: LocationData | null) => void;
  setCustomEndLocation: (location: LocationData | null) => void;
  swapLocations: () => void;

  // UI操作
  setStartLocationText: (text: string) => void;
  setEndLocationText: (text: string) => void;
  setMapReady: (ready: boolean) => void;

  // 路线操作
  setRouteResult: (result: RouteResult) => void;
  setSelectedRoute: (route: SelectedRoute) => void;
  updateRouteLoading: (loading: boolean) => void;
  updateRouteError: (error: string | null) => void;

  // 初始化操作
  setInitialized: (initialized: boolean) => void;

  // 复合操作
  reset: () => void;
}

// ==================== Store实现 ====================

export const useMapNavigationStore = create<
  MapNavigationState & MapNavigationActions
>()(
  devtools(
    persist(
      subscribeWithSelector(set => ({
        // === 初始状态 ===
        nativeLocation: null,
        customStartLocation: null,
        customEndLocation: null,
        startLocationText: '我的位置',
        endLocationText: '目标地址',
        isLocationSwapped: false,
        mapReady: false,
        routeResult: {
          routes: [],
          isLoading: false,
          error: null,
          drivingAlternatives: [],
        },
        selectedRoute: {
          mode: RouteMode.DRIVING,
          alternativeIndex: 0,
        },
        initialized: false,

        // === 删除错误的计算属性实现 ===
        // 计算属性应该通过选择器Hook实现，不在Store内部

        // === 位置操作 ===
        setNativeLocation: location =>
          set({ nativeLocation: location }, false, 'setNativeLocation'),

        setCustomStartLocation: location =>
          set(
            { customStartLocation: location },
            false,
            'setCustomStartLocation'
          ),

        setCustomEndLocation: location =>
          set({ customEndLocation: location }, false, 'setCustomEndLocation'),

        swapLocations: () =>
          set(
            state => ({
              // 交换文本
              startLocationText: state.endLocationText,
              endLocationText: state.startLocationText,

              // 交换坐标
              customStartLocation: state.customEndLocation,
              customEndLocation: state.customStartLocation,

              // 更新切换状态
              isLocationSwapped: !state.isLocationSwapped,
            }),
            false,
            'swapLocations'
          ),

        // === UI操作 ===
        setStartLocationText: text =>
          set({ startLocationText: text }, false, 'setStartLocationText'),

        setEndLocationText: text =>
          set({ endLocationText: text }, false, 'setEndLocationText'),

        setMapReady: ready => set({ mapReady: ready }, false, 'setMapReady'),

        // === 路线操作 ===
        setRouteResult: result =>
          set({ routeResult: result }, false, 'setRouteResult'),

        setSelectedRoute: route =>
          set({ selectedRoute: route }, false, 'setSelectedRoute'),

        updateRouteLoading: loading =>
          set(
            state => {
              const currentRouteResult = state.routeResult || {
                routes: [],
                isLoading: false,
                error: null,
                drivingAlternatives: [],
              };
              return {
                routeResult: {
                  ...currentRouteResult,
                  isLoading: loading,
                  error: loading ? null : currentRouteResult.error,
                },
              };
            },
            false,
            'updateRouteLoading'
          ),

        updateRouteError: error =>
          set(
            state => {
              const currentRouteResult = state.routeResult || {
                routes: [],
                isLoading: false,
                error: null,
                drivingAlternatives: [],
              };
              return {
                routeResult: {
                  ...currentRouteResult,
                  error,
                  isLoading: false,
                },
              };
            },
            false,
            'updateRouteError'
          ),

        // === 初始化操作 ===
        setInitialized: initialized =>
          set({ initialized }, false, 'setInitialized'),

        // === 复合操作 ===
        reset: () =>
          set(
            {
              nativeLocation: null,
              customStartLocation: null,
              customEndLocation: null,
              startLocationText: '我的位置',
              endLocationText: '目标地址',
              isLocationSwapped: false,
              mapReady: false,
              routeResult: {
                routes: [],
                isLoading: false,
                error: null,
                drivingAlternatives: [],
              },
              selectedRoute: {
                mode: RouteMode.DRIVING,
                alternativeIndex: 0,
              },
              initialized: false,
            },
            false,
            'reset'
          ),
      })),
      {
        name: 'map-navigation-store',
        // 选择性持久化（不持久化UI状态和加载状态）
        partialize: state => ({
          customStartLocation: state.customStartLocation,
          customEndLocation: state.customEndLocation,
          startLocationText: state.startLocationText,
          endLocationText: state.endLocationText,
          selectedRoute: state.selectedRoute,
          // 不持久化UI状态：mapReady, isLocationSwapped, routeResult, initialized
        }),
      }
    ),
    {
      name: 'MapNavigationStore',
    }
  )
);

// ==================== 选择器Hook（性能优化） ====================

// 位置状态选择器
export const useNativeLocation = () =>
  useMapNavigationStore(state => state.nativeLocation);
export const useCustomStartLocation = () =>
  useMapNavigationStore(state => state.customStartLocation);
export const useCustomEndLocation = () =>
  useMapNavigationStore(state => state.customEndLocation);

// UI状态选择器
export const useStartLocationText = () =>
  useMapNavigationStore(state => state.startLocationText);
export const useEndLocationText = () =>
  useMapNavigationStore(state => state.endLocationText);
export const useIsLocationSwapped = () =>
  useMapNavigationStore(state => state.isLocationSwapped);
export const useMapReady = () => useMapNavigationStore(state => state.mapReady);

// 路线状态选择器
export const useRouteResult = () =>
  useMapNavigationStore(
    state =>
      state.routeResult || {
        routes: [],
        isLoading: false,
        error: null,
        drivingAlternatives: [],
      }
  );
export const useSelectedRoute = () =>
  useMapNavigationStore(state => state.selectedRoute);
export const useRouteLoading = () =>
  useMapNavigationStore(state => state.routeResult?.isLoading || false);
export const useRouteError = () =>
  useMapNavigationStore(state => state.routeResult?.error || null);

// 初始化状态选择器
export const useInitialized = () =>
  useMapNavigationStore(state => state.initialized);

// 复合状态选择器（计算属性）
// 🔧 紧急修复：使用稳定的选择器避免对象重新创建
const DEFAULT_START_LOCATION = {
  latitude: 0,
  longitude: 0,
  address: '未知位置',
};

const DEFAULT_END_LOCATION = {
  latitude: 0,
  longitude: 0,
  address: '未知位置',
};

// 🔧 修复计算属性的安全访问
export const useCurrentStartLocation = () =>
  useMapNavigationStore(state => {
    if (state.customStartLocation) {
      return state.customStartLocation;
    }
    if (state.nativeLocation) {
      return {
        latitude: state.nativeLocation.latitude,
        longitude: state.nativeLocation.longitude,
        address: '我的位置',
      };
    }
    return DEFAULT_START_LOCATION;
  });

export const useCurrentEndLocation = () =>
  useMapNavigationStore(state => {
    return state.customEndLocation || DEFAULT_END_LOCATION;
  });

export const useHasValidRoute = () =>
  useMapNavigationStore(state => {
    const routeResult = state.routeResult;
    return (
      routeResult &&
      Array.isArray(routeResult.routes) &&
      routeResult.routes.length > 0 &&
      !routeResult.error
    );
  });

export const useCanNavigate = () =>
  useMapNavigationStore(state => {
    const routeResult = state.routeResult;
    const hasValidRoute =
      routeResult &&
      Array.isArray(routeResult.routes) &&
      routeResult.routes.length > 0 &&
      !routeResult.error;
    return hasValidRoute && state.mapReady;
  });

// 🔧 紧急修复：修复无限循环 - 使用稳定的单独选择器
// 替代返回对象的选择器，避免每次创建新对象

// 位置操作选择器
export const useSetNativeLocation = () =>
  useMapNavigationStore(state => state.setNativeLocation);
export const useSetCustomStartLocation = () =>
  useMapNavigationStore(state => state.setCustomStartLocation);
export const useSetCustomEndLocation = () =>
  useMapNavigationStore(state => state.setCustomEndLocation);
export const useSwapLocations = () =>
  useMapNavigationStore(state => state.swapLocations);

// UI操作选择器
export const useSetStartLocationText = () =>
  useMapNavigationStore(state => state.setStartLocationText);
export const useSetEndLocationText = () =>
  useMapNavigationStore(state => state.setEndLocationText);
export const useSetMapReady = () =>
  useMapNavigationStore(state => state.setMapReady);

// 路线操作选择器
export const useSetRouteResult = () =>
  useMapNavigationStore(state => state.setRouteResult);
export const useSetSelectedRoute = () =>
  useMapNavigationStore(state => state.setSelectedRoute);
export const useUpdateRouteLoading = () =>
  useMapNavigationStore(state => state.updateRouteLoading);
export const useUpdateRouteError = () =>
  useMapNavigationStore(state => state.updateRouteError);

// 初始化操作选择器
export const useSetInitialized = () =>
  useMapNavigationStore(state => state.setInitialized);

// 复合操作选择器
export const useReset = () => useMapNavigationStore(state => state.reset);

// 保留原有接口以兼容（但会导致无限循环，已弃用）
export const useMapNavigationActions = () =>
  useMapNavigationStore(state => ({
    // 位置操作
    setNativeLocation: state.setNativeLocation,
    setCustomStartLocation: state.setCustomStartLocation,
    setCustomEndLocation: state.setCustomEndLocation,
    swapLocations: state.swapLocations,

    // UI操作
    setStartLocationText: state.setStartLocationText,
    setEndLocationText: state.setEndLocationText,
    setMapReady: state.setMapReady,

    // 路线操作
    setRouteResult: state.setRouteResult,
    setSelectedRoute: state.setSelectedRoute,
    updateRouteLoading: state.updateRouteLoading,
    updateRouteError: state.updateRouteError,

    // 初始化操作
    setInitialized: state.setInitialized,

    // 复合操作
    reset: state.reset,
  }));

// ==================== 订阅器Hook（副作用监听） ====================

/**
 * 监听路线结果变化的Hook
 * 用于在路线计算完成后执行副作用
 */
export const useRouteResultSubscription = (
  callback: (routeResult: RouteResult) => void
) => {
  return useMapNavigationStore.subscribe(state => state.routeResult, callback, {
    equalityFn: (a, b) =>
      a.routes.length === b.routes.length && a.error === b.error,
    fireImmediately: false,
  });
};

/**
 * 监听位置变化的Hook
 * 用于在起点或终点变化后执行副作用
 */
export const useLocationChangeSubscription = (
  callback: (locations: {
    start: LocationData | null;
    end: LocationData | null;
  }) => void
) => {
  return useMapNavigationStore.subscribe(
    state => ({
      start: state.customStartLocation,
      end: state.customEndLocation,
    }),
    callback,
    {
      equalityFn: (a, b) =>
        a.start?.address === b.start?.address &&
        a.end?.address === b.end?.address,
      fireImmediately: false,
    }
  );
};

export default useMapNavigationStore;
