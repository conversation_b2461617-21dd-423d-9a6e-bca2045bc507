"""
地图服务层

企业级架构 - Service层
处理地图相关的业务逻辑，包括房源地理查询、距离计算、地图聚合等。
严格遵循单一职责原则，只处理业务逻辑，不直接访问数据库。
"""

from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.property_geo_repository import PropertyGeoRepository
from app.models.property.property import Property
import logging
from app.core.exceptions import BusinessError, ValidationError

logger = logging.getLogger(__name__)


class MapService:
    """
    地图服务
    
    企业级业务逻辑层，处理地图相关的复杂业务需求。
    包括房源聚合、距离计算、地图视窗优化等功能。
    """
    
    def __init__(self, session: AsyncSession):
        """
        初始化地图服务
        
        Args:
            session: 异步数据库会话
        """
        self.session = session
        self.geo_repository = PropertyGeoRepository(session)
    
    async def search_properties_by_location(
        self,
        center_lat: float,
        center_lng: float,
        radius_meters: int = 5000,
        limit: int = 50,
        offset: int = 0,
        include_distances: bool = False
    ) -> Dict[str, Any]:
        """
        按地理位置搜索房源
        
        业务逻辑：
        1. 验证输入参数
        2. 执行地理查询
        3. 格式化返回数据
        4. 添加业务元数据
        
        Args:
            center_lat: 中心点纬度
            center_lng: 中心点经度
            radius_meters: 搜索半径（米）
            limit: 返回数量限制
            offset: 偏移量
            include_distances: 是否包含距离信息
            
        Returns:
            包含房源列表和元数据的字典
        """
        try:
            # 业务验证
            self._validate_coordinates(center_lat, center_lng)
            self._validate_search_params(radius_meters, limit)
            
            logger.info(
                f"开始地理位置搜索: 中心点({center_lat}, {center_lng}), "
                f"半径{radius_meters}m, 限制{limit}个"
            )
            
            if include_distances:
                # 获取带距离信息的房源
                properties_with_distances = await self.geo_repository.get_properties_with_distances(
                    center_lat, center_lng, radius_meters, limit
                )
                
                properties = []
                for property_obj, distance in properties_with_distances:
                    property_data = self._format_property_for_map(property_obj)
                    property_data["distance_meters"] = round(distance, 2)
                    properties.append(property_data)
                    
            else:
                # 获取普通房源列表
                property_objects = await self.geo_repository.find_properties_within_radius(
                    center_lat, center_lng, radius_meters, limit, offset
                )
                
                properties = [
                    self._format_property_for_map(prop) for prop in property_objects
                ]
            
            # 构建响应数据
            response = {
                "properties": properties,
                "search_params": {
                    "center": {"lat": center_lat, "lng": center_lng},
                    "radius_meters": radius_meters,
                    "limit": limit,
                    "offset": offset
                },
                "metadata": {
                    "total_found": len(properties),
                    "has_more": len(properties) == limit,
                    "include_distances": include_distances
                }
            }
            
            logger.info(f"地理位置搜索完成: 找到{len(properties)}个房源")
            return response
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"地理位置搜索失败: {str(e)}")
            raise BusinessError(f"地理位置搜索失败: {str(e)}")
    
    async def search_properties_by_bounds(
        self,
        north_lat: float,
        south_lat: float,
        east_lng: float,
        west_lng: float,
        limit: int = 100,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        按地图边界搜索房源
        
        适用于地图视窗查询，当用户拖拽地图时使用。
        
        Args:
            north_lat: 北边界纬度
            south_lat: 南边界纬度
            east_lng: 东边界经度
            west_lng: 西边界经度
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            包含房源列表和元数据的字典
        """
        try:
            # 业务验证
            self._validate_bounds(north_lat, south_lat, east_lng, west_lng)
            
            logger.info(
                f"开始边界搜索: 边界({south_lat}-{north_lat}, {west_lng}-{east_lng})"
            )
            
            # 执行边界查询
            property_objects = await self.geo_repository.find_properties_within_bounds(
                north_lat, south_lat, east_lng, west_lng, limit, offset
            )
            
            properties = [
                self._format_property_for_map(prop) for prop in property_objects
            ]
            
            # 构建响应数据
            response = {
                "properties": properties,
                "search_params": {
                    "bounds": {
                        "north": north_lat,
                        "south": south_lat,
                        "east": east_lng,
                        "west": west_lng
                    },
                    "limit": limit,
                    "offset": offset
                },
                "metadata": {
                    "total_found": len(properties),
                    "has_more": len(properties) == limit
                }
            }
            
            logger.info(f"边界搜索完成: 找到{len(properties)}个房源")
            return response
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"边界搜索失败: {str(e)}")
            raise BusinessError(f"边界搜索失败: {str(e)}")
    
    async def calculate_property_distance(
        self,
        property_id: UUID,
        target_lat: float,
        target_lng: float
    ) -> Dict[str, Any]:
        """
        计算房源到指定点的距离
        
        Args:
            property_id: 房源ID
            target_lat: 目标点纬度
            target_lng: 目标点经度
            
        Returns:
            距离信息字典
        """
        try:
            # 业务验证
            self._validate_coordinates(target_lat, target_lng)
            
            logger.info(f"计算房源{property_id}到目标点({target_lat}, {target_lng})的距离")
            
            # 计算距离
            distance = await self.geo_repository.calculate_distance_to_property(
                property_id, target_lat, target_lng
            )
            
            if distance is None:
                raise BusinessError("房源不存在或无地理位置信息")
            
            # 格式化距离信息
            response = {
                "property_id": str(property_id),
                "target_point": {"lat": target_lat, "lng": target_lng},
                "distance_meters": round(distance, 2),
                "distance_km": round(distance / 1000, 3),
                "distance_display": self._format_distance_display(distance)
            }
            
            logger.info(f"距离计算完成: {distance:.2f}米")
            return response
            
        except ValidationError:
            raise
        except BusinessError:
            raise
        except Exception as e:
            logger.error(f"距离计算失败: {str(e)}")
            raise BusinessError(f"距离计算失败: {str(e)}")
    
    async def update_property_location(
        self,
        property_id: UUID,
        latitude: float,
        longitude: float
    ) -> Dict[str, Any]:
        """
        更新房源地理位置
        
        Args:
            property_id: 房源ID
            latitude: 纬度
            longitude: 经度
            
        Returns:
            更新结果
        """
        try:
            # 业务验证
            self._validate_coordinates(latitude, longitude)
            
            logger.info(f"更新房源{property_id}地理位置: ({latitude}, {longitude})")
            
            # 执行更新
            success = await self.geo_repository.update_property_location(
                property_id, latitude, longitude
            )
            
            if not success:
                raise BusinessError("房源不存在或更新失败")
            
            response = {
                "property_id": str(property_id),
                "location": {"lat": latitude, "lng": longitude},
                "updated": True,
                "message": "地理位置更新成功"
            }
            
            logger.info(f"房源{property_id}地理位置更新成功")
            return response
            
        except ValidationError:
            raise
        except BusinessError:
            raise
        except Exception as e:
            logger.error(f"地理位置更新失败: {str(e)}")
            raise BusinessError(f"地理位置更新失败: {str(e)}")
    
    def _validate_coordinates(self, lat: float, lng: float) -> None:
        """验证坐标有效性"""
        if not (-90 <= lat <= 90):
            raise ValidationError(f"纬度无效: {lat}，必须在-90到90之间")
        if not (-180 <= lng <= 180):
            raise ValidationError(f"经度无效: {lng}，必须在-180到180之间")
    
    def _validate_search_params(self, radius: int, limit: int) -> None:
        """验证搜索参数"""
        if radius <= 0 or radius > 50000:  # 最大50公里
            raise ValidationError(f"搜索半径无效: {radius}，必须在1到50000米之间")
        if limit <= 0 or limit > 1000:  # 最大1000个
            raise ValidationError(f"返回数量限制无效: {limit}，必须在1到1000之间")
    
    def _validate_bounds(self, north: float, south: float, east: float, west: float) -> None:
        """验证边界参数"""
        if north <= south:
            raise ValidationError(f"边界无效: 北边界({north})必须大于南边界({south})")
        if east <= west:
            raise ValidationError(f"边界无效: 东边界({east})必须大于西边界({west})")
        
        # 验证坐标范围
        self._validate_coordinates(north, east)
        self._validate_coordinates(south, west)
    
    def _format_property_for_map(self, property_obj: Property) -> Dict[str, Any]:
        """格式化房源数据用于地图显示"""
        return {
            "id": str(property_obj.id),
            "title": property_obj.title,
            "property_type": property_obj.property_type,
            "sub_type": property_obj.sub_type,
            "address": property_obj.address,
            "location": {
                "lat": property_obj.latitude,
                "lng": property_obj.longitude
            },
            "area": property_obj.area,
            "rent_price": property_obj.rent_price,
            "sale_price": property_obj.sale_price,
            "status": property_obj.status,
            "created_at": property_obj.created_at.isoformat() if property_obj.created_at else None
        }
    
    def _format_distance_display(self, distance_meters: float) -> str:
        """格式化距离显示"""
        if distance_meters < 1000:
            return f"{int(distance_meters)}米"
        else:
            return f"{distance_meters/1000:.1f}公里"
