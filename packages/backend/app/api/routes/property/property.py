"""
房源API路由

提供房源相关的RESTful API接口，包括CRUD操作、搜索筛选等功能。
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import CurrentUser
from app.core.db import get_async_session
from app.models.user import User
from app.models.property import Property
from pydantic import BaseModel
from app.schemas.property import (
    PropertyCreate, PropertyUpdate, PropertyStatusUpdate, PropertyQuery,
    PropertyResponse, PropertyListResponse
)
from app.services.property.property_service import PropertyService
from app.core.constants import UserRole, PropertyStatus

router = APIRouter(prefix="/properties", tags=["properties"])


class TagRecommendationsResponse(BaseModel):
    tags: List[str]


@router.get("/tags/recommendations", response_model=TagRecommendationsResponse)
async def get_tag_recommendations(
    property_type: str = Query(..., description="房源类型, 例如 '商铺'"),
    count: int = Query(8, ge=1, le=20, description="需要获取的标签数量"),
    session: AsyncSession = Depends(get_async_session)
):
    """
    获取指定房源类型的推荐标签，用于“换一换”功能。
    """
    property_service = PropertyService(session)
    tags = await property_service.get_tag_recommendations(property_type, count)
    return TagRecommendationsResponse(tags=tags)


@router.post("/", response_model=PropertyResponse, status_code=status.HTTP_201_CREATED)
async def create_property(
    property_data: PropertyCreate,
    current_user: User = CurrentUser,
    session: AsyncSession = Depends(get_async_session)
):
    """
    创建房源

    只有房东和管理员可以创建房源。
    """
    # 权限检查：只有房东和管理员可以创建房源
    if current_user.role not in [UserRole.LANDLORD, UserRole.ADMIN, UserRole.MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有房东和管理员可以创建房源"
        )

    property_service = PropertyService(session)

    try:
        property_obj = await property_service.create_property(property_data, current_user.id)
        return PropertyResponse.model_validate(property_obj)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=PropertyListResponse)
async def search_properties(
    property_type: Optional[str] = Query(None, description="房源类型"),
    sub_type: Optional[str] = Query(None, description="房源子类型"),
    status: Optional[str] = Query(None, description="房源状态"),
    min_area: Optional[float] = Query(None, ge=0, description="最小面积"),
    max_area: Optional[float] = Query(None, ge=0, description="最大面积"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    session: AsyncSession = Depends(get_async_session)
):
    """
    搜索房源

    支持多种筛选条件和分页查询。
    """
    # 构建查询参数
    query_params = PropertyQuery(
        property_type=property_type,
        sub_type=sub_type,
        status=status,
        min_area=min_area,
        max_area=max_area,
        keyword=keyword,
        page=page,
        size=size,
        sort_by=sort_by,
        sort_order=sort_order
    )

    property_service = PropertyService(session)
    return await property_service.search_properties(query_params)


@router.get("/{property_id}", response_model=PropertyResponse)
async def get_property(
    property_id: str,
    session: AsyncSession = Depends(get_async_session)
):
    """
    获取房源详情

    所有用户都可以查看已发布的房源详情。
    """
    property_service = PropertyService(session)
    property_obj = await property_service.get_property_by_id(property_id)

    if not property_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房源不存在"
        )

    # 只有已发布的房源才能被查看（除非是房东或管理员）
    if property_obj.status != PropertyStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房源不存在或未发布"
        )

    return PropertyResponse.model_validate(property_obj)


@router.get("/my/properties", response_model=PropertyListResponse)
async def get_my_properties(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = CurrentUser,
    session: AsyncSession = Depends(get_async_session)
):
    """
    获取当前用户的房源列表

    返回当前用户作为房东的所有房源。
    """
    query_params = PropertyQuery(
        owner_id=current_user.id,
        page=page,
        size=size
    )

    property_service = PropertyService(session)
    return await property_service.search_properties(query_params)


@router.put("/{property_id}", response_model=PropertyResponse)
async def update_property(
    property_id: int,
    property_data: PropertyUpdate,
    current_user: User = CurrentUser,
    session: AsyncSession = Depends(get_async_session)
):
    """
    更新房源信息

    只有房源所有者和管理员可以更新房源。
    """
    property_service = PropertyService(session)

    try:
        property_obj = await property_service.update_property(
            property_id, property_data, current_user.id, current_user.role
        )

        if not property_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="房源不存在"
            )

        return PropertyResponse.model_validate(property_obj)

    except PermissionError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此房源"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.patch("/{property_id}/status", response_model=PropertyResponse)
async def update_property_status(
    property_id: int,
    status_data: PropertyStatusUpdate,
    current_user: User = CurrentUser,
    session: AsyncSession = Depends(get_async_session)
):
    """
    更新房源状态

    房源所有者可以更新状态，管理员可以更新状态和审核状态。
    """
    property_service = PropertyService(session)

    try:
        property_obj = await property_service.update_property_status(
            property_id, status_data, current_user.id, current_user.role
        )

        if not property_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="房源不存在"
            )

        return PropertyResponse.model_validate(property_obj)

    except PermissionError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此房源状态"
        )


@router.delete("/{property_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_property(
    property_id: int,
    current_user: User = CurrentUser,
    session: AsyncSession = Depends(get_async_session)
):
    """
    删除房源

    只有房源所有者和管理员可以删除房源。
    """
    property_service = PropertyService(session)

    try:
        success = await property_service.delete_property(
            property_id, current_user.id, current_user.role
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="房源不存在"
            )

    except PermissionError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此房源"
        )
