/**
 * 用户中心统一样式管理
 * 完整保留原始ProfileScreen.tsx的所有样式，确保UI完全一致
 */
import { StyleSheet } from 'react-native';
import {
  wp,
  hp,
  fp,
  spacing,
  fontSize,
  borderRadius,
} from '@shared/utils/responsiveUtils';

// 定义统一的模块间距 - 原间距的二分之一
const UNIFIED_MODULE_SPACING = spacing.md / 2; // wp(8) - 统一模块间距

export const styles = StyleSheet.create({
  // ==================== 主容器样式 ====================
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF', // 纯白色背景
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#FFFFFF', // 滚动视图也是白色背景
  },

  // ==================== 用户信息区域样式 ====================
  userInfoContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: spacing.lg, // wp(24) - 企业级响应式间距
    paddingVertical: spacing.lg, // wp(24)
    marginBottom: UNIFIED_MODULE_SPACING, // 统一模块间距
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userProfile: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    width: wp(70), // 响应式头像容器
    height: wp(70),
    borderRadius: wp(35),
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  avatar: {
    width: wp(66), // 响应式头像尺寸
    height: wp(66),
    borderRadius: wp(33),
  },
  userDetails: {
    marginLeft: spacing.md, // wp(16) - 企业级间距
    flex: 1,
  },
  userName: {
    fontSize: fontSize.lg, // fp(18) - 响应式字体
    fontWeight: '600',
    color: '#333333',
    marginBottom: spacing.xs / 2, // wp(2)
  },
  userType: {
    fontSize: fontSize.sm, // fp(14)
    color: '#666666',
    marginBottom: spacing.xs / 4, // wp(1)
  },
  userPhone: {
    fontSize: fontSize.xs, // fp(12)
    color: '#999999',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md, // wp(16) - 响应式间隙
  },
  actionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.sm, // wp(8)
    minWidth: wp(44), // Material Design最小触摸区域
    minHeight: wp(44),
  },
  actionText: {
    fontSize: fontSize.xs, // fp(12)
    color: '#666666',
    marginTop: spacing.xs / 2, // wp(2)
  },

  // ==================== 核心功能菜单样式 ====================
  menuSection: {
    // 去掉白色容器背景，直接使用网格布局
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg, // wp(24) - 保持水平内边距
    marginBottom: UNIFIED_MODULE_SPACING, // 统一模块间距
  },
  menuItem: {
    width: '22%', // 更合理的宽度分配
    alignItems: 'center',
    paddingVertical: spacing.sm, // wp(8)
    minHeight: wp(44), // Material Design触摸标准
  },
  iconContainer: {
    position: 'relative',
    marginBottom: spacing.sm, // wp(8)
  },
  badge: {
    position: 'absolute',
    top: -wp(6),
    right: -wp(6),
    backgroundColor: '#FF4444',
    borderRadius: borderRadius.full, // 圆形徽章
    minWidth: wp(20),
    height: wp(20),
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xs / 2, // wp(2)
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: fontSize.xs, // fp(12)
    fontWeight: '600',
  },
  menuText: {
    fontSize: fontSize.xs, // fp(12)
    color: '#333333', // 默认黑色
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: fp(16), // 响应式行高
  },
  numberText: {
    fontSize: fontSize['2xl'], // fp(24) - 大号数字
    fontWeight: 'bold',
    textAlign: 'center',
    minWidth: 28, // 确保与图标大小一致
    minHeight: 28,
    textAlignVertical: 'center',
  },

  // ==================== 统计卡片区域样式 ====================
  statsSection: {
    // 强制减少与下方模块的间距
    marginBottom: UNIFIED_MODULE_SPACING / 2,
  },

  // ==================== 更多功能区域样式 ====================
  moreSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: spacing.md, // wp(16)
    paddingVertical: spacing.md, // wp(16)
    marginBottom: UNIFIED_MODULE_SPACING, // 统一模块间距
    // 移除负边距，避免样式冲突
    marginTop: 0,
    borderRadius: borderRadius.md, // rp(12)
    marginHorizontal: spacing.md, // wp(16)
  },
  sectionTitle: {
    fontSize: fontSize.base, // fp(16)
    fontWeight: '600',
    color: '#333333',
    marginBottom: spacing.md, // wp(16)
    paddingHorizontal: spacing.xs, // wp(4)
  },
  moreGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  moreItem: {
    width: '30%',
    backgroundColor: '#F8F9FA',
    borderRadius: borderRadius.md, // rp(12)
    paddingVertical: spacing.md, // wp(16)
    paddingHorizontal: spacing.sm, // wp(8)
    alignItems: 'center',
    marginBottom: spacing.md, // wp(16)
    minHeight: wp(80), // 保证触摸区域
  },
  moreItemText: {
    fontSize: fontSize.sm, // fp(14)
    color: '#333333',
    marginTop: spacing.sm, // wp(8)
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: fp(18), // 响应式行高
  },

  // ==================== 底部空白区域样式 ====================
  bottomSpace: {
    height: hp(100), // 响应式底部间距
  },
});
