/**
 * 企业级表单验证Hook（简化版）
 * 
 * 提供基础的表单验证、键盘管理、错误处理功能
 */

import { useState, useCallback, useMemo } from 'react';
import { z } from 'zod';
import { FormFieldError } from './useKeyboardAwareForm';

// 字段验证规则接口
export interface FieldValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any, allValues?: any) => { valid: boolean; message?: string };
}

// 表单配置接口
export interface EnterpriseFormConfig<T = any> {
  schema?: z.ZodSchema<T>;
  fields: Record<string, FieldValidationRule>;
  defaultValues?: Partial<T>;
  realTimeValidation?: boolean;
  validateOnBlur?: boolean;
  showWarnings?: boolean;
}

// Hook返回值接口
export interface UseEnterpriseFormValidationReturn<T = any> {
  values: T;
  errors: Record<string, string>;
  warnings: Record<string, string>;
  isFormValid: boolean;
  touchedFields: Set<string>;
  setValue: (field: keyof T, value: any) => void;
  setValues: (values: Partial<T>) => void;
  setError: (field: string, message: string) => void;
  clearError: (field: string) => void;
  clearAllErrors: () => void;
  validateField: (field: keyof T, value?: any) => boolean;
  validateAllFields: () => boolean;
  getFieldErrors: () => FormFieldError[];
  markFieldTouched: (field: string) => void;
  isFieldTouched: (field: string) => boolean;
  resetForm: () => void;
  submitForm: (onSubmit: (values: T) => Promise<void>) => Promise<boolean>;
}

/**
 * 企业级表单验证Hook
 */
export function useEnterpriseFormValidation<T = any>(
  config: EnterpriseFormConfig<T>
): UseEnterpriseFormValidationReturn<T> {
  
  const {
    schema,
    fields,
    defaultValues = {} as Partial<T>,
    realTimeValidation = true,
    validateOnBlur = true,
    showWarnings = true,
  } = config;
  
  // 状态管理
  const [values, setValues] = useState<T>({
    ...defaultValues,
  } as T);
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [warnings, setWarnings] = useState<Record<string, string>>({});
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());
  
  // 获取字段显示名称
  const getFieldDisplayName = useCallback((fieldName: string): string => {
    const displayNames: Record<string, string> = {
      title: '标题',
      property_certificate_address: '地址',
      area: '面积',
      floor: '楼层',
      total_floors: '总楼层',
      description: '描述',
      rent_price: '租金',
      sale_price: '售价',
      transfer_price: '转让费',
      transaction_types: '交易类型',
      sub_type: '房源类型',
      orientation: '朝向',
      decoration_level: '装修情况',
      rent_payment_method: '支付方式',
    };
    return displayNames[fieldName] || fieldName;
  }, []);
  
  // 字段验证函数
  const validateField = useCallback((
    field: keyof T, 
    value?: any
  ): boolean => {
    const fieldName = String(field);
    const fieldValue = value !== undefined ? value : values[field];
    const rule = fields[fieldName];
    
    if (!rule) return true;
    
    let errorMessage = '';
    let warningMessage = '';
    
    try {
      // 必填验证
      if (rule.required) {
        if (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === '')) {
          errorMessage = `请填写${getFieldDisplayName(fieldName)}`;
        }
      }
      
      // 只有在有值的时候才进行其他验证
      if (fieldValue) {
        // 字符串验证
        if (typeof fieldValue === 'string') {
          if (rule.minLength && fieldValue.length < rule.minLength) {
            if (fieldValue.length === 0) {
              warningMessage = `请填写${getFieldDisplayName(fieldName)}`;
            } else {
              errorMessage = `${getFieldDisplayName(fieldName)}至少需要${rule.minLength}个字符，当前${fieldValue.length}个字符`;
            }
          }
          
          if (rule.maxLength && fieldValue.length > rule.maxLength) {
            errorMessage = `${getFieldDisplayName(fieldName)}最多${rule.maxLength}个字符`;
          }
        }
        
        // 数值验证
        const numValue = typeof fieldValue === 'number' ? fieldValue : parseFloat(fieldValue);
        if (!isNaN(numValue)) {
          if (rule.min !== undefined && numValue < rule.min) {
            errorMessage = `${getFieldDisplayName(fieldName)}不能小于${rule.min}`;
          }
          if (rule.max !== undefined && numValue > rule.max) {
            errorMessage = `${getFieldDisplayName(fieldName)}不能大于${rule.max}`;
          }
        }
        
        // 正则验证
        if (rule.pattern && typeof fieldValue === 'string' && !rule.pattern.test(fieldValue)) {
          errorMessage = `${getFieldDisplayName(fieldName)}格式不正确`;
        }
        
        // 自定义验证
        if (rule.custom) {
          const customResult = rule.custom(fieldValue, values);
          if (!customResult.valid) {
            errorMessage = customResult.message || `${getFieldDisplayName(fieldName)}验证失败`;
          }
        }
      }
      
      // 更新错误状态
      setErrors(prev => ({
        ...prev,
        [fieldName]: errorMessage
      }));
      
      // 更新警告状态
      if (showWarnings) {
        setWarnings(prev => ({
          ...prev,
          [fieldName]: warningMessage
        }));
      }
      
      return !errorMessage;
      
    } catch (error) {
      console.error(`字段验证失败 ${fieldName}:`, error);
      setErrors(prev => ({
        ...prev,
        [fieldName]: '验证出错，请重新输入'
      }));
      return false;
    }
  }, [fields, values, showWarnings, getFieldDisplayName]);
  
  // 其他方法的简化实现
  const setValue = useCallback((field: keyof T, value: any) => {
    setValues(prev => ({
      ...prev,
      [field]: value
    }));
    
    if (realTimeValidation) {
      setTimeout(() => validateField(field, value), 300);
    }
  }, [validateField, realTimeValidation]);
  
  const setValuesCallback = useCallback((newValues: Partial<T>) => {
    setValues(prev => ({
      ...prev,
      ...newValues
    }));
  }, []);
  
  const validateAllFields = useCallback((): boolean => {
    let allValid = true;
    const fieldNames = Object.keys(fields);
    
    fieldNames.forEach(fieldName => {
      const isValid = validateField(fieldName as keyof T);
      if (!isValid) {
        allValid = false;
      }
    });
    
    return allValid;
  }, [fields, validateField]);
  
  const getFieldErrors = useCallback((): FormFieldError[] => {
    const fieldErrors: FormFieldError[] = [];
    
    Object.entries(errors).forEach(([fieldName, message]) => {
      if (message) {
        fieldErrors.push({
          fieldName,
          message,
          priority: fields[fieldName]?.required ? 1 : 2,
        });
      }
    });
    
    return fieldErrors.sort((a, b) => a.priority - b.priority);
  }, [errors, fields]);
  
  const markFieldTouched = useCallback((field: string) => {
    setTouchedFields(prev => new Set([...prev, field]));
    
    if (validateOnBlur) {
      validateField(field as keyof T);
    }
  }, [validateField, validateOnBlur]);
  
  const isFieldTouched = useCallback((field: string) => {
    return touchedFields.has(field);
  }, [touchedFields]);
  
  const setError = useCallback((field: string, message: string) => {
    setErrors(prev => ({
      ...prev,
      [field]: message
    }));
  }, []);
  
  const clearError = useCallback((field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
    
    setWarnings(prev => {
      const newWarnings = { ...prev };
      delete newWarnings[field];
      return newWarnings;
    });
  }, []);
  
  const clearAllErrors = useCallback(() => {
    setErrors({});
    setWarnings({});
  }, []);
  
  const resetForm = useCallback(() => {
    setValues({ ...defaultValues } as T);
    setErrors({});
    setWarnings({});
    setTouchedFields(new Set());
  }, [defaultValues]);
  
  const submitForm = useCallback(async (
    onSubmit: (values: T) => Promise<void>
  ): Promise<boolean> => {
    try {
      const isValid = validateAllFields();
      
      if (!isValid) {
        return false;
      }
      
      await onSubmit(values);
      return true;
      
    } catch (error) {
      console.error('表单提交失败:', error);
      return false;
    }
  }, [validateAllFields, values]);
  
  const isFormValid = useMemo(() => {
    return Object.keys(errors).length === 0 || 
           Object.values(errors).every(error => !error);
  }, [errors]);
  
  return {
    values,
    errors,
    warnings,
    isFormValid,
    touchedFields,
    setValue,
    setValues: setValuesCallback,
    setError,
    clearError,
    clearAllErrors,
    validateField,
    validateAllFields,
    getFieldErrors,
    markFieldTouched,
    isFieldTouched,
    resetForm,
    submitForm,
  };
}

export default useEnterpriseFormValidation;