/**
 * 基础转换器抽象类
 * 
 * 定义所有数据转换器的统一接口和通用功能
 * 确保企业级代码的一致性和可维护性
 */

import { z } from 'zod';
import { TransformResult, TransformOptions, ValidationRule } from '../types/TransformTypes';

/**
 * 基础转换器抽象类
 * 所有业务转换器都应继承此类
 */
export abstract class BaseTransformer<TInput = any, TOutput = any> {
  protected name: string;
  protected version: string;
  protected validationRules: Map<string, ValidationRule> = new Map();

  constructor(name: string, version: string = '1.0.0') {
    this.name = name;
    this.version = version;
    this.initializeValidationRules();
  }

  /**
   * 初始化验证规则（子类实现）
   */
  protected abstract initializeValidationRules(): void;

  /**
   * 转换为API格式（子类实现）
   */
  abstract toAPI(input: TInput, options?: TransformOptions): TransformResult<TOutput>;

  /**
   * 从API格式转换（子类实现）
   */
  abstract fromAPI(input: TOutput, options?: TransformOptions): TransformResult<TInput>;

  /**
   * 验证输入数据
   */
  protected validateInput(input: any, ruleName: string): TransformResult<any> {
    try {
      const rule = this.validationRules.get(ruleName);
      if (!rule) {
        return {
          success: false,
          error: `验证规则 ${ruleName} 不存在`,
          originalValue: input
        };
      }

      const validatedData = rule.schema.parse(input);
      return {
        success: true,
        data: validatedData,
        originalValue: input
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof z.ZodError 
          ? this.formatZodError(error)
          : `验证失败: ${error}`,
        originalValue: input
      };
    }
  }

  /**
   * 格式化Zod验证错误
   */
  private formatZodError(error: z.ZodError): string {
    const issues = error.issues.map(issue => 
      `${issue.path.join('.')}: ${issue.message}`
    );
    return `数据验证失败: ${issues.join(', ')}`;
  }

  /**
   * 安全转换（带错误处理）
   */
  protected safeTransform<T>(
    transformFn: () => T,
    context: string
  ): TransformResult<T> {
    try {
      const result = transformFn();
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error(`[${this.name}] ${context} 转换失败:`, error);
      return {
        success: false,
        error: `${context} 转换失败: ${error instanceof Error ? error.message : error}`
      };
    }
  }

  /**
   * 批量转换
   */
  protected batchTransform<TIn, TOut>(
    items: TIn[],
    transformFn: (item: TIn) => TransformResult<TOut>
  ): TransformResult<TOut[]> {
    const results: TOut[] = [];
    const errors: string[] = [];

    for (let i = 0; i < items.length; i++) {
      const result = transformFn(items[i]);
      if (result.success && result.data) {
        results.push(result.data);
      } else {
        errors.push(`项目 ${i}: ${result.error}`);
      }
    }

    if (errors.length > 0) {
      return {
        success: false,
        error: `批量转换失败: ${errors.join('; ')}`,
        data: results.length > 0 ? results : undefined
      };
    }

    return {
      success: true,
      data: results
    };
  }

  /**
   * 添加验证规则
   */
  protected addValidationRule(name: string, rule: ValidationRule): void {
    this.validationRules.set(name, rule);
  }

  /**
   * 获取转换器信息
   */
  getInfo() {
    return {
      name: this.name,
      version: this.version,
      validationRules: Array.from(this.validationRules.keys())
    };
  }

  /**
   * 转换器健康检查
   */
  healthCheck(): { healthy: boolean; issues: string[] } {
    const issues: string[] = [];

    // 检查验证规则
    if (this.validationRules.size === 0) {
      issues.push('缺少验证规则');
    }

    // 检查必要方法实现
    if (!this.toAPI || !this.fromAPI) {
      issues.push('缺少必要的转换方法实现');
    }

    return {
      healthy: issues.length === 0,
      issues
    };
  }
}

/**
 * 转换器工厂函数
 */
export function createTransformer<TInput, TOutput>(
  name: string,
  config: {
    version?: string;
    toAPI: (input: TInput, options?: TransformOptions) => TransformResult<TOutput>;
    fromAPI: (input: TOutput, options?: TransformOptions) => TransformResult<TInput>;
    validationRules?: Record<string, ValidationRule>;
  }
): BaseTransformer<TInput, TOutput> {
  return new (class extends BaseTransformer<TInput, TOutput> {
    protected initializeValidationRules(): void {
      if (config.validationRules) {
        Object.entries(config.validationRules).forEach(([name, rule]) => {
          this.addValidationRule(name, rule);
        });
      }
    }

    toAPI(input: TInput, options?: TransformOptions): TransformResult<TOutput> {
      return config.toAPI(input, options);
    }

    fromAPI(input: TOutput, options?: TransformOptions): TransformResult<TInput> {
      return config.fromAPI(input, options);
    }
  })(name, config.version);
}

export default BaseTransformer;
