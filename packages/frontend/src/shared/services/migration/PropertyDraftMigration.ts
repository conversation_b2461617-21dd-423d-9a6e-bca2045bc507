/**
 * 房源草稿数据迁移服务 - 企业级版本
 *
 * 功能：
 * 1. 将本地AsyncStorage中的房源草稿迁移到服务器
 * 2. 数据格式转换和验证
 * 3. 批量上传和错误处理
 * 4. 迁移进度跟踪
 * 5. 回滚机制
 *
 * 使用场景：
 * - 用户首次登录时自动迁移
 * - 手动触发迁移
 * - 系统升级时批量迁移
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Transformers } from '../dataTransform';
import { publishAPI } from '../../../domains/publish/services/publishAPI';
import FeedbackService from '../FeedbackService';

// 迁移状态枚举
export enum MigrationStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  PARTIAL = 'partial',
}

// 迁移结果接口
export interface MigrationResult {
  status: MigrationStatus;
  totalDrafts: number;
  migratedCount: number;
  failedCount: number;
  errors: Array<{
    draftId: string;
    error: string;
  }>;
  migratedIds: string[];
  duration: number;
}

// 本地草稿数据结构
interface LocalPropertyDraft {
  id: string;
  propertyType: string;
  formData: any;
  selectedTags: string[];
  localMediaFiles: any[];
  currentImageIndex: number;
  status: 'draft';
  savedAt: string;
}

/**
 * 房源草稿迁移服务
 */
export class PropertyDraftMigrationService {
  private static instance: PropertyDraftMigrationService;
  private migrationInProgress = false;

  private constructor() {}

  static getInstance(): PropertyDraftMigrationService {
    if (!PropertyDraftMigrationService.instance) {
      PropertyDraftMigrationService.instance =
        new PropertyDraftMigrationService();
    }
    return PropertyDraftMigrationService.instance;
  }

  /**
   * 检查是否需要迁移
   */
  async needsMigration(): Promise<boolean> {
    try {
      const localDrafts = await AsyncStorage.getItem('property_drafts');
      const migrationStatus = await AsyncStorage.getItem(
        'property_draft_migration_status'
      );

      return !!(localDrafts && migrationStatus !== MigrationStatus.COMPLETED);
    } catch (error) {
      console.error('[PropertyDraftMigration] 检查迁移状态失败:', error);
      return false;
    }
  }

  /**
   * 获取本地草稿数量
   */
  async getLocalDraftCount(): Promise<number> {
    try {
      const localDrafts = await AsyncStorage.getItem('property_drafts');
      if (!localDrafts) return 0;

      const drafts = JSON.parse(localDrafts);
      return Array.isArray(drafts) ? drafts.length : 0;
    } catch (error) {
      console.error('[PropertyDraftMigration] 获取本地草稿数量失败:', error);
      return 0;
    }
  }

  /**
   * 执行迁移
   */
  async migrate(showProgress = true): Promise<MigrationResult> {
    if (this.migrationInProgress) {
      throw new Error('迁移正在进行中，请勿重复执行');
    }

    const startTime = Date.now();
    this.migrationInProgress = true;

    try {
      console.log('[PropertyDraftMigration] 🚀 开始迁移房源草稿');

      // 设置迁移状态
      await AsyncStorage.setItem(
        'property_draft_migration_status',
        MigrationStatus.IN_PROGRESS
      );

      if (showProgress) {
        FeedbackService.showInfo('正在迁移本地草稿到云端...');
      }

      // 获取本地草稿
      const localDrafts = await this.getLocalDrafts();
      if (localDrafts.length === 0) {
        console.log('[PropertyDraftMigration] 没有本地草稿需要迁移');
        return this.createResult(
          MigrationStatus.COMPLETED,
          0,
          0,
          0,
          [],
          [],
          Date.now() - startTime
        );
      }

      console.log(
        `[PropertyDraftMigration] 发现 ${localDrafts.length} 个本地草稿`
      );

      // 批量迁移
      const results = await this.migrateDrafts(localDrafts, showProgress);

      // 统计结果
      const migratedCount = results.filter(r => r.success).length;
      const failedCount = results.filter(r => !r.success).length;
      const errors = results
        .filter(r => !r.success)
        .map(r => ({
          draftId: r.draftId,
          error: r.error || '未知错误',
        }));
      const migratedIds = results
        .filter(r => r.success)
        .map(r => r.serverId)
        .filter(Boolean);

      // 确定最终状态
      let finalStatus: MigrationStatus;
      if (migratedCount === localDrafts.length) {
        finalStatus = MigrationStatus.COMPLETED;
      } else if (migratedCount > 0) {
        finalStatus = MigrationStatus.PARTIAL;
      } else {
        finalStatus = MigrationStatus.FAILED;
      }

      // 保存迁移状态
      await AsyncStorage.setItem(
        'property_draft_migration_status',
        finalStatus
      );

      // 如果完全成功，清理本地草稿
      if (finalStatus === MigrationStatus.COMPLETED) {
        await this.cleanupLocalDrafts();
        console.log('[PropertyDraftMigration] ✅ 迁移完成，已清理本地草稿');
      }

      const duration = Date.now() - startTime;
      const result = this.createResult(
        finalStatus,
        localDrafts.length,
        migratedCount,
        failedCount,
        errors,
        migratedIds,
        duration
      );

      if (showProgress) {
        this.showMigrationResult(result);
      }

      console.log('[PropertyDraftMigration] 迁移完成:', result);
      return result;
    } catch (error) {
      console.error('[PropertyDraftMigration] 迁移失败:', error);
      await AsyncStorage.setItem(
        'property_draft_migration_status',
        MigrationStatus.FAILED
      );

      const duration = Date.now() - startTime;
      const result = this.createResult(
        MigrationStatus.FAILED,
        0,
        0,
        0,
        [
          {
            draftId: 'unknown',
            error: error instanceof Error ? error.message : '迁移失败',
          },
        ],
        [],
        duration
      );

      if (showProgress) {
        FeedbackService.showError('草稿迁移失败，请稍后重试');
      }

      return result;
    } finally {
      this.migrationInProgress = false;
    }
  }

  /**
   * 获取本地草稿
   */
  private async getLocalDrafts(): Promise<LocalPropertyDraft[]> {
    try {
      const localDrafts = await AsyncStorage.getItem('property_drafts');
      if (!localDrafts) return [];

      const drafts = JSON.parse(localDrafts);
      return Array.isArray(drafts) ? drafts : [];
    } catch (error) {
      console.error('[PropertyDraftMigration] 获取本地草稿失败:', error);
      return [];
    }
  }

  /**
   * 批量迁移草稿
   */
  private async migrateDrafts(
    drafts: LocalPropertyDraft[],
    showProgress: boolean
  ): Promise<
    Array<{
      success: boolean;
      draftId: string;
      serverId?: string;
      error?: string;
    }>
  > {
    const results = [];

    for (let i = 0; i < drafts.length; i++) {
      const draft = drafts[i];

      if (showProgress) {
        console.log(
          `[PropertyDraftMigration] 迁移进度: ${i + 1}/${drafts.length}`
        );
      }

      try {
        // 转换数据格式
        const transformResult = Transformers.property.toAPI(draft.formData, {
          context: 'draft',
          propertyType: draft.propertyType,
          selectedTags: draft.selectedTags,
        });

        if (!transformResult.success) {
          throw new Error(`数据转换失败: ${transformResult.error}`);
        }

        const serverData = {
          ...transformResult.data,
          status: 'DRAFT' as const,
        };

        // 上传到服务器
        const result = await publishAPI.createProperty(serverData);

        results.push({
          success: true,
          draftId: draft.id,
          serverId: result.id,
        });

        console.log(
          `[PropertyDraftMigration] ✅ 草稿迁移成功: ${draft.id} -> ${result.id}`
        );
      } catch (error) {
        console.error(
          `[PropertyDraftMigration] ❌ 草稿迁移失败: ${draft.id}`,
          error
        );
        results.push({
          success: false,
          draftId: draft.id,
          error: error instanceof Error ? error.message : '迁移失败',
        });
      }

      // 避免请求过于频繁
      if (i < drafts.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    return results;
  }

  /**
   * 清理本地草稿
   */
  private async cleanupLocalDrafts(): Promise<void> {
    try {
      await AsyncStorage.removeItem('property_drafts');
      console.log('[PropertyDraftMigration] 本地草稿已清理');
    } catch (error) {
      console.error('[PropertyDraftMigration] 清理本地草稿失败:', error);
    }
  }

  /**
   * 创建迁移结果
   */
  private createResult(
    status: MigrationStatus,
    totalDrafts: number,
    migratedCount: number,
    failedCount: number,
    errors: Array<{ draftId: string; error: string }>,
    migratedIds: string[],
    duration: number
  ): MigrationResult {
    return {
      status,
      totalDrafts,
      migratedCount,
      failedCount,
      errors,
      migratedIds,
      duration,
    };
  }

  /**
   * 显示迁移结果
   */
  private showMigrationResult(result: MigrationResult): void {
    const { status, totalDrafts, migratedCount, failedCount } = result;

    switch (status) {
      case MigrationStatus.COMPLETED:
        FeedbackService.showSuccess(`成功迁移 ${migratedCount} 个草稿到云端`);
        break;
      case MigrationStatus.PARTIAL:
        FeedbackService.showWarning(
          `部分迁移成功：${migratedCount}/${totalDrafts} 个草稿已迁移`
        );
        break;
      case MigrationStatus.FAILED:
        FeedbackService.showError('草稿迁移失败，请稍后重试');
        break;
    }
  }

  /**
   * 重置迁移状态（用于测试或重新迁移）
   */
  async resetMigrationStatus(): Promise<void> {
    await AsyncStorage.removeItem('property_draft_migration_status');
    console.log('[PropertyDraftMigration] 迁移状态已重置');
  }
}

// 导出单例实例
export const propertyDraftMigration =
  PropertyDraftMigrationService.getInstance();
