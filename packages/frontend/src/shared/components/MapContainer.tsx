/**
 * MapContainer - 基础地图容器组件
 *
 * 功能：
 * 1. 基础地图显示和交互
 * 2. 用户定位和导航
 * 3. 房源标记显示
 * 4. 画圈搜索功能
 * 5. 地图样式和控件
 *
 * 基于高德地图react-native-amap3d
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  Modal,
  Text,
  TouchableOpacity,
  Linking,
  Platform,
} from 'react-native';
import { MapView, Marker, Circle } from 'react-native-amap3d';
import LocationService, { LocationResult } from '../services/LocationService';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import { wp, hp } from '../utils/responsive'; // 暂时不需要

const PRIVACY_AGREEMENT_KEY = 'amap_privacy_agreed_v1';

// 安全导入高德地图SDK
let AMapSdk: any = null;
try {
  const amapModule = require('react-native-amap3d');
  AMapSdk = amapModule.AMapSdk;
  console.log('[MapContainer] ✅ react-native-amap3d模块加载成功');
} catch (error) {
  console.error('[MapContainer] ❌ react-native-amap3d模块加载失败:', error);
}

// 地图中心点接口
interface MapCenter {
  latitude: number;
  longitude: number;
}

// 房源标记接口
interface PropertyMarker {
  id: string;
  latitude: number;
  longitude: number;
  title: string;
  price?: number;
  area?: number;
  propertyType?: string;
}

// 搜索圆圈接口
interface SearchCircle {
  center: MapCenter;
  radius: number;
}

// MapContainer Props
interface MapContainerProps {
  // 地图中心点
  center?: MapCenter;
  // 房源标记数据
  markers?: PropertyMarker[];
  // 搜索圆圈
  searchCircle?: SearchCircle;
  // 地图缩放级别
  zoomLevel?: number;
  // 是否启用高德原生定位
  enableNativeLocation?: boolean;
  // 是否显示原生定位按钮
  showNativeLocationButton?: boolean;
  // 是否启用长按搜索
  enableLongPressSearch?: boolean;

  // 事件回调
  onMapReady?: () => void;
  onUserLocationChanged?: (location: MapCenter) => void;
  onNativeLocationUpdate?: (location: {
    latitude: number;
    longitude: number;
    accuracy: number;
  }) => void;
  onMarkerPress?: (marker: PropertyMarker) => void;
  onLongPress?: (coordinate: MapCenter) => void;
  onRegionChange?: (region: any) => void;
}

export const MapContainer: React.FC<MapContainerProps> = ({
  center,
  markers = [],
  searchCircle,
  zoomLevel = 15,
  enableNativeLocation = true,
  showNativeLocationButton = true,
  enableLongPressSearch = true,
  onMapReady,
  onUserLocationChanged,
  onNativeLocationUpdate,
  onMarkerPress,
  onLongPress,
  onRegionChange,
}) => {
  const mapRef = useRef<MapView>(null);
  const [currentCenter, setCurrentCenter] = useState<MapCenter>(
    center || { latitude: 22.8167, longitude: 108.3669 } // 默认南宁市中心
  );
  const [isMapReady, setIsMapReady] = useState(false);
  // 权限管理状态（按专家建议统一管理，避免SDK自动处理冲突）
  const [permissionGranted, setPermissionGranted] = useState(false);
  // 融合弹窗状态：同时处理隐私同意和位置权限
  const [showPrivacyPermissionDialog, setShowPrivacyPermissionDialog] =
    useState(false);
  // SDK 初始化状态
  const [isSDKInitialized, setIsSDKInitialized] = useState(false);

  // 🔧 MapContainer层面的防抖机制 - 参考PropertyNavigationMapSimple
  const lastLocationRef = useRef<{
    latitude: number;
    longitude: number;
    accuracy: number;
    timestamp: number;
  } | null>(null);

  // 初始化定位服务 - 先检查隐私同意，再请求权限
  useEffect(() => {
    console.log('[MapContainer] 🚀 组件挂载，开始初始化');
    console.log('[MapContainer] 📊 挂载时间:', new Date().toISOString());
    console.log('[MapContainer] 🔍 挂载时props:', {
      center,
      enableNativeLocation,
    });

    checkPrivacyAndInitialize();

    // 🔧 关键修复：组件卸载时清理所有异步操作
    return () => {
      console.log('[MapContainer] 🧹 组件卸载，清理所有异步操作');
      console.log('[MapContainer] 📊 卸载时间:', new Date().toISOString());
      console.log('[MapContainer] 🔍 卸载原因: 父组件MapSearchScreen卸载');
      // 这里可以添加具体的清理逻辑，如取消定时器、中断请求等
    };
  }, []); // 只在组件挂载时执行一次

  // 当center props改变时更新地图中心
  useEffect(() => {
    if (
      center &&
      (center.latitude !== currentCenter.latitude ||
        center.longitude !== currentCenter.longitude)
    ) {
      setCurrentCenter(center);
      // 使用高德地图原生API动画移动到新中心点（严格按照官方CameraPosition类型）
      if (isMapReady && mapRef.current) {
        mapRef.current.moveCamera(
          {
            target: {
              latitude: center.latitude,
              longitude: center.longitude,
            },
            zoom: zoomLevel,
          },
          1000
        );
      }
    }
  }, [center, currentCenter, isMapReady]);

  /**
   * 🗺️ 初始化高德地图SDK
   */
  const initializeAmapSDK = useCallback(async () => {
    try {
      console.log('[MapContainer] 🗺️ 开始初始化高德地图SDK...');

      // 检查AMapSdk模块可用性
      if (!AMapSdk) {
        throw new Error('AMapSdk模块未正确加载，请检查react-native-amap3d安装');
      }

      if (typeof AMapSdk.init !== 'function') {
        throw new Error('AMapSdk.init不是函数，请检查react-native-amap3d版本');
      }

      const apiKey = Platform.select({
        android:
          process.env.EXPO_PUBLIC_AMAP_FRONTEND_KEY ||
          '7623c396c3d7dcfa3b17f032db28a0bc',
        ios:
          process.env.EXPO_PUBLIC_AMAP_FRONTEND_KEY ||
          '7623c396c3d7dcfa3b17f032db28a0bc',
      });

      console.log('[MapContainer] 🔄 开始调用AMapSdk.init...');
      await AMapSdk.init(apiKey);

      console.log('[MapContainer] ✅ 高德地图SDK初始化成功');
      setIsSDKInitialized(true);
    } catch (error) {
      console.error('[MapContainer] ❌ 高德地图SDK初始化失败:', error);
      setIsSDKInitialized(false);

      const errorMessage = (error as Error)?.message || '未知错误';
      Alert.alert(
        '地图初始化失败',
        `地图服务初始化失败：${errorMessage}。您可以继续使用其他功能。`,
        [{ text: '知道了' }]
      );
    }
  }, []);

  /**
   * 权限请求和定位初始化
   */
  const initializeLocationWithPermissions = useCallback(async () => {
    try {
      console.log('[MapContainer] 🔐 开始请求位置权限...');

      // 调试：检查动态导入
      console.log('[MapContainer] 🔍 尝试动态导入LocationPermissions...');
      try {
        const locationModule = await import('../utils/LocationPermissions');
        console.log('[MapContainer] ✅ 动态导入成功:', typeof locationModule);
        console.log(
          '[MapContainer] - LocationPermissions类型:',
          typeof locationModule.LocationPermissions
        );

        const { LocationPermissions } = locationModule;

        // 智能请求权限
        console.log('[MapContainer] 🔐 开始调用权限请求方法...');
        const permissionResult =
          await LocationPermissions.smartRequestPermissions();
        console.log('[MapContainer] 🔐 权限请求结果:', permissionResult);

        if (!permissionResult.granted) {
          console.log('[MapContainer] ❌ 位置权限被拒绝，使用默认位置');
          if (!center) {
            const defaultCenter = { latitude: 22.8167, longitude: 108.3669 };
            setCurrentCenter(defaultCenter);
          }
          return;
        }

        console.log('[MapContainer] ✅ 位置权限获取成功，设置权限状态');
        setPermissionGranted(true);
        await initializeLocation();
      } catch (importError) {
        console.error(
          '[MapContainer] ❌ 动态导入LocationPermissions失败:',
          importError
        );
        throw importError;
      }
    } catch (error) {
      console.error('[MapContainer] ❌ 权限请求失败:', error);
      // 降级到默认位置
      if (!center) {
        const defaultCenter = { latitude: 22.8167, longitude: 108.3669 };
        setCurrentCenter(defaultCenter);
      }
    }
  }, [center]);

  /**
   * 🔐 融合隐私同意和权限检查 - 主流APP最佳实践
   */
  const checkPrivacyAndInitialize = useCallback(async () => {
    try {
      console.log('[MapContainer] 🔐 检查隐私同意状态...');

      // 检查用户是否已经同意过隐私政策
      const hasAgreed = await AsyncStorage.getItem(PRIVACY_AGREEMENT_KEY);

      if (hasAgreed === 'true') {
        console.log(
          '[MapContainer] ✅ 用户已同意隐私政策，初始化SDK并请求位置权限'
        );
        await initializeAmapSDK();
        await initializeLocationWithPermissions();
      } else {
        console.log('[MapContainer] 🔔 用户未同意隐私政策，显示融合弹窗');
        setShowPrivacyPermissionDialog(true);
      }
    } catch (error) {
      console.error('[MapContainer] ❌ 隐私检查失败:', error);
      // 降级到默认位置
      if (!center) {
        const defaultCenter = { latitude: 22.8167, longitude: 108.3669 };
        setCurrentCenter(defaultCenter);
      }
    }
  }, [center, initializeAmapSDK, initializeLocationWithPermissions]);

  /**
   * 简化初始化 - 只设置默认中心点（权限已处理）
   */
  const initializeLocation = useCallback(async () => {
    try {
      console.log(
        '[MapContainer] 🗺️ 简化初始化：让高德SDK自动处理定位和权限...'
      );

      // 只获取缓存位置用于初始中心点，不强制权限检查
      const locationResult: LocationResult =
        await LocationService.getCurrentLocation();

      if (locationResult.success && locationResult.coordinates) {
        const newLocation = {
          latitude: locationResult.coordinates.latitude,
          longitude: locationResult.coordinates.longitude,
        };

        // setUserLocation(newLocation); // SDK内部管理，无需手动设置

        // 如果没有传入center，则使用定位结果作为中心点
        if (!center) {
          setCurrentCenter(newLocation);
          console.log(
            '[MapContainer] 🎯 使用缓存定位作为地图中心:',
            newLocation
          );
        }

        // 通知父组件用户位置更新
        onUserLocationChanged?.(newLocation);
      } else {
        console.log('[MapContainer] ⚠️ 使用默认南宁中心点');
        // 使用默认南宁中心点
        if (!center) {
          const defaultCenter = { latitude: 22.8167, longitude: 108.3669 };
          setCurrentCenter(defaultCenter);
        }
      }
    } catch (error) {
      console.error('[MapContainer] ❌ 初始化失败:', error);
    }
  }, [center, onUserLocationChanged]);

  /**
   * 地图准备就绪回调
   */
  const handleMapReady = useCallback(() => {
    console.log('[MapContainer] 🗺️ 地图准备就绪');
    console.log('[MapContainer] 🗺️ 地图加载完成时的中心点:', currentCenter);
    console.log('[MapContainer] 🗺️ 地图实际显示的区域应该是南宁:', {
      latitude: 22.8167,
      longitude: 108.3669,
    });
    setIsMapReady(true);
    onMapReady?.();
  }, [onMapReady, currentCenter]);

  /**
   * 处理地图长按事件
   */
  const handleMapLongPress = useCallback(
    (event: any) => {
      if (!enableLongPressSearch) return;

      const { coordinate } = event.nativeEvent;
      console.log('[MapContainer] 👆 地图长按事件:', coordinate);

      onLongPress?.(coordinate);
    },
    [enableLongPressSearch, onLongPress]
  );

  /**
   * 处理房源标记点击事件
   */
  const handleMarkerPress = useCallback(
    (marker: PropertyMarker) => {
      console.log('[MapContainer] 📍 房源标记被点击:', marker.id);
      onMarkerPress?.(marker);
    },
    [onMarkerPress]
  );

  /**
   * 处理地图区域变化
   */
  const handleRegionChange = useCallback(
    (region: any) => {
      console.log('[MapContainer] 🗺️ 地图区域变化:', region);
      onRegionChange?.(region);
    },
    [onRegionChange]
  );

  // 移除locateToUserPosition函数 - 让高德SDK内置定位按钮处理手动定位

  /**
   * 处理高德原生定位更新 - 使用纯高德LocationService
   */
  const handleNativeLocationUpdate = useCallback(
    async (event: any) => {
      try {
        const { nativeEvent } = event;
        const coords = nativeEvent?.coords;

        if (!coords?.latitude || !coords?.longitude) {
          return;
        }

        // 🔧 MapContainer层面的防抖 - 参考PropertyNavigationMapSimple
        const currentLocation = {
          latitude: coords.latitude,
          longitude: coords.longitude,
          accuracy: coords.accuracy || 0,
          timestamp: nativeEvent.timestamp || Date.now(),
        };

        // 只有位置变化超过10米才处理更新，减少频繁回调
        const shouldProcess = !lastLocationRef.current ||
          Math.abs(lastLocationRef.current.latitude - currentLocation.latitude) > 0.0001 ||
          Math.abs(lastLocationRef.current.longitude - currentLocation.longitude) > 0.0001;

        if (!shouldProcess) {
          // 静默跳过，避免日志刷屏
          return;
        }

        console.log('[MapContainer] 📍 收到高德原生定位回调:', nativeEvent);

        // 更新防抖缓存
        lastLocationRef.current = currentLocation;

        // 使用LocationService处理高德原生定位数据
        const locationResult =
          await LocationService.handleNativeLocationUpdate(nativeEvent);

        if (locationResult.success && locationResult.coordinates) {
          const newLocation = {
            latitude: locationResult.coordinates.latitude,
            longitude: locationResult.coordinates.longitude,
          };

          console.log('[MapContainer] ✅ 高德原生定位处理成功:', newLocation);
          console.log(
            `[MapContainer] 📊 定位精度: ±${locationResult.accuracy?.toFixed(0) || '未知'}米`
          );

          // setUserLocation(newLocation); // SDK内部管理，无需手动设置
          onUserLocationChanged?.(newLocation);

          // 调用外部的原生定位更新回调，传递包含精度信息的完整数据
          onNativeLocationUpdate?.({
            latitude: locationResult.coordinates.latitude,
            longitude: locationResult.coordinates.longitude,
            accuracy: locationResult.accuracy || 0,
          });
        } else {
          console.log(
            '[MapContainer] ⚠️ 高德原生定位处理失败:',
            locationResult.error
          );
        }
      } catch (error) {
        console.error('[MapContainer] ❌ 处理原生定位更新失败:', error);
      }
    },
    [onUserLocationChanged, onNativeLocationUpdate]
  );

  /**
   * 🎯 融合弹窗：用户同意隐私政策并授权位置权限
   */
  const handlePrivacyAndPermissionAgree = useCallback(async () => {
    try {
      console.log('[MapContainer] ✅ 用户同意隐私政策并授权位置权限');

      // 保存隐私同意状态
      await AsyncStorage.setItem(PRIVACY_AGREEMENT_KEY, 'true');

      // 关闭弹窗
      setShowPrivacyPermissionDialog(false);

      // 先初始化SDK，再请求位置权限
      await initializeAmapSDK();
      await initializeLocationWithPermissions();
    } catch (error) {
      console.error('[MapContainer] ❌ 处理用户同意失败:', error);
      Alert.alert('提示', '设置保存失败，请重试。');
    }
  }, [initializeAmapSDK, initializeLocationWithPermissions]);

  /**
   * 🚫 用户拒绝隐私政策或位置权限
   */
  const handlePrivacyAndPermissionDeny = useCallback(() => {
    console.log('[MapContainer] ❌ 用户拒绝隐私政策或位置权限');

    // 关闭弹窗
    setShowPrivacyPermissionDialog(false);

    // 降级到默认位置，地图依然可用但无定位功能
    if (!center) {
      const defaultCenter = { latitude: 22.8167, longitude: 108.3669 };
      setCurrentCenter(defaultCenter);
    }

    // 温和的提示
    Alert.alert(
      '提示',
      '您可以继续浏览地图，如需使用定位功能，请在设置中开启位置权限。',
      [{ text: '知道了' }]
    );
  }, [center]);

  /**
   * 🔗 打开隐私政策链接
   */
  const openPrivacyPolicy = useCallback(() => {
    Linking.openURL('https://lbs.amap.com/pages/privacy/').catch(err => {
      console.error('[MapContainer] 打开隐私政策链接失败:', err);
      Alert.alert('提示', '无法打开链接，请稍后重试。');
    });
  }, []);

  console.log('[MapContainer] 🎯 当前地图中心:', currentCenter);
  console.log('[MapContainer] 🛰️ 高德原生定位配置:', {
    enableNativeLocation,
    showNativeLocationButton,
  });
  console.log('[MapContainer] 📍 initialCameraPosition配置:', {
    target: {
      latitude: currentCenter.latitude,
      longitude: currentCenter.longitude,
    },
    zoom: zoomLevel,
  });
  console.log('[MapContainer] 🔧 简化模式：让高德SDK自动处理权限和定位');

  /**
   * 🎨 渲染简洁的权限弹窗 - 主流APP风格
   */
  const renderPrivacyPermissionDialog = () => (
    <Modal
      visible={showPrivacyPermissionDialog}
      transparent={true}
      animationType="fade"
      onRequestClose={handlePrivacyAndPermissionDeny}
    >
      <View style={privacyStyles.overlay}>
        <View style={privacyStyles.dialog}>
          <Text style={privacyStyles.title}>开启定位服务</Text>

          <Text style={privacyStyles.content}>
            需要获取您的位置信息来显示附近房源{'\n'}
            仅在使用地图时获取，不会后台追踪
          </Text>

          <View style={privacyStyles.buttonContainer}>
            <TouchableOpacity
              style={[privacyStyles.button, privacyStyles.denyButton]}
              onPress={handlePrivacyAndPermissionDeny}
            >
              <Text style={privacyStyles.denyButtonText}>取消</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[privacyStyles.button, privacyStyles.agreeButton]}
              onPress={handlePrivacyAndPermissionAgree}
            >
              <Text style={privacyStyles.agreeButtonText}>确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* 只有在SDK初始化成功后才显示MapView */}
      {isSDKInitialized ? (
        <MapView
          ref={mapRef}
          style={styles.map}
          // 使用官方标准的initialCameraPosition设置地图中心
          initialCameraPosition={{
            target: {
              latitude: currentCenter.latitude,
              longitude: currentCenter.longitude,
            },
            zoom: zoomLevel,
          }}
          // 🎯 按专家建议：使用状态变量控制定位，避免SDK自动处理权限冲突
          myLocationEnabled={permissionGranted && enableNativeLocation} // 权限+配置双重控制
          myLocationButtonEnabled={
            permissionGranted && showNativeLocationButton
          } // 权限+配置双重控制
          showsUserLocation={permissionGranted && enableNativeLocation} // 权限+配置双重控制
          distanceFilter={10} // 设定定位的最小更新距离(iOS)
          headingFilter={1} // 设定最小更新角度，默认为 1 度(iOS)
          compassEnabled={true} // 是否显示指南针
          scaleControlsEnabled={false} // 是否显示比例尺
          onLoad={handleMapReady} // 地图初始化完成事件
          onLocation={handleNativeLocationUpdate} // 地图定位更新事件
          onLongPress={handleMapLongPress}
          onRegionChangeComplete={handleRegionChange}
        >
          {/* 房源标记 */}
          {markers.map(marker => (
            <Marker
              key={marker.id}
              coordinate={{
                latitude: marker.latitude,
                longitude: marker.longitude,
              }}
              title={marker.title}
              description={`${marker.price ? `${marker.price}元/月` : ''} ${marker.area ? `${marker.area}㎡` : ''}`}
              onPress={() => handleMarkerPress(marker)}
            />
          ))}

          {/* 搜索圆圈 */}
          {searchCircle && (
            <Circle
              center={searchCircle.center}
              radius={searchCircle.radius}
              strokeColor="rgba(255, 79, 25, 0.8)" // 项目主色调
              fillColor="rgba(255, 79, 25, 0.2)"
              strokeWidth={2}
            />
          )}
        </MapView>
      ) : (
        /* SDK未初始化时显示占位内容 */
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>
            {showPrivacyPermissionDialog
              ? '请同意隐私政策以使用地图功能'
              : '地图初始化中...'}
          </Text>
        </View>
      )}

      {/* 融合弹窗：隐私同意 + 位置权限 */}
      {renderPrivacyPermissionDialog()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
});

// 简洁弹窗样式 - 中间小弹窗
const privacyStyles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  dialog: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    width: '85%',
    maxWidth: 300,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
    color: '#333333',
  },
  content: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  button: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  denyButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  agreeButton: {
    backgroundColor: '#007AFF',
  },
  denyButtonText: {
    color: '#666666',
    fontSize: 14,
    fontWeight: '500',
  },
  agreeButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
});

// 导出定位功能供外部使用
export { LocationService };

// MapRef Hook - 为外部组件提供地图操作方法
export const useMapRef = () => {
  const mapRef = useRef<MapView>(null);

  const animateToRegion = useCallback(
    (
      region: {
        latitude: number;
        longitude: number;
        latitudeDelta: number;
        longitudeDelta: number;
      },
      duration = 1000
    ) => {
      if (mapRef.current) {
        mapRef.current.animateToRegion(region, duration);
      }
    },
    []
  );

  const moveToLocation = useCallback(
    (latitude: number, longitude: number) => {
      animateToRegion({
        latitude,
        longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    },
    [animateToRegion]
  );

  return {
    mapRef,
    animateToRegion,
    moveToLocation,
  };
};
