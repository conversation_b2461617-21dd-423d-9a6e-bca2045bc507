#!/usr/bin/env python3
"""
修复缺少now_utc导入的文件
"""

import os
import re
import glob

def fix_now_utc_import(file_path):
    """修复单个文件中的now_utc导入"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 检查是否使用了now_utc但没有导入
    if 'now_utc' in content and 'from app.core.timezone_utils import now_utc' not in content:
        # 找到导入部分的位置
        lines = content.split('\n')
        import_line_index = -1
        
        # 找到最后一个import语句的位置
        for i, line in enumerate(lines):
            if line.strip().startswith('import ') or line.strip().startswith('from '):
                import_line_index = i
        
        if import_line_index >= 0:
            # 在最后一个import语句后添加now_utc导入
            lines.insert(import_line_index + 1, 'from app.core.timezone_utils import now_utc')
            content = '\n'.join(lines)
        else:
            # 如果没找到import语句，在文件开头添加
            content = 'from app.core.timezone_utils import now_utc\n' + content
    
    # 如果内容有变化，写回文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Fixed: {file_path}")
        return True
    return False

def main():
    """主函数"""
    model_files = glob.glob('packages/backend/app/models/**/*.py', recursive=True)
    
    fixed_count = 0
    for file_path in model_files:
        if fix_now_utc_import(file_path):
            fixed_count += 1
    
    print(f"Total files fixed: {fixed_count}")

if __name__ == "__main__":
    main()
