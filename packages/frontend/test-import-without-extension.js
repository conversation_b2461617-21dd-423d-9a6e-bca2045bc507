// 测试不带扩展名的动态导入（React Native环境中的实际情况）
console.log('测试不带扩展名的动态导入...');

async function testImportWithoutExtension() {
  try {
    // 在React Native环境中，动态导入通常不需要扩展名
    console.log('1. 尝试不带扩展名的动态导入...');
    
    const importPath = './src/screens/Publish/PropertyDetailFormScreen/hooks/validationSchema';
    const moduleImport = await import(importPath);
    
    console.log('2. ✅ 动态导入成功');
    console.log('3. 导入的模块keys:', Object.keys(moduleImport));
    
    return { success: true, moduleImport };
    
  } catch (error) {
    console.log('❌ 不带扩展名的导入失败:', error.message);
    
    // 尝试带扩展名但使用js
    try {
      console.log('4. 尝试使用.js扩展名...');
      const importPath = './src/screens/Publish/PropertyDetailFormScreen/hooks/validationSchema.js';
      const moduleImport = await import(importPath);
      
      console.log('5. ✅ 使用.js扩展名的动态导入成功');
      return { success: true, moduleImport };
      
    } catch (jsError) {
      console.log('❌ 使用.js扩展名也失败:', jsError.message);
      
      // 最后尝试require
      try {
        console.log('6. 尝试使用require...');
        const moduleImport = require('./src/screens/Publish/PropertyDetailFormScreen/hooks/validationSchema.ts');
        
        console.log('7. ✅ require成功');
        return { success: true, moduleImport };
        
      } catch (requireError) {
        console.log('❌ require也失败:', requireError.message);
        return { success: false, error: requireError };
      }
    }
  }
}

// 运行测试
testImportWithoutExtension().then(result => {
  console.log('\n=== 最终测试结果 ===');
  if (result.success) {
    console.log('✅ 找到了可以工作的导入方式');
    
    // 检查导入的内容
    if (result.moduleImport) {
      const { propertyFormSchema, defaultFormValues } = result.moduleImport;
      console.log('📋 导入内容检查:');
      console.log('   - propertyFormSchema:', typeof propertyFormSchema);
      console.log('   - defaultFormValues:', typeof defaultFormValues);
      
      if (defaultFormValues) {
        console.log('   - defaultFormValues字段数:', Object.keys(defaultFormValues).length);
      }
    }
  } else {
    console.log('❌ 所有导入方式都失败了');
    console.log('这表明存在配置问题或者模块解析问题');
  }
}).catch(error => {
  console.error('❌ 测试脚本本身失败:', error);
});