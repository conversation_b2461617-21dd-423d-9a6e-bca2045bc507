/**
 * 高德地图API服务
 * 企业级地理编码和搜索服务
 */

import Constants from 'expo-constants';

interface GeocodeResult {
  longitude: number;
  latitude: number;
  formatted_address: string;
  district?: string;
  city?: string;
}

interface ReverseGeocodeResult {
  address: string;
  district: string;
  city: string;
  province: string;
}

interface SearchSuggestion {
  name: string;
  address: string;
  location: {
    longitude: number;
    latitude: number;
  };
  district: string;
  city: string;
}

class AmapGeocodingService {
  private baseURL = 'https://restapi.amap.com/v3';
  private key: string;

  constructor() {
    // 使用前端专用的高德API Key
    this.key = Constants.expoConfig?.extra?.EXPO_PUBLIC_AMAP_FRONTEND_KEY || 
               process.env.EXPO_PUBLIC_AMAP_FRONTEND_KEY || '';
    
    if (!this.key) {
      console.warn('⚠️ 高德地图API密钥未配置，地图功能可能无法正常使用');
    }
  }

  /**
   * 地址转坐标 (地理编码)
   * 用于房源发布时将地址转换为经纬度
   */
  async geocode(address: string): Promise<GeocodeResult> {
    if (!this.key) {
      throw new Error('高德地图API密钥未配置');
    }

    try {
      const url = `${this.baseURL}/geocode/geo?key=${this.key}&address=${encodeURIComponent(address)}`;
      
      console.log('🗺️ 高德地理编码请求:', address);
      
      const response = await fetch(url);
      const data = await response.json();

      if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
        const geocode = data.geocodes[0];
        const [longitude, latitude] = geocode.location.split(',').map(Number);
        
        const result: GeocodeResult = {
          longitude,
          latitude,
          formatted_address: geocode.formatted_address,
          district: geocode.district,
          city: geocode.city,
        };

        console.log('✅ 地理编码成功:', result);
        return result;
      }

      throw new Error(data.info || '地理编码失败');
    } catch (error) {
      console.error('❌ 高德地理编码错误:', error);
      throw error;
    }
  }

  /**
   * 坐标转地址 (逆地理编码)
   * 用于地图上显示位置信息
   */
  async reverseGeocode(longitude: number, latitude: number): Promise<ReverseGeocodeResult> {
    if (!this.key) {
      throw new Error('高德地图API密钥未配置');
    }

    try {
      const url = `${this.baseURL}/geocode/regeo?key=${this.key}&location=${longitude},${latitude}`;
      
      console.log('🗺️ 高德逆地理编码请求:', { longitude, latitude });
      
      const response = await fetch(url);
      const data = await response.json();

      if (data.status === '1' && data.regeocode) {
        const regeocode = data.regeocode;
        const addressComponent = regeocode.addressComponent;
        
        const result: ReverseGeocodeResult = {
          address: regeocode.formatted_address,
          district: addressComponent.district,
          city: addressComponent.city,
          province: addressComponent.province,
        };

        console.log('✅ 逆地理编码成功:', result);
        return result;
      }

      throw new Error(data.info || '逆地理编码失败');
    } catch (error) {
      console.error('❌ 高德逆地理编码错误:', error);
      throw error;
    }
  }

  /**
   * 地址搜索提示 (输入提示)
   * 用于搜索框的自动补全功能
   */
  async searchSuggestions(keyword: string, city?: string): Promise<SearchSuggestion[]> {
    if (!this.key || !keyword.trim()) {
      return [];
    }

    try {
      let url = `${this.baseURL}/assistant/inputtips?key=${this.key}&keywords=${encodeURIComponent(keyword)}`;
      
      if (city) {
        url += `&city=${encodeURIComponent(city)}`;
      }
      
      console.log('🗺️ 高德搜索提示请求:', keyword);
      
      const response = await fetch(url);
      const data = await response.json();

      if (data.status === '1' && data.tips && Array.isArray(data.tips)) {
        const suggestions: SearchSuggestion[] = data.tips
          .filter((tip: any) => tip.location && tip.location !== '')
          .map((tip: any) => {
            const [longitude, latitude] = tip.location.split(',').map(Number);
            return {
              name: tip.name,
              address: tip.address,
              location: { longitude, latitude },
              district: tip.district,
              city: tip.city,
            };
          });

        console.log('✅ 搜索提示成功:', suggestions.length, '条结果');
        return suggestions;
      }

      return [];
    } catch (error) {
      console.error('❌ 高德搜索提示错误:', error);
      return [];
    }
  }

  /**
   * POI搜索 (兴趣点搜索)
   * 用于搜索周边商业设施
   */
  async searchPOI(
    keyword: string,
    center?: { longitude: number; latitude: number },
    radius: number = 3000
  ): Promise<SearchSuggestion[]> {
    if (!this.key || !keyword.trim()) {
      return [];
    }

    try {
      let url = `${this.baseURL}/place/text?key=${this.key}&keywords=${encodeURIComponent(keyword)}`;
      
      if (center) {
        url += `&location=${center.longitude},${center.latitude}&radius=${radius}`;
      }
      
      console.log('🗺️ 高德POI搜索请求:', keyword);
      
      const response = await fetch(url);
      const data = await response.json();

      if (data.status === '1' && data.pois && Array.isArray(data.pois)) {
        const results: SearchSuggestion[] = data.pois.map((poi: any) => {
          const [longitude, latitude] = poi.location.split(',').map(Number);
          return {
            name: poi.name,
            address: poi.address,
            location: { longitude, latitude },
            district: poi.adname,
            city: poi.cityname,
          };
        });

        console.log('✅ POI搜索成功:', results.length, '条结果');
        return results;
      }

      return [];
    } catch (error) {
      console.error('❌ 高德POI搜索错误:', error);
      return [];
    }
  }

  /**
   * 检查API Key是否有效
   */
  async checkApiKey(): Promise<boolean> {
    if (!this.key) {
      return false;
    }

    try {
      // 使用一个简单的逆地理编码请求来测试API Key
      const url = `${this.baseURL}/geocode/regeo?key=${this.key}&location=116.397428,39.90923`;
      const response = await fetch(url);
      const data = await response.json();
      
      return data.status === '1';
    } catch (error) {
      console.error('❌ 高德API Key验证失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const amapService = new AmapGeocodingService();
export type { GeocodeResult, ReverseGeocodeResult, SearchSuggestion };
