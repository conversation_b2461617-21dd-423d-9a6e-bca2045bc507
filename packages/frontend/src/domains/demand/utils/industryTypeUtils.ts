/**
 * 行业类型工具函数
 * 提供行业类型的中文显示转换
 */

/**
 * 行业类型英文到中文的映射
 */
const INDUSTRY_TYPE_MAP: Record<string, string> = {
  RESTAURANT: '餐饮行业',
  RETAIL: '零售行业',
  SERVICE: '服务行业',
  MANUFACTURING: '制造业',
  LOGISTICS: '物流仓储',
  TECHNOLOGY: '科技互联网',
  EDUCATION: '教育培训',
  HEALTHCARE: '医疗健康',
  FINANCE: '金融保险',
  OTHER: '其他行业',
};

/**
 * 获取行业类型的中文显示名称
 * @param industryType 英文行业类型
 * @returns 中文显示名称
 */
export const getIndustryTypeDisplayName = (industryType: string): string => {
  return INDUSTRY_TYPE_MAP[industryType] || industryType;
};

/**
 * 获取行业类型数组的中文显示名称
 * @param industryTypes 英文行业类型数组
 * @returns 中文显示名称数组
 */
export const getIndustryTypesDisplayNames = (
  industryTypes: string[]
): string[] => {
  return industryTypes.map(type => getIndustryTypeDisplayName(type));
};

/**
 * 获取行业类型数组的中文显示字符串（用逗号分隔）
 * @param industryTypes 英文行业类型数组
 * @returns 中文显示字符串
 */
export const getIndustryTypesDisplayString = (
  industryTypes: string[]
): string => {
  return getIndustryTypesDisplayNames(industryTypes).join('、');
};
