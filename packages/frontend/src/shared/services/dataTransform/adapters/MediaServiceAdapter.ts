/**
 * 媒体服务适配器 - 统一转换层集成
 *
 * 职责：
 * 1. 将UnifiedMediaProcessor集成到dataTransform架构中
 * 2. 提供统一的媒体操作接口
 * 3. 处理数据格式转换
 * 4. 管理业务场景配置
 *
 * 设计原则：
 * - 适配器模式：统一不同服务的接口
 * - 单一职责：专注于媒体服务的数据转换
 * - 类型安全：完整的TypeScript类型支持
 * - 错误处理：统一的错误处理机制
 *
 * 更新：集成新的抽象层架构
 * - 统一媒体处理器：支持图片动态处理和视频预转码
 * - 元数据缓存：提供媒体信息的快速访问
 * - 状态跟踪：实时监控媒体处理进度
 *
 * 作者: AI编码助手
 * 创建时间: 2025年7月15日
 * 更新时间: 2025年7月16日
 */

import { MediaBusinessType, MediaProcessingConfig, CompressionConfig, WatermarkConfig } from '../transformers/MediaTransformer';
import { TransformResult, TransformOptions } from '../types/TransformTypes';
import { BaseTransformer } from '../core/BaseTransformer';

// 导入新的抽象层组件
import { UnifiedMediaProcessor } from '../processors/UnifiedMediaProcessor';
import { ImageProcessor } from '../processors/ImageProcessor';
import { VideoProcessor } from '../processors/VideoProcessor';
import { MemoryMetadataCache } from '../cache/MemoryMetadataCache';
import {
  IMediaProcessor,
  MediaUploadResult as AbstractMediaUploadResult,
  MediaStatus,
  MediaSize
} from '../types/MediaTypes';

// 创建统一媒体处理器实例
let unifiedMediaProcessor: IMediaProcessor | null = null;

function getUnifiedMediaProcessor(): IMediaProcessor {
  if (!unifiedMediaProcessor) {
    const imageProcessor = new ImageProcessor();
    const videoProcessor = new VideoProcessor();
    const metadataCache = new MemoryMetadataCache();

    unifiedMediaProcessor = new UnifiedMediaProcessor(
      imageProcessor,
      videoProcessor,
      metadataCache
    );
  }

  return unifiedMediaProcessor;
}

// 统一上传选项
export interface UnifiedUploadOptions {
  businessType: MediaBusinessType;
  propertyId?: string;
  userId?: string;
  onProgress?: (progress: { loaded: number; total: number; percentage: number }) => void;
  processing?: Partial<MediaProcessingConfig>;
}

// 统一上传结果
export interface UnifiedUploadResult {
  success: boolean;
  objectKey?: string;
  url?: string;
  thumbnailUrl?: string;
  processedUrl?: string;
  originalUrl?: string;
  watermarkedUrl?: string;
  businessType: MediaBusinessType;
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
}

// 查看URL选项
export interface ViewUrlOptions {
  size?: 'thumbnail' | 'medium' | 'large' | 'original';
  withWatermark?: boolean;
  businessType?: MediaBusinessType;
}

/**
 * 媒体服务适配器类
 * 
 * 将UnifiedMediaService适配到统一转换层架构中
 */
export class MediaServiceAdapter extends BaseTransformer<any, any> {

  constructor() {
    super('MediaServiceAdapter', '1.0.0');
  }

  // 实现抽象方法：转换为API格式
  toAPI(data: any): any {
    return data; // 媒体服务适配器不需要特殊的API转换
  }

  // 实现抽象方法：从API格式转换
  fromAPI(data: any): any {
    return data; // 媒体服务适配器不需要特殊的API转换
  }
  
  /**
   * 初始化验证规则
   */
  protected initializeValidationRules(): void {
    // 媒体服务适配器的验证规则
    this.addValidationRule('file', (file: any) => {
      if (!file || !file.uri || !file.name || !file.type) {
        return { isValid: false, message: '文件信息不完整' };
      }
      return { isValid: true };
    });
    
    this.addValidationRule('businessType', (businessType: any) => {
      if (!Object.values(MediaBusinessType).includes(businessType)) {
        return { isValid: false, message: '不支持的业务类型' };
      }
      return { isValid: true };
    });
  }
  
  /**
   * 获取媒体处理器实例
   */
  private getMediaProcessor(): IMediaProcessor {
    return getUnifiedMediaProcessor();
  }
  
  /**
   * 上传文件 - 统一接口（使用新的抽象层）
   */
  async uploadFile(
    file: { uri: string; name: string; type: string; size: number },
    options: UnifiedUploadOptions
  ): Promise<TransformResult<UnifiedUploadResult>> {
    try {
      // 验证输入
      const fileValidation = this.validationEngine.validate(file, 'file');
      if (!fileValidation.isValid) {
        return {
          success: false,
          error: fileValidation.message || '文件验证失败',
          data: null,
          metadata: { source: 'MediaServiceAdapter.uploadFile' }
        };
      }

      const businessTypeValidation = this.validationEngine.validate(options.businessType, 'businessType');
      if (!businessTypeValidation.isValid) {
        return {
          success: false,
          error: businessTypeValidation.message || '业务类型验证失败',
          data: null,
          metadata: { source: 'MediaServiceAdapter.uploadFile' }
        };
      }

      // 转换文件格式（从React Native格式转换为Web File对象）
      const webFile = await this.convertToWebFile(file);

      // 获取统一媒体处理器
      const mediaProcessor = this.getMediaProcessor();

      // 执行上传
      const uploadResult = await mediaProcessor.upload(webFile, options.businessType);

      // 转换结果格式
      const transformedResult = this.transformUploadResult(uploadResult);

      return {
        success: true,
        data: transformedResult,
        error: null,
        metadata: {
          source: 'MediaServiceAdapter.uploadFile',
          businessType: options.businessType,
          fileSize: file.size,
          fileName: file.name,
          mediaId: uploadResult.mediaId,
          mediaType: uploadResult.type
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败',
        data: null,
        metadata: { source: 'MediaServiceAdapter.uploadFile' }
      };
    }
  }

  /**
   * 转换React Native文件格式为Web File对象
   */
  private async convertToWebFile(file: { uri: string; name: string; type: string; size: number }): Promise<File> {
    try {
      // 在React Native环境中，需要从URI读取文件数据
      // 这里提供一个基础实现，实际使用时可能需要根据平台调整

      if (typeof window !== 'undefined' && window.File) {
        // Web环境：如果uri是blob URL，直接使用
        if (file.uri.startsWith('blob:')) {
          const response = await fetch(file.uri);
          const blob = await response.blob();
          return new File([blob], file.name, { type: file.type });
        }
      }

      // React Native环境：创建模拟的File对象
      const mockFile = {
        name: file.name,
        type: file.type,
        size: file.size,
        lastModified: Date.now(),
        uri: file.uri, // 保留原始URI用于后续处理

        // 模拟File对象的方法
        arrayBuffer: async () => {
          throw new Error('arrayBuffer not implemented in React Native mock');
        },
        slice: () => {
          throw new Error('slice not implemented in React Native mock');
        },
        stream: () => {
          throw new Error('stream not implemented in React Native mock');
        },
        text: async () => {
          throw new Error('text not implemented in React Native mock');
        }
      } as File;

      return mockFile;

    } catch (error) {
      console.error('Failed to convert file format:', error);
      throw new Error('文件格式转换失败');
    }
  }

  /**
   * 转换上传结果格式
   */
  private transformUploadResult(uploadResult: AbstractMediaUploadResult): UnifiedUploadResult {
    return {
      success: uploadResult.status !== 'failed',
      mediaId: uploadResult.mediaId,
      urls: uploadResult.urls || {},
      metadata: {
        type: uploadResult.type,
        status: uploadResult.status,
        progress: uploadResult.progress || 0,
        estimatedTime: uploadResult.estimatedTime
      },
      error: uploadResult.error || null
    };
  }

  /**
   * 批量上传文件
   */
  async uploadFiles(
    files: Array<{ uri: string; name: string; type: string; size: number }>,
    options: UnifiedUploadOptions
  ): Promise<TransformResult<UnifiedUploadResult[]>> {
    try {
      // 验证输入
      for (const file of files) {
        const validation = this.validationEngine.validate(file, 'file');
        if (!validation.isValid) {
          return {
            success: false,
            error: `文件 ${file.name} 验证失败: ${validation.message}`,
            data: null,
            metadata: { source: 'MediaServiceAdapter.uploadFiles' }
          };
        }
      }
      
      // 获取媒体服务
      const mediaService = await this.getMediaService();
      
      // 执行批量上传
      const results = await mediaService.uploadFiles(files, options);
      
      return {
        success: true,
        data: results,
        metadata: {
          source: 'MediaServiceAdapter.uploadFiles',
          businessType: options.businessType,
          fileCount: files.length,
          successCount: results.filter(r => r.success).length
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量上传失败',
        data: null,
        metadata: { source: 'MediaServiceAdapter.uploadFiles' }
      };
    }
  }
  
  /**
   * 获取查看URL
   */
  async getViewUrl(
    objectKey: string,
    options: ViewUrlOptions = {}
  ): Promise<TransformResult<string>> {
    try {
      // 获取媒体服务
      const mediaService = await this.getMediaService();
      
      // 获取URL
      const url = await mediaService.getViewUrl(objectKey, options);
      
      return {
        success: true,
        data: url,
        metadata: {
          source: 'MediaServiceAdapter.getViewUrl',
          objectKey,
          size: options.size || 'medium',
          withWatermark: options.withWatermark || false
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取查看URL失败',
        data: null,
        metadata: { source: 'MediaServiceAdapter.getViewUrl' }
      };
    }
  }

  /**
   * 获取媒体访问URL - 新增方法
   */
  async getMediaUrl(
    mediaId: string,
    size: MediaSize = 'medium'
  ): Promise<TransformResult<string>> {
    try {
      const mediaProcessor = this.getMediaProcessor();
      const url = await mediaProcessor.getUrl(mediaId, size);

      return {
        success: true,
        data: url,
        error: null,
        metadata: {
          source: 'MediaServiceAdapter.getMediaUrl',
          mediaId,
          size
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取URL失败',
        data: null,
        metadata: { source: 'MediaServiceAdapter.getMediaUrl' }
      };
    }
  }

  /**
   * 获取媒体处理状态 - 新增方法
   */
  async getMediaStatus(mediaId: string): Promise<TransformResult<MediaStatus>> {
    try {
      const mediaProcessor = this.getMediaProcessor();
      const status = await mediaProcessor.getStatus(mediaId);

      return {
        success: true,
        data: status,
        error: null,
        metadata: {
          source: 'MediaServiceAdapter.getMediaStatus',
          mediaId
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取状态失败',
        data: null,
        metadata: { source: 'MediaServiceAdapter.getMediaStatus' }
      };
    }
  }

  /**
   * 删除文件 - 使用新的抽象层
   */
  async deleteFile(mediaId: string): Promise<TransformResult<boolean>> {
    try {
      const mediaProcessor = this.getMediaProcessor();

      // 执行删除
      const result = await mediaProcessor.delete(mediaId);

      return {
        success: result,
        data: result,
        error: result ? null : '删除失败',
        metadata: {
          source: 'MediaServiceAdapter.deleteFile',
          mediaId
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除文件失败',
        data: false,
        metadata: { source: 'MediaServiceAdapter.deleteFile' }
      };
    }
  }
  
  /**
   * 获取业务场景的默认配置
   */
  getBusinessTypeConfig(businessType: MediaBusinessType): MediaProcessingConfig {
    const defaultConfigs: Record<MediaBusinessType, MediaProcessingConfig> = {
      [MediaBusinessType.PROPERTY_IMAGE]: {
        compression: { enabled: true, quality: 85, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
        watermark: { enabled: true, text: '慧选址', opacity: 0.4, position: 'center', color: '#FFFFFF', fontSize: 48 },
        generateThumbnail: true,
        enableContentSecurity: true,
        enableCDNAcceleration: true,
        enableAntiHotlinking: true,
        businessType: MediaBusinessType.PROPERTY_IMAGE
      },
      [MediaBusinessType.PROPERTY_VIDEO]: {
        compression: { enabled: true, quality: 80, maxWidth: 1280, maxHeight: 720 },
        watermark: { enabled: true, text: '慧选址', opacity: 0.4, position: 'center', color: '#FFFFFF', fontSize: 36 },
        generateThumbnail: true,
        enableContentSecurity: true,
        enableCDNAcceleration: true,
        enableAntiHotlinking: true,
        businessType: MediaBusinessType.PROPERTY_VIDEO
      },
      [MediaBusinessType.PROPERTY_VR_PANORAMA]: {
        compression: { enabled: true, quality: 90, maxWidth: 2048, maxHeight: 1024, format: 'jpeg' },
        watermark: { enabled: true, text: '慧选址', opacity: 0.7, position: 'bottom-right', color: '#FFFFFF', fontSize: 24 },
        generateThumbnail: true,
        enableContentSecurity: true,
        enableCDNAcceleration: true,
        enableAntiHotlinking: true,
        businessType: MediaBusinessType.PROPERTY_VR_PANORAMA
      },
      [MediaBusinessType.PROPERTY_FLOOR_PLAN]: {
        compression: { enabled: true, quality: 90, maxWidth: 1920, maxHeight: 1440, format: 'jpeg' },
        watermark: { enabled: true, text: '慧选址', opacity: 0.7, position: 'bottom-right', color: '#FFFFFF', fontSize: 24 },
        generateThumbnail: true,
        enableContentSecurity: true,
        enableCDNAcceleration: true,
        enableAntiHotlinking: true,
        businessType: MediaBusinessType.PROPERTY_FLOOR_PLAN
      },
      [MediaBusinessType.PROPERTY_THUMBNAIL]: {
        compression: { enabled: true, quality: 80, maxWidth: 400, maxHeight: 300, format: 'jpeg' },
        watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
        generateThumbnail: false,
        enableContentSecurity: true,
        enableCDNAcceleration: true,
        enableAntiHotlinking: true,
        businessType: MediaBusinessType.PROPERTY_THUMBNAIL
      },
      [MediaBusinessType.IDENTITY_CARD]: {
        compression: { enabled: true, quality: 95, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
        watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
        generateThumbnail: false,
        enableContentSecurity: true,
        enableCDNAcceleration: false,
        enableAntiHotlinking: false,
        businessType: MediaBusinessType.IDENTITY_CARD
      },
      [MediaBusinessType.BUSINESS_LICENSE]: {
        compression: { enabled: true, quality: 95, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
        watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
        generateThumbnail: false,
        enableContentSecurity: true,
        enableCDNAcceleration: false,
        enableAntiHotlinking: false,
        businessType: MediaBusinessType.BUSINESS_LICENSE
      },
      [MediaBusinessType.PROPERTY_CERT]: {
        compression: { enabled: true, quality: 95, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
        watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
        generateThumbnail: false,
        enableContentSecurity: true,
        enableCDNAcceleration: false,
        enableAntiHotlinking: false,
        businessType: MediaBusinessType.PROPERTY_CERT
      },
      [MediaBusinessType.USER_AVATAR]: {
        compression: { enabled: true, quality: 90, maxWidth: 512, maxHeight: 512, format: 'jpeg' },
        watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
        generateThumbnail: true,
        enableContentSecurity: false,
        enableCDNAcceleration: true,
        enableAntiHotlinking: false,
        businessType: MediaBusinessType.USER_AVATAR
      },
      [MediaBusinessType.FINANCIAL_DOC]: {
        compression: { enabled: true, quality: 95, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
        watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
        generateThumbnail: false,
        enableContentSecurity: true,
        enableCDNAcceleration: false,
        enableAntiHotlinking: false,
        businessType: MediaBusinessType.FINANCIAL_DOC
      },
      [MediaBusinessType.OTHER_DOC]: {
        compression: { enabled: true, quality: 85, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
        watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
        generateThumbnail: false,
        enableContentSecurity: false,
        enableCDNAcceleration: true,
        enableAntiHotlinking: false,
        businessType: MediaBusinessType.OTHER_DOC
      }
    };
    
    return defaultConfigs[businessType];
  }
  
  /**
   * 构建OSS水印URL参数
   */
  buildWatermarkParams(config: WatermarkConfig): string {
    if (!config.enabled) return '';
    
    // 将文字转换为base64
    const textBase64 = btoa(unescape(encodeURIComponent(config.text)));
    
    // 位置映射
    const positionMap = {
      'center': 'center',
      'top-left': 'nw',
      'top-right': 'ne',
      'bottom-left': 'sw',
      'bottom-right': 'se'
    };
    
    // 构建水印参数
    const params = [
      'x-oss-process=image/watermark',
      `text_${textBase64}`,
      `color_${config.color.replace('#', '')}`,
      `size_${config.fontSize}`,
      `g_${positionMap[config.position]}`,
      `t_${Math.round(config.opacity * 100)}`
    ];
    
    return params.join(',');
  }
}

// 导出单例实例
export const mediaServiceAdapter = new MediaServiceAdapter();
