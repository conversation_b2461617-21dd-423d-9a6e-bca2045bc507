"""
统一地图服务API模块

企业级架构 - 统一的地图服务接口
支持房源详情页地图、地图找房页、圆形搜索等多种场景
"""

from fastapi import APIRouter

from .search import router as search_router
from .property_detail import router as property_detail_router
from .map_search import router as map_search_router

# 创建统一的地图路由器
router = APIRouter(prefix="/map", tags=["地图服务 Map Services"])

# 注册各个子模块路由
router.include_router(search_router, tags=["圆形搜索 Circle Search"])
router.include_router(property_detail_router, prefix="/detail", tags=["房源详情地图 Property Detail Map"])
router.include_router(map_search_router, prefix="/find", tags=["地图找房 Map Search"])
