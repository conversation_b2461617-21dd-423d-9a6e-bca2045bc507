"""add_district_field_only

Revision ID: ed202ef58643
Revises: fc0032c090c6
Create Date: 2025-07-22 17:09:14.739236

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'ed202ef58643'
down_revision = 'fc0032c090c6'
branch_labels = None
depends_on = None


def upgrade():
    # 添加properties表缺失的字段
    op.add_column('properties', sa.Column('district', sa.String(length=100), nullable=True))
    op.add_column('properties', sa.Column('city', sa.String(length=50), nullable=True))  
    op.add_column('properties', sa.Column('province', sa.String(length=50), nullable=True))
    op.add_column('properties', sa.Column('geo_accuracy', sa.Integer(), nullable=True))
    op.add_column('properties', sa.Column('geo_source', sa.String(length=50), nullable=True))


def downgrade():
    # 删除添加的字段
    op.drop_column('properties', 'geo_source')
    op.drop_column('properties', 'geo_accuracy')
    op.drop_column('properties', 'province')
    op.drop_column('properties', 'city')
    op.drop_column('properties', 'district')
