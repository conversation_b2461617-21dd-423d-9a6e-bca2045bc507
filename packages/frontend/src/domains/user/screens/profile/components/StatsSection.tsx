/**
 * 双角色统计卡片区域组件
 * 功能：租买需求统计卡片 + 业主服务统计卡片
 * 完整保留原始样式，确保UI完全一致，目标：70行
 */
import React from 'react';
import { View } from 'react-native';
import { TenantBuyerStatsCard } from '../../../components/TenantBuyerStatsCard';
import { LandlordStatsCard } from '../../../components/LandlordStatsCard';
import { styles } from './styles';

interface StatsSectionProps {
  tenantBuyerStats: any | null;
  landlordStats: any | null;
  isLoggedIn: boolean;
  onStatsCardPress: (type: 'tenant_buyer' | 'landlord') => void;
  onStatsItemPress: (type: 'tenant_buyer' | 'landlord', itemType: string) => void;
}

export const StatsSection: React.FC<StatsSectionProps> = ({
  tenantBuyerStats,
  landlordStats,
  isLoggedIn,
  onStatsCardPress,
  onStatsItemPress,
}) => {
  // 仅登录用户显示统计卡片 - 完全保留原始逻辑
  if (!isLoggedIn) {
    return null;
  }

  return (
    <View style={styles.statsSection}>
      {/* 租买需求统计卡片 - 完全保留原始组件和逻辑 */}
      {tenantBuyerStats && (
        <TenantBuyerStatsCard
          stats={tenantBuyerStats}
          onCardPress={() => onStatsCardPress('tenant_buyer')}
          onStatsPress={(itemType) => onStatsItemPress('tenant_buyer', itemType)}
        />
      )}

      {/* 业主服务统计卡片 - 完全保留原始组件和逻辑 */}
      {landlordStats && (
        <LandlordStatsCard
          stats={landlordStats}
          onCardPress={() => onStatsCardPress('landlord')}
          onStatsPress={(itemType) => onStatsItemPress('landlord', itemType)}
        />
      )}
    </View>
  );
};
