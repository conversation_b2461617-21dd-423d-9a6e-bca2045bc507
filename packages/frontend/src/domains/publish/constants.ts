/**
 * 发布系统常量配置
 * 
 * 包含房源类型、交易类型、装修等级等配置选项
 */

// 房源子类型配置
export const SUB_TYPE_CONFIG = {
  // 住宅类型
  residential: {
    apartment: { label: '公寓', value: 'apartment' },
    villa: { label: '别墅', value: 'villa' },
    townhouse: { label: '联排别墅', value: 'townhouse' },
    duplex: { label: '复式', value: 'duplex' },
    loft: { label: 'LOFT', value: 'loft' },
    studio: { label: '单间配套', value: 'studio' }
  },
  
  // 商业类型
  commercial: {
    office: { label: '写字楼', value: 'office' },
    shop: { label: '商铺', value: 'shop' },
    warehouse: { label: '仓库', value: 'warehouse' },
    factory: { label: '厂房', value: 'factory' },
    hotel: { label: '酒店', value: 'hotel' },
    restaurant: { label: '餐饮', value: 'restaurant' }
  },
  
  // 其他类型
  other: {
    parking: { label: '车位', value: 'parking' },
    land: { label: '土地', value: 'land' },
    other: { label: '其他', value: 'other' }
  }
};

// 交易类型配置
export const TRANSACTION_TYPE_CONFIG = {
  sale: { 
    label: '出售', 
    value: 'sale',
    color: '#FF6B35',
    icon: 'home'
  },
  rent: { 
    label: '出租', 
    value: 'rent',
    color: '#4A90E2',
    icon: 'key'
  },
  lease: { 
    label: '转租', 
    value: 'lease',
    color: '#7ED321',
    icon: 'repeat'
  }
};

// 装修等级配置
export const DECORATION_LEVEL_CONFIG = {
  rough: {
    label: '毛坯',
    value: 'rough',
    description: '未装修，需要自行装修'
  },
  simple: {
    label: '简装',
    value: 'simple', 
    description: '基础装修，可以入住'
  },
  medium: {
    label: '中装',
    value: 'medium',
    description: '装修良好，设施完善'
  },
  luxury: {
    label: '精装',
    value: 'luxury',
    description: '豪华装修，品质优良'
  },
  hardcover: {
    label: '豪装',
    value: 'hardcover',
    description: '顶级装修，奢华配置'
  }
};

// 朝向选项配置
export const ORIENTATION_OPTIONS = [
  { label: '东', value: 'east' },
  { label: '南', value: 'south' },
  { label: '西', value: 'west' },
  { label: '北', value: 'north' },
  { label: '东南', value: 'southeast' },
  { label: '东北', value: 'northeast' },
  { label: '西南', value: 'southwest' },
  { label: '西北', value: 'northwest' },
  { label: '南北', value: 'north_south' },
  { label: '东西', value: 'east_west' }
];

// 楼层类型配置
export const FLOOR_TYPE_CONFIG = {
  low: { label: '低楼层', value: 'low', range: '1-3层' },
  middle: { label: '中楼层', value: 'middle', range: '4-15层' },
  high: { label: '高楼层', value: 'high', range: '16层以上' }
};

// 房屋用途配置
export const PROPERTY_USE_CONFIG = {
  residential: { label: '住宅', value: 'residential' },
  commercial: { label: '商业', value: 'commercial' },
  office: { label: '办公', value: 'office' },
  industrial: { label: '工业', value: 'industrial' },
  mixed: { label: '综合', value: 'mixed' }
};

// 配套设施选项
export const FACILITIES_OPTIONS = [
  // 基础设施
  { label: '电梯', value: 'elevator', category: 'basic' },
  { label: '停车位', value: 'parking', category: 'basic' },
  { label: '空调', value: 'air_conditioning', category: 'basic' },
  { label: '暖气', value: 'heating', category: 'basic' },
  
  // 安全设施
  { label: '门禁系统', value: 'access_control', category: 'security' },
  { label: '监控系统', value: 'surveillance', category: 'security' },
  { label: '安保', value: 'security_guard', category: 'security' },
  
  // 生活便利
  { label: '超市', value: 'supermarket', category: 'convenience' },
  { label: '餐厅', value: 'restaurant', category: 'convenience' },
  { label: '健身房', value: 'gym', category: 'convenience' },
  { label: '游泳池', value: 'swimming_pool', category: 'convenience' },
  
  // 交通设施
  { label: '地铁', value: 'subway', category: 'transport' },
  { label: '公交', value: 'bus', category: 'transport' },
  { label: '机场', value: 'airport', category: 'transport' }
];

// 表单验证规则
export const VALIDATION_RULES = {
  title: {
    required: true,
    minLength: 5,
    maxLength: 50,
    message: '标题长度应在5-50字符之间'
  },
  area: {
    required: true,
    min: 1,
    max: 10000,
    message: '面积应在1-10000平米之间'
  },
  price: {
    required: true,
    min: 0.01,
    message: '请输入有效价格'
  },
  rooms: {
    min: 0,
    max: 20,
    message: '房间数量应在0-20之间'
  }
};

// 默认值配置
export const DEFAULT_VALUES = {
  transactionType: 'rent',
  decorationLevel: 'simple',
  orientation: 'south',
  propertyUse: 'residential',
  facilities: []
};