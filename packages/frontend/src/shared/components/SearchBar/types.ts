/**
 * 搜索栏抽象层类型定义
 * 统一不同页面搜索栏的接口和行为
 */

import { Animated } from 'react-native';

/**
 * 搜索栏模式
 */
export type SearchBarMode = 
  | 'carousel'      // 轮播模式 - 首页使用，显示轮播提示词
  | 'input'         // 输入模式 - 地图页面使用，可输入搜索
  | 'animated'      // 动画模式 - 房源类型页面使用，滚动时隐藏/显示
  | 'compact';      // 紧凑模式 - 房源类型页面头部使用，滚动时显示

/**
 * 搜索栏主题
 */
export type SearchBarTheme = 
  | 'homepage'      // 首页主题
  | 'property'      // 房源页面主题
  | 'map'           // 地图页面主题
  | 'minimal';      // 极简主题

/**
 * 搜索栏基础属性
 */
export interface BaseSearchBarProps {
  /** 搜索栏模式 */
  mode: SearchBarMode;
  /** 主题样式 */
  theme?: SearchBarTheme;
  /** 占位符文本 */
  placeholder?: string;
  /** 搜索回调 */
  onSearch?: (query: string) => void;
  /** 焦点回调 */
  onFocus?: () => void;
  /** 点击回调（轮播模式使用） */
  onPress?: () => void;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 轮播模式特有属性
 */
export interface CarouselSearchBarProps extends BaseSearchBarProps {
  mode: 'carousel';
  /** 轮播提示词数组 */
  placeholders: string[];
  /** 轮播间隔（毫秒） */
  interval?: number;
  /** 城市选择器 */
  citySelector?: {
    currentCity: string;
    onCityPress: () => void;
  };
  /** 右侧图标 */
  rightIcon?: {
    icon: string;
    onPress: () => void;
  };
}

/**
 * 输入模式特有属性
 */
export interface InputSearchBarProps extends BaseSearchBarProps {
  mode: 'input';
  /** 受控值 */
  value?: string;
  /** 值变化回调 */
  onChangeText?: (text: string) => void;
  /** 清除按钮 */
  showClearButton?: boolean;
}

/**
 * 动画模式特有属性
 */
export interface AnimatedSearchBarProps extends BaseSearchBarProps {
  mode: 'animated';
  /** 滚动动画值 */
  scrollY: Animated.Value;
  /** 值变化回调 */
  onChangeText?: (text: string) => void;
  /** 房源类型（用于占位符） */
  propertyType?: string;
}

/**
 * 紧凑模式特有属性
 */
export interface CompactSearchBarProps extends BaseSearchBarProps {
  mode: 'compact';
  /** 滚动动画值 */
  scrollY: Animated.Value;
  /** 值变化回调 */
  onChangeText?: (text: string) => void;
  /** 房源类型（用于占位符） */
  propertyType?: string;
}

/**
 * 统一搜索栏属性类型
 */
export type UniversalSearchBarProps = 
  | CarouselSearchBarProps
  | InputSearchBarProps
  | AnimatedSearchBarProps
  | CompactSearchBarProps;

/**
 * 搜索栏样式配置
 */
export interface SearchBarStyleConfig {
  container: {
    backgroundColor: string;
    paddingHorizontal: number;
    paddingVertical: number;
  };
  searchBox: {
    backgroundColor: string;
    borderRadius: number;
    height: number;
    paddingHorizontal: number;
  };
  text: {
    fontSize: number;
    color: string;
    placeholderColor: string;
  };
  icon: {
    size: number;
    color: string;
  };
}
