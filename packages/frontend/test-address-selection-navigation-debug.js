/**
 * 🔍 地址选择导航调试脚本 - Stage3验证
 *
 * 目的：验证地址选择后跳转回地图，地址更新和距离计算流程
 */

// 🎯 测试场景分析
console.log(`
📋 地址选择导航完整流程调试分析

🔍 问题现象：
- 地址选择后跳转回地图
- 地图没有显示更新的地址
- 没有重新计算距离和时间

🎯 调试重点：
1. useAddressSearch导航参数传递
2. PropertyNavigationMap参数接收
3. useFocusEffect回调执行
4. calculateRoute函数调用
5. 自定义位置状态更新

📂 关键文件：
- useAddressSearch.ts (第117-124行)
- PropertyNavigationMap.tsx (第121-187行)
- PropertyNavigationScreen.tsx (第59-73行)

🔧 修复方案验证：
Stage3修复是否生效？
`);

// 🚀 验证点1：导航参数格式
console.log(`
🔍 验证点1：导航参数格式检查

预期导航调用：
navigation.navigate({
  name: 'PropertyNavigation',
  params: {
    selectedAddress: {
      id: 'address_id',
      name: '地址名称',
      address: '详细地址',
      latitude: 22.811832,
      longitude: 108.372917,
      formattedAddress: '完整地址'
    },
    returnKey: 'startLocation' 或 'endLocation'
  },
  merge: true
});

❌ 可能的问题：
- merge: true 可能不被支持
- PropertyNavigationScreen 没有正确传递参数
- useFocusEffect 依赖问题导致不执行
`);

// 🚀 验证点2：PropertyNavigationScreen参数传递
console.log(`
🔍 验证点2：PropertyNavigationScreen参数传递

当前实现 (PropertyNavigationScreen.tsx 第59-73行)：
const mapParams = {
  route: {
    params: {
      propertyLocation: { ... },
      selectedAddress: selectedAddress,  // ✅ 正确传递
      returnKey: returnKey,              // ✅ 正确传递
    }
  }
};

⚠️ 潜在问题：
- selectedAddress 可能被 propertyInfo 覆盖
- PropertyNavigationMap 接收的是嵌套的 route.params
`);

// 🚀 验证点3：PropertyNavigationMap参数接收
console.log(`
🔍 验证点3：PropertyNavigationMap参数接收

当前useFocusEffect实现：
useFocusEffect(
  useCallback(() => {
    const routeParams = route.params as any;
    if (routeParams?.selectedAddress && routeParams?.returnKey) {
      // 处理逻辑
    }
  }, [route.params, navigation])  // ⚠️ 依赖问题
);

❌ 问题分析：
1. useCallback依赖 [route.params, navigation] 可能导致无限循环
2. route.params 可能在每次渲染时都是新对象
3. useFocusEffect 可能不执行或执行时参数已被清除
`);

// 🚀 修复建议
console.log(`
🛠️ 建议修复方案：

1. 简化useFocusEffect依赖：
useFocusEffect(
  useCallback(() => {
    const routeParams = route.params as any;
    console.log('🔍 [Focus调试] 接收参数:', routeParams);
    
    if (routeParams?.selectedAddress && routeParams?.returnKey) {
      console.log('✅ [Focus调试] 找到地址选择参数');
      // 处理逻辑
    } else {
      console.log('❌ [Focus调试] 未找到地址选择参数');
    }
  }, []) // 🔧 移除依赖，只在focus时执行
);

2. 添加详细调试日志：
- 记录navigation调用参数
- 记录PropertyNavigationScreen接收参数
- 记录PropertyNavigationMap处理逻辑
- 记录calculateRoute调用情况

3. 验证参数持久性：
- 确认selectedAddress在跳转过程中不丢失
- 确认returnKey正确传递
- 确认参数格式符合预期
`);

// 🎯 测试步骤
console.log(`
📋 完整测试步骤：

1. 📱 启动应用，进入PropertyNavigation页面
2. 🔍 点击起点或终点地址输入框
3. 📝 输入地址关键词，选择搜索结果
4. 📊 观察控制台日志：
   - [地址选择] useAddressSearch导航调用
   - [PropertyNavigationScreen] 参数接收
   - [PropertyNavigationMap] useFocusEffect执行
   - [地址搜索] 坐标更新
   - [路线规划] calculateRoute调用
5. ✅ 验证地址文本和距离计算是否更新

🔍 关键调试日志：
- 📍 [地址选择] 使用返回参数模式
- 📍 [地址选择] PropertyNavigation传参
- 🏠 [PropertyNavigationScreen] 房源信息详细检查
- 📍 [地址选择回调] 接收到数据
- 📍 [地址搜索] 更新起点/终点坐标
- 🔄 [地址搜索] 重新计算路线
`);

console.log(`
🎯 Stage3修复状态检查完成
如果地址选择后仍无法更新，需要进一步调试useFocusEffect执行情况
`);
