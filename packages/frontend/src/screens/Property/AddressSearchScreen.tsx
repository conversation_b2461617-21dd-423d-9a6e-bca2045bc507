/**
 * 简单地址搜索页面 - 恢复原始简单架构
 * 功能：
 * 1. 简单的地址搜索界面
 * 2. 高德地图API搜索地址
 * 3. 返回选中的地址给导航页面
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';

// 🔍 高德地图搜索API配置
const AMAP_API_BASE = 'https://restapi.amap.com/v3';
const AMAP_WEB_KEY = 'd4930e00ccca3f4e9ee4968cbc148aa4';

// 简化的地址结果类型
interface AddressResult {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  formattedAddress?: string;
  distance?: number;
}

interface RouteParams {
  returnKey: string;
  currentText: string;
  placeholder: string;
}

const AddressSearchScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const params = route.params as RouteParams;

  // 搜索状态
  const [searchQuery, setSearchQuery] = useState(params?.currentText || '');
  const [searchResults, setSearchResults] = useState<AddressResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  // 🔍 高德地图搜索API调用
  const searchAddresses = useCallback(async (query: string) => {
    if (!query || query.trim().length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    setSearchError(null);

    try {
      // 使用高德地图POI搜索API
      const url = `${AMAP_API_BASE}/place/text?keywords=${encodeURIComponent(query.trim())}&city=南宁&types=&children=1&offset=20&page=1&key=${AMAP_WEB_KEY}&extensions=all`;

      console.log('🔍 [地址搜索] 开始搜索:', {
        query: query.trim(),
        url: url,
      });

      const response = await fetch(url);
      const data = await response.json();

      console.log('🔍 [地址搜索] API响应:', {
        status: data.status,
        count: data.count,
        poisLength: data.pois?.length || 0,
      });

      if (data.status === '1' && data.pois && data.pois.length > 0) {
        const results: AddressResult[] = data.pois.map(
          (poi: any, index: number) => {
            // 解析坐标（高德API返回格式：'longitude,latitude'）
            const [longitude, latitude] = poi.location
              .split(',')
              .map((coord: string) => parseFloat(coord));

            return {
              id: poi.id || `poi-${index}`,
              name: poi.name || '未知地点',
              address: poi.address || '',
              latitude: latitude,
              longitude: longitude,
              formattedAddress:
                poi.pname && poi.cityname && poi.adname
                  ? `${poi.pname}${poi.cityname}${poi.adname}${poi.address || ''}`
                  : poi.address || poi.name,
              distance: poi.distance ? parseInt(poi.distance) : undefined,
            };
          }
        );

        setSearchResults(results);
        console.log('✅ [地址搜索] 搜索成功:', {
          count: results.length,
          firstResult: results[0]?.name,
        });
      } else {
        setSearchResults([]);
        console.log('⚠️ [地址搜索] 没有找到结果');
      }
    } catch (error) {
      console.error('❌ [地址搜索] 搜索失败:', error);
      setSearchError('搜索失败，请稍后重试');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // 🎯 处理搜索输入
  const handleSearchInputChange = useCallback(
    (text: string) => {
      setSearchQuery(text);

      // 防抖搜索：300ms后执行搜索
      const timeoutId = setTimeout(() => {
        searchAddresses(text);
      }, 300);

      return () => clearTimeout(timeoutId);
    },
    [searchAddresses]
  );

  // 🎯 处理地址选择
  const handleAddressSelect = useCallback(
    (address: AddressResult) => {
      console.log('🎯 [地址选择] 选中地址:', address);

      try {
        // 获取PropertyNavigation参数，将新选择的地址合并进去
        const navigationState = navigation.getState();

        if (navigationState) {
          const currentRouteIndex = navigationState.index;
          const routes = navigationState.routes;

          // 查找PropertyNavigation路由
          let propertyNavRoute = null;
          for (let i = currentRouteIndex - 1; i >= 0; i--) {
            if (routes[i].name === 'PropertyNavigation') {
              propertyNavRoute = routes[i];
              break;
            }
          }

          if (propertyNavRoute) {
            console.log('🎯 [地址选择] 找到PropertyNavigation路由，更新参数');

            // 构建新的参数，保持原有参数并添加选中地址
            const newParams = {
              ...propertyNavRoute.params,
              selectedAddress: address,
              returnKey: params?.returnKey,
            };

            // 使用navigate替换当前的PropertyNavigation路由参数
            (navigation as any).navigate('PropertyNavigation', newParams);
          } else {
            // 如果没有找到PropertyNavigation路由，直接导航过去
            console.log('🎯 [地址选择] 未找到PropertyNavigation路由，直接导航');
            (navigation as any).navigate('PropertyNavigation', {
              selectedAddress: address,
              returnKey: params?.returnKey,
              // 需要提供基本的propertyInfo
              propertyInfo: {
                id: 'unknown',
                title: '房源导航',
                address: address.formattedAddress || address.address,
                latitude: address.latitude,
                longitude: address.longitude,
              },
            });
          }
        } else {
          // navigationState为空，使用简单方案
          console.log('🎯 [地址选择] navigationState为空，使用简单导航');
          (navigation as any).navigate('PropertyNavigation', {
            selectedAddress: address,
            returnKey: params?.returnKey,
          });
        }
      } catch (error) {
        console.error('🎯 [地址选择] 导航失败:', error);
        // 备用方案：直接goBack
        navigation.goBack();
      }
    },
    [navigation, params?.returnKey]
  );

  // 🚀 返回按钮
  const handleGoBack = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  // 🧹 清除搜索
  const handleClearSearch = useCallback(() => {
    setSearchQuery('');
    setSearchResults([]);
    setSearchError(null);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      {/* 搜索头部 */}
      <View style={styles.searchHeader}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>

        <View style={styles.searchInputContainer}>
          <TextInput
            style={styles.searchInput}
            value={searchQuery}
            onChangeText={handleSearchInputChange}
            placeholder={params?.placeholder || '搜索地址'}
            placeholderTextColor="#999"
            returnKeyType="search"
            onSubmitEditing={() => searchAddresses(searchQuery)}
            autoFocus
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={handleClearSearch}
            >
              <Text style={styles.clearIcon}>×</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* 内容区域 */}
      <View style={styles.content}>
        {/* 加载状态 */}
        {isSearching && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#007AFF" />
            <Text style={styles.loadingText}>搜索中...</Text>
          </View>
        )}

        {/* 错误状态 */}
        {searchError && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorIcon}>⚠️</Text>
            <Text style={styles.errorText}>{searchError}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => searchAddresses(searchQuery)}
            >
              <Text style={styles.retryButtonText}>重试</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* 搜索结果 */}
        {searchResults.length > 0 && !isSearching && (
          <FlatList
            data={searchResults}
            keyExtractor={item => item.id}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.resultItem}
                onPress={() => handleAddressSelect(item)}
                activeOpacity={0.7}
              >
                <View style={styles.resultItemContent}>
                  <View style={styles.resultItemMain}>
                    <Text style={styles.resultItemName} numberOfLines={1}>
                      {item.name}
                    </Text>
                    <Text style={styles.resultItemAddress} numberOfLines={2}>
                      {item.formattedAddress || item.address}
                    </Text>
                  </View>

                  {item.distance && item.distance > 0 && (
                    <View style={styles.resultItemDistance}>
                      <Text style={styles.distanceText}>
                        {item.distance > 1000
                          ? `${(item.distance / 1000).toFixed(1)}km`
                          : `${Math.round(item.distance)}m`}
                      </Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            )}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            showsVerticalScrollIndicator={false}
          />
        )}

        {/* 空状态 */}
        {searchQuery.length > 0 &&
          searchResults.length === 0 &&
          !isSearching &&
          !searchError && (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyIcon}>🔍</Text>
              <Text style={styles.emptyText}>未找到相关地址</Text>
              <Text style={styles.emptySubtext}>尝试使用其他关键词搜索</Text>
            </View>
          )}

        {/* 默认提示 */}
        {searchQuery.length === 0 && (
          <View style={styles.hintContainer}>
            <Text style={styles.hintIcon}>📍</Text>
            <Text style={styles.hintText}>输入地址、建筑物或地标名称</Text>
            <Text style={styles.hintSubtext}>开始搜索地址位置</Text>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },

  // 搜索头部
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  backIcon: {
    fontSize: 20,
    color: '#333333',
    fontWeight: 'bold',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 40,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    paddingVertical: 0,
  },
  clearButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  clearIcon: {
    fontSize: 18,
    color: '#999999',
    fontWeight: 'bold',
  },

  // 内容区域
  content: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },

  // 搜索结果
  resultItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  resultItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultItemMain: {
    flex: 1,
  },
  resultItemName: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
    marginBottom: 4,
  },
  resultItemAddress: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  resultItemDistance: {
    marginLeft: 12,
  },
  distanceText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
  },

  // 分隔线
  separator: {
    height: 1,
    backgroundColor: '#F0F0F0',
    marginLeft: 16,
  },

  // 加载状态
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 8,
  },

  // 错误状态
  errorContainer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 32,
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3333',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },

  // 空状态
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 32,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
  },

  // 默认提示
  hintContainer: {
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 32,
  },
  hintIcon: {
    fontSize: 48,
    marginBottom: 16,
    opacity: 0.6,
  },
  hintText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 8,
  },
  hintSubtext: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
  },
});

export default AddressSearchScreen;
