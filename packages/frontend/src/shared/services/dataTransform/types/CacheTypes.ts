/**
 * 缓存抽象层类型定义
 * 
 * 定义了多层缓存系统的接口和类型，支持：
 * 1. 内存缓存（L1）- 最快访问
 * 2. Redis缓存（L2）- 持久化缓存
 * 3. CDN缓存（L3）- 边缘缓存
 * 
 * 设计原则：
 * - 统一接口：所有缓存提供商使用相同接口
 * - 智能路由：根据数据类型选择最优缓存策略
 * - 一致性保证：多层缓存的数据一致性管理
 * - 性能优化：缓存命中率和响应时间优化
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月16日
 */

// 缓存级别
export enum CacheLevel {
  L1_MEMORY = 'l1_memory',     // 内存缓存
  L2_REDIS = 'l2_redis',       // Redis缓存
  L3_CDN = 'l3_cdn'            // CDN缓存
}

// 缓存策略
export enum CacheStrategy {
  WRITE_THROUGH = 'write_through',     // 写穿透：同时写入缓存和存储
  WRITE_BACK = 'write_back',           // 写回：先写缓存，延迟写存储
  WRITE_AROUND = 'write_around',       // 写绕过：直接写存储，不写缓存
  READ_THROUGH = 'read_through',       // 读穿透：缓存未命中时从存储读取
  CACHE_ASIDE = 'cache_aside'          // 缓存旁路：应用程序管理缓存
}

// 缓存数据类型
export enum CacheDataType {
  MEDIA_METADATA = 'media_metadata',   // 媒体元数据
  USER_SESSION = 'user_session',       // 用户会话
  PROPERTY_DATA = 'property_data',     // 房源数据
  SEARCH_RESULT = 'search_result',     // 搜索结果
  API_RESPONSE = 'api_response'        // API响应
}

// 缓存配置
export interface CacheConfig {
  level: CacheLevel;
  strategy: CacheStrategy;
  ttl: number;                         // 生存时间（秒）
  maxSize?: number;                    // 最大缓存大小
  compressionEnabled?: boolean;        // 是否启用压缩
  encryptionEnabled?: boolean;         // 是否启用加密
}

// 缓存统计信息
export interface CacheStats {
  hitCount: number;                    // 命中次数
  missCount: number;                   // 未命中次数
  hitRate: number;                     // 命中率
  totalRequests: number;               // 总请求数
  averageResponseTime: number;         // 平均响应时间（毫秒）
  cacheSize: number;                   // 当前缓存大小
  maxSize: number;                     // 最大缓存大小
  evictionCount: number;               // 淘汰次数
}

// 缓存操作结果
export interface CacheResult<T> {
  success: boolean;
  data?: T;
  fromCache: boolean;
  level?: CacheLevel;
  responseTime: number;
  error?: string;
}

// 缓存失效模式
export interface InvalidationPattern {
  pattern: string;                     // 失效模式（支持通配符）
  cascade: boolean;                    // 是否级联失效
  levels: CacheLevel[];                // 影响的缓存级别
}

/**
 * 统一缓存提供商接口
 */
export interface ICacheProvider {
  /**
   * 获取缓存数据
   */
  get<T>(key: string): Promise<CacheResult<T>>;

  /**
   * 设置缓存数据
   */
  set<T>(key: string, value: T, ttl?: number): Promise<boolean>;

  /**
   * 删除缓存数据
   */
  delete(key: string): Promise<boolean>;

  /**
   * 批量获取
   */
  mget<T>(keys: string[]): Promise<Map<string, CacheResult<T>>>;

  /**
   * 批量设置
   */
  mset<T>(entries: Map<string, T>, ttl?: number): Promise<boolean>;

  /**
   * 检查键是否存在
   */
  exists(key: string): Promise<boolean>;

  /**
   * 设置过期时间
   */
  expire(key: string, ttl: number): Promise<boolean>;

  /**
   * 获取剩余生存时间
   */
  ttl(key: string): Promise<number>;

  /**
   * 模式匹配删除
   */
  invalidate(pattern: string): Promise<number>;

  /**
   * 清空所有缓存
   */
  clear(): Promise<boolean>;

  /**
   * 获取缓存统计
   */
  stats(): Promise<CacheStats>;

  /**
   * 健康检查
   */
  health(): Promise<boolean>;
}

/**
 * 多层缓存管理器接口
 */
export interface IMultiLevelCacheManager {
  /**
   * 获取数据（自动多层查找）
   */
  get<T>(key: string, dataType: CacheDataType): Promise<CacheResult<T>>;

  /**
   * 设置数据（自动多层写入）
   */
  set<T>(key: string, value: T, dataType: CacheDataType, ttl?: number): Promise<boolean>;

  /**
   * 删除数据（多层删除）
   */
  delete(key: string, dataType: CacheDataType): Promise<boolean>;

  /**
   * 失效缓存（支持模式匹配）
   */
  invalidate(pattern: InvalidationPattern): Promise<number>;

  /**
   * 预热缓存
   */
  warmup<T>(key: string, loader: () => Promise<T>, dataType: CacheDataType): Promise<boolean>;

  /**
   * 获取综合统计
   */
  getStats(): Promise<Map<CacheLevel, CacheStats>>;

  /**
   * 健康检查
   */
  healthCheck(): Promise<Map<CacheLevel, boolean>>;
}

/**
 * 缓存事件类型
 */
export enum CacheEventType {
  HIT = 'hit',
  MISS = 'miss',
  SET = 'set',
  DELETE = 'delete',
  INVALIDATE = 'invalidate',
  EVICT = 'evict',
  ERROR = 'error'
}

/**
 * 缓存事件
 */
export interface CacheEvent {
  type: CacheEventType;
  key: string;
  level: CacheLevel;
  timestamp: number;
  responseTime?: number;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * 缓存事件监听器接口
 */
export interface ICacheEventListener {
  onEvent(event: CacheEvent): void;
}

/**
 * 缓存数据类型配置映射
 */
export const CacheDataTypeConfigs: Record<CacheDataType, CacheConfig[]> = {
  [CacheDataType.MEDIA_METADATA]: [
    { level: CacheLevel.L1_MEMORY, strategy: CacheStrategy.CACHE_ASIDE, ttl: 300 },    // 5分钟
    { level: CacheLevel.L2_REDIS, strategy: CacheStrategy.WRITE_THROUGH, ttl: 3600 }   // 1小时
  ],
  [CacheDataType.USER_SESSION]: [
    { level: CacheLevel.L1_MEMORY, strategy: CacheStrategy.CACHE_ASIDE, ttl: 600 },    // 10分钟
    { level: CacheLevel.L2_REDIS, strategy: CacheStrategy.WRITE_THROUGH, ttl: 7200 }   // 2小时
  ],
  [CacheDataType.PROPERTY_DATA]: [
    { level: CacheLevel.L1_MEMORY, strategy: CacheStrategy.CACHE_ASIDE, ttl: 180 },    // 3分钟
    { level: CacheLevel.L2_REDIS, strategy: CacheStrategy.WRITE_THROUGH, ttl: 1800 },  // 30分钟
    { level: CacheLevel.L3_CDN, strategy: CacheStrategy.WRITE_AROUND, ttl: 3600 }      // 1小时
  ],
  [CacheDataType.SEARCH_RESULT]: [
    { level: CacheLevel.L1_MEMORY, strategy: CacheStrategy.CACHE_ASIDE, ttl: 120 },    // 2分钟
    { level: CacheLevel.L2_REDIS, strategy: CacheStrategy.WRITE_THROUGH, ttl: 900 }    // 15分钟
  ],
  [CacheDataType.API_RESPONSE]: [
    { level: CacheLevel.L1_MEMORY, strategy: CacheStrategy.CACHE_ASIDE, ttl: 60 },     // 1分钟
    { level: CacheLevel.L2_REDIS, strategy: CacheStrategy.WRITE_THROUGH, ttl: 300 }    // 5分钟
  ]
};

/**
 * 缓存键生成器
 */
export class CacheKeyGenerator {
  private static readonly SEPARATOR = ':';
  private static readonly PREFIX = 'huixuanzhi';

  /**
   * 生成媒体元数据缓存键
   */
  static mediaMetadata(mediaId: string): string {
    return `${this.PREFIX}${this.SEPARATOR}media${this.SEPARATOR}${mediaId}`;
  }

  /**
   * 生成用户会话缓存键
   */
  static userSession(userId: string): string {
    return `${this.PREFIX}${this.SEPARATOR}session${this.SEPARATOR}${userId}`;
  }

  /**
   * 生成房源数据缓存键
   */
  static propertyData(propertyId: string): string {
    return `${this.PREFIX}${this.SEPARATOR}property${this.SEPARATOR}${propertyId}`;
  }

  /**
   * 生成搜索结果缓存键
   */
  static searchResult(query: string, filters: Record<string, any>): string {
    const filterHash = this.hashObject(filters);
    const queryHash = this.hashString(query);
    return `${this.PREFIX}${this.SEPARATOR}search${this.SEPARATOR}${queryHash}${this.SEPARATOR}${filterHash}`;
  }

  /**
   * 生成API响应缓存键
   */
  static apiResponse(endpoint: string, params: Record<string, any>): string {
    const paramHash = this.hashObject(params);
    const endpointKey = endpoint.replace(/[^a-zA-Z0-9]/g, '_');
    return `${this.PREFIX}${this.SEPARATOR}api${this.SEPARATOR}${endpointKey}${this.SEPARATOR}${paramHash}`;
  }

  /**
   * 哈希字符串
   */
  private static hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 哈希对象
   */
  private static hashObject(obj: Record<string, any>): string {
    const sortedKeys = Object.keys(obj).sort();
    const sortedObj = sortedKeys.reduce((result, key) => {
      result[key] = obj[key];
      return result;
    }, {} as Record<string, any>);
    return this.hashString(JSON.stringify(sortedObj));
  }
}
