"""clean office description validation

Revision ID: 20250722_clean_validation
Revises: 20250722_remove_description
Create Date: 2025-07-22 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20250722_clean_validation'
down_revision = '20250722_remove_description'
branch_labels = None
depends_on = None


def upgrade():
    """Remove any remaining validation or constraints related to office_description"""
    
    # 确保office_description字段完全删除
    try:
        # 如果字段仍然存在，删除它
        op.drop_column('properties', 'office_description')
    except Exception:
        # 如果字段不存在，忽略错误
        pass
    
    try:
        # 删除其他相关描述字段（如果仍然存在）
        op.drop_column('properties', 'shop_description')
    except Exception:
        pass
    
    try:
        op.drop_column('properties', 'factory_land_description')
    except Exception:
        pass
    
    try:
        op.drop_column('properties', 'meeting_activity_description')
    except Exception:
        pass
    
    # 删除任何可能的约束或检查条件
    connection = op.get_bind()
    
    # 检查并删除任何相关的检查约束
    result = connection.execute(sa.text("""
        SELECT constraint_name, table_name 
        FROM information_schema.check_constraints 
        WHERE constraint_name LIKE '%office_description%' 
           OR constraint_name LIKE '%shop_description%'
           OR constraint_name LIKE '%factory_land_description%'
           OR constraint_name LIKE '%meeting_activity_description%'
    """))
    
    for row in result:
        try:
            op.drop_constraint(row[0], row[1])
        except Exception:
            pass


def downgrade():
    """Add back the description fields if needed"""
    # 在降级时重新添加字段（可选）
    op.add_column('properties', sa.Column('office_description', sa.String(length=500), nullable=True))
    op.add_column('properties', sa.Column('shop_description', sa.String(length=500), nullable=True))
    op.add_column('properties', sa.Column('factory_land_description', sa.String(length=500), nullable=True))
    op.add_column('properties', sa.Column('meeting_activity_description', sa.String(length=500), nullable=True))