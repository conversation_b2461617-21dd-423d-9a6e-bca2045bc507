/**
 * 房源数据转换器
 * 
 * 遵循AI编码指导规范：
 * - 继承BaseTransformer基础类
 * - 完整的类型安全
 * - 企业级错误处理
 * - 单一职责原则
 * 
 * @fileoverview 处理房源相关的数据转换
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */

import { z } from 'zod';
import { BaseTransformer } from '../core/BaseTransformer';
import {
  TransformResult,
  TransformOptions,
  UIPropertyType,
  APIPropertyType,
  StorePropertyParams,
  APIPropertyParams,
  PropertyListItem,
  PropertyAPIResponse,
  PaginatedResponse,
  MyProperty,
  UserPropertyParams,
  UserPropertyAPIParams,
  PropertyPublishFormData,
  PropertyPublishAPIRequest
} from '../types/TransformTypes';

/**
 * 房源转换器
 * 负责房源相关数据的双向转换
 */
export class PropertyTransformer extends BaseTransformer<any, any> {
  
  constructor() {
    super('PropertyTransformer', '1.0.0');
  }

  /**
   * 初始化验证规则
   */
  protected initializeValidationRules(): void {
    // UI房源类型验证
    this.addValidationRule('uiPropertyType', {
      name: 'uiPropertyType',
      description: 'UI层房源类型验证',
      schema: z.enum(['商铺', '写字楼', '厂房', '仓库', '土地', '会所', '活动会议室']),
      required: true
    });

    // API房源类型验证
    this.addValidationRule('apiPropertyType', {
      name: 'apiPropertyType',
      description: 'API层房源类型验证',
      schema: z.enum(['SHOP', 'OFFICE', 'FACTORY', 'WAREHOUSE', 'LAND', 'CLUBHOUSE', 'MEETING_ROOM']),
      required: true
    });

    // Store查询参数验证
    this.addValidationRule('storeParams', {
      name: 'storeParams',
      description: 'Store层查询参数验证',
      schema: z.object({
        propertyType: z.enum(['商铺', '写字楼', '厂房', '仓库', '土地', '会所', '活动会议室']),
        sortField: z.string(),
        keyword: z.string().optional(),
        page: z.number().min(1),
        size: z.number().min(1).max(100),
        filters: z.record(z.any()).optional()
      }),
      required: true
    });

    // API响应验证 - 修复以匹配实际API响应格式
    this.addValidationRule('apiResponse', {
      name: 'apiResponse',
      description: 'API响应数据验证',
      schema: z.object({
        items: z.array(z.object({
          id: z.string(), // 修复：UUID字符串
          title: z.string(),
          property_type: z.string(), // 修复：支持所有字符串类型
          sub_type: z.string().optional(),
          address: z.string().optional(),
          building_name: z.string().optional(),
          floor: z.number().optional(),
          total_floors: z.number().optional(),
          total_area: z.number().optional(),
          usable_area: z.number().optional(),
          decoration_level: z.string().optional(),
          delivery_status: z.string().optional(),
          status: z.string(),
          verification_status: z.string().optional(),
          rent_price: z.number().optional(),
          sale_price: z.number().optional(),
          transfer_price: z.number().optional(),
          deposit_months: z.number().optional(),
          transaction_types: z.array(z.string()).optional(),
          description: z.string().optional(),
          owner_id: z.string(),
          created_at: z.string().optional(),
          updated_at: z.string().optional()
        })),
        total: z.number(),
        page: z.number(),
        size: z.number(),
        pages: z.number()
      }),
      required: true
    });

    // 用户房源查询参数验证
    this.addValidationRule('userPropertyParams', {
      name: 'userPropertyParams',
      description: '用户房源查询参数验证',
      schema: z.object({
        page: z.number().min(1),
        size: z.number().min(1).max(100),
        status: z.enum(['active', 'draft', 'inactive'])
      }),
      required: true
    });

    // 房源发布表单验证
    this.addValidationRule('propertyPublishForm', {
      name: 'propertyPublishForm',
      description: '房源发布表单验证',
      schema: z.object({
        title: z.string().min(8, '标题至少需要8个字符').max(20, '标题最多20个字符'),
        property_certificate_address: z.string().min(1, '房产证地址不能为空'),
        sub_type: z.string().min(1, '请选择房源子类型'),
        area: z.union([z.string().min(1, '面积不能为空'), z.number().min(0.1, '面积必须大于0')]),
        transaction_types: z.array(z.string()).min(1, '至少选择一种交易类型'),
        description: z.string().optional(),
        floor: z.string().optional(),
        total_floors: z.string().optional(),
        orientation: z.string().optional(),
        decoration_level: z.string().optional(),
        rent_payment_method: z.string().optional()
      }),
      required: true
    });
  }

  /**
   * 转换为API格式
   */
  toAPI(input: any, options?: TransformOptions): TransformResult<any> {
    return this.safeTransform(() => {
      if (this.isStorePropertyParams(input)) {
        return this.transformStoreParamsToAPI(input);
      }

      if (this.isUserPropertyParams(input)) {
        return this.transformUserPropertyParamsToAPI(input);
      }

      if (this.isPropertyPublishFormData(input)) {
        return this.transformPropertyPublishFormToAPI(input, options);
      }

      if (this.isUIPropertyType(input)) {
        return this.transformUIPropertyTypeToAPI(input);
      }

      throw new Error(`不支持的输入类型: ${typeof input}`);
    }, 'toAPI转换');
  }

  /**
   * 从API格式转换
   */
  fromAPI(input: any, options?: TransformOptions): TransformResult<any> {
    return this.safeTransform(() => {
      if (this.isAPIPropertyResponse(input)) {
        // 检查是否为用户房源列表（通过options或其他方式区分）
        if (options?.context === 'myProperties') {
          return this.transformAPIResponseToMyProperties(input);
        }
        return this.transformAPIResponseToStore(input);
      }

      if (this.isAPIPropertyType(input)) {
        return this.transformAPIPropertyTypeToUI(input);
      }

      throw new Error(`不支持的API输入类型: ${typeof input}`);
    }, 'fromAPI转换');
  }

  // ==================== 具体转换方法 ====================

  /**
   * Store参数转换为API参数
   */
  private transformStoreParamsToAPI(storeParams: StorePropertyParams): APIPropertyParams {
    // 验证房源类型
    if (!this.isValidUIPropertyType(storeParams.propertyType)) {
      throw new Error(`无效的房源类型: ${storeParams.propertyType}`);
    }

    const propertyTypeMap: Record<UIPropertyType, APIPropertyType> = {
      '商铺': 'SHOP',
      '写字楼': 'OFFICE',
      '厂房': 'FACTORY',
      '仓库': 'WAREHOUSE',
      '土地': 'LAND',
      '会所': 'CLUBHOUSE',
      '活动会议室': 'MEETING_ROOM'
    };

    const sortFieldMap: Record<string, string> = {
      '默认排序': 'created_at',
      '最新发布': 'created_at',
      '价格排序': 'price',
      '面积排序': 'total_area',
      '热度排序': 'view_count'
    };

    return {
      property_type: propertyTypeMap[storeParams.propertyType],
      keyword: storeParams.keyword,
      page: storeParams.page,
      size: storeParams.size,
      sort_by: sortFieldMap[storeParams.sortField] || 'created_at',
      sort_order: 'desc',
      // 处理筛选条件
      ...Object.entries(storeParams.filters || {}).reduce((acc, [key, value]) => {
        if (value && value !== '全部') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>)
    };
  }

  /**
   * 用户房源参数转换为API参数
   */
  private transformUserPropertyParamsToAPI(userParams: UserPropertyParams): UserPropertyAPIParams {
    // 验证用户房源参数
    if (!this.isValidUserPropertyStatus(userParams.status)) {
      throw new Error(`无效的房源状态: ${userParams.status}`);
    }

    // 状态映射：前端状态 → API状态
    const statusMap: Record<string, string> = {
      'active': 'ACTIVE',
      'draft': 'PENDING',
      'inactive': 'INACTIVE'
    };

    return {
      page: userParams.page,
      size: userParams.size,
      status: statusMap[userParams.status] || 'ACTIVE'
    };
  }

  /**
   * API响应转换为Store格式
   */
  private transformAPIResponseToStore(apiResponse: PaginatedResponse<PropertyAPIResponse>): {
    properties: PropertyListItem[];
    pagination: {
      total: number;
      page: number;
      size: number;
      pages: number;
      hasNextPage: boolean;
    };
  } {
    const properties: PropertyListItem[] = apiResponse.items.map((item, index) => ({
      id: item.id, // 已经是字符串类型
      title: item.title,
      price: this.formatPrice(item),
      area: `${item.total_area || 0}㎡`,
      location: item.address || item.building_name || '位置待完善',
      imageUrl: `https://picsum.photos/300/200?random=${encodeURIComponent(item.id)}`,
      tags: this.generateTagsFromProperty(item),
      aiTags: item.transaction_types || [],
      isVip: index % 3 === 0, // 临时VIP逻辑，后续从API获取
      isFeatured: index % 5 === 0,
      vipLabel: index % 3 === 0 ? ['临街铺面', '精装修', '可明火', '地铁口'][index % 4] : undefined,
      _rawData: item
    }));

    return {
      properties,
      pagination: {
        total: apiResponse.total,
        page: apiResponse.page,
        size: apiResponse.size,
        pages: apiResponse.pages,
        hasNextPage: apiResponse.page < apiResponse.pages
      }
    };
  }

  /**
   * 房源发布表单转换为API格式
   */
  private transformPropertyPublishFormToAPI(
    formData: PropertyPublishFormData,
    options?: TransformOptions
  ): PropertyPublishAPIRequest {
    // 验证表单数据
    const validationResult = this.validateInput(formData, 'propertyPublishForm');
    if (!validationResult.success) {
      throw new Error(validationResult.error);
    }

    // 获取房源类型（从options中传入）
    const propertyType = options?.propertyType || 'SHOP';
    const selectedTags = options?.selectedTags || [];

    // 转换基本信息
    const apiRequest: PropertyPublishAPIRequest = {
      title: formData.title,
      property_type: propertyType,
      sub_type: formData.sub_type || undefined,
      address: formData.property_certificate_address || undefined,
      total_area: typeof formData.area === 'number' ? formData.area : parseFloat(formData.area) || 0,
      floor: formData.floor ? parseInt(formData.floor) : undefined,
      total_floors: formData.total_floors ? parseInt(formData.total_floors) : undefined,
      orientation: formData.orientation || undefined,
      decoration_level: formData.decoration_level || undefined,
      description: formData.description || undefined,
      transaction_types: formData.transaction_types,
      tags: selectedTags
    };


    // 转换特性
    apiRequest.features = {
      has_elevator: formData.has_elevator,
      has_parking: formData.has_parking,
      parking_spaces: formData.parking_spaces ? parseInt(formData.parking_spaces) : undefined,
      has_air_conditioning: formData.has_air_conditioning,
      has_heating: formData.has_heating,
      has_fire_protection: formData.has_fire_protection,
      has_security: formData.has_security,
      has_water_supply: formData.has_water_supply,
      has_natural_gas: formData.has_natural_gas,
      has_internet: formData.has_internet,
      floor_height: formData.floor_height ? parseFloat(formData.floor_height) : undefined
    };

    // 根据房源类型添加特定特性
    if (propertyType === 'SHOP') {
      Object.assign(apiRequest.features, {
        frontage_width: formData.frontage_width ? parseFloat(formData.frontage_width) : undefined,
        depth: formData.depth ? parseFloat(formData.depth) : undefined,
        can_open_fire: formData.can_open_fire,
        has_chimney: formData.has_chimney,
        has_outdoor_area: formData.has_outdoor_area,
        suitable_business_types: formData.suitable_business_types || undefined,
        has_private_toilet: formData.has_private_toilet,
        has_water_drainage: formData.has_water_drainage
      });
    } else if (propertyType === 'OFFICE') {
      Object.assign(apiRequest.features, {
        space_efficiency: formData.space_efficiency ? parseFloat(formData.space_efficiency) : undefined,
        has_independent_ac: formData.has_independent_ac,
        has_reception_service: formData.has_reception_service,
        elevator_count: formData.elevator_count ? parseInt(formData.elevator_count) : undefined,
        has_meeting_room: formData.has_meeting_room
      });
    }

    // 转换价格信息
    const prices = [];
    for (const transactionType of formData.transaction_types) {
      const priceInfo: any = {
        transaction_type: transactionType
      };

      if (transactionType === 'RENT' && formData.rent_price) {
        priceInfo.rent_price = parseFloat(formData.rent_price);
        if (formData.rent_deposit_months) {
          priceInfo.rent_deposit_months = parseInt(formData.rent_deposit_months);
        }
        // 添加租金支付方式，默认季付
        priceInfo.rent_payment_method = formData.rent_payment_method || 'QUARTERLY';
      } else if (transactionType === 'SALE' && formData.sale_price) {
        priceInfo.sale_price = parseFloat(formData.sale_price);
      } else if (transactionType === 'TRANSFER' && formData.transfer_price) {
        priceInfo.transfer_price = parseFloat(formData.transfer_price);
      }

      if (formData.property_fee) {
        priceInfo.property_fee = parseFloat(formData.property_fee);
      }

      prices.push(priceInfo);
    }

    apiRequest.prices = prices;

    return apiRequest;
  }

  /**
   * API响应转换为MyProperties格式
   */
  private transformAPIResponseToMyProperties(apiResponse: PaginatedResponse<PropertyAPIResponse>): MyProperty[] {
    return apiResponse.items.map((item): MyProperty => ({
      id: item.id.toString(),
      type: this.determinePropertyType(item.property_type),
      title: item.title,
      price: this.formatPrice(item),
      area: `${item.total_area || 0}m²`,
      location: item.address || '位置待完善',
      description: item.description || '暂无描述',
      imageUrl: `https://picsum.photos/200/150?random=${item.id}`,
      status: this.mapAPIStatusToUIStatus(item.status || 'INACTIVE'),
      exposureCount: 0, // 暂时固定，后续从API获取
      favoriteCount: 0,
      inquiryCount: 0,
      viewCount: 0,
      tenantMatches: 0,
      isPremiumOwner: false,
      priceRange: { min: 0, max: 0 },
      areaRange: { min: 0, max: 0 },
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at),
    }));
  }

  /**
   * UI房源类型转换为API类型
   */
  private transformUIPropertyTypeToAPI(uiType: UIPropertyType): APIPropertyType {
    const typeMap: Record<UIPropertyType, APIPropertyType> = {
      '商铺': 'SHOP',
      '写字楼': 'OFFICE',
      '厂房': 'FACTORY',
      '仓库': 'WAREHOUSE',
      '土地': 'LAND',
      '会所': 'CLUBHOUSE',
      '活动会议室': 'MEETING_ROOM'
    };

    return typeMap[uiType];
  }

  /**
   * API房源类型转换为UI类型
   */
  private transformAPIPropertyTypeToUI(apiType: APIPropertyType): UIPropertyType {
    const typeMap: Record<APIPropertyType, UIPropertyType> = {
      'SHOP': '商铺',
      'OFFICE': '写字楼',
      'FACTORY': '厂房',
      'WAREHOUSE': '仓库',
      'LAND': '土地',
      'CLUBHOUSE': '会所',
      'MEETING_ROOM': '活动会议室'
    };

    return typeMap[apiType];
  }

  /**
   * 格式化价格显示 - 修复以处理新的API响应格式
   */
  private formatPrice(item: any): string {
    // 优先显示租金
    if (item.rent_price && item.rent_price > 0) {
      if (item.property_type === 'SHOP') {
        return `${item.rent_price}元/月`;
      } else if (item.property_type === 'OFFICE') {
        return `${Math.round(item.rent_price / (item.total_area || 1) / 30)}元/㎡·天`;
      } else {
        return `${item.rent_price}元/月`;
      }
    }

    // 其次显示售价
    if (item.sale_price && item.sale_price > 0) {
      const priceInWan = Math.round(item.sale_price / 10000);
      return `${priceInWan}万元`;
    }

    // 最后显示转让费
    if (item.transfer_price && item.transfer_price > 0) {
      const priceInWan = Math.round(item.transfer_price / 10000);
      return `转让${priceInWan}万元`;
    }

    return '面议';
  }

  /**
   * 从房源数据生成标签
   */
  private generateTagsFromProperty(item: any): string[] {
    const tags: string[] = [];

    // 装修等级标签
    if (item.decoration_level) {
      const decorationMap: Record<string, string> = {
        'REFINED': '精装修',
        'SIMPLE': '简装修',
        'ROUGH': '毛坯',
        'LUXURY': '豪华装修'
      };
      const decorationTag = decorationMap[item.decoration_level];
      if (decorationTag) tags.push(decorationTag);
    }

    // 楼层标签
    if (item.floor) {
      if (item.floor === 1) {
        tags.push('一楼');
      } else if (item.floor <= 3) {
        tags.push('低楼层');
      } else if (item.floor >= 10) {
        tags.push('高楼层');
      }
    }

    // 面积标签
    if (item.total_area) {
      if (item.total_area <= 50) {
        tags.push('小面积');
      } else if (item.total_area >= 200) {
        tags.push('大面积');
      }
    }

    // 交易类型标签
    if (item.transaction_types && item.transaction_types.length > 0) {
      item.transaction_types.forEach((type: string) => {
        const typeMap: Record<string, string> = {
          'RENT': '可租',
          'SALE': '可售',
          'TRANSFER': '可转让'
        };
        const typeTag = typeMap[type];
        if (typeTag) tags.push(typeTag);
      });
    }

    return tags;
  }

  /**
   * 确定房源类型（API → MyProperties）
   */
  private determinePropertyType(_apiPropertyType: string): 'rental' | 'sale' {
    // 根据API房源类型确定是租赁还是销售
    // 这里可以根据实际业务逻辑调整
    return 'rental'; // 暂时固定为租赁，后续可以根据transaction_types字段判断
  }

  /**
   * 映射API状态到UI状态
   */
  private mapAPIStatusToUIStatus(apiStatus: string): 'active' | 'draft' | 'inactive' {
    const statusMap: Record<string, 'active' | 'draft' | 'inactive'> = {
      'ACTIVE': 'active',
      'PENDING': 'draft',
      'INACTIVE': 'inactive',
      'DRAFT': 'draft',
      'PUBLISHED': 'active',
      'REJECTED': 'inactive',
      'EXPIRED': 'inactive'
    };

    return statusMap[apiStatus] || 'inactive';
  }

  // ==================== 类型检查方法 ====================

  private isStorePropertyParams(input: any): input is StorePropertyParams {
    return input && typeof input === 'object' && 'propertyType' in input && 'page' in input;
  }

  private isUIPropertyType(input: any): input is UIPropertyType {
    return typeof input === 'string' && ['商铺', '写字楼', '厂房', '仓库', '土地', '会所', '活动会议室'].includes(input);
  }

  private isAPIPropertyType(input: any): input is APIPropertyType {
    return typeof input === 'string' && ['SHOP', 'OFFICE', 'FACTORY', 'WAREHOUSE', 'LAND', 'CLUBHOUSE', 'MEETING_ROOM'].includes(input);
  }

  private isAPIPropertyResponse(input: any): input is PaginatedResponse<PropertyAPIResponse> {
    return input && typeof input === 'object' && 'items' in input && Array.isArray(input.items);
  }

  private isValidUIPropertyType(type: any): type is UIPropertyType {
    return typeof type === 'string' && ['商铺', '写字楼', '厂房', '仓库', '土地', '会所', '活动会议室'].includes(type);
  }

  private isUserPropertyParams(input: any): input is UserPropertyParams {
    return input &&
           typeof input === 'object' &&
           'status' in input &&
           'page' in input &&
           'size' in input &&
           typeof input.page === 'number' &&
           typeof input.size === 'number' &&
           typeof input.status === 'string' &&
           ['active', 'draft', 'inactive'].includes(input.status) &&
           // 确保不是StorePropertyParams（通过检查propertyType字段）
           !('propertyType' in input);
  }

  private isValidUserPropertyStatus(status: any): status is 'active' | 'draft' | 'inactive' {
    return typeof status === 'string' && ['active', 'draft', 'inactive'].includes(status);
  }

  private isPropertyPublishFormData(input: any): input is PropertyPublishFormData {
    return input &&
           typeof input === 'object' &&
           'title' in input &&
           'property_certificate_address' in input &&
           'sub_type' in input &&
           'area' in input &&
           'transaction_types' in input &&
           Array.isArray(input.transaction_types) &&
           typeof input.title === 'string' &&
           typeof input.property_certificate_address === 'string' &&
           typeof input.sub_type === 'string' &&
           (typeof input.area === 'string' || typeof input.area === 'number');
  }
}

export default PropertyTransformer;
