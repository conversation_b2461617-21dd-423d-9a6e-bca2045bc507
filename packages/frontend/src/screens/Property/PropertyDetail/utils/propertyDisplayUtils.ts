/**
 * 房源详情页显示工具函数
 * 
 * 处理房源详情页的UI显示逻辑和工具函数
 */

import { Dimensions } from 'react-native';
import type { PropertyDetailData } from '@property/types';
import type { ScreenSize } from '../types/propertyDetail.types';

/**
 * 获取屏幕尺寸分类
 */
export const getScreenSize = (): ScreenSize => {
  const { width } = Dimensions.get('window');
  
  if (width < 375) {
    return 'small' as ScreenSize;
  } else if (width < 768) {
    return 'medium' as ScreenSize;
  } else {
    return 'large' as ScreenSize;
  }
};

/**
 * 计算响应式尺寸
 * 
 * @param baseSize 基础尺寸
 * @param screenSize 屏幕尺寸
 * @returns 响应式尺寸
 */
export const getResponsiveSize = (
  baseSize: number,
  screenSize: ScreenSize = getScreenSize()
): number => {
  const multipliers = {
    small: 0.85,
    medium: 1.0,
    large: 1.15,
  };
  
  return baseSize * multipliers[screenSize];
};

/**
 * 格式化距离显示
 * 
 * @param distance 距离（米）
 * @returns 格式化的距离字符串
 */
export const formatDistance = (distance: number): string => {
  if (distance < 1000) {
    return `${Math.round(distance)}米`;
  } else {
    return `${(distance / 1000).toFixed(1)}公里`;
  }
};

/**
 * 格式化时间显示
 * 
 * @param timestamp 时间戳或ISO字符串
 * @returns 格式化的时间字符串
 */
export const formatTimeAgo = (timestamp: string | number | Date): string => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffMs = now.getTime() - time.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffHours < 1) {
    return '刚刚';
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return time.toLocaleDateString('zh-CN');
  }
};

/**
 * 生成房源卡片颜色
 * 
 * @param propertyType 房源类型
 * @returns 颜色配置
 */
export const getPropertyCardColors = (propertyType: string) => {
  const colorMap: Record<string, { primary: string; secondary: string; background: string }> = {
    'OFFICE': {
      primary: '#007AFF',
      secondary: '#E3F2FD',
      background: '#F8FBFF',
    },
    'SHOP': {
      primary: '#FF9500',
      secondary: '#FFF3E0',
      background: '#FFFBF5',
    },
    'FACTORY': {
      primary: '#34C759',
      secondary: '#E8F5E8',
      background: '#F8FDF8',
    },
    'CLUBHOUSE': {
      primary: '#AF52DE',
      secondary: '#F3E5F5',
      background: '#FDFAFF',
    },
    'MEETING_ROOM': {
      primary: '#FF3B30',
      secondary: '#FFEBEE',
      background: '#FFFBFA',
    },
    'LAND': {
      primary: '#8E8E93',
      secondary: '#F2F2F7',
      background: '#FAFAFA',
    },
    'TERRACE': {
      primary: '#00C7BE',
      secondary: '#E0F7FA',
      background: '#F7FDFC',
    },
  };
  
  return colorMap[propertyType] || colorMap['OFFICE'];
};

/**
 * 计算标签显示数量
 * 
 * @param tags 标签数组
 * @param maxTags 最大显示数量
 * @returns 显示的标签数组和剩余数量
 */
export const calculateTagsDisplay = (
  tags: string[],
  maxTags: number = 3
): { displayTags: string[]; remainingCount: number } => {
  if (tags.length <= maxTags) {
    return { displayTags: tags, remainingCount: 0 };
  }
  
  return {
    displayTags: tags.slice(0, maxTags),
    remainingCount: tags.length - maxTags,
  };
};

/**
 * 生成图片占位符URL
 * 
 * @param width 宽度
 * @param height 高度
 * @param text 占位文字
 * @returns 占位符URL
 */
export const generatePlaceholderUrl = (
  width: number = 400,
  height: number = 300,
  text: string = '房源图片'
): string => {
  return `https://via.placeholder.com/${width}x${height}/f0f0f0/666?text=${encodeURIComponent(text)}`;
};

/**
 * 计算媒体轮播高度
 * 
 * @param screenWidth 屏幕宽度
 * @param aspectRatio 宽高比
 * @returns 轮播高度
 */
export const calculateCarouselHeight = (
  screenWidth: number,
  aspectRatio: number = 4/3
): number => {
  return screenWidth / aspectRatio;
};

/**
 * 获取设备平台特定样式
 * 
 * @param iosStyle iOS样式
 * @param androidStyle Android样式
 * @returns 平台特定样式
 */
export const getPlatformStyle = <T>(iosStyle: T, androidStyle: T): T => {
  const { Platform } = require('react-native');
  return Platform.OS === 'ios' ? iosStyle : androidStyle;
};

/**
 * 计算安全区域内边距
 * 
 * @param insets 安全区域
 * @param defaultPadding 默认内边距
 * @returns 安全内边距
 */
export const getSafeAreaPadding = (
  insets: { top: number; bottom: number; left: number; right: number },
  defaultPadding: number = 16
): { paddingTop: number; paddingBottom: number; paddingLeft: number; paddingRight: number } => {
  return {
    paddingTop: Math.max(insets.top, defaultPadding),
    paddingBottom: Math.max(insets.bottom, defaultPadding),
    paddingLeft: Math.max(insets.left, defaultPadding),
    paddingRight: Math.max(insets.right, defaultPadding),
  };
};

/**
 * 防抖函数
 * 
 * @param func 要防抖的函数
 * @param delay 延迟时间
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * 节流函数
 * 
 * @param func 要节流的函数
 * @param delay 延迟时间
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
};

/**
 * 检查是否为有效URL
 * 
 * @param url URL字符串
 * @returns 是否有效
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * 获取图片优化URL
 * 
 * @param originalUrl 原始URL
 * @param width 目标宽度
 * @param height 目标高度
 * @param quality 质量(0-1)
 * @returns 优化后的URL
 */
export const getOptimizedImageUrl = (
  originalUrl: string,
  width?: number,
  height?: number,
  quality: number = 0.8
): string => {
  if (!isValidUrl(originalUrl)) {
    return originalUrl;
  }
  
  // 这里可以集成实际的图片优化服务，如阿里云OSS
  const params = new URLSearchParams();
  if (width) params.append('w', width.toString());
  if (height) params.append('h', height.toString());
  params.append('q', Math.round(quality * 100).toString());
  
  return `${originalUrl}?${params.toString()}`;
};

/**
 * 显示工具函数集合
 */
export const PropertyDisplayUtils = {
  getScreenSize,
  getResponsiveSize,
  formatDistance,
  formatTimeAgo,
  getPropertyCardColors,
  calculateTagsDisplay,
  generatePlaceholderUrl,
  calculateCarouselHeight,
  getPlatformStyle,
  getSafeAreaPadding,
  debounce,
  throttle,
  isValidUrl,
  getOptimizedImageUrl,
} as const;