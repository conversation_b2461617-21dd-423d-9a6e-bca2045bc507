/**
 * MapContainer 高德原生定位功能使用示例
 * 
 * 展示如何在实际页面中集成高德原生定位功能
 */

import React, { useState, useCallback } from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';
import { MapContainer, LocationService, useMapRef } from './MapContainer';

// 示例房源数据
const sampleProperties = [
  {
    id: '1',
    latitude: 22.8167,
    longitude: 108.3669,
    title: '万象城公寓',
    price: 3500,
    area: 85,
    propertyType: 'apartment',
  },
  {
    id: '2',
    latitude: 22.8200,
    longitude: 108.3700,
    title: '青秀山别墅',
    price: 8800,
    area: 180,
    propertyType: 'villa',
  },
];

interface MapCenter {
  latitude: number;
  longitude: number;
}

export const MapScreenWithNativeLocation: React.FC = () => {
  // 地图状态管理
  const [mapCenter, setMapCenter] = useState<MapCenter>({
    latitude: 22.8167,
    longitude: 108.3669,
  });
  const [userLocation, setUserLocation] = useState<MapCenter | null>(null);
  const [isLocationEnabled, setIsLocationEnabled] = useState(true);
  const [locationAccuracy, setLocationAccuracy] = useState<number | null>(null);

  // 使用MapRef Hook获取地图操作方法
  const { mapRef, animateToRegion, moveToLocation } = useMapRef();

  /**
   * 处理高德原生定位更新
   */
  const handleNativeLocationUpdate = useCallback((location: { 
    latitude: number; 
    longitude: number; 
    accuracy: number 
  }) => {
    console.log('🎯 [MapScreen] 收到高德原生定位更新:', location);
    
    setUserLocation({
      latitude: location.latitude,
      longitude: location.longitude,
    });
    setLocationAccuracy(location.accuracy);
    
    // 显示定位精度提示
    if (location.accuracy > 100) {
      Alert.alert('定位精度提醒', `当前定位精度为±${location.accuracy.toFixed(0)}米，建议到空旷地区获取更精确定位`);
    }
  }, []);

  /**
   * 手动触发定位
   */
  const handleManualLocation = useCallback(async () => {
    try {
      console.log('📍 [MapScreen] 手动触发定位...');
      
      const locationResult = await LocationService.getCurrentLocation();
      
      if (locationResult.success && locationResult.coordinates) {
        const newLocation = locationResult.coordinates;
        
        // 更新地图中心点
        setMapCenter(newLocation);
        
        // 动画移动到用户位置
        moveToLocation(newLocation.latitude, newLocation.longitude);
        
        Alert.alert('定位成功', `当前位置：${locationResult.city?.name || '未知城市'}`);
      } else {
        Alert.alert('定位失败', locationResult.error || '无法获取当前位置');
      }
    } catch (error) {
      console.error('❌ [MapScreen] 手动定位失败:', error);
      Alert.alert('定位失败', '定位服务异常');
    }
  }, [moveToLocation]);

  /**
   * 切换高德原生定位开关
   */
  const toggleNativeLocation = useCallback(() => {
    setIsLocationEnabled(prev => !prev);
    console.log(`🛰️ [MapScreen] 高德原生定位${!isLocationEnabled ? '已开启' : '已关闭'}`);
  }, [isLocationEnabled]);

  /**
   * 处理房源标记点击
   */
  const handleMarkerPress = useCallback((marker: any) => {
    console.log('🏠 [MapScreen] 房源标记被点击:', marker.title);
    
    // 移动到房源位置
    moveToLocation(marker.latitude, marker.longitude);
    
    Alert.alert('房源信息', `${marker.title}\n价格：${marker.price}元/月\n面积：${marker.area}㎡`);
  }, [moveToLocation]);

  /**
   * 处理地图长按
   */
  const handleMapLongPress = useCallback((coordinate: MapCenter) => {
    console.log('👆 [MapScreen] 地图长按:', coordinate);
    
    Alert.alert(
      '搜索此区域',
      `是否在此位置(${coordinate.latitude.toFixed(4)}, ${coordinate.longitude.toFixed(4)})附近搜索房源？`,
      [
        { text: '取消', style: 'cancel' },
        { 
          text: '搜索', 
          onPress: () => {
            // 移动到长按位置并开始搜索
            moveToLocation(coordinate.latitude, coordinate.longitude);
            console.log('🔍 开始搜索房源...');
          }
        },
      ]
    );
  }, [moveToLocation]);

  return (
    <View style={styles.container}>
      {/* 地图容器 */}
      <MapContainer
        center={mapCenter}
        markers={sampleProperties}
        zoomLevel={15}
        // 高德原生定位配置
        enableNativeLocation={isLocationEnabled}
        showNativeLocationButton={true}  // Android显示原生定位按钮
        enableLongPressSearch={true}
        // 事件回调
        onNativeLocationUpdate={handleNativeLocationUpdate}
        onMarkerPress={handleMarkerPress}
        onLongPress={handleMapLongPress}
        onUserLocationChanged={(location) => {
          console.log('📍 [MapScreen] 用户位置变化:', location);
          setUserLocation(location);
        }}
      />

      {/* 控制面板 */}
      <View style={styles.controlPanel}>
        {/* 定位状态显示 */}
        <View style={styles.statusPanel}>
          <Text style={styles.statusText}>
            高德原生定位: {isLocationEnabled ? '已开启' : '已关闭'}
          </Text>
          {locationAccuracy && (
            <Text style={styles.accuracyText}>
              定位精度: ±{locationAccuracy.toFixed(0)}米
            </Text>
          )}
          {userLocation && (
            <Text style={styles.locationText}>
              当前位置: {userLocation.latitude.toFixed(6)}, {userLocation.longitude.toFixed(6)}
            </Text>
          )}
        </View>

        {/* 控制按钮 */}
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[styles.button, isLocationEnabled ? styles.buttonEnabled : styles.buttonDisabled]}
            onPress={toggleNativeLocation}
          >
            <Text style={styles.buttonText}>
              {isLocationEnabled ? '关闭' : '开启'}原生定位
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.button}
            onPress={handleManualLocation}
          >
            <Text style={styles.buttonText}>手动定位</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.button}
            onPress={() => {
              // 回到南宁市中心
              const nanningCenter = { latitude: 22.8167, longitude: 108.3669 };
              setMapCenter(nanningCenter);
              moveToLocation(nanningCenter.latitude, nanningCenter.longitude);
            }}
          >
            <Text style={styles.buttonText}>回到中心</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  controlPanel: {
    position: 'absolute',
    bottom: 50,
    left: 16,
    right: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  statusPanel: {
    marginBottom: 12,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  accuracyText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  locationText: {
    fontSize: 10,
    color: '#999',
    fontFamily: 'monospace',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    backgroundColor: '#FF4F19',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  buttonEnabled: {
    backgroundColor: '#FF4F19',
  },
  buttonDisabled: {
    backgroundColor: '#666',
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

// 使用说明文档
export const USAGE_DOCUMENTATION = `
# MapContainer 高德原生定位功能使用指南

## 🎯 核心功能

### 1. 高德原生定位
- **myLocationEnabled**: 开启高德SDK原生定位服务
- **myLocationButtonEnabled**: 显示原生定位按钮(仅Android)
- **distanceFilter**: 设置位置更新最小距离(iOS)
- **onLocation**: 监听原生定位更新事件

### 2. 定位精度控制  
- 自动获取定位精度信息
- 精度低于100米时提供用户提示
- 支持位置更新频率控制

### 3. 用户交互
- 长按地图触发区域搜索
- 点击房源标记查看详情
- 手动触发定位按钮
- 地图动画移动

## 📋 使用步骤

### 1. 基础配置
\`\`\`tsx
<MapContainer
  enableNativeLocation={true}      // 开启高德原生定位
  showNativeLocationButton={true}  // 显示原生定位按钮
  onNativeLocationUpdate={handleLocationUpdate}
/>
\`\`\`

### 2. 定位更新处理
\`\`\`tsx
const handleLocationUpdate = (location) => {
  console.log('定位更新:', location);
  console.log('精度:', location.accuracy);
};
\`\`\`

### 3. 地图操作
\`\`\`tsx
const { moveToLocation, animateToRegion } = useMapRef();

// 移动到指定位置
moveToLocation(latitude, longitude);

// 动画移动到区域
animateToRegion({
  latitude, longitude,
  latitudeDelta: 0.01,
  longitudeDelta: 0.01
});
\`\`\`

## ⚡ 性能优化

### 1. 定位频率控制
- iOS: 使用 distanceFilter 控制更新距离
- Android: 使用 myLocationButtonEnabled 控制按钮显示

### 2. 内存管理
- 组件卸载时自动清理定位监听
- 合理使用 useCallback 避免重复渲染

### 3. 电量优化
- 根据业务需求调整定位精度
- 不需要时及时关闭原生定位

## 🔧 故障排查

### 1. 定位不工作
- 检查是否开启 myLocationEnabled
- 确认设备位置权限已授权
- 查看控制台定位日志

### 2. 精度不准确
- 建议用户到空旷地区
- 检查 distanceFilter 设置
- 查看定位精度数值

### 3. 性能问题
- 避免频繁的地图动画
- 合理设置更新频率
- 监控内存使用情况
`;