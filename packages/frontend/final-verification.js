/**
 * 最终验证脚本
 * 验证所有修复是否正确实施
 */

const fs = require('fs');
const path = require('path');

console.log('🎯 [最终验证] 开始验证所有修复...\n');

// 验证1：定位频繁请求修复
function verifyLocationFix() {
  console.log('📍 [验证1] 定位频繁请求修复');

  const hookPath = path.join(
    __dirname,
    'src/domains/map/hooks/useMapScreenState.ts'
  );
  const content = fs.readFileSync(hookPath, 'utf8');

  const checks = {
    hasInitCheck: content.includes('isInitializedRef.current'),
    hasUseEffectControl: content.includes('}, [])'),
    hasLocationService: content.includes('LocationService.getCurrentLocation'),
    hasProperInitialization:
      content.includes('isInitializedRef.current = true') ||
      content.includes('已经初始化完成'),
  };

  const passed = Object.values(checks).every(Boolean);

  console.log(
    `  ✅ 初始化状态检查: ${checks.hasInitCheck ? '通过' : '❌失败'}`
  );
  console.log(
    `  ✅ useEffect控制: ${checks.hasUseEffectControl ? '通过' : '❌失败'}`
  );
  console.log(
    `  ✅ LocationService调用: ${checks.hasLocationService ? '通过' : '❌失败'}`
  );
  console.log(
    `  ✅ 正确初始化逻辑: ${checks.hasProperInitialization ? '通过' : '❌失败'}`
  );
  console.log(`  🎯 总体结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);

  return passed;
}

// 验证2：筛选弹窗内容修复
function verifyFilterModalFix() {
  console.log('🔍 [验证2] 筛选弹窗内容修复');

  const rentModalPath = path.join(
    __dirname,
    'src/domains/map/components/FilterModal/RentFilterModal.tsx'
  );
  const saleModalPath = path.join(
    __dirname,
    'src/domains/map/components/FilterModal/SaleFilterModal.tsx'
  );

  const rentContent = fs.readFileSync(rentModalPath, 'utf8');
  const saleContent = fs.readFileSync(saleModalPath, 'utf8');

  const rentChecks = {
    hasUnlimitedOption: rentContent.includes("'不限'"),
    hasCorrectPriceDefault: rentContent.includes('[0, 999999]'),
    hasDefaultSelection: rentContent.includes("propertyTypes: ['不限']"),
  };

  const saleChecks = {
    hasUnlimitedOption: saleContent.includes("'不限'"),
    hasCorrectPriceDefault: saleContent.includes('[0, 99999999]'),
    hasDefaultSelection: saleContent.includes("propertyTypes: ['不限']"),
  };

  const rentPassed = Object.values(rentChecks).every(Boolean);
  const salePassed = Object.values(saleChecks).every(Boolean);

  console.log(`  租赁筛选弹窗:`);
  console.log(
    `    ✅ "不限"选项: ${rentChecks.hasUnlimitedOption ? '通过' : '❌失败'}`
  );
  console.log(
    `    ✅ 正确价格默认值: ${rentChecks.hasCorrectPriceDefault ? '通过' : '❌失败'}`
  );
  console.log(
    `    ✅ 默认选中设置: ${rentChecks.hasDefaultSelection ? '通过' : '❌失败'}`
  );

  console.log(`  买房筛选弹窗:`);
  console.log(
    `    ✅ "不限"选项: ${saleChecks.hasUnlimitedOption ? '通过' : '❌失败'}`
  );
  console.log(
    `    ✅ 正确价格默认值: ${saleChecks.hasCorrectPriceDefault ? '通过' : '❌失败'}`
  );
  console.log(
    `    ✅ 默认选中设置: ${saleChecks.hasDefaultSelection ? '通过' : '❌失败'}`
  );

  const passed = rentPassed && salePassed;
  console.log(`  🎯 总体结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);

  return passed;
}

// 验证3：房源类型选择器
function verifyPropertyTypeSelector() {
  console.log('🏷️ [验证3] 房源类型选择器');

  const selectorPath = path.join(
    __dirname,
    'src/domains/map/components/PropertyTypeSelector/PropertyTypeSelector.tsx'
  );
  const screenPath = path.join(
    __dirname,
    'src/domains/map/screens/MapSearchScreen.tsx'
  );
  const hookPath = path.join(
    __dirname,
    'src/domains/map/hooks/useMapScreenState.ts'
  );

  const selectorExists = fs.existsSync(selectorPath);
  const screenContent = fs.readFileSync(screenPath, 'utf8');
  const hookContent = fs.readFileSync(hookPath, 'utf8');

  const checks = {
    selectorExists,
    screenIntegration: screenContent.includes('PropertyTypeSelector'),
    hookSupport:
      hookContent.includes('selectedPropertyType') &&
      hookContent.includes('handlePropertyTypeChange'),
    defaultShop: hookContent.includes("'shop'"),
  };

  const passed = Object.values(checks).every(Boolean);

  console.log(
    `  ✅ 选择器组件存在: ${checks.selectorExists ? '通过' : '❌失败'}`
  );
  console.log(`  ✅ 屏幕集成: ${checks.screenIntegration ? '通过' : '❌失败'}`);
  console.log(`  ✅ Hook支持: ${checks.hookSupport ? '通过' : '❌失败'}`);
  console.log(`  ✅ 默认商铺: ${checks.defaultShop ? '通过' : '❌失败'}`);
  console.log(`  🎯 总体结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);

  return passed;
}

// 验证4：TypeScript类型定义
function verifyTypeDefinitions() {
  console.log('📝 [验证4] TypeScript类型定义');

  const hookPath = path.join(
    __dirname,
    'src/domains/map/hooks/useMapScreenState.ts'
  );
  const content = fs.readFileSync(hookPath, 'utf8');

  const checks = {
    hasReturnType: content.includes('UseMapScreenStateReturn'),
    hasPropertyTypeInReturn: content.includes('selectedPropertyType: string'),
    hasHandlerInReturn: content.includes(
      'handlePropertyTypeChange: (type: string) => void'
    ),
  };

  const passed = Object.values(checks).every(Boolean);

  console.log(`  ✅ 返回类型定义: ${checks.hasReturnType ? '通过' : '❌失败'}`);
  console.log(
    `  ✅ 房源类型属性: ${checks.hasPropertyTypeInReturn ? '通过' : '❌失败'}`
  );
  console.log(
    `  ✅ 处理函数类型: ${checks.hasHandlerInReturn ? '通过' : '❌失败'}`
  );
  console.log(`  🎯 总体结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);

  return passed;
}

// 执行所有验证
const results = {
  locationFix: verifyLocationFix(),
  filterModalFix: verifyFilterModalFix(),
  propertyTypeSelector: verifyPropertyTypeSelector(),
  typeDefinitions: verifyTypeDefinitions(),
};

const allPassed = Object.values(results).every(Boolean);

console.log('📊 [最终结果] 验证汇总:');
console.log(
  `  定位频繁请求修复: ${results.locationFix ? '✅ 通过' : '❌ 失败'}`
);
console.log(
  `  筛选弹窗内容修复: ${results.filterModalFix ? '✅ 通过' : '❌ 失败'}`
);
console.log(
  `  房源类型选择器: ${results.propertyTypeSelector ? '✅ 通过' : '❌ 失败'}`
);
console.log(
  `  TypeScript类型定义: ${results.typeDefinitions ? '✅ 通过' : '❌ 失败'}`
);

console.log(
  `\n🎯 [总体评估] ${allPassed ? '✅ 所有修复验证通过' : '❌ 部分修复需要进一步处理'}`
);

if (allPassed) {
  console.log('\n🚀 [下一步] 建议进行实际设备测试:');
  console.log('  1. 验证定位不再频繁请求');
  console.log('  2. 验证筛选弹窗内容正确显示');
  console.log('  3. 验证房源类型选择器功能');
  console.log('  4. 验证租买筛选交互逻辑');
} else {
  console.log('\n⚠️ [建议] 修复失败的验证项目后重新测试');
}

// 生成验证报告
const report = `# 地图筛选功能修复验证报告

## 验证时间
${new Date().toLocaleString()}

## 验证结果
- 定位频繁请求修复: ${results.locationFix ? '✅ 通过' : '❌ 失败'}
- 筛选弹窗内容修复: ${results.filterModalFix ? '✅ 通过' : '❌ 失败'}
- 房源类型选择器: ${results.propertyTypeSelector ? '✅ 通过' : '❌ 失败'}
- TypeScript类型定义: ${results.typeDefinitions ? '✅ 通过' : '❌ 失败'}

## 总体评估
${allPassed ? '✅ 所有修复验证通过，可以进行实际设备测试' : '❌ 部分修复需要进一步处理'}

## 修复要点
1. 按照高德地图官方文档实现定位控制
2. 为筛选选项添加"不限"选项并设置合理默认值
3. 实现房源类型选择器的完整功能
4. 确保TypeScript类型定义的完整性

## 下一步测试项目
- [ ] 验证定位不再频繁请求
- [ ] 验证筛选弹窗内容正确显示
- [ ] 验证房源类型选择器功能
- [ ] 验证租买筛选交互逻辑
`;

fs.writeFileSync(path.join(__dirname, 'verification-report.md'), report);
console.log('\n📄 验证报告已生成: verification-report.md');
