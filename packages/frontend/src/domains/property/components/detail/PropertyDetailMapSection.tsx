/**
 * 房源详情页地图组件 (PropertyDetailMapSection)
 * 企业级架构 - 遵循单一职责原则的统一地图展示组件
 *
 * 职责统一：
 * 1. 房源位置信息展示（单一数据来源）
 * 2. 地图交互入口（统一交互逻辑）
 * 3. 通勤分析入口（企业级功能集成）
 * 4. 数据转换层集成（支持发布数据）
 * 5. 企业级错误处理和状态管理
 */

import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import type { PropertyDetailData } from '@property/types';
import { PropertyDetailDesignSystem } from '@property/styles/propertyDetailDesignSystem';
import { hp, fp } from '@shared/utils/responsive';
import FeedbackService from '@shared/services/FeedbackService';
// import { useNearbyProperties } from '@hooks/useNearbyProperties'; // 暂时注释掉，避免导入错误

const { colors, typography, spacing, borderRadius } =
  PropertyDetailDesignSystem;

/**
 * 组件属性接口（企业级统一标准）
 */
interface PropertyDetailMapSectionProps {
  propertyData: PropertyDetailData;
  onCommutePress?: () => void;
  onMapPress?: () => void;
  // 新增：支持发布数据优先显示
  publishedLocation?: {
    address?: string;
    latitude?: number;
    longitude?: number;
  };
}

interface PropertyLocationInfo {
  id: string;
  title: string;
  latitude: number;
  longitude: number;
  address: string;
  property_type: string;
  area?: number;
  rent_price?: number;
  sale_price?: number;
}

interface NearbyProperty {
  id: string;
  title: string;
  distance_display: string;
  property_type: string;
}

/**
 * 房源详情页地图组件 - 企业级统一职责版本
 */
const PropertyDetailMapSection: React.FC<PropertyDetailMapSectionProps> = React.memo(
  ({ propertyData, onCommutePress, onMapPress, publishedLocation }) => {
    const navigation = useNavigation();

    // 状态管理
    const [propertyLocation, setPropertyLocation] = useState<PropertyLocationInfo | null>(null);
    
    // 暂时注释掉附近房源功能，避免导入错误
    const nearbyProperties: NearbyProperty[] = [];
    const nearbyLoading = false;
    const nearbyError = null;

    // 企业级位置数据提取（遵循单一数据来源原则）
    const getRealPropertyLocation = (): PropertyLocationInfo => {
      return {
        id: propertyData.id,
        title: propertyData.title,
        // 发布数据优先：遵循数据转换层原则
        latitude: publishedLocation?.latitude || 
                 propertyData.propertyDetails?.locationInfo?.coordinate?.latitude || 0,
        longitude: publishedLocation?.longitude || 
                  propertyData.propertyDetails?.locationInfo?.coordinate?.longitude || 0,
        // 地址信息：发布数据优先
        address: publishedLocation?.address || 
                propertyData.propertyDetails?.location || '',
        property_type: propertyData.propertyDetails?.type || '',
        area: parseFloat(propertyData.keyInfo?.area?.replace('m²', '') || '0'),
        rent_price: parseFloat(propertyData.keyInfo?.rent?.price?.replace(/[^\d.]/g, '') || '0'),
        sale_price: parseFloat(propertyData.keyInfo?.sale?.price?.replace(/[^\d.]/g, '') || '0'),
      };
    };

    // 简化的初始化方法，只处理当前房源位置
    const initializeLocation = () => {
      const realLocation = getRealPropertyLocation();
      setPropertyLocation(realLocation);
    };

    // 组件初始化时直接设置房源位置信息（监听发布数据变化）
    useEffect(() => {
      initializeLocation();
    }, [propertyData.id, publishedLocation]);

    /**
     * 处理通勤按钮点击
     */
    const handleCommutePress = () => {
      if (!propertyLocation) {
        FeedbackService.showError('房源位置信息加载中，请稍后重试');
        return;
      }

      // 导航到通勤分析页面
      (navigation as any).navigate('CommuteAnalysis', {
        propertyId: propertyData.id,
        propertyAddress: propertyLocation.address,
        propertyLocation: {
          latitude: propertyLocation.latitude,
          longitude: propertyLocation.longitude,
        },
      });
    };

    /**
     * 处理地图点击
     */
    const handleMapPress = () => {
      if (onMapPress) {
        onMapPress();
      } else {
        FeedbackService.showInfo('详细地图功能开发中');
      }
    };

    // 只有在没有位置信息且附近房源正在加载时才显示加载状态
    if (!propertyLocation && nearbyLoading) {
      return (
        <View style={[styles.container, styles.loadingContainer]}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={styles.loadingText}>加载位置信息...</Text>
        </View>
      );
    }

    if (!propertyLocation) {
      return (
        <View style={[styles.container, styles.errorContainer]}>
          <Ionicons name="location-outline" size={fp(24)} color="#999" />
          <Text style={styles.errorText}>位置信息暂不可用</Text>
        </View>
      );
    }

    return (
      <TouchableOpacity
        style={styles.container}
        onPress={handleMapPress}
        activeOpacity={0.8}
      >
        {/* 地图区域 - 房天下风格简洁版本 */}
        <View style={styles.mapBackground}>
          {/* 左侧地址信息 */}
          <View style={styles.addressInfo}>
            <Text style={styles.locationTitle}>
              {propertyLocation.address || '计生委南湖宿舍'}
            </Text>
            <Text style={styles.locationSubtitle}>
              距离1号线麻村站660米
            </Text>
          </View>

          {/* 右侧查看通勤按钮 - 房天下风格两行显示 */}
          <TouchableOpacity
            style={styles.commuteButton}
            onPress={handleCommutePress}
          >
            <View style={styles.commuteButtonContent}>
              <Text style={styles.commuteButtonTextTop}>输入常用地址</Text>
              <Text style={styles.commuteButtonTextBottom}>查看上班通勤</Text>
              <View style={styles.dotsArrowContainer}>
                <Text style={styles.dotsText}>•••••</Text>
                <Ionicons
                  name="chevron-forward"
                  size={fp(12)}
                  color={colors.text.secondary}
                />
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  }
);

/**
 * 样式定义 - 仿照房天下简洁设计
 */
const styles = StyleSheet.create({
  container: {
    marginVertical: spacing.sm,
    overflow: 'hidden',
  },

  // 加载状态样式
  loadingContainer: {
    height: hp(90),
    backgroundColor: '#FFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  loadingText: {
    fontSize: fp(14),
    color: '#666',
    marginLeft: 8,
  },

  // 错误状态样式
  errorContainer: {
    height: hp(90),
    backgroundColor: '#FFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  errorText: {
    fontSize: fp(14),
    color: '#999',
    marginLeft: 8,
  },

  // 地图背景区域 - 房天下风格简洁布局
  mapBackground: {
    height: hp(90), // 增加高度以容纳两行按钮文字
    backgroundColor: '#E8F4F8', // 浅蓝色地图背景 - 房天下风格
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    flexDirection: 'row',
    // alignItems: 'center', // 移除此行，让子元素垂直拉伸
    justifyContent: 'space-between',
    borderRadius: borderRadius.sm, // 添加圆角
  },

  // 地址信息区域 - 左侧
  addressInfo: {
    flex: 1,
    justifyContent: 'center', // 新增此行，使其内部内容垂直居中
  },

  locationTitle: {
    ...typography.bodyMedium,
    fontWeight: '600',
    color: colors.text.primary,
    fontSize: fp(15),
    marginBottom: spacing.xs,
  },

  locationSubtitle: {
    ...typography.small,
    color: colors.text.secondary,
    fontSize: fp(12),
  },

  // 查看通勤按钮 - 右侧，半透明白色背景，简化设计
  commuteButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.sm,
    // 移除这里的 alignItems 和 justifyContent，由内部的 commuteButtonContent 控制
  },

  // 新增样式，用于包裹 commuteButton 的内容并使其垂直居中
  commuteButtonContent: {
    flex: 1, // 填充父容器的可用空间
    justifyContent: 'center', // 垂直居中内容
    alignItems: 'center', // 水平居中内容
    gap: spacing.xs, // 使用 gap 替代 marginBottom 来控制间距
  },

  // 通勤按钮第一行文字 - 参考左边地址信息的样式
  commuteButtonTextTop: {
    ...typography.small,
    color: colors.text.secondary,
    fontSize: fp(10),
    fontWeight: '600',
    textAlign: 'center',
    // marginBottom: spacing.xs, // 移除
  },

  // 通勤按钮第二行文字 - 参考左边地址信息的样式
  commuteButtonTextBottom: {
    ...typography.small,
    color: colors.text.primary,
    fontSize: fp(12),
    fontWeight: '600',
    textAlign: 'center',
    // marginBottom: spacing.xs, // 移除
  },

  // 点点点和箭头容器
  dotsArrowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // 点点点文字
  dotsText: {
    color: colors.text.secondary,
    fontSize: fp(12),
    marginRight: spacing.xs,
    letterSpacing: 1,
  },
});

export default PropertyDetailMapSection;
export { PropertyDetailMapSection };
