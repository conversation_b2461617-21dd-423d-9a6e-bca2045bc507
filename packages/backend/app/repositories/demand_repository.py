"""
需求Repository层实现
负责需求相关的数据访问操作
"""

import uuid
from typing import List, Optional, Dict, Any
from sqlmodel.ext.asyncio.session import AsyncSession
from sqlmodel import select, and_, or_, desc, asc, func, delete
from app.db.repositories.base import BaseRepository
from app.models.user_demand import UserDemand, DemandPropertyMatch, DemandResponse, DemandTag


class DemandRepository(BaseRepository[UserDemand]):
    """需求数据访问层"""
    
    def __init__(self, session: AsyncSession):
        super().__init__(UserDemand, session)
    
    async def get_by_user_id(self, user_id: uuid.UUID, skip: int = 0, limit: int = 20) -> List[UserDemand]:
        """根据用户ID获取需求列表 - 按修改时间排序"""
        statement = (
            select(UserDemand)
            .where(UserDemand.user_id == user_id)
            .order_by(desc(UserDemand.updated_at))  # 🔥 按修改时间排序
            .offset(skip)
            .limit(limit)
        )
        result = await self.session.exec(statement)
        return result.all()
    
    async def get_active_demands(self, skip: int = 0, limit: int = 20) -> List[UserDemand]:
        """获取活跃的需求"""
        statement = (
            select(UserDemand)
            .where(UserDemand.status == 'ACTIVE')
            .order_by(desc(UserDemand.created_at))
            .offset(skip)
            .limit(limit)
        )
        result = await self.session.exec(statement)
        return result.all()
    
    async def search_demands(
        self, 
        keyword: Optional[str] = None,
        demand_type: Optional[str] = None,
        property_type: Optional[str] = None,
        skip: int = 0, 
        limit: int = 20
    ) -> List[UserDemand]:
        """搜索需求"""
        conditions = []
        
        if keyword:
            conditions.append(
                or_(
                    UserDemand.title.contains(keyword),
                    UserDemand.description.contains(keyword)
                )
            )
        
        if demand_type:
            conditions.append(UserDemand.demand_type == demand_type)
            
        if property_type:
            conditions.append(UserDemand.property_type == property_type)
        
        statement = select(UserDemand)
        if conditions:
            statement = statement.where(and_(*conditions))
        
        statement = statement.order_by(desc(UserDemand.created_at)).offset(skip).limit(limit)
        
        result = await self.session.exec(statement)
        return result.all()
    
    async def count_by_user(self, user_id: uuid.UUID) -> int:
        """统计用户需求数量"""
        statement = select(func.count(UserDemand.id)).where(UserDemand.user_id == user_id)
        result = await self.session.exec(statement)
        return result.first() or 0

    async def get_by_id_and_user(self, demand_id: uuid.UUID, user_id: uuid.UUID) -> Optional[UserDemand]:
        """
        根据ID和用户ID获取需求

        Args:
            demand_id: 需求ID
            user_id: 用户ID

        Returns:
            需求实例或None
        """
        statement = select(UserDemand).where(
            and_(
                UserDemand.id == demand_id,
                UserDemand.user_id == user_id,
                UserDemand.is_deleted == False  # 企业级软删除检查
            )
        )
        result = await self.session.exec(statement)
        return result.first()