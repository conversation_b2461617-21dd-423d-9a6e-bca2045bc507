/**
 * DemandForm 提交逻辑Hook
 * @fileoverview 封装需求发布表单的提交逻辑
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🔒 功能保证：100%保留原有DemandFormScreen.tsx中的提交逻辑
 */

import { useCallback, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { Alert } from 'react-native';
import { useQueryClient } from '@tanstack/react-query';

// 导入服务和工具
import { DemandAPI } from '../../../services/demandAPI';
import { Transformers } from '../../../../../shared/services/dataTransform';
import FeedbackService from '../../../../../shared/services/FeedbackService';
import { safeNavigate } from '../../../../../shared/types/navigation.types';

// 导入Hook和Store
import {
  useDemandFormData,
  useDemandFormUI,
  useDemandFormEditMode,
  useDemandFormTags,
  useDemandFormActions,
} from '../stores/DemandFormStore';
import { useDemandValidation } from './useDemandValidation';
import { useAuth } from '../../../../../contexts/AuthContext';

/**
 * 提交逻辑Hook - 完整保留原有DemandFormScreen.tsx的提交逻辑
 */
export const useDemandSubmit = () => {
  // === 导航 ===
  const navigation = useNavigation();

  // === 认证状态 ===
  const { state } = useAuth();
  const isAuthenticated = !!state.userToken;

  // === React Query ===
  const queryClient = useQueryClient();

  // === Store状态 ===
  const formData = useDemandFormData();
  const ui = useDemandFormUI();
  const editMode = useDemandFormEditMode();
  const tags = useDemandFormTags();
  const actions = useDemandFormActions();

  // === 验证Hook ===
  const validation = useDemandValidation();

  // === 本地状态 ===
  const [isSubmitting, setIsSubmitting] = useState(false);

  // === 🔒 保留原有的handleSmartFormSubmit逻辑 ===
  const handleSmartFormSubmit = useCallback(
    async (isDraft: boolean = false) => {
      console.log('🚀 [提交] 开始智能表单提交:', {
        isDraft,
        editMode: editMode.isEditMode,
      });

      if (isSubmitting) {
        console.log('⚠️ [提交] 正在提交中，忽略重复提交');
        return;
      }

      try {
        setIsSubmitting(true);
        actions.setSubmitting(true);
        actions.setError(null);

        // === 1. 用户认证检查 ===
        if (!isAuthenticated) {
          console.log('❌ [提交] 用户未登录');
          Alert.alert('请先登录', '发布需求需要先登录账号', [
            { text: '取消', style: 'cancel' },
            {
              text: '去登录',
              onPress: () => safeNavigate(navigation, 'Login'),
            },
          ]);
          return;
        }

        // === 2. 表单验证（非草稿模式） ===
        if (!isDraft) {
          console.log('🔍 [提交] 开始表单验证');

          // 构建完整的验证数据
          const validationData = {
            ...formData,
            selectedFeatureTags: tags.selectedFeatureTags,
          };

          const validationResult =
            await validation.validateFormWithSmartScroll(validationData);

          if (!validationResult.isValid) {
            console.log('❌ [提交] 表单验证失败:', validationResult.errors);

            // 显示第一个错误信息
            const firstError = Object.values(validationResult.errors)[0];
            if (firstError) {
              FeedbackService.showError(firstError);
            }

            return;
          }

          console.log('✅ [提交] 表单验证通过');
        }

        // === 3. 数据转换 ===
        console.log('🔄 [提交] 开始数据转换');

        const transformContext = {
          context: editMode.isEditMode ? 'update' : 'create',
          isDraft,
          selectedFeatureTags: tags.selectedFeatureTags,
        };

        const transformResult = Transformers.demand.toAPI(
          formData,
          transformContext
        );

        if (!transformResult.success) {
          console.error('❌ [提交] 数据转换失败:', transformResult.error);
          throw new Error(transformResult.error || '数据转换失败');
        }

        console.log('✅ [提交] 数据转换成功:', transformResult.data);

        // === 4. API调用 ===
        console.log('📡 [提交] 开始API调用');

        let response;
        if (editMode.isEditMode && editMode.editDemandId) {
          // 更新需求
          console.log('📝 [提交] 更新需求:', editMode.editDemandId);
          response = await DemandAPI.updateDemand(
            editMode.editDemandId,
            transformResult.data
          );
        } else {
          // 创建需求
          console.log('📝 [提交] 创建需求');
          response = await DemandAPI.createDemand(transformResult.data);
        }

        if (response.success) {
          console.log('✅✅✅ [提交] API调用成功:', response.data);
          console.log('🔍🔍🔍 [提交] 准备检查缓存更新条件');

          // === 5. 成功处理 ===

          // 清除缓存
          actions.clearCache();

          // 🚀 立即更新React Query缓存 - 确保列表页面立即显示最新数据
          console.log('🔍 [提交] 检查编辑模式状态:', {
            isEditMode: editMode.isEditMode,
            editDemandId: editMode.editDemandId,
            isDraft: isDraft,
          });

          if (editMode.isEditMode && editMode.editDemandId) {
            console.log(
              '🔄 [提交] 立即更新React Query缓存，确保列表页面显示最新数据'
            );

            // 方法1：直接更新缓存数据（乐观更新）- 同时更新两套缓存系统

            // 更新UniversalListScene的缓存
            queryClient.setQueryData(
              ['demand', 'list', 'active', 'normal'],
              (oldData: any) => {
                console.log(
                  '📊 [提交] 更新UniversalListScene active缓存数据:',
                  oldData
                );
                if (oldData && Array.isArray(oldData)) {
                  const updatedData = oldData.map((demand: any) =>
                    demand.id === editMode.editDemandId
                      ? {
                          ...demand,
                          ...response.data,
                          updated_at: new Date().toISOString(),
                        }
                      : demand
                  );
                  console.log(
                    '📊 [提交] UniversalListScene active缓存数据已更新'
                  );
                  return updatedData;
                }
                return oldData;
              }
            );

            // 更新DemandListScene的缓存（MyDemandsScreen中的旧系统）
            queryClient.setQueryData(['demands', 'active'], (oldData: any) => {
              console.log(
                '📊 [提交] 更新DemandListScene active缓存数据:',
                oldData
              );
              if (oldData && Array.isArray(oldData)) {
                const updatedData = oldData.map((demand: any) =>
                  demand.id === editMode.editDemandId
                    ? {
                        ...demand,
                        ...response.data,
                        updated_at: new Date().toISOString(),
                      }
                    : demand
                );
                console.log('📊 [提交] DemandListScene active缓存数据已更新');
                return updatedData;
              }
              return oldData;
            });

            // 同时更新其他状态的缓存
            queryClient.setQueryData(
              ['demand', 'list', 'draft', 'normal'],
              (oldData: any) => {
                if (oldData && Array.isArray(oldData)) {
                  return oldData.filter(
                    (demand: any) => demand.id !== editMode.editDemandId
                  );
                }
                return oldData;
              }
            );

            queryClient.setQueryData(['demands', 'draft'], (oldData: any) => {
              if (oldData && Array.isArray(oldData)) {
                return oldData.filter(
                  (demand: any) => demand.id !== editMode.editDemandId
                );
              }
              return oldData;
            });

            // 方法2：强制刷新缓存（确保数据一致性）
            console.log('🔄 [提交] 开始强制刷新所有相关缓存');

            // 刷新UniversalListScene的所有缓存
            queryClient.invalidateQueries({ queryKey: ['demand', 'list'] });

            // 刷新旧系统的缓存（兼容性）
            queryClient.invalidateQueries({ queryKey: ['demands'] });

            // 刷新统计数据
            queryClient.invalidateQueries({
              queryKey: ['detailed-demand-stats'],
            });

            console.log('✅ [提交] 所有缓存刷新完成');

            console.log('✅ [提交] 缓存更新完成，列表页面将显示最新数据');
          } else {
            console.log(
              '⚠️ [提交] 不是编辑模式或缺少editDemandId，跳过缓存更新'
            );
          }

          // 显示成功消息
          const message = isDraft
            ? '草稿保存成功'
            : editMode.isEditMode
              ? '需求更新成功'
              : '需求发布成功';

          FeedbackService.showSuccess(message);

          // 设置草稿ID（如果是草稿）
          if (isDraft && response.data?.id) {
            actions.setDraftId(response.data.id);
            actions.setLastSavedTime(new Date());
          }

          // 导航处理
          if (!isDraft) {
            // 非草稿模式：跳转到需求详情页面
            if (response.data?.id) {
              console.log('🧭 [提交] 跳转到需求详情页面:', response.data.id);
              safeNavigate(navigation, 'DemandInfo', {
                demandId: response.data.id,
                fromEdit: editMode.isEditMode,
              });
            } else {
              // 如果没有返回ID，跳转到我的需求页面
              console.log('🧭 [提交] 跳转到我的需求页面');
              safeNavigate(navigation, 'MyDemands');
            }
          }
        } else {
          console.error('❌ [提交] API调用失败:', response.message);
          throw new Error(response.message || '提交失败');
        }
      } catch (error) {
        console.error('❌ [提交] 提交过程出错:', error);

        // 错误处理
        const errorMessage =
          error instanceof Error ? error.message : '提交失败，请重试';
        actions.setError(errorMessage);
        FeedbackService.showError(errorMessage);
      } finally {
        setIsSubmitting(false);
        actions.setSubmitting(false);

        // 如果是保存草稿，也要重置保存草稿状态
        if (isDraft) {
          actions.setSavingDraft(false);
        }

        console.log('🏁 [提交] 提交流程结束');
      }
    },
    [
      isSubmitting,
      isAuthenticated,
      formData,
      tags.selectedFeatureTags,
      editMode.isEditMode,
      editMode.editDemandId,
      actions,
      validation,
      navigation,
    ]
  );

  // === 保存草稿 - 保留原有逻辑 ===
  const handleSaveDraft = useCallback(async () => {
    console.log('💾 [草稿] 开始保存草稿');

    actions.setSavingDraft(true);

    try {
      await handleSmartFormSubmit(true);
      console.log('✅ [草稿] 草稿保存成功');
    } catch (error) {
      console.error('❌ [草稿] 草稿保存失败:', error);
    } finally {
      actions.setSavingDraft(false);
    }
  }, [handleSmartFormSubmit, actions]);

  // === 发布需求 - 保留原有逻辑 ===
  const handlePublish = useCallback(async () => {
    console.log('📢 [发布] 开始发布需求');

    try {
      await handleSmartFormSubmit(false);
      console.log('✅ [发布] 需求发布成功');
    } catch (error) {
      console.error('❌ [发布] 需求发布失败:', error);
    }
  }, [handleSmartFormSubmit]);

  // === 快速提交（兼容原有接口） ===
  const handleSubmit = useCallback(
    async (isDraft: boolean = false) => {
      return handleSmartFormSubmit(isDraft);
    },
    [handleSmartFormSubmit]
  );

  // === 检查是否可以提交 ===
  const canSubmit = useCallback(() => {
    return (
      isAuthenticated &&
      !isSubmitting &&
      !ui.isSubmitting &&
      formData.propertyType.length > 0 &&
      formData.location.districts.length > 0 &&
      formData.industryType.length > 0 &&
      formData.contactInfo.surname &&
      formData.contactInfo.phone
    );
  }, [
    isAuthenticated,
    isSubmitting,
    ui.isSubmitting,
    formData.propertyType,
    formData.location.districts,
    formData.industryType,
    formData.contactInfo.surname,
    formData.contactInfo.phone,
  ]);

  // === 检查是否可以保存草稿 ===
  const canSaveDraft = useCallback(() => {
    return (
      isAuthenticated &&
      !isSubmitting &&
      !ui.isSavingDraft &&
      (formData.propertyType.length > 0 ||
        formData.location.districts.length > 0 ||
        formData.contactInfo.surname ||
        formData.specialRequirements)
    );
  }, [
    isAuthenticated,
    isSubmitting,
    ui.isSavingDraft,
    formData.propertyType,
    formData.location.districts,
    formData.contactInfo.surname,
    formData.specialRequirements,
  ]);

  // === 返回接口 ===
  return {
    // === 提交状态 ===
    isSubmitting: isSubmitting || ui.isSubmitting,
    isSavingDraft: ui.isSavingDraft,

    // === 提交方法 ===
    handleSubmit,
    handleSaveDraft,
    handlePublish,
    handleSmartFormSubmit, // 保留原有方法名

    // === 状态检查方法 ===
    canSubmit: canSubmit(),
    canSaveDraft: canSaveDraft(),

    // === 错误信息 ===
    error: ui.error,
  };
};
