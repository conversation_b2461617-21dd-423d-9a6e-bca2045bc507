/**
 * 实时同步Hook - 纯服务器草稿机制
 * @fileoverview 实现编辑过程中的实时同步到服务器
 * <AUTHOR> Assistant
 * @since 2025-01-02
 */

import { useCallback, useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { DemandAPI } from '../../domains/demand/services/demandAPI';
import { PropertyAPI } from '../../domains/property/services/propertyAPI';

interface SyncOptions {
  entityType: 'demand' | 'property';
  entityId?: string;
  debounceMs?: number;
  onSyncSuccess?: (id: string) => void;
  onSyncError?: (error: Error) => void;
}

interface SyncData {
  [key: string]: any;
  status?: 'DRAFT' | 'ACTIVE' | 'OFFLINE';
}

/**
 * 实时同步Hook
 * 🚀 纯服务器草稿机制：编辑过程实时同步到服务器
 */
export const useRealTimeSync = (options: SyncOptions) => {
  const {
    entityType,
    entityId,
    debounceMs = 2000,
    onSyncSuccess,
    onSyncError,
  } = options;

  const queryClient = useQueryClient();
  const syncTimeoutRef = useRef<NodeJS.Timeout>();
  const lastSyncDataRef = useRef<string>('');
  const isSyncingRef = useRef(false);

  /**
   * 执行同步到服务器
   */
  const performSync = useCallback(
    async (data: SyncData) => {
      if (isSyncingRef.current) {
        console.log('[useRealTimeSync] 同步进行中，跳过');
        return;
      }

      try {
        isSyncingRef.current = true;
        console.log(`[useRealTimeSync] 开始同步 ${entityType}:`, entityId);

        // 🔥 设置为草稿状态
        const syncData = {
          ...data,
          status: 'DRAFT' as const,
        };

        let response;
        if (entityId) {
          // 更新现有草稿
          if (entityType === 'demand') {
            response = await DemandAPI.updateDemand(entityId, syncData as any);
          } else {
            response = await PropertyAPI.updateProperty(
              entityId,
              syncData as any
            );
          }
        } else {
          // 创建新草稿
          if (entityType === 'demand') {
            response = await DemandAPI.createDemand(syncData as any);
          } else {
            response = await PropertyAPI.createProperty(syncData as any);
          }
        }

        if (response.success) {
          const newId = response.data?.id || entityId;
          console.log(`[useRealTimeSync] ✅ 同步成功: ${newId}`);

          // 失效相关缓存
          queryClient.invalidateQueries({
            queryKey: [`${entityType}-status-counts`],
          });

          onSyncSuccess?.(newId);
          return newId;
        } else {
          throw new Error(response.message || '同步失败');
        }
      } catch (error) {
        console.error(`[useRealTimeSync] ❌ 同步失败:`, error);
        onSyncError?.(error as Error);
        throw error;
      } finally {
        isSyncingRef.current = false;
      }
    },
    [entityType, entityId, queryClient, onSyncSuccess, onSyncError]
  );

  /**
   * 防抖同步函数
   */
  const debouncedSync = useCallback(
    (data: SyncData) => {
      // 检查数据是否有变化
      const dataString = JSON.stringify(data);
      if (dataString === lastSyncDataRef.current) {
        console.log('[useRealTimeSync] 数据无变化，跳过同步');
        return;
      }
      lastSyncDataRef.current = dataString;

      // 清除之前的定时器
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }

      // 设置新的防抖定时器
      syncTimeoutRef.current = setTimeout(() => {
        performSync(data).catch(error => {
          console.warn('[useRealTimeSync] 防抖同步失败:', error);
        });
      }, debounceMs);

      console.log(`[useRealTimeSync] 已设置防抖同步，${debounceMs}ms后执行`);
    },
    [debounceMs, performSync]
  );

  /**
   * 立即同步（不防抖）
   */
  const immediateSync = useCallback(
    async (data: SyncData) => {
      // 清除防抖定时器
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }

      return await performSync(data);
    },
    [performSync]
  );

  /**
   * 智能同步决策
   */
  const smartSync = useCallback(
    (
      data: SyncData,
      options?: {
        force?: boolean;
        immediate?: boolean;
      }
    ) => {
      const { force = false, immediate = false } = options || {};

      // 计算数据完整度
      const completeness = calculateCompleteness(data);

      if (immediate || force || completeness > 30) {
        // 高价值数据或强制同步：立即同步
        return immediateSync(data);
      } else {
        // 低价值数据：防抖同步
        debouncedSync(data);
        return Promise.resolve();
      }
    },
    [immediateSync, debouncedSync]
  );

  /**
   * 计算数据完整度
   */
  const calculateCompleteness = (data: SyncData): number => {
    const requiredFields =
      entityType === 'demand'
        ? ['propertyType', 'location', 'areaRange']
        : ['title', 'propertyType', 'location', 'area'];

    const filledFields = requiredFields.filter(field => {
      const value = data[field];
      return value !== undefined && value !== null && value !== '';
    });

    return (filledFields.length / requiredFields.length) * 100;
  };

  /**
   * 清理定时器
   */
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
    };
  }, []);

  return {
    // 防抖同步（默认）
    sync: debouncedSync,
    // 立即同步
    syncNow: immediateSync,
    // 智能同步
    smartSync,
    // 同步状态
    isSyncing: isSyncingRef.current,
  };
};

/**
 * 需求实时同步Hook
 */
export const useDemandRealTimeSync = (demandId?: string) => {
  return useRealTimeSync({
    entityType: 'demand',
    entityId: demandId,
  });
};

/**
 * 房源实时同步Hook
 */
export const usePropertyRealTimeSync = (propertyId?: string) => {
  return useRealTimeSync({
    entityType: 'property',
    entityId: propertyId,
  });
};
