/**
 * 测试通用数据更新系统
 * @fileoverview 用于测试和验证通用数据更新系统功能
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  useUniversalDataManager,
  getDataUpdateSystemStatus,
} from '../shared/services/dataUpdate';

export const TestDataUpdateScreen: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const { updateStatus, publishEntity, unpublishEntity } =
    useUniversalDataManager();

  const addResult = (message: string) => {
    setTestResults(prev => [
      ...prev,
      `${new Date().toLocaleTimeString()}: ${message}`,
    ]);
  };

  const testSystemStatus = () => {
    try {
      const status = getDataUpdateSystemStatus();
      addResult(`系统状态: ${JSON.stringify(status, null, 2)}`);
    } catch (error) {
      addResult(`系统状态检查失败: ${error}`);
    }
  };

  const testDemandStatusUpdate = async () => {
    try {
      addResult('开始测试需求状态更新...');

      const result = await updateStatus({
        entityType: 'demand',
        entityId: '428b885c-958d-4fc4-ae36-327c0bbeaa4c',
        newStatus: 'OFFLINE',
        oldStatus: 'ACTIVE',
        source: 'TestScreen',
      });

      if (result.success) {
        addResult('✅ 需求状态更新成功');
      } else {
        addResult(`❌ 需求状态更新失败: ${result.error}`);
      }
    } catch (error) {
      addResult(`❌ 需求状态更新异常: ${error}`);
    }
  };

  const testDemandPublish = async () => {
    try {
      addResult('开始测试需求上架...');

      const result = await publishEntity(
        'demand',
        '428b885c-958d-4fc4-ae36-327c0bbeaa4c',
        'TestScreen'
      );

      if (result.success) {
        addResult('✅ 需求上架成功');
      } else {
        addResult(`❌ 需求上架失败: ${result.error}`);
      }
    } catch (error) {
      addResult(`❌ 需求上架异常: ${error}`);
    }
  };

  const testDemandUnpublish = async () => {
    try {
      addResult('开始测试需求下架...');

      const result = await unpublishEntity(
        'demand',
        '428b885c-958d-4fc4-ae36-327c0bbeaa4c',
        '测试下架',
        'TestScreen'
      );

      if (result.success) {
        addResult('✅ 需求下架成功');
      } else {
        addResult(`❌ 需求下架失败: ${result.error}`);
      }
    } catch (error) {
      addResult(`❌ 需求下架异常: ${error}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>通用数据更新系统测试</Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={testSystemStatus}>
          <Text style={styles.buttonText}>检查系统状态</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={testDemandStatusUpdate}
        >
          <Text style={styles.buttonText}>测试需求状态更新</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testDemandPublish}>
          <Text style={styles.buttonText}>测试需求上架</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testDemandUnpublish}>
          <Text style={styles.buttonText}>测试需求下架</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>清空结果</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>测试结果:</Text>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
    color: '#333333',
  },
  buttonContainer: {
    marginBottom: 24,
  },
  button: {
    backgroundColor: '#FF6B35',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    alignItems: 'center',
  },
  clearButton: {
    backgroundColor: '#666666',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resultsContainer: {
    backgroundColor: '#F5F5F5',
    padding: 16,
    borderRadius: 8,
    minHeight: 200,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333333',
  },
  resultText: {
    fontSize: 12,
    marginBottom: 8,
    color: '#666666',
    fontFamily: 'monospace',
  },
});
