#!/usr/bin/env node
/**
 * 地址搜索功能测试脚本
 * 测试后端POI搜索和输入提示API是否正常工作
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:8082/api/v1';

async function testGeocoding() {
  console.log('🧪 测试地理编码API...');
  
  try {
    const response = await fetch(`${BASE_URL}/map/geocode/address`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        address: '南宁市青秀区民族大道',
        city: '南宁'
      }),
    });
    
    const data = await response.json();
    console.log('✅ 地理编码测试结果:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log(`   📍 坐标: (${data.longitude}, ${data.latitude})`);
      console.log(`   📍 地址: ${data.formatted_address}`);
    }
  } catch (error) {
    console.error('❌ 地理编码测试失败:', error.message);
  }
}

async function testPOISearch() {
  console.log('\n🧪 测试POI搜索API...');
  
  try {
    const response = await fetch(`${BASE_URL}/map/geocode/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        keywords: '银行',
        city: '南宁',
        page: 1,
        offset: 5
      }),
    });
    
    const data = await response.json();
    console.log('✅ POI搜索测试结果:', JSON.stringify(data, null, 2));
    
    if (data.success && data.pois) {
      console.log(`   🏢 找到${data.count}个POI，显示前${data.pois.length}个:`);
      data.pois.forEach((poi, index) => {
        console.log(`   ${index + 1}. ${poi.name} - ${poi.address}`);
      });
    }
  } catch (error) {
    console.error('❌ POI搜索测试失败:', error.message);
  }
}

async function testInputTips() {
  console.log('\n🧪 测试输入提示API...');
  
  try {
    const response = await fetch(`${BASE_URL}/map/geocode/tips`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        keywords: '万达',
        city: '南宁',
        datatype: 'all'
      }),
    });
    
    const data = await response.json();
    console.log('✅ 输入提示测试结果:', JSON.stringify(data, null, 2));
    
    if (data.success && data.tips) {
      console.log(`   💡 找到${data.count}个提示，显示前${data.tips.length}个:`);
      data.tips.forEach((tip, index) => {
        console.log(`   ${index + 1}. ${tip.name} - ${tip.district}`);
      });
    }
  } catch (error) {
    console.error('❌ 输入提示测试失败:', error.message);
  }
}

async function testReverseGeocoding() {
  console.log('\n🧪 测试逆地理编码API...');
  
  try {
    const response = await fetch(`${BASE_URL}/map/geocode/reverse`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        longitude: 108.3665,
        latitude: 22.8173
      }),
    });
    
    const data = await response.json();
    console.log('✅ 逆地理编码测试结果:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log(`   📍 地址: ${data.address}`);
      console.log(`   🏙️ 城市: ${data.city}`);
      console.log(`   🏛️ 区县: ${data.district}`);
    }
  } catch (error) {
    console.error('❌ 逆地理编码测试失败:', error.message);
  }
}

async function runAllTests() {
  console.log('🚀 开始地址搜索功能完整测试...\n');
  
  await testGeocoding();
  await testPOISearch();
  await testInputTips();
  await testReverseGeocoding();
  
  console.log('\n✨ 所有测试完成！');
  console.log('如果看到错误，请检查:');
  console.log('1. 后端服务是否运行 (docker compose up -d)');
  console.log('2. 高德地图API密钥是否配置正确');
  console.log('3. 网络连接是否正常');
}

// 运行测试
runAllTests().catch(console.error);