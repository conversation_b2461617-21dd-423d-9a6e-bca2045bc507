/**
 * 用户中心业务逻辑Hook - 企业级重新设计
 * 功能：封装所有业务逻辑处理，完整保留原始功能
 * 架构师重新设计：遵循Zustand最佳实践，避免过度选择器化
 * 核心原则：直接使用AuthStore，避免不必要的状态同步，保持功能完整性
 */
import { useCallback } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useAuthStore } from '../../../../auth/services/authStore';

interface UseProfileLogicOptions {
  onShowOneClickLogin?: () => void;
}

interface UseProfileLogicReturn {
  // 基础状态
  isLoggedIn: boolean;
  user: any;
  
  // 业务逻辑方法
  handleMenuItemPress: (itemId: string, itemTitle: string) => void;
  handleStatsCardPress: (type: 'tenant_buyer' | 'landlord') => void;
  handleStatsItemPress: (type: 'tenant_buyer' | 'landlord', itemType: string) => void;
  handleSettingsPress: () => void;
  handleCustomerServicePress: () => void;
  handleMoreMenuPress: (itemId: string, itemTitle: string) => void;
  handleShowOnboarding: () => void;
  
  // 权限控制方法
  requireLoginAction: (actionName: string) => void;
}

export const useProfileLogic = ({
  onShowOneClickLogin,
}: UseProfileLogicOptions = {}): UseProfileLogicReturn => {
  const navigation = useNavigation() as any; // 类型断言以支持导航到任意页面

  // 企业级修复：直接从AuthStore获取状态，避免选择器循环
  const { user } = useAuthStore();
  const isLoggedIn = !!user;

  // 需要登录的操作处理 - 完全保留原始逻辑
  const requireLoginAction = useCallback((actionName: string) => {
    console.log(`[useProfileLogic] 执行功能: ${actionName}`);
    
    if (!isLoggedIn) {
      console.log(`[useProfileLogic] 需要登录才能使用: ${actionName}`);
      if (onShowOneClickLogin) {
        onShowOneClickLogin();
      }
      return;
    }
    
    // 已登录用户的功能处理
    switch (actionName) {
      case '我的收藏':
        console.log('[useProfileLogic] 导航到我的收藏');
        navigation.navigate('Favorites');
        break;
      case '预约看房':
        console.log('[useProfileLogic] 导航到预约看房');
        navigation.navigate('Appointments');
        break;
      case '我的订单':
        console.log('[useProfileLogic] 导航到我的订单');
        navigation.navigate('Orders');
        break;
      case '求租资源':
        console.log('[useProfileLogic] 导航到求租资源');
        navigation.navigate('TenantResource');
        break;
      case '浏览记录':
        console.log('[useProfileLogic] 导航到浏览记录');
        // 暂时导航到我的收藏，后续可以创建专门的浏览记录页面
        navigation.navigate('Favorites');
        break;
      case '兑换中心':
        console.log('[useProfileLogic] 导航到兑换中心');
        // 暂时导航到设置页面，后续可以创建专门的兑换中心页面
        navigation.navigate('Settings');
        break;
      case '选址评估':
        console.log('[useProfileLogic] 导航到选址评估');
        // 暂时导航到设置页面，后续可以创建专门的评估页面
        navigation.navigate('Settings');
        break;
      case '费用评估':
        console.log('[useProfileLogic] 导航到费用评估');
        // 暂时导航到设置页面，后续可以创建专门的计算器页面
        navigation.navigate('Settings');
        break;
      default:
        console.log(`[useProfileLogic] 未知功能: ${actionName}`);
    }
  }, [isLoggedIn, onShowOneClickLogin]);

  // 核心菜单项点击处理
  const handleMenuItemPress = useCallback((itemId: string, itemTitle: string) => {
    console.log(`[useProfileLogic] 核心菜单点击: ${itemId} - ${itemTitle}`);
    requireLoginAction(itemTitle);
  }, [requireLoginAction]);

  // 统计卡片点击处理
  const handleStatsCardPress = useCallback((type: 'tenant_buyer' | 'landlord') => {
    console.log(`[useProfileLogic] 统计卡片点击: ${type}`);

    if (type === 'tenant_buyer') {
      // 🔧 修复：租买用户点击应该导航到"我的求租求购"而不是"我的订单"
      console.log('[useProfileLogic] 导航到我的求租求购');
      navigation.navigate('MyDemands');
    } else if (type === 'landlord') {
      console.log('[useProfileLogic] 导航到我的房源');
      navigation.navigate('MyProperties');
    }
  }, [navigation]);

  // 统计项点击处理
  const handleStatsItemPress = useCallback((type: 'tenant_buyer' | 'landlord', itemType: string) => {
    console.log(`[useProfileLogic] 统计项点击: ${type} - ${itemType}`);
    
    // 根据统计项类型导航到对应页面
    if (type === 'tenant_buyer') {
      switch (itemType) {
        case 'rental':
          console.log('[useProfileLogic] 导航到租房需求');
          navigation.navigate('MyDemands');
          break;
        case 'purchase':
          console.log('[useProfileLogic] 导航到购房需求');
          navigation.navigate('MyDemands');
          break;
        case 'interested':
          console.log('[useProfileLogic] 导航到感兴趣房源');
          navigation.navigate('Favorites');
          break;
      }
    } else if (type === 'landlord') {
      switch (itemType) {
        case 'rental':
          console.log('[useProfileLogic] 导航到出租房源');
          navigation.navigate('MyProperties', { initialTab: 'published' });
          break;
        case 'sale':
          console.log('[useProfileLogic] 导航到出售房源');
          navigation.navigate('MyProperties', { initialTab: 'published' });
          break;
        case 'favorites':
          console.log('[useProfileLogic] 导航到收藏统计');
          navigation.navigate('Favorites');
          break;
        case 'inquiries':
          console.log('[useProfileLogic] 导航到咨询统计');
          // 导航到消息页面
          navigation.navigate('MainTabs', { screen: 'Messages' });
          break;
      }
    }
  }, [navigation]);

  // 设置按钮处理
  const handleSettingsPress = useCallback(() => {
    console.log('[useProfileLogic] 设置按钮点击');
    navigation.navigate('Settings');
  }, [navigation]);

  // 客服按钮处理
  const handleCustomerServicePress = useCallback(() => {
    console.log('[useProfileLogic] 客服按钮点击');
    // 打开客服聊天或拨打客服电话
  }, []);

  // 更多功能菜单处理
  const handleMoreMenuPress = useCallback((itemId: string, itemTitle: string) => {
    console.log(`[useProfileLogic] 更多功能点击: ${itemId} - ${itemTitle}`);
    
    // 不需要登录的功能
    if (itemId === 'onboarding') {
      handleShowOnboarding();
      return;
    }
    
    if (itemId === 'feedback') {
      console.log('[useProfileLogic] 意见反馈');
      // navigation.navigate('Feedback');
      return;
    }
    
    if (itemId === 'help') {
      console.log('[useProfileLogic] 常见问题');
      // navigation.navigate('Help');
      return;
    }
    
    // 需要登录的功能
    requireLoginAction(itemTitle);
  }, [requireLoginAction]);

  // 新人引导处理 - 不需要登录
  const handleShowOnboarding = useCallback(() => {
    console.log('[useProfileLogic] 显示新人引导');
    // 先导航到首页
    navigation.reset({
      index: 0,
      routes: [{ name: 'MainTabs', params: { screen: 'Home' } }],
    });

    // 延迟导航到新人引导屏幕，确保首页完全加载
    setTimeout(() => {
      navigation.navigate('Onboarding');
    }, 500);
  }, [navigation]);

  return {
    // 企业级架构师重新设计：直接使用AuthStore状态，保持功能完整性
    isLoggedIn,
    user,
    
    // 业务逻辑方法
    handleMenuItemPress,
    handleStatsCardPress,
    handleStatsItemPress,
    handleSettingsPress,
    handleCustomerServicePress,
    handleMoreMenuPress,
    handleShowOnboarding,
    
    // 权限控制方法
    requireLoginAction,
  };
};
