/**
 * 媒体文件统一转换器 - 企业级版本
 *
 * 职责：
 * 1. 上传策略智能路由（STS vs 预签名URL）
 * 2. 媒体文件数据格式转换（前端UI ↔ 后端API）
 * 3. 上传配置参数转换
 * 4. 媒体文件元数据处理
 * 5. 业务场景配置管理
 * 6. 压缩和水印参数转换
 *
 * 遵循企业级架构原则：
 * - 使用统一转换层架构
 * - 类型安全和错误处理
 * - 业务逻辑封装
 * - 可扩展性设计
 * - 支持多种业务场景
 */

import { BaseTransformer } from '../core/BaseTransformer';
import { TransformResult, TransformOptions } from '../types/TransformTypes';

// 业务场景枚举
// 按安全级别分类
export enum SecurityLevel {
  NORMAL = 'normal',        // 普通数据 -> huixuanzhi-main
  SENSITIVE = 'sensitive'   // 敏感数据 -> huixuanzhi-sensitive
}

// 按访问级别分类
export enum AccessLevel {
  PUBLIC = 'public',        // 公开访问 -> main/public/
  PRIVATE = 'private'       // 私有访问 -> main/private/
}

// 按媒体类型分类
export enum MediaType {
  IMAGE = 'image',          // 图片：需要压缩、水印、多尺寸
  VIDEO = 'video',          // 视频：需要转码、压缩、截图
  DOCUMENT = 'document'     // 文档：基本存储即可
}

// 媒体业务类型枚举
export enum MediaBusinessType {
  PROPERTY_IMAGE = 'property_image',
  PROPERTY_VIDEO = 'property_video',
  PROPERTY_VR_PANORAMA = 'property_vr_panorama',
  PROPERTY_FLOOR_PLAN = 'property_floor_plan',
  PROPERTY_THUMBNAIL = 'property_thumbnail',
  IDENTITY_CARD = 'identity_card',
  BUSINESS_LICENSE = 'business_license',
  PROPERTY_CERT = 'property_cert',
  USER_AVATAR = 'user_avatar',
  FINANCIAL_DOC = 'financial_doc',
  OTHER_DOC = 'other_doc'
}

// 媒体分类组合
export interface MediaCategory {
  securityLevel: SecurityLevel;
  accessLevel: AccessLevel;
  mediaType: MediaType;
  description?: string;     // 用于日志和调试
}

// 简化的压缩配置
export interface CompressionConfig {
  enabled: boolean;
  quality: number;        // 压缩质量 0-100
  maxWidth: number;       // 最大宽度
  maxHeight: number;      // 最大高度
  format?: 'jpeg' | 'webp' | 'png';  // 输出格式
}

// 简化的视频配置
export interface VideoConfig {
  maxWidth: number;
  maxHeight: number;
  maxDuration: number;    // 最大时长（秒）
  maxFileSize: number;    // 最大文件大小（字节）
  bitrate?: string;       // 码率
}

// 水印配置
export interface WatermarkConfig {
  enabled: boolean;
  text: string;           // 水印文字
  opacity: number;        // 透明度 0-1
  position: 'center' | 'bottom-right' | 'top-left' | 'top-right' | 'bottom-left';
  color: string;          // 颜色
  fontSize: number;       // 字体大小
}

// 简化的媒体处理配置
export interface MediaProcessingConfig {
  compression: CompressionConfig;
  watermark: WatermarkConfig;
  videoConfig?: VideoConfig;              // 视频处理配置
  generateThumbnail: boolean;
  enableContentSecurity: boolean;         // 内容安全检查
  enableCDNAcceleration?: boolean;        // CDN加速
  businessType?: MediaBusinessType;       // 业务类型
  category: MediaCategory;                // 媒体分类
}

// 媒体上传请求类型
export interface MediaUploadRequest {
  fileName: string;
  fileSize: number;
  fileType: string;
  userId: string;
  propertyId?: string;
  isBatch?: boolean;
}

// 上传策略响应类型
export interface UploadStrategyResponse {
  strategy: 'STS' | 'PRESIGNED_URL';
  endpoint: string;
  config: UploadConfig;
  maxFileSize: number;
  allowedTypes: string[];
}

// 上传配置类型
export interface UploadConfig {
  chunkSize?: number;
  maxRetries: number;
  timeout: number;
  enableResume?: boolean;
  enableProgress?: boolean;
}

// 媒体文件表单数据类型
export interface MediaFileFormData {
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadPath: string;
  localUri?: string;
  thumbnailUri?: string;
  duration?: number; // 视频时长
  dimensions?: { width: number; height: number };
}

// 媒体文件API数据类型
export interface MediaFileAPIData {
  file_name: string;
  file_type: string;
  file_size: number;
  upload_path: string;
  property_id?: string;
  user_id: string;
  content_security_status: 'pending' | 'approved' | 'rejected';
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  metadata?: {
    duration?: number;
    width?: number;
    height?: number;
    thumbnail_path?: string;
  };
}

// 简化的媒体配置 - 参考主流APP最佳实践
export const MediaConfigs = {
  // 公开图片（房源图片等）
  publicImage: {
    compression: { enabled: true, quality: 85, maxWidth: 1920, maxHeight: 1440, format: 'jpeg' as const },
    watermark: { enabled: true, text: '慧选址', opacity: 0.7, position: 'bottom-right' as const, color: '#FFFFFF', fontSize: 24 },
    generateThumbnail: true,
    enableContentSecurity: true,
    category: { securityLevel: SecurityLevel.NORMAL, accessLevel: AccessLevel.PUBLIC, mediaType: MediaType.IMAGE }
  } as MediaProcessingConfig,

  // 私有图片（用户头像等）
  privateImage: {
    compression: { enabled: true, quality: 90, maxWidth: 400, maxHeight: 400, format: 'jpeg' as const },
    watermark: { enabled: false, text: '', opacity: 0, position: 'center' as const, color: '#FFFFFF', fontSize: 16 },
    generateThumbnail: true,
    enableContentSecurity: false,
    category: { securityLevel: SecurityLevel.NORMAL, accessLevel: AccessLevel.PRIVATE, mediaType: MediaType.IMAGE }
  } as MediaProcessingConfig,

  // 敏感图片（身份证等）
  sensitiveImage: {
    compression: { enabled: true, quality: 95, maxWidth: 2048, maxHeight: 1536, format: 'jpeg' as const },
    watermark: { enabled: false, text: '', opacity: 0, position: 'center' as const, color: '#FFFFFF', fontSize: 16 },
    generateThumbnail: false,
    enableContentSecurity: true,
    category: { securityLevel: SecurityLevel.SENSITIVE, accessLevel: AccessLevel.PRIVATE, mediaType: MediaType.IMAGE }
  } as MediaProcessingConfig,

  // 公开视频（房源视频等）
  publicVideo: {
    compression: { enabled: true, quality: 75, maxWidth: 1920, maxHeight: 1080 },
    videoConfig: { maxWidth: 1920, maxHeight: 1080, maxDuration: 300, maxFileSize: 500 * 1024 * 1024, bitrate: '2500k' },
    watermark: { enabled: true, text: '慧选址', opacity: 0.7, position: 'bottom-right' as const, color: '#FFFFFF', fontSize: 32 },
    generateThumbnail: true,
    enableContentSecurity: true,
    category: { securityLevel: SecurityLevel.NORMAL, accessLevel: AccessLevel.PUBLIC, mediaType: MediaType.VIDEO }
  } as MediaProcessingConfig,

  // 敏感文档
  sensitiveDocument: {
    compression: { enabled: false, quality: 100, maxWidth: 2048, maxHeight: 2048 },
    watermark: { enabled: false, text: '', opacity: 0, position: 'center' as const, color: '#FFFFFF', fontSize: 16 },
    generateThumbnail: false,
    enableContentSecurity: true,
    category: { securityLevel: SecurityLevel.SENSITIVE, accessLevel: AccessLevel.PRIVATE, mediaType: MediaType.DOCUMENT }
  } as MediaProcessingConfig
};

export class MediaTransformer extends BaseTransformer<any, any> {

  constructor() {
    super('MediaTransformer', '1.0.0');
  }

  /**
   * 初始化验证规则 - 实现抽象方法
   */
  protected initializeValidationRules(): void {
    // 文件信息验证规则
    this.addValidationRule('file', (file: any) => {
      if (!file || !file.uri || !file.name || !file.type) {
        return { isValid: false, message: '文件信息不完整' };
      }
      return { isValid: true };
    });

    // 媒体分类验证规则
    this.addValidationRule('category', (category: any) => {
      if (!category || typeof category !== 'object') {
        return { isValid: false, message: '媒体分类不能为空' };
      }

      const { securityLevel, accessLevel, mediaType } = category;

      if (!Object.values(SecurityLevel).includes(securityLevel)) {
        return { isValid: false, message: `无效的安全级别: ${securityLevel}` };
      }

      if (!Object.values(AccessLevel).includes(accessLevel)) {
        return { isValid: false, message: `无效的访问级别: ${accessLevel}` };
      }

      if (!Object.values(MediaType).includes(mediaType)) {
        return { isValid: false, message: `无效的媒体类型: ${mediaType}` };
      }

      return { isValid: true };
    });

    // 文件大小验证规则
    this.addValidationRule('fileSize', (size: number) => {
      if (size <= 0) {
        return { isValid: false, message: '文件大小无效' };
      }
      if (size > 500 * 1024 * 1024) { // 500MB限制
        return { isValid: false, message: '文件大小超过限制' };
      }
      return { isValid: true };
    });

    // 文件类型验证规则
    this.addValidationRule('fileType', (type: string) => {
      const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/webp',
        'video/mp4', 'video/mov', 'video/avi'
      ];
      if (!allowedTypes.includes(type.toLowerCase())) {
        return { isValid: false, message: '不支持的文件类型' };
      }
      return { isValid: true };
    });
  }
  
  /**
   * 上传策略转换 - 智能路由决策
   * 
   * 根据文件特征自动选择最优上传方案：
   * - 大文件(>50MB)或视频 → STS方案（支持分片上传）
   * - 小文件或图片 → 预签名URL方案（简单快速）
   * 
   * @param input 媒体上传请求数据
   * @param options 转换选项
   * @returns 上传策略响应
   */
  toUploadStrategy(input: MediaUploadRequest, options?: TransformOptions): TransformResult<UploadStrategyResponse> {
    return this.safeTransform(() => {
      // 1. 参数验证
      this._validateUploadRequest(input);
      
      // 2. 智能策略决策
      const strategy = this._determineUploadStrategy(input.fileSize, input.fileType, input.isBatch);
      
      // 3. 生成响应数据
      const response: UploadStrategyResponse = {
        strategy,
        endpoint: this._getEndpoint(strategy),
        config: this._getUploadConfig(strategy, input.fileType),
        maxFileSize: this._getMaxFileSize(input.fileType),
        allowedTypes: this._getAllowedTypes()
      };
      
      return response;
      
    }, '媒体上传策略转换');
  }

  /**
   * 媒体文件信息转换 - 前端UI数据到API格式
   * 
   * @param input 前端表单数据
   * @param options 转换选项（包含propertyId、userId等）
   * @returns API格式的媒体文件数据
   */
  toAPI(input: MediaFileFormData, options?: TransformOptions): TransformResult<MediaFileAPIData> {
    return this.safeTransform(() => {
      // 1. 数据验证
      this._validateMediaFileData(input);
      
      // 2. 格式转换
      const apiData: MediaFileAPIData = {
        file_name: input.fileName,
        file_type: input.fileType,
        file_size: input.fileSize,
        upload_path: input.uploadPath,
        property_id: options?.propertyId,
        user_id: options?.userId || '',
        content_security_status: 'pending',
        processing_status: 'pending'
      };

      // 3. 添加元数据
      if (input.duration || input.dimensions || input.thumbnailUri) {
        apiData.metadata = {
          duration: input.duration,
          width: input.dimensions?.width,
          height: input.dimensions?.height,
          thumbnail_path: input.thumbnailUri
        };
      }

      return apiData;
      
    }, '媒体文件API转换');
  }

  /**
   * API数据转换为前端显示格式
   * 
   * @param input API返回的媒体文件数据
   * @param options 转换选项
   * @returns 前端显示用的媒体文件数据
   */
  fromAPI(input: MediaFileAPIData, options?: TransformOptions): TransformResult<MediaFileFormData> {
    return this.safeTransform(() => {
      const formData: MediaFileFormData = {
        fileName: input.file_name,
        fileType: input.file_type,
        fileSize: input.file_size,
        uploadPath: input.upload_path,
        duration: input.metadata?.duration,
        dimensions: input.metadata?.width && input.metadata?.height ? {
          width: input.metadata.width,
          height: input.metadata.height
        } : undefined,
        thumbnailUri: input.metadata?.thumbnail_path
      };

      return formData;
      
    }, '媒体文件前端转换');
  }

  /**
   * 智能上传策略决策
   * 
   * @param fileSize 文件大小（字节）
   * @param fileType 文件类型
   * @param isBatch 是否批量上传
   * @returns 上传策略
   */
  private _determineUploadStrategy(fileSize: number, fileType: string, isBatch?: boolean): 'STS' | 'PRESIGNED_URL' {
    // 大文件策略：超过50MB使用STS
    if (fileSize > 50 * 1024 * 1024) {
      return 'STS';
    }
    
    // 视频文件策略：视频文件使用STS（支持分片上传和断点续传）
    if (fileType.startsWith('video/')) {
      return 'STS';
    }
    
    // 批量上传策略：批量操作使用STS
    if (isBatch) {
      return 'STS';
    }
    
    // 默认策略：小文件使用预签名URL
    return 'PRESIGNED_URL';
  }

  /**
   * 获取API端点
   */
  private _getEndpoint(strategy: 'STS' | 'PRESIGNED_URL'): string {
    return strategy === 'STS' ? '/sts/upload-credentials' : '/sts/presigned-url';
  }

  /**
   * 获取上传配置
   */
  private _getUploadConfig(strategy: 'STS' | 'PRESIGNED_URL', fileType: string): UploadConfig {
    if (strategy === 'STS') {
      return {
        chunkSize: fileType.startsWith('video/') ? 5 * 1024 * 1024 : 1 * 1024 * 1024, // 视频5MB，其他1MB
        maxRetries: 3,
        timeout: 300000, // 5分钟
        enableResume: true,
        enableProgress: true
      };
    } else {
      return {
        maxRetries: 2,
        timeout: 60000, // 1分钟
        enableResume: false,
        enableProgress: true
      };
    }
  }

  /**
   * 获取最大文件大小限制
   */
  private _getMaxFileSize(fileType: string): number {
    if (fileType.startsWith('video/')) {
      return 500 * 1024 * 1024; // 视频500MB
    } else if (fileType.startsWith('image/')) {
      return 20 * 1024 * 1024; // 图片20MB
    }
    return 100 * 1024 * 1024; // 其他100MB
  }

  /**
   * 获取允许的文件类型
   */
  private _getAllowedTypes(): string[] {
    return [
      'image/jpeg',
      'image/png', 
      'image/webp',
      'image/gif',
      'video/mp4',
      'video/mov',
      'video/avi'
    ];
  }

  /**
   * 验证上传请求参数
   */
  private _validateUploadRequest(input: MediaUploadRequest): void {
    if (!input.fileName || input.fileName.trim().length === 0) {
      throw new Error('文件名不能为空');
    }
    
    if (!input.fileType || input.fileType.trim().length === 0) {
      throw new Error('文件类型不能为空');
    }
    
    if (!input.fileSize || input.fileSize <= 0) {
      throw new Error('文件大小必须大于0');
    }
    
    if (!input.userId || input.userId.trim().length === 0) {
      throw new Error('用户ID不能为空');
    }

    // 验证文件类型
    const allowedTypes = this._getAllowedTypes();
    if (!allowedTypes.includes(input.fileType)) {
      throw new Error(`不支持的文件类型: ${input.fileType}`);
    }

    // 验证文件大小
    const maxSize = this._getMaxFileSize(input.fileType);
    if (input.fileSize > maxSize) {
      throw new Error(`文件大小超过限制: ${Math.round(maxSize / 1024 / 1024)}MB`);
    }
  }

  /**
   * 验证媒体文件数据
   */
  private _validateMediaFileData(input: MediaFileFormData): void {
    if (!input.fileName || input.fileName.trim().length === 0) {
      throw new Error('文件名不能为空');
    }
    
    if (!input.fileType || input.fileType.trim().length === 0) {
      throw new Error('文件类型不能为空');
    }
    
    if (!input.fileSize || input.fileSize <= 0) {
      throw new Error('文件大小必须大于0');
    }
    
    if (!input.uploadPath || input.uploadPath.trim().length === 0) {
      throw new Error('上传路径不能为空');
    }
  }
}
