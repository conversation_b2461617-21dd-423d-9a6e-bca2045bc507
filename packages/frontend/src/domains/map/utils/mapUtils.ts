/**
 * 地图工具函数
 * 专家算法 + 企业级实用工具
 */

// import type { Region } from 'expo-maps';

// 地球半径（米）
const EARTH_RADIUS = 6371000;

/**
 * 精确计算地图区域半径 (专家算法)
 * 从地图Region计算出合适的搜索半径
 */
export const calculateRadiusFromRegion = (region: any): number => {
  // 计算纬度方向距离
  const latDistance = region.latitudeDelta * EARTH_RADIUS * Math.PI / 180;

  // 计算经度方向距离（考虑纬度修正）
  const lngDistance = region.longitudeDelta * EARTH_RADIUS * Math.PI / 180 *
                     Math.cos(region.latitude * Math.PI / 180);

  // 取较小值作为圆形半径，确保搜索区域在可见范围内
  const radius = Math.min(latDistance, lngDistance) / 2;
  
  // 限制半径范围：最小100米，最大50公里
  return Math.max(100, Math.min(50000, radius));
};

/**
 * 计算两点间距离 (Haversine公式)
 * 高精度地理距离计算
 */
export const calculateDistance = (
  lat1: number, lng1: number,
  lat2: number, lng2: number
): number => {
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return EARTH_RADIUS * c;
};

/**
 * 格式化距离显示
 * 智能选择单位（米/公里）
 */
export const formatDistance = (meters: number): string => {
  if (meters < 1000) {
    return `${Math.round(meters)}m`;
  } else if (meters < 10000) {
    return `${(meters / 1000).toFixed(1)}km`;
  } else {
    return `${Math.round(meters / 1000)}km`;
  }
};

/**
 * 计算地图边界
 * 根据中心点和半径计算地图显示区域
 */
export const calculateMapBounds = (
  center: { latitude: number; longitude: number },
  radius: number
) => {
  // 纬度变化量
  const latDelta = (radius / EARTH_RADIUS) * (180 / Math.PI) * 2;
  
  // 经度变化量（考虑纬度修正）
  const lngDelta = (radius / EARTH_RADIUS) * (180 / Math.PI) * 2 / 
                   Math.cos(center.latitude * Math.PI / 180);

  return {
    latitude: center.latitude,
    longitude: center.longitude,
    latitudeDelta: latDelta,
    longitudeDelta: lngDelta,
  };
};

/**
 * 检查点是否在圆形区域内
 */
export const isPointInCircle = (
  point: { latitude: number; longitude: number },
  center: { latitude: number; longitude: number },
  radius: number
): boolean => {
  const distance = calculateDistance(
    point.latitude, point.longitude,
    center.latitude, center.longitude
  );
  return distance <= radius;
};

/**
 * 计算多个点的中心位置
 * 用于自动调整地图视角
 */
export const calculateCenterPoint = (
  points: Array<{ latitude: number; longitude: number }>
): { latitude: number; longitude: number } => {
  if (points.length === 0) {
    return { latitude: 0, longitude: 0 };
  }

  if (points.length === 1) {
    return points[0];
  }

  let totalLat = 0;
  let totalLng = 0;

  points.forEach(point => {
    totalLat += point.latitude;
    totalLng += point.longitude;
  });

  return {
    latitude: totalLat / points.length,
    longitude: totalLng / points.length,
  };
};

/**
 * 计算包含所有点的最小区域
 * 用于自动缩放地图
 */
export const calculateBoundingRegion = (
  points: Array<{ latitude: number; longitude: number }>,
  padding: number = 0.01
) => {
  if (points.length === 0) {
    return {
      latitude: 22.8167,   // 南宁市中心
      longitude: 108.3669,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    };
  }

  if (points.length === 1) {
    return {
      latitude: points[0].latitude,
      longitude: points[0].longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };
  }

  let minLat = points[0].latitude;
  let maxLat = points[0].latitude;
  let minLng = points[0].longitude;
  let maxLng = points[0].longitude;

  points.forEach(point => {
    minLat = Math.min(minLat, point.latitude);
    maxLat = Math.max(maxLat, point.latitude);
    minLng = Math.min(minLng, point.longitude);
    maxLng = Math.max(maxLng, point.longitude);
  });

  const centerLat = (minLat + maxLat) / 2;
  const centerLng = (minLng + maxLng) / 2;
  const latDelta = (maxLat - minLat) + padding;
  const lngDelta = (maxLng - minLng) + padding;

  return {
    latitude: centerLat,
    longitude: centerLng,
    latitudeDelta: Math.max(latDelta, 0.01),
    longitudeDelta: Math.max(lngDelta, 0.01),
  };
};

/**
 * 生成搜索圆圈的显示配置
 * 专家的视觉设计
 */
export const getCircleStyle = (radius: number) => {
  // 根据半径调整透明度和颜色
  const opacity = Math.max(0.1, Math.min(0.4, 1000 / radius));
  
  return {
    strokeColor: 'rgba(255, 79, 25, 0.8)',  // 项目主色调
    fillColor: `rgba(255, 79, 25, ${opacity})`,
    strokeWidth: 2,
  };
};

/**
 * 防抖函数
 * 用于地图移动时的搜索防抖
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(() => {
      func(...args);
    }, wait);
  };
};

/**
 * 节流函数
 * 用于地图缩放时的性能优化
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean = false;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * 坐标系转换工具
 * 高德地图使用GCJ-02坐标系，GPS使用WGS-84
 */
export const coordinateTransform = {
  /**
   * WGS-84 转 GCJ-02 (GPS转高德)
   */
  wgs84ToGcj02: (lng: number, lat: number): { longitude: number; latitude: number } => {
    // 简化的转换算法，实际项目中建议使用专业的坐标转换库
    const dlat = transformLat(lng - 105.0, lat - 35.0);
    const dlng = transformLng(lng - 105.0, lat - 35.0);
    const radlat = lat / 180.0 * Math.PI;
    let magic = Math.sin(radlat);
    magic = 1 - 0.00669342162296594323 * magic * magic;
    const sqrtmagic = Math.sqrt(magic);
    const dlat2 = (dlat * 180.0) / ((6378245.0 * (1 - 0.00669342162296594323)) / (magic * sqrtmagic) * Math.PI);
    const dlng2 = (dlng * 180.0) / (6378245.0 / sqrtmagic * Math.cos(radlat) * Math.PI);
    
    return {
      longitude: lng + dlng2,
      latitude: lat + dlat2,
    };
  },
};

// 辅助函数
function transformLat(lng: number, lat: number): number {
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0;
  ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0;
  return ret;
}

function transformLng(lng: number, lat: number): number {
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lng * Math.PI) + 40.0 * Math.sin(lng / 3.0 * Math.PI)) * 2.0 / 3.0;
  ret += (150.0 * Math.sin(lng / 12.0 * Math.PI) + 300.0 * Math.sin(lng / 30.0 * Math.PI)) * 2.0 / 3.0;
  return ret;
}
