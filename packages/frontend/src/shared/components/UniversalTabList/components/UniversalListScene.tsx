/**
 * 🏗️ 企业级架构：通用列表场景组件
 * 遵循前端企业级架构规范 - UI层
 * 参考主流APP列表设计：微信、支付宝、ni淘宝的列表页面
 */

import React from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  StyleSheet,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { TabStatus, TabListTheme, defaultTheme } from '../types';
import { spacing, fontSize } from '../../../../shared/utils/responsiveUtils';

interface UniversalListSceneProps<T = any> {
  type: 'property' | 'demand';
  status: TabStatus;
  
  // 数据相关
  data?: T[]; // 直接传入数据（搜索模式）
  dataFetcher?: (status: TabStatus) => Promise<T[]>; // 数据获取函数（正常模式）
  
  // 渲染相关
  renderItem: (item: T) => React.ReactElement;
  keyExtractor: (item: T) => string;
  
  // 状态相关
  isLoading?: boolean;
  isEmpty?: boolean;
  
  // 组件相关
  emptyComponent?: React.ComponentType<{ status: TabStatus }>;
  loadingComponent?: React.ComponentType;
  
  // 事件相关
  onItemPress?: (item: T) => void;
  onRefresh?: (status: TabStatus) => Promise<void>;
  
  // 搜索相关
  searchMode?: boolean;
  searchText?: string;
  
  // 主题
  theme?: TabListTheme;
}

export const UniversalListScene = <T extends any>({
  type,
  status,
  data,
  dataFetcher,
  renderItem,
  keyExtractor,
  isLoading: externalLoading,
  isEmpty: externalIsEmpty,
  emptyComponent: EmptyComponent,
  loadingComponent: LoadingComponent,
  onItemPress,
  onRefresh,
  searchMode = false,
  searchText = '',
  theme = defaultTheme,
}: UniversalListSceneProps<T>) => {
  
  // 🏗️ 企业级架构：数据获取（仅在非搜索模式下使用）
  const { 
    data: queryData, 
    isLoading: queryLoading, 
    refetch,
    error 
  } = useQuery({
    queryKey: [type, 'list', status],
    queryFn: () => dataFetcher ? dataFetcher(status) : Promise.resolve([]),
    enabled: !searchMode && !!dataFetcher,
    staleTime: 2 * 60 * 1000, // 2分钟缓存
    retry: 1,
  });

  // 🎯 计算最终数据和状态
  const finalData = searchMode ? (data || []) : (queryData || []);
  const finalIsLoading = searchMode ? (externalLoading || false) : queryLoading;
  const finalIsEmpty = searchMode ?
    (externalIsEmpty || (finalData.length === 0 && !finalIsLoading)) :
    (finalData.length === 0 && !finalIsLoading);

  // 🔧 企业级调试：搜索模式状态跟踪
  if (searchMode) {
    console.log(`[UniversalListScene] 搜索模式状态:`, {
      searchText,
      dataLength: finalData.length,
      isLoading: finalIsLoading,
      isEmpty: finalIsEmpty,
      externalIsEmpty
    });
  }

  // 🔧 刷新处理
  const handleRefresh = async () => {
    if (searchMode) return; // 搜索模式不支持刷新

    try {
      console.log(`[UniversalListScene] 🔄 开始刷新 ${type} 数据, status: ${status}`);

      if (onRefresh) {
        await onRefresh(status);
      }

      // 强制重新获取数据，忽略缓存
      await refetch();

      console.log(`[UniversalListScene] ✅ ${type} 数据刷新完成`);
    } catch (error) {
      console.error(`[UniversalListScene] ${type} 刷新失败:`, error);
    }
  };

  // 🔧 项目点击处理
  const handleItemPress = (item: T) => {
    onItemPress?.(item);
  };

  // 🎨 渲染项目包装器（添加点击事件）
  const renderItemWrapper = ({ item }: { item: T }) => {
    try {
      if (!item) {
        console.warn(`[UniversalListScene] renderItem收到空数据`);
        return null;
      }

      const itemElement = renderItem(item);

      if (onItemPress) {
        return React.cloneElement(itemElement, {
          onPress: () => handleItemPress(item),
        });
      }

      return itemElement;
    } catch (error) {
      console.error(`[UniversalListScene] renderItem错误:`, error);
      return null;
    }
  };

  // 🎨 渲染加载状态
  const renderLoading = () => {
    if (LoadingComponent) {
      return <LoadingComponent />;
    }
    
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={[styles.loadingText, { color: theme.text.secondary }]}>
          {searchMode ? '搜索中...' : '加载中...'}
        </Text>
      </View>
    );
  };

  // 🎨 渲染空状态
  const renderEmpty = () => {
    try {
      if (EmptyComponent) {
        return <EmptyComponent status={status} />;
      }

      const getEmptyText = () => {
        if (searchMode) {
          const safeSearchText = searchText || '';
          return {
            icon: '🔍',
            title: `找到 0 条记录`,
            description: `没有找到包含"${safeSearchText}"的${type === 'property' ? '房源' : '需求'}，试试其他关键词吧`,
          };
        }

      const statusText = {
        active: '已发布',
        published: '已发布',
        draft: '草稿',
        inactive: '已下架',
      }[status] || '相关';

      return {
        icon: type === 'property' ? '🏠' : '📋',
        title: `暂无${statusText}${type === 'property' ? '房源' : '需求'}`,
        description: '', // 🔧 企业级UI优化：删除描述文字
      };
    };
    
    const emptyInfo = getEmptyText();
    
    return (
      <View style={[
        styles.emptyContainer,
        searchMode && styles.searchEmptyContainer // 🔧 企业级UI：搜索模式使用不同样式
      ]}>
        <Text style={[
          styles.emptyIcon,
          searchMode && styles.searchEmptyIcon // 🔧 企业级UI：搜索模式图标样式
        ]}>
          {emptyInfo.icon}
        </Text>
        <Text style={[
          styles.emptyTitle,
          { color: theme.text.primary },
          searchMode && styles.searchEmptyTitle // 🔧 企业级UI：搜索模式标题样式
        ]}>
          {emptyInfo.title}
        </Text>
        {/* 🔧 企业级UI优化：搜索模式显示简化描述 */}
        {emptyInfo.description && searchMode && (
          <Text style={[styles.searchEmptyDescription, { color: theme.text.secondary }]}>
            {emptyInfo.description}
          </Text>
        )}
      </View>
    );
    } catch (error) {
      console.error(`[UniversalListScene] renderEmpty错误:`, error);
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyTitle, { color: theme.text.primary }]}>
            找到 0 条记录
          </Text>
        </View>
      );
    }
  };

  // 🎨 主渲染
  if (finalIsLoading) {
    return renderLoading();
  }

  // 🔧 企业级修复：安全的keyExtractor，避免空数据崩溃
  const safeKeyExtractor = (item: any, index: number) => {
    try {
      if (!item) return `empty-${index}`;
      return keyExtractor ? keyExtractor(item) : `item-${index}`;
    } catch (error) {
      console.warn(`[UniversalListScene] keyExtractor错误:`, error);
      return `fallback-${index}`;
    }
  };

  // 🔧 企业级修复：包装整个FlatList以捕获渲染错误
  try {
    return (
      <FlatList
        data={finalData}
        keyExtractor={safeKeyExtractor}
        renderItem={renderItemWrapper}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          styles.listContainer,
          finalIsEmpty && styles.emptyListContainer
        ]}
        ListEmptyComponent={renderEmpty}
      refreshControl={
        !searchMode ? (
          <RefreshControl
            refreshing={false}
            onRefresh={handleRefresh}
            colors={[theme.primary]}
            tintColor={theme.primary}
          />
        ) : undefined
      }
      // 性能优化
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={10}
      getItemLayout={undefined} // 让FlatList自动计算
    />
  );
  } catch (error) {
    console.error(`[UniversalListScene] FlatList渲染错误:`, error);
    // 🔧 企业级降级：渲染安全的空状态
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyIcon}>🔍</Text>
        <Text style={[styles.emptyTitle, { color: theme.text.primary }]}>
          找到 0 条记录
        </Text>
        <Text style={[styles.emptyDescription, { color: theme.text.secondary }]}>
          搜索遇到问题，请重试
        </Text>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  
  loadingText: {
    fontSize: fontSize.base,
    marginTop: spacing.md,
  },
  
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.md, // 🔧 企业级UI优化：减少顶部间距，上移位置
    paddingBottom: spacing.xl,
    minHeight: 250, // 🔧 企业级响应式：减少最小高度
    marginTop: -20, // 🔧 企业级UI优化：整体上移20px
  },
  
  emptyIcon: {
    fontSize: 64,
    marginBottom: spacing.lg,
  },
  
  emptyTitle: {
    fontSize: fontSize.lg,
    fontWeight: '600',
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  
  emptyDescription: {
    fontSize: fontSize.base,
    textAlign: 'center',
    lineHeight: 20,
  },
  
  listContainer: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.lg,
  },
  
  emptyListContainer: {
    flexGrow: 1,
  },

  // 🔧 企业级UI：搜索模式专用样式
  searchEmptyContainer: {
    marginTop: 0, // 搜索模式不需要上移
    minHeight: 200, // 搜索模式更紧凑
    paddingTop: spacing.lg,
  },

  searchEmptyIcon: {
    fontSize: 48, // 搜索模式图标更小
  },

  searchEmptyTitle: {
    fontSize: fontSize.base, // 搜索模式标题更小
    fontWeight: '500',
  },

  searchEmptyDescription: {
    fontSize: fontSize.sm,
    textAlign: 'center',
    lineHeight: 18,
    marginTop: spacing.xs,
  },
});
