/**
 * 地址搜索Hook - 企业级稳定版本
 * 参考7.28收藏页面修复方案，完全避免无限循环
 */

import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import {
  useAddressSearchStore,
  useSearchQuery,
  useSearchResults,
  useSearchLoading,
  useSearchError,
  useSearchHistory,
  useCurrentLocation,
  useQuickLocations,
} from '../stores/AddressSearchStore';
import {
  AddressResult,
  SerializableAddress,
} from '../types/addressSearch.types';
import { Transformers } from '../../../../../shared/services/dataTransform';

// 路由参数类型 - 更新为官方推荐的序列化方案
type AddressSearchRouteProp = RouteProp<
  {
    AddressSearch: {
      searchType: 'origin' | 'destination';
      currentAddress?: string;
      returnScreen?: string; // 返回的屏幕名称
      returnKey?: string; // 标识返回数据的类型
      onAddressSelect?: (address: AddressResult) => void; // 🔧 保持向后兼容，但推荐使用returnScreen模式
    };
  },
  'AddressSearch'
>;

export const useAddressSearch = () => {
  const navigation = useNavigation();
  const route = useRoute<AddressSearchRouteProp>();

  // Store状态 - 直接使用选择器，避免复杂的actions
  const searchQuery = useSearchQuery();
  const searchResults = useSearchResults();
  const isSearching = useSearchLoading();
  const searchError = useSearchError();
  const searchHistory = useSearchHistory();
  const currentLocation = useCurrentLocation();
  const quickLocations = useQuickLocations();

  // 路由参数
  const {
    searchType,
    currentAddress,
    returnScreen,
    returnKey,
    onAddressSelect,
  } = route.params || {};

  // 🔧 修复循环错误：简化初始化逻辑，避免嵌套setTimeout
  const initializedRef = useRef(false);

  // 简化同步初始化 - 避免复杂的异步调用链导致循环错误
  useEffect(() => {
    if (initializedRef.current) return;

    console.log('[useAddressSearch] 简化初始化开始');

    try {
      const store = useAddressSearchStore.getState();

      // 同步重置状态
      store.reset();

      // 如果有初始地址，设置到搜索框（避免过滤默认值）
      if (
        currentAddress &&
        currentAddress !== '我的位置' &&
        currentAddress !== '目标地址' &&
        currentAddress.trim().length > 0
      ) {
        store.setSearchQuery(currentAddress);
      }

      // 异步获取位置（不阻塞初始化）
      store.getCurrentLocation().catch(error => {
        console.log(
          '[useAddressSearch] 获取位置失败，但不影响搜索功能:',
          error
        );
      });

      console.log('[useAddressSearch] 简化初始化完成');
    } catch (error) {
      console.error('[useAddressSearch] 初始化出错:', error);
    }

    initializedRef.current = true;
  }, []); // 完全无依赖，只执行一次

  // 搜索处理 - 完全无依赖，使用Store.getState()
  const handleSearch = useCallback(async (query: string) => {
    if (!query.trim()) return;
    console.log('[useAddressSearch] 执行搜索:', query);
    await useAddressSearchStore.getState().performSearch(query);
  }, []);

  // 地址选择处理 - 支持两种模式：回调模式（向后兼容）和返回参数模式（官方推荐）
  const handleAddressSelect = useCallback(
    (address: AddressResult) => {
      console.log('[useAddressSearch] 选择地址:', address);

      try {
        // 记录到历史
        useAddressSearchStore.getState().selectAddress(address);

        // 🔧 优先使用官方推荐的返回参数模式
        if (returnScreen && returnKey) {
          console.log(
            `📍 [地址选择] 使用返回参数模式: ${returnScreen} -> ${returnKey}`
          );

          // 🚀 使用统一转换层处理地址数据
          const transformResult = Transformers.map?.toAPI?.(address, {
            context: 'addressSearch',
          });

          if (!transformResult?.success) {
            console.warn(
              '⚠️ [地址选择] 地址转换失败，使用备用转换:',
              transformResult?.error
            );
          }

          const serializableAddress: SerializableAddress =
            transformResult?.success
              ? transformResult.data
              : fallbackAddressConversion(address);
          console.log('📍 [地址选择] 统一转换层处理结果:', serializableAddress);

          // 🔧 修复导航参数序列化错误：使用标准导航方式，确保参数正确传递
          if (returnScreen === 'PropertyNavigation') {
            // PropertyNavigation页面：需要构造完整的propertyInfo参数
            console.log(
              `📍 [地址选择] PropertyNavigation传参: returnKey=${returnKey}`
            );

            // 🚀 Stage3崩溃修复：构造PropertyNavigation需要的propertyInfo参数
            const propertyInfo = {
              id: address.id || 'selected_address_id',
              title: address.name || '选定地址',
              address: address.formattedAddress || address.address,
              latitude: address.location?.latitude || null,
              longitude: address.location?.longitude || null,
            };

            console.log('📍 [地址选择] 传递完整参数:', {
              propertyInfo: propertyInfo,
              selectedAddress: serializableAddress,
              returnKey: returnKey,
            });

            // 🚀 Stage3崩溃修复：传递正确的参数格式
            navigation.navigate(returnScreen as any, {
              propertyInfo: propertyInfo, // ✅ 必需参数
              selectedAddress: serializableAddress,
              returnKey: returnKey,
            });
          } else {
            // 其他页面：标准序列化安全导航
            console.log(
              `📍 [地址选择] 其他页面传参: ${returnScreen}, returnKey=${returnKey}`
            );
            navigation.navigate(returnScreen as any, {
              selectedAddress: serializableAddress,
              returnKey: returnKey,
            });
          }
        } else if (onAddressSelect) {
          // 🔄 向后兼容：使用回调模式
          console.log('📍 [地址选择] 使用回调模式（向后兼容）');
          onAddressSelect(address);
          navigation.goBack();
        } else {
          // 默认行为：直接返回
          console.log('📍 [地址选择] 使用默认返回');
          navigation.goBack();
        }
      } catch (error) {
        console.error('[useAddressSearch] 地址选择处理失败:', error);
        // 🔧 错误处理：安全回退到基本导航
        navigation.goBack();
      }
    },
    [returnScreen, returnKey, onAddressSelect, navigation]
  );

  // 🔧 备用地址转换方法（当统一转换层不可用时）
  const fallbackAddressConversion = useCallback(
    (address: AddressResult): SerializableAddress => {
      try {
        return {
          id: typeof address.id === 'string' ? address.id : '',
          name: typeof address.name === 'string' ? address.name : '',
          address: typeof address.address === 'string' ? address.address : '',
          latitude:
            typeof address.location?.latitude === 'number'
              ? address.location.latitude
              : 0,
          longitude:
            typeof address.location?.longitude === 'number'
              ? address.location.longitude
              : 0,
          formattedAddress:
            typeof address.formattedAddress === 'string'
              ? address.formattedAddress
              : undefined,
          district:
            typeof address.district === 'string' ? address.district : undefined,
          citycode:
            typeof address.citycode === 'string' ? address.citycode : undefined,
          adcode:
            typeof address.adcode === 'string' ? address.adcode : undefined,
          type: typeof address.type === 'string' ? address.type : undefined,
          typecode:
            typeof address.typecode === 'string' ? address.typecode : undefined,
        };
      } catch (error) {
        console.error('[fallbackAddressConversion] 转换失败:', error);
        return {
          id: 'fallback_id',
          name: '未知地址',
          address: '地址信息不完整',
          latitude: 0,
          longitude: 0,
        };
      }
    },
    []
  );

  // 历史记录选择
  const handleHistorySelect = useCallback(
    (address: AddressResult) => {
      handleAddressSelect(address);
    },
    [handleAddressSelect]
  );

  // 当前位置选择
  const handleCurrentLocationSelect = useCallback(() => {
    const state = useAddressSearchStore.getState();

    if (!state.currentLocation) {
      state.getCurrentLocation();
      return;
    }

    const locationAddress: AddressResult = {
      id: 'current_location',
      name: '我的位置',
      address: state.currentLocation.address,
      location: {
        latitude: state.currentLocation.latitude,
        longitude: state.currentLocation.longitude,
      },
      district: '',
      citycode: '',
      adcode: '',
      type: 'current_location',
      typecode: 'current_location',
      formattedAddress: state.currentLocation.address,
    };

    handleAddressSelect(locationAddress);
  }, [handleAddressSelect]);

  // 清除搜索
  const handleClearSearch = useCallback(() => {
    useAddressSearchStore.getState().clearSearch();
  }, []);

  // 返回上一页
  const handleGoBack = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  // 加载更多结果
  const handleLoadMore = useCallback(() => {
    useAddressSearchStore.getState().loadMoreResults();
  }, []);

  // 设置搜索查询
  const setSearchQuery = useCallback((query: string) => {
    useAddressSearchStore.getState().setSearchQuery(query);
  }, []);

  // 移除历史记录
  const removeFromHistory = useCallback((addressId: string) => {
    useAddressSearchStore.getState().removeFromHistory(addressId);
  }, []);

  // 清空历史记录
  const clearHistory = useCallback(() => {
    useAddressSearchStore.getState().clearHistory();
  }, []);

  // 添加快捷位置
  const addQuickLocation = useCallback((location: AddressResult) => {
    useAddressSearchStore.getState().addQuickLocation(location);
  }, []);

  // 移除快捷位置
  const removeQuickLocation = useCallback((locationId: string) => {
    useAddressSearchStore.getState().removeQuickLocation(locationId);
  }, []);

  // 🔧 修复循环错误：简化useMemo计算，避免复杂依赖导致无限重计算
  const computedData = useMemo(() => {
    const searchQueryLength = searchQuery.trim().length;
    const searchResultsLength = searchResults.length;
    const searchHistoryLength = searchHistory.length;

    return {
      // 简单布尔计算
      showHistory: searchQueryLength === 0 && searchHistoryLength > 0,
      showResults: searchQueryLength > 0 && searchResultsLength > 0,
      showEmptyState:
        searchQueryLength > 0 &&
        !isSearching &&
        searchResultsLength === 0 &&
        !searchError,

      // 简化历史记录处理 - 避免复杂排序
      sortedHistory: searchHistory.slice(0, 10), // 直接截取，Store中已排序

      // 简化快捷位置 - 减少条件判断
      displayQuickLocations: currentLocation
        ? [
            {
              id: 'current_location',
              name: '我的位置',
              address: currentLocation.address || '当前位置',
              formattedAddress: currentLocation.address || '当前位置',
              location: {
                latitude: currentLocation.latitude || 0,
                longitude: currentLocation.longitude || 0,
              },
              district: '',
              citycode: '',
              adcode: '',
              type: 'current_location',
              typecode: 'current_location',
            } as AddressResult,
            ...quickLocations,
          ]
        : quickLocations,

      // 🔧 修复Text错误：类型安全的状态文本处理
      searchStatusText: (() => {
        if (isSearching) return '搜索中...';
        if (searchError) {
          // searchError类型为string | null，安全处理
          return typeof searchError === 'string' &&
            searchError.trim().length > 0
            ? searchError
            : '搜索出错';
        }
        if (searchQueryLength > 0 && searchResultsLength === 0)
          return '暂无搜索结果';
        if (searchResultsLength > 0)
          return `找到 ${searchResultsLength} 个结果`;
        return ''; // 明确返回空字符串，避免undefined
      })(),

      // 静态文本
      pageTitle: searchType === 'origin' ? '选择起点' : '选择终点',
      searchPlaceholder:
        searchType === 'origin' ? '搜索起点位置' : '搜索目的地',
    };
  }, [
    // 简化依赖列表，只包含必要的状态
    searchQuery.trim().length,
    searchResults.length,
    searchHistory.length,
    isSearching,
    searchError,
    searchType,
    currentLocation?.latitude, // 只依赖位置坐标变化
    currentLocation?.longitude,
    quickLocations.length, // 只依赖数量变化
  ]); // 稳定的依赖数组

  // 🔧 修复循环错误：直接返回对象，避免引用缓存可能导致的问题
  return {
    // 状态 - 直接返回Store值
    searchQuery,
    searchResults,
    isSearching,
    searchError,
    searchHistory: computedData.sortedHistory,
    currentLocation,
    quickLocations: computedData.displayQuickLocations,

    // 计算属性 - 来自简化的computedData
    showHistory: computedData.showHistory,
    showResults: computedData.showResults,
    showEmptyState: computedData.showEmptyState,
    searchStatusText: computedData.searchStatusText,
    pageTitle: computedData.pageTitle,
    searchPlaceholder: computedData.searchPlaceholder,

    // 操作方法 - 稳定的useCallback引用
    setSearchQuery,
    handleSearch,
    handleAddressSelect,
    handleHistorySelect,
    handleCurrentLocationSelect,
    handleClearSearch,
    handleGoBack,
    handleLoadMore,
    removeFromHistory,
    clearHistory,
    addQuickLocation,
    removeQuickLocation,

    // 路由参数 - 不变值
    searchType,
    currentAddress,
  };
};
