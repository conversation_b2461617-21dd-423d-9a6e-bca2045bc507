#!/usr/bin/env python3
"""
清理office_description相关的验证逻辑

这个脚本用于：
1. 检查数据库中是否还存在office_description字段
2. 检查是否有相关的约束或验证逻辑
3. 清理任何遗留的验证代码
"""

import asyncio
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.db import get_async_session
from app.models.property.property import Property


async def check_database_schema():
    """检查数据库schema是否还包含office_description字段"""
    print("=== 检查数据库Schema ===")
    
    async for db in get_async_session():
        try:
            # 检查properties表的列
            result = await db.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'properties'
                AND column_name LIKE '%description%'
                ORDER BY column_name;
            """))
            
            columns = result.fetchall()
            print(f"找到 {len(columns)} 个description相关字段：")
            
            for column in columns:
                print(f"  - {column[0]} ({column[1]}, nullable: {column[2]})")
            
            # 检查约束
            result = await db.execute(text("""
                SELECT constraint_name, constraint_type
                FROM information_schema.table_constraints
                WHERE table_name = 'properties'
                AND constraint_name LIKE '%description%';
            """))
            
            constraints = result.fetchall()
            if constraints:
                print(f"\n找到 {len(constraints)} 个description相关约束：")
                for constraint in constraints:
                    print(f"  - {constraint[0]} ({constraint[1]})")
            else:
                print("\n✅ 未找到description相关的约束")
                
        except Exception as e:
            print(f"❌ 检查数据库Schema时出错: {e}")
        
        break


async def check_property_model():
    """检查Property模型是否还包含office_description字段"""
    print("\n=== 检查Property模型 ===")
    
    try:
        # 检查Property模型的字段
        property_fields = list(Property.__fields__.keys()) if hasattr(Property, '__fields__') else []
        table_columns = [col.name for col in Property.__table__.columns] if hasattr(Property, '__table__') else []
        
        description_fields = [field for field in property_fields if 'description' in field.lower()]
        description_columns = [col for col in table_columns if 'description' in col.lower()]
        
        print(f"Property模型字段中包含description的: {description_fields}")
        print(f"Property表列中包含description的: {description_columns}")
        
        # 检查是否还有office_description相关字段
        problematic_fields = [field for field in (description_fields + description_columns) 
                             if 'office' in field.lower()]
        
        if problematic_fields:
            print(f"⚠️  发现可能有问题的字段: {problematic_fields}")
        else:
            print("✅ 未发现office_description相关字段")
            
    except Exception as e:
        print(f"❌ 检查Property模型时出错: {e}")


async def test_property_creation():
    """测试创建写字楼类型房源"""
    print("\n=== 测试写字楼房源创建 ===")
    
    async for db in get_async_session():
        try:
            from app.schemas.property.property_sqlmodel import PropertyCreate
            from app.models.property.enums import PropertyType, TransactionType, DeliveryStatus
            
            # 创建测试数据
            test_property_data = PropertyCreate(
                title="测试写字楼",
                property_type=PropertyType.OFFICE,
                sub_type="STANDARD_OFFICE",
                total_area=100.0,
                transaction_types=[TransactionType.RENT],
                delivery_status=DeliveryStatus.IMMEDIATE,
                description="测试写字楼描述"
            )
            
            print("✅ PropertyCreate schema验证通过")
            print(f"   - 标题: {test_property_data.title}")
            print(f"   - 类型: {test_property_data.property_type}")
            print(f"   - 子类型: {test_property_data.sub_type}")
            print(f"   - 描述: {test_property_data.description}")
            
        except Exception as e:
            print(f"❌ 创建写字楼房源测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        break


async def main():
    """主函数"""
    print("开始检查和清理office_description验证逻辑...\n")
    
    await check_database_schema()
    await check_property_model() 
    await test_property_creation()
    
    print("\n=== 总结 ===")
    print("1. 如果数据库中仍然存在office_description字段，请运行迁移:")
    print("   cd /data/my-real-estate-app/packages/backend")
    print("   alembic upgrade head")
    
    print("\n2. 如果仍然出现验证错误，检查以下位置:")
    print("   - Pydantic模型验证器")
    print("   - 数据库约束")
    print("   - 前端验证逻辑")
    
    print("\n3. 验证写字楼房源创建:")
    print("   应该能够在不提供office_description的情况下创建写字楼房源")


if __name__ == "__main__":
    asyncio.run(main())