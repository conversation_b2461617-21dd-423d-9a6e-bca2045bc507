/**
 * 键盘响应TextInput组件
 * 整合useKeyboardAwareField功能
 */

import React from 'react';
import { TextInput, TextInputProps } from 'react-native';
import { useKeyboardAwareField, UseKeyboardAwareFormReturn } from '../../../../shared/hooks/useKeyboardAwareForm';

interface KeyboardAwareTextInputProps extends TextInputProps {
  fieldName: string;
  formControl: UseKeyboardAwareFormReturn;
}

export const KeyboardAwareTextInput: React.FC<KeyboardAwareTextInputProps> = ({
  fieldName,
  formControl,
  onFocus,
  onBlur,
  ...props
}) => {
  const { inputRef, handleFocus, handleBlur, handleLayout } = useKeyboardAwareField(fieldName, formControl);

  return (
    <TextInput
      ref={inputRef}
      onFocus={(e) => {
        handleFocus();
        onFocus?.(e);
      }}
      onBlur={(e) => {
        handleBlur();
        onBlur?.(e);
      }}
      onLayout={handleLayout}
      {...props}
    />
  );
};

export default KeyboardAwareTextInput;