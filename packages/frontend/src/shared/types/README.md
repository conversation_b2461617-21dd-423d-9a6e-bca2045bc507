# 🎯 TypeScript类型系统文档

## 📋 概述

本文档描述了前端项目中的完整TypeScript类型系统，包括类型定义、使用规范和最佳实践。

## 🗂️ 类型文件结构

```
src/shared/types/
├── README.md                 # 本文档
├── api.types.ts             # API响应类型定义
├── navigation.types.ts      # 导航类型定义
├── component.types.ts       # 组件接口定义
├── activity.types.ts        # 活动类型枚举
└── index.ts                 # 类型导出入口
```

## 🔧 核心类型系统

### 1. API类型 (`api.types.ts`)

#### **标准API响应格式**
```typescript
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  code?: string;
  timestamp?: string;
}
```

**使用场景**：
- 所有API调用的标准响应格式
- 统一错误处理机制
- 类型安全的数据获取

**示例用法**：
```typescript
const response: ApiResponse<UserData> = await userAPI.getUserInfo();
if (response.success && response.data) {
  setUser(response.data);
}
```

#### **分页响应格式**
```typescript
interface PaginatedResponse<T = any> {
  items: T[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  hasMore: boolean;
  totalPages: number;
}
```

**使用场景**：
- 列表数据的分页显示
- 无限滚动实现
- 数据统计信息

### 2. 导航类型 (`navigation.types.ts`)

#### **类型安全导航**
```typescript
export const safeNavigate = (
  navigation: any,
  screenName: string,
  params?: any
) => {
  try {
    if (navigation && typeof navigation.navigate === 'function') {
      navigation.navigate(screenName, params);
    }
  } catch (error) {
    console.error(`Navigation error:`, error);
  }
};
```

**核心特性**：
- 防止导航崩溃
- 类型安全检查
- 错误日志记录

**推荐用法**：
```typescript
// ✅ 推荐：使用safeNavigate
safeNavigate(navigation, 'ProfileScreen', { userId: '123' });

// ❌ 不推荐：直接使用navigation
navigation.navigate('ProfileScreen', { userId: '123' });
```

### 3. 组件类型 (`component.types.ts`)

#### **基础组件Props**
```typescript
interface BaseComponentProps {
  children?: ReactNode;
  style?: StyleProp<ViewStyle>;
  testID?: string;
}
```

#### **表单组件Props**
```typescript
interface FormFieldProps extends BaseComponentProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
}
```

**设计原则**：
- 继承基础Props，保持一致性
- 支持可选属性，提高灵活性
- 包含错误状态和禁用状态

### 4. 活动类型 (`activity.types.ts`)

#### **活动类型枚举**
```typescript
export enum ActivityType {
  PROPERTY_VIEWED = 'property_viewed',
  PROPERTY_FAVORITED = 'property_favorited',
  DEMAND_CREATED = 'demand_created',
  INQUIRY_SENT = 'inquiry_sent',
  // ... 更多活动类型
}
```

**使用场景**：
- 用户行为追踪
- 活动日志记录
- 统计分析

## 📝 类型使用规范

### 1. 导入规范

#### **推荐的导入方式**
```typescript
// ✅ 使用具体类型导入
import { ApiResponse, PaginatedResponse } from '@/shared/types/api.types';
import { safeNavigate } from '@/shared/types/navigation.types';
import { BaseComponentProps } from '@/shared/types/component.types';
```

#### **统一导出入口**
```typescript
// src/shared/types/index.ts
export * from './api.types';
export * from './navigation.types';
export * from './component.types';
export * from './activity.types';

// 使用方式
import { ApiResponse, safeNavigate, BaseComponentProps } from '@/shared/types';
```

### 2. 类型定义规范

#### **接口命名规范**
- **Props接口**：以`Props`结尾，如`UserCardProps`
- **数据接口**：使用描述性名称，如`UserData`, `PropertyInfo`
- **响应接口**：以`Response`结尾，如`LoginResponse`
- **请求接口**：以`Request`结尾，如`CreateUserRequest`

#### **类型注解规范**
```typescript
// ✅ 推荐：明确的类型注解
const handleUserUpdate = async (userData: UserData): Promise<ApiResponse<UserData>> => {
  return await userAPI.updateUser(userData);
};

// ❌ 不推荐：缺少类型注解
const handleUserUpdate = async (userData) => {
  return await userAPI.updateUser(userData);
};
```

### 3. 泛型使用规范

#### **API响应泛型**
```typescript
// ✅ 推荐：明确指定泛型类型
const getUserData = (): Promise<ApiResponse<UserData>> => {
  return apiClient.get<UserData>('/users/me');
};

// ✅ 推荐：列表数据使用分页响应
const getUserList = (): Promise<ApiResponse<PaginatedResponse<UserData>>> => {
  return apiClient.get('/users');
};
```

#### **组件Props泛型**
```typescript
// ✅ 推荐：可复用的泛型组件
interface ListComponentProps<T> extends BaseComponentProps {
  items: T[];
  renderItem: (item: T) => ReactNode;
  onItemPress?: (item: T) => void;
}

const ListComponent = <T,>({ items, renderItem, onItemPress }: ListComponentProps<T>) => {
  // 组件实现
};
```

## 🔍 类型检查和验证

### 1. 编译时检查

#### **TypeScript配置**
确保`tsconfig.json`包含严格的类型检查：
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  }
}
```

#### **常用检查命令**
```bash
# 类型检查（不生成文件）
npx tsc --noEmit

# 类型检查（跳过库文件检查）
npx tsc --noEmit --skipLibCheck

# ESLint类型相关检查
npm run lint
```

### 2. 运行时验证

#### **类型守卫函数**
```typescript
// API响应验证
export const isApiSuccess = <T>(response: ApiResponse<T>): response is ApiResponse<T> & { success: true; data: T } => {
  return response.success === true && response.data !== undefined;
};

// 使用示例
const response = await getUserData();
if (isApiSuccess(response)) {
  // 这里TypeScript知道response.data存在且类型为UserData
  setUser(response.data);
}
```

#### **数据验证**
```typescript
// 使用zod进行运行时验证
import { z } from 'zod';

const UserDataSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  avatar: z.string().optional(),
});

export type UserData = z.infer<typeof UserDataSchema>;

// 验证API响应数据
export const validateUserData = (data: unknown): UserData => {
  return UserDataSchema.parse(data);
};
```

## 🚀 最佳实践

### 1. 类型设计原则

#### **单一职责原则**
```typescript
// ✅ 推荐：职责明确的接口
interface UserBasicInfo {
  id: string;
  name: string;
  avatar?: string;
}

interface UserContactInfo {
  email: string;
  phone?: string;
}

interface UserProfile extends UserBasicInfo, UserContactInfo {
  bio?: string;
  preferences: UserPreferences;
}
```

#### **开放封闭原则**
```typescript
// ✅ 推荐：可扩展的基础接口
interface BaseEvent {
  id: string;
  timestamp: string;
  type: string;
}

interface UserEvent extends BaseEvent {
  type: 'user_action';
  userId: string;
  action: UserAction;
}

interface SystemEvent extends BaseEvent {
  type: 'system_event';
  severity: 'info' | 'warning' | 'error';
  message: string;
}
```

### 2. 错误处理模式

#### **统一错误类型**
```typescript
interface ApiError {
  success: false;
  message: string;
  code: string;
  details?: any;
  timestamp?: string;
}

// 错误处理工具函数
export const handleApiError = (error: ApiError): string => {
  switch (error.code) {
    case 'VALIDATION_ERROR':
      return '输入数据有误，请检查后重试';
    case 'UNAUTHORIZED':
      return '请先登录';
    case 'FORBIDDEN':
      return '权限不足';
    default:
      return error.message || '操作失败';
  }
};
```

### 3. 性能优化

#### **类型导入优化**
```typescript
// ✅ 推荐：按需导入
import type { UserData } from '@/shared/types/user.types';
import type { ApiResponse } from '@/shared/types/api.types';

// ❌ 不推荐：全量导入
import * as Types from '@/shared/types';
```

#### **条件类型优化**
```typescript
// 复杂类型计算可以使用条件类型
type ApiResponseData<T> = T extends ApiResponse<infer U> ? U : never;

// 使用示例
type UserDataFromResponse = ApiResponseData<ApiResponse<UserData>>; // UserData
```

## 📚 相关资源

### 1. 官方文档
- [TypeScript官方文档](https://www.typescriptlang.org/docs/)
- [React Native TypeScript](https://reactnative.dev/docs/typescript)

### 2. 项目内相关文件
- `src/shared/types/` - 类型定义目录
- `src/shared/dto/` - 数据传输对象
- `src/shared/services/` - API服务层

### 3. 代码检查工具
- ESLint TypeScript规则
- TypeScript编译器
- Prettier代码格式化

## 🔄 更新日志

### 2025-07-09
- 创建完整的类型系统文档
- 建立类型使用规范和最佳实践
- 补充类型检查和验证方法
- 添加性能优化建议

---

## 📝 注意事项

1. **类型安全优先**：始终使用明确的类型注解，避免`any`类型
2. **渐进式增强**：在现有代码基础上逐步完善类型定义
3. **文档同步更新**：类型变更时及时更新相关文档
4. **团队规范统一**：确保团队成员遵循相同的类型定义规范

这个类型系统为项目提供了完整的类型安全保障，提升了代码质量和开发效率。在使用过程中如有问题，请参考相关示例或查阅官方文档。