// 测试动态导入的简单脚本
const path = require('path');
const fs = require('fs');

console.log('测试动态导入 validationSchema.ts');

// 检查文件路径
const targetPath = './src/screens/Publish/PropertyDetailFormScreen/hooks/validationSchema.ts';
const fullPath = path.resolve(targetPath);

console.log('1. 文件路径:', fullPath);
console.log('2. 文件存在:', fs.existsSync(fullPath));

// 读取文件内容
try {
  const content = fs.readFileSync(fullPath, 'utf-8');
  console.log('3. 文件内容长度:', content.length);
  
  // 检查导出
  const hasPropertyFormSchema = content.includes('export const propertyFormSchema');
  const hasDefaultFormValues = content.includes('export const defaultFormValues');
  const hasPropertyFormData = content.includes('export type PropertyFormData');
  
  console.log('4. 导出检查:');
  console.log('   - propertyFormSchema:', hasPropertyFormSchema);
  console.log('   - defaultFormValues:', hasDefaultFormValues);
  console.log('   - PropertyFormData:', hasPropertyFormData);
  
  // 检查语法错误
  const hasBasicSyntaxErrors = content.includes('export const') && 
                              content.includes('z.object') && 
                              content.includes('z.string()');
  
  console.log('5. 基本语法检查通过:', hasBasicSyntaxErrors);
  
  // 检查可能的语法问题
  const potentialIssues = [];
  
  if (!content.includes('import { z } from')) {
    potentialIssues.push('缺少 zod 导入');
  }
  
  if (!content.includes('export const propertyFormSchema =')) {
    potentialIssues.push('缺少 propertyFormSchema 导出');
  }
  
  if (!content.includes('export const defaultFormValues')) {
    potentialIssues.push('缺少 defaultFormValues 导出');
  }
  
  if (potentialIssues.length > 0) {
    console.log('6. 潜在问题:', potentialIssues);
  } else {
    console.log('6. 文件格式看起来正确');
  }
  
} catch (error) {
  console.error('读取文件失败:', error.message);
}