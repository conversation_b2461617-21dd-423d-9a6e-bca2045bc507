"""
地图找房页API
企业级架构 - 专门为地图找房页提供高级搜索功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum
import logging

from app.core.db import get_async_session
from app.services.map_service import MapService
from app.core.exceptions import ValidationError, BusinessError
from app.core.constants import PropertyType

router = APIRouter()
logger = logging.getLogger(__name__)


class MapViewMode(str, Enum):
    """地图视图模式"""
    STANDARD = "standard"      # 标准视图
    SATELLITE = "satellite"    # 卫星视图
    HYBRID = "hybrid"         # 混合视图


class PriceRange(BaseModel):
    """价格范围"""
    min_price: Optional[float] = Field(None, ge=0, description="最低价格")
    max_price: Optional[float] = Field(None, ge=0, description="最高价格")


class AreaRange(BaseModel):
    """面积范围"""
    min_area: Optional[float] = Field(None, ge=0, description="最小面积（平方米）")
    max_area: Optional[float] = Field(None, ge=0, description="最大面积（平方米）")


class MapSearchFilters(BaseModel):
    """地图搜索筛选条件"""
    property_types: Optional[List[PropertyType]] = Field(None, description="房源类型列表")
    price_range: Optional[PriceRange] = Field(None, description="价格范围")
    area_range: Optional[AreaRange] = Field(None, description="面积范围")
    keywords: Optional[str] = Field(None, max_length=100, description="关键词搜索")
    has_parking: Optional[bool] = Field(None, description="是否有停车位")
    has_elevator: Optional[bool] = Field(None, description="是否有电梯")
    decoration_level: Optional[str] = Field(None, description="装修程度")


class MapBounds(BaseModel):
    """地图边界"""
    north_lat: float = Field(..., ge=-90, le=90, description="北边界纬度")
    south_lat: float = Field(..., ge=-90, le=90, description="南边界纬度")
    east_lng: float = Field(..., ge=-180, le=180, description="东边界经度")
    west_lng: float = Field(..., ge=-180, le=180, description="西边界经度")


class MapSearchRequest(BaseModel):
    """地图搜索请求"""
    bounds: MapBounds = Field(..., description="地图边界")
    filters: Optional[MapSearchFilters] = Field(None, description="筛选条件")
    limit: int = Field(100, ge=1, le=500, description="返回数量限制")
    offset: int = Field(0, ge=0, description="偏移量")
    view_mode: MapViewMode = Field(MapViewMode.STANDARD, description="地图视图模式")


class PropertyMarker(BaseModel):
    """地图房源标记"""
    id: str
    title: str
    latitude: float
    longitude: float
    property_type: PropertyType
    area: Optional[float] = None
    rent_price: Optional[float] = None
    sale_price: Optional[float] = None
    address: Optional[str] = None
    thumbnail_url: Optional[str] = None
    is_featured: bool = False
    is_urgent: bool = False
    created_at: Optional[str] = None


class MapSearchResponse(BaseModel):
    """地图搜索响应"""
    properties: List[PropertyMarker]
    total_count: int
    bounds: MapBounds
    filters_applied: Optional[MapSearchFilters] = None
    search_metadata: Dict[str, Any]
    
    class Config:
        json_schema_extra = {
            "example": {
                "properties": [
                    {
                        "id": "123e4567-e89b-12d3-a456-426614174000",
                        "title": "CBD核心商铺出租",
                        "latitude": 22.547,
                        "longitude": 114.085,
                        "property_type": "SHOP",
                        "area": 120.5,
                        "rent_price": 15000.0,
                        "sale_price": None,
                        "address": "深圳市福田区中心大道123号",
                        "thumbnail_url": "https://example.com/thumb.jpg",
                        "is_featured": True,
                        "is_urgent": False,
                        "created_at": "2024-01-15T10:30:00Z"
                    }
                ],
                "total_count": 1,
                "bounds": {
                    "north_lat": 22.560,
                    "south_lat": 22.530,
                    "east_lng": 114.100,
                    "west_lng": 114.070
                },
                "filters_applied": {
                    "property_types": ["SHOP"],
                    "price_range": {"min_price": 10000, "max_price": 20000}
                },
                "search_metadata": {
                    "search_time_ms": 150,
                    "cache_hit": False,
                    "total_in_bounds": 1
                }
            }
        }


@router.post(
    "/search",
    response_model=MapSearchResponse,
    summary="地图找房搜索",
    description="""
    地图找房页的高级搜索功能。
    
    **功能特点**：
    - 支持地图边界搜索
    - 多维度筛选条件
    - 高性能PostGIS查询
    - 支持分页和排序
    
    **使用场景**：
    - 地图找房页主要搜索
    - 用户拖拽地图时的实时搜索
    - 高级筛选功能
    """
)
async def map_search_properties(
    request: MapSearchRequest,
    session: AsyncSession = Depends(get_async_session)
) -> MapSearchResponse:
    """
    地图找房搜索
    """
    try:
        import time
        start_time = time.time()
        
        logger.info(f"地图找房搜索: 边界({request.bounds.south_lat}-{request.bounds.north_lat}, "
                   f"{request.bounds.west_lng}-{request.bounds.east_lng})")
        
        # 创建地图服务
        map_service = MapService(session)
        
        # 执行边界搜索
        search_result = await map_service.search_properties_by_bounds(
            north_lat=request.bounds.north_lat,
            south_lat=request.bounds.south_lat,
            east_lng=request.bounds.east_lng,
            west_lng=request.bounds.west_lng,
            limit=request.limit,
            offset=request.offset
        )
        
        # 转换为地图标记格式
        property_markers = []
        for prop in search_result["properties"]:
            # 这里可以根据筛选条件进一步过滤
            if request.filters:
                if not _apply_filters(prop, request.filters):
                    continue
            
            property_markers.append(PropertyMarker(
                id=prop["id"],
                title=prop["title"],
                latitude=prop["location"]["lat"],
                longitude=prop["location"]["lng"],
                property_type=prop["property_type"],
                area=prop["area"],
                rent_price=prop["rent_price"],
                sale_price=prop["sale_price"],
                address=prop["address"],
                thumbnail_url=None,  # 后续集成媒体服务
                is_featured=False,   # 后续集成房源特色标记
                is_urgent=False,     # 后续集成紧急标记
                created_at=prop["created_at"]
            ))
        
        # 计算搜索元数据
        search_time_ms = int((time.time() - start_time) * 1000)
        
        response = MapSearchResponse(
            properties=property_markers,
            total_count=len(property_markers),
            bounds=request.bounds,
            filters_applied=request.filters,
            search_metadata={
                "search_time_ms": search_time_ms,
                "cache_hit": False,
                "total_in_bounds": len(property_markers),
                "view_mode": request.view_mode
            }
        )
        
        logger.info(f"地图找房搜索完成: 找到{len(property_markers)}个房源，耗时{search_time_ms}ms")
        return response
        
    except ValidationError as e:
        logger.warning(f"参数验证失败: {str(e)}")
        raise HTTPException(status_code=422, detail=str(e))
    except BusinessError as e:
        logger.error(f"业务逻辑错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"地图搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get(
    "/clusters",
    summary="获取地图聚合信息",
    description="""
    获取地图上的房源聚合信息，用于优化大量标记的显示。
    
    **功能特点**：
    - 智能聚合算法
    - 支持多级缩放
    - 性能优化
    
    **使用场景**：
    - 地图缩放时的聚合显示
    - 大量房源的性能优化
    """
)
async def get_property_clusters(
    north_lat: float = Query(..., ge=-90, le=90, description="北边界纬度"),
    south_lat: float = Query(..., ge=-90, le=90, description="南边界纬度"),
    east_lng: float = Query(..., ge=-180, le=180, description="东边界经度"),
    west_lng: float = Query(..., ge=-180, le=180, description="西边界经度"),
    zoom_level: int = Query(10, ge=1, le=20, description="地图缩放级别"),
    session: AsyncSession = Depends(get_async_session)
):
    """
    获取地图聚合信息（未来功能）
    """
    try:
        # 这是一个高级功能，暂时返回简化的聚合信息
        # 后续可以实现基于PostGIS的空间聚合算法
        
        return {
            "clusters": [
                {
                    "center": {"lat": (north_lat + south_lat) / 2, "lng": (east_lng + west_lng) / 2},
                    "count": 10,
                    "bounds": {
                        "north": north_lat,
                        "south": south_lat,
                        "east": east_lng,
                        "west": west_lng
                    }
                }
            ],
            "zoom_level": zoom_level,
            "total_properties": 10
        }
        
    except Exception as e:
        logger.error(f"获取聚合信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


def _apply_filters(property_data: Dict[str, Any], filters: MapSearchFilters) -> bool:
    """
    应用筛选条件
    """
    try:
        # 房源类型筛选
        if filters.property_types:
            if property_data["property_type"] not in filters.property_types:
                return False
        
        # 价格范围筛选
        if filters.price_range:
            price = property_data.get("rent_price") or property_data.get("sale_price") or 0
            if filters.price_range.min_price and price < filters.price_range.min_price:
                return False
            if filters.price_range.max_price and price > filters.price_range.max_price:
                return False
        
        # 面积范围筛选
        if filters.area_range:
            area = property_data.get("area") or 0
            if filters.area_range.min_area and area < filters.area_range.min_area:
                return False
            if filters.area_range.max_area and area > filters.area_range.max_area:
                return False
        
        # 关键词筛选
        if filters.keywords:
            keywords = filters.keywords.lower()
            title = property_data.get("title", "").lower()
            address = property_data.get("address", "").lower()
            if keywords not in title and keywords not in address:
                return False
        
        # 其他筛选条件可以在这里添加
        # 如停车位、电梯等特性筛选
        
        return True
        
    except Exception as e:
        logger.error(f"筛选条件应用失败: {str(e)}")
        return True  # 出错时不过滤
