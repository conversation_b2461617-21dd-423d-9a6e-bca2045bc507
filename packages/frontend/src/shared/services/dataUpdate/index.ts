/**
 * 通用数据更新系统 - 统一导出和初始化
 * @fileoverview 提供统一的服务初始化和导出
 * <AUTHOR> Assistant
 * @since 2025-08-01
 */

// 核心服务导出
export { EventBusService } from './EventBusService';
export { CacheInvalidationService } from './CacheInvalidationService';
export { OptimisticUpdateService } from './OptimisticUpdateService';
export { UniversalDataManager } from './UniversalDataManager';

// QueryClient导入
import { queryClient } from '../queryClient';

// Hook导出
export {
  useStatusToggle,
  useSimpleStatusToggle,
} from '../../hooks/useStatusToggle';

// 类型导出
export type {
  DataUpdateEvent,
  StatusUpdateEvent,
  EventListener,
  EventFilter,
} from './EventBusService';

export type {
  EntityType,
  InvalidationConfig,
} from './CacheInvalidationService';

export type { OptimisticUpdateConfig } from './OptimisticUpdateService';

export type {
  UpdateEntityConfig,
  StatusUpdateConfig,
  UpdateResult,
} from './UniversalDataManager';

// React Hook导出
export { useDataUpdateEvent, useStatusUpdateEvent } from './EventBusService';

export { useCacheInvalidation } from './CacheInvalidationService';

export { useOptimisticUpdate } from './OptimisticUpdateService';

export { useUniversalDataManager } from './UniversalDataManager';

/**
 * 获取系统状态（按需初始化）
 */
export const getDataUpdateSystemStatus = () => {
  return UniversalDataManager.getInstance().getServiceStatus();
};

/**
 * 手动初始化系统（可选，系统会自动按需初始化）
 * @deprecated 系统已改为按需初始化，无需手动调用
 */
export const initializeDataUpdateSystem = () => {
  console.warn(
    '🌐 [DataUpdateSystem] initializeDataUpdateSystem已废弃，系统现在使用按需初始化'
  );
  // 触发一次获取实例以确保初始化
  UniversalDataManager.getInstance();
};

// 默认导出
export default {
  EventBusService,
  CacheInvalidationService,
  OptimisticUpdateService,
  UniversalDataManager,
  getDataUpdateSystemStatus,
  initializeDataUpdateSystem, // 保留兼容性，但已废弃
};
