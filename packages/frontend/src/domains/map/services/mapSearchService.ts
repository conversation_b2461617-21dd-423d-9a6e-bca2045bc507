/**
 * 地图搜索服务 - 企业级统一转换层集成版本
 * 与后端API通信，执行圆形区域房源搜索
 * 严格遵循AI编码指导文件的统一转换层规范
 */

import { API_BASE_URL } from '../../../config/api';
import { Transformers } from '../../../shared/services/dataTransform';
import type {
  MapTransformer,
  PostGISPropertyResult,
  MapMarker,
  FrontendSearchState
} from '../../../shared/services/dataTransform/transformers/MapTransformer';

// 使用统一转换层的类型定义，移除重复定义
interface SearchCircleParams {
  center: { latitude: number; longitude: number };
  radius: number;
  query?: string;
  propertyType?: string;
  minPrice?: number;
  maxPrice?: number;
  limit?: number;
}

// 后端API响应类型（PostGIS查询结果）
interface MapSearchResponse {
  properties: PostGISPropertyResult[];
  total: number;
  search_center: { lat: number; lng: number };
  search_radius: number;
}

class MapSearchService {
  private baseURL: string;

  constructor() {
    this.baseURL = `${API_BASE_URL}/api/map`;
  }

  /**
   * 圆形区域搜索房源
   * 专家的PostGIS查询 + 企业级统一转换层集成
   */
  async searchInCircle(params: SearchCircleParams): Promise<MapMarker[]> {
    try {
      console.log('🗺️ 地图搜索请求:', {
        center: params.center,
        radius: params.radius,
        query: params.query,
      });

      // 使用统一转换层转换前端搜索状态为API参数
      const searchState: FrontendSearchState = {
        center: params.center,
        searchRadius: params.radius,
        selectedPropertyType: params.propertyType || '',
        priceRange: { min: params.minPrice, max: params.maxPrice },
        maxResults: params.limit || 50,
      };

      const transformResult = Transformers.map.searchStateToAPI(searchState);
      if (!transformResult.success) {
        throw new Error(`参数转换失败: ${transformResult.error}`);
      }

      const requestBody = transformResult.data;

      const response = await fetch(`${this.baseURL}/search-circle`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`搜索失败: ${response.status} - ${errorText}`);
      }

      const data: MapSearchResponse = await response.json();

      console.log('✅ 地图搜索API响应:', {
        count: data.properties.length,
        total: data.total,
        center: data.search_center,
        radius: data.search_radius,
      });

      // 使用统一转换层转换PostGIS结果为前端地图标记
      const markersResult = Transformers.map.postgisToMarkers(data.properties);
      if (!markersResult.success) {
        throw new Error(`数据转换失败: ${markersResult.error}`);
      }

      console.log('✅ 地图标记转换成功:', {
        markersCount: markersResult.data?.length || 0,
      });

      return markersResult.data || [];
    } catch (error) {
      console.error('❌ 地图搜索服务错误:', error);
      throw error;
    }
  }

  /**
   * 简化版搜索 (GET请求)
   * 兼容专家原始设计
   */
  async searchInCircleSimple(
    lat: number,
    lng: number,
    radius: number = 1000,
    propertyType?: string,
    limit: number = 50
  ): Promise<MapMarker[]> {
    try {
      const params = new URLSearchParams({
        lat: lat.toString(),
        lng: lng.toString(),
        radius: radius.toString(),
        limit: limit.toString(),
      });

      if (propertyType) {
        params.append('property_type', propertyType);
      }

      const response = await fetch(`${this.baseURL}/search-circle-simple?${params}`);

      if (!response.ok) {
        throw new Error(`搜索失败: ${response.status}`);
      }

      const data: MapSearchResponse = await response.json();

      // 使用统一转换层转换结果
      const markersResult = Transformers.map.postgisToMarkers(data.properties);
      if (!markersResult.success) {
        throw new Error(`数据转换失败: ${markersResult.error}`);
      }

      return markersResult.data || [];
    } catch (error) {
      console.error('❌ 简化地图搜索错误:', error);
      throw error;
    }
  }

  /**
   * 获取房源详细信息
   */
  async getPropertyDetail(propertyId: string): Promise<MapMarker | null> {
    try {
      const response = await fetch(`${this.baseURL}/property/${propertyId}`);

      if (!response.ok) {
        throw new Error(`获取房源详情失败: ${response.status}`);
      }

      const data = await response.json();

      // 如果有数据，使用统一转换层转换
      if (data.property) {
        const markersResult = Transformers.map.postgisToMarkers([data.property]);
        if (markersResult.success && markersResult.data && markersResult.data.length > 0) {
          return markersResult.data[0];
        }
      }

      return null;
    } catch (error) {
      console.error('❌ 获取房源详情错误:', error);
      throw error;
    }
  }

  /**
   * 批量获取房源信息
   */
  async getPropertiesByIds(propertyIds: string[]): Promise<MapMarker[]> {
    if (propertyIds.length === 0) {
      return [];
    }

    try {
      const response = await fetch(`${this.baseURL}/properties/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ property_ids: propertyIds }),
      });

      if (!response.ok) {
        throw new Error(`批量获取房源失败: ${response.status}`);
      }

      const data = await response.json();

      // 使用统一转换层转换结果
      const markersResult = Transformers.map.postgisToMarkers(data.properties || []);
      if (!markersResult.success) {
        throw new Error(`数据转换失败: ${markersResult.error}`);
      }

      return markersResult.data || [];
    } catch (error) {
      console.error('❌ 批量获取房源错误:', error);
      throw error;
    }
  }

  /**
   * 搜索热点区域
   * 返回用户搜索频率高的区域
   */
  async getHotSearchAreas(city?: string): Promise<Array<{
    name: string;
    center: { latitude: number; longitude: number };
    radius: number;
    search_count: number;
  }>> {
    try {
      const params = city ? `?city=${encodeURIComponent(city)}` : '';
      const response = await fetch(`${this.baseURL}/hot-areas${params}`);

      if (!response.ok) {
        throw new Error(`获取热点区域失败: ${response.status}`);
      }

      const data = await response.json();
      return data.hot_areas || [];
    } catch (error) {
      console.error('❌ 获取热点区域错误:', error);
      return [];
    }
  }

  /**
   * 记录搜索行为
   * 用于分析和优化
   */
  async recordSearchAction(params: {
    center: { latitude: number; longitude: number };
    radius: number;
    query?: string;
    results_count: number;
    user_id?: string;
  }): Promise<void> {
    try {
      await fetch(`${this.baseURL}/search-analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          search_center: params.center,
          search_radius: params.radius,
          search_query: params.query,
          results_count: params.results_count,
          user_id: params.user_id,
          timestamp: new Date().toISOString(),
        }),
      });
    } catch (error) {
      // 分析数据记录失败不影响主要功能
      console.warn('⚠️ 搜索行为记录失败:', error);
    }
  }
}

// 导出单例实例
export const mapSearchService = new MapSearchService();

// 导出类型（使用统一转换层的类型）
export type { SearchCircleParams, MapSearchResponse };
export type { MapMarker, PostGISPropertyResult, FrontendSearchState } from '../../../shared/services/dataTransform/transformers/MapTransformer';
