/**
 * PropertyDetailScreen 组件样式
 * 统一的样式定义文件
 */

import { StyleSheet } from 'react-native';
import { PropertyDetailDesignSystem } from '@property/styles/propertyDetailDesignSystem';
import { wp, hp } from '@shared/utils/responsive';

const { colors, typography, spacing, borderRadius } = PropertyDetailDesignSystem;

export const propertyDetailStyles = StyleSheet.create({
  // 主容器 - 浅灰色背景
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5', // 浅灰色背景，参考微信、支付宝等APP
  },

  // 加载容器
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
  },

  // 加载文字
  loadingText: {
    ...typography.body,
    color: colors.text.secondary,
  },

  // 错误容器
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
  },

  // 错误文字
  errorText: {
    ...typography.body,
    color: colors.status.error,
    marginBottom: spacing.md,
  },

  // 重试按钮
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.sm,
  },

  // 重试按钮文字
  retryButtonText: {
    ...typography.button.secondary,
    color: colors.text.inverse,
  },

  // 重试文字
  retryText: {
    ...typography.button.secondary,
    color: colors.text.inverse,
  },

  // 滚动视图
  scrollView: {
    flex: 1,
  },

  // 底部间距
  bottomSpacing: {
    height: hp(100), // 为底部操作栏留出空间
  },
});