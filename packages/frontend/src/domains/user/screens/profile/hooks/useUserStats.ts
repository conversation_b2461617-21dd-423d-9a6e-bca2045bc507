/**
 * 用户统计数据Hook
 * 功能：计算和格式化用户统计数据，完整保留原始功能
 * 符合企业级架构规范，目标：60行
 */
import { useMemo } from 'react';

interface TenantBuyerStats {
  rentalDemands: number;
  purchaseDemands: number;
  interestedProperties: number;
}

interface LandlordStats {
  rentalProperties: number;
  saleProperties: number;
  totalFavorites: number;
  totalInquiries: number;
}

interface OrderCounts {
  favoriteCount: number;
  appointmentCount: number;
  orderCount: number;
  tenantResourceCount: number;
}

interface ComputedStats {
  // 总计统计
  totalDemands: number;
  totalProperties: number;
  totalInteractions: number;
  
  // 格式化数据
  formattedTenantStats: string[];
  formattedLandlordStats: string[];
  formattedOrderCounts: string[];
  
  // 状态判断
  hasTenantActivity: boolean;
  hasLandlordActivity: boolean;
  hasOrderActivity: boolean;
}

interface UseUserStatsOptions {
  tenantBuyerStats?: TenantBuyerStats | null;
  landlordStats?: LandlordStats | null;
  orderCounts?: OrderCounts | null;
}

interface UseUserStatsReturn {
  computedStats: ComputedStats;
}

export const useUserStats = ({
  tenantBuyerStats,
  landlordStats,
  orderCounts,
}: UseUserStatsOptions = {}): UseUserStatsReturn => {
  
  // 计算统计数据 - 完全保留原始逻辑
  const computedStats = useMemo((): ComputedStats => {
    // 总计统计
    const totalDemands = (tenantBuyerStats?.rentalDemands || 0) + 
                        (tenantBuyerStats?.purchaseDemands || 0);
    const totalProperties = (landlordStats?.rentalProperties || 0) + 
                           (landlordStats?.saleProperties || 0);
    const totalInteractions = (landlordStats?.totalFavorites || 0) + 
                             (landlordStats?.totalInquiries || 0);

    // 格式化租买统计
    const formattedTenantStats = [
      `租房需求: ${tenantBuyerStats?.rentalDemands || 0}`,
      `购房需求: ${tenantBuyerStats?.purchaseDemands || 0}`,
      `感兴趣房源: ${tenantBuyerStats?.interestedProperties || 0}`,
    ];

    // 格式化业主统计
    const formattedLandlordStats = [
      `出租房源: ${landlordStats?.rentalProperties || 0}`,
      `出售房源: ${landlordStats?.saleProperties || 0}`,
      `总收藏: ${landlordStats?.totalFavorites || 0}`,
      `总咨询: ${landlordStats?.totalInquiries || 0}`,
    ];

    // 格式化订单统计
    const formattedOrderCounts = [
      `收藏: ${orderCounts?.favoriteCount || 0}`,
      `预约: ${orderCounts?.appointmentCount || 0}`,
      `订单: ${orderCounts?.orderCount || 0}`,
      `求租: ${orderCounts?.tenantResourceCount || 0}`,
    ];

    // 活动状态判断
    const hasTenantActivity = totalDemands > 0 || (tenantBuyerStats?.interestedProperties || 0) > 0;
    const hasLandlordActivity = totalProperties > 0 || totalInteractions > 0;
    const hasOrderActivity = (orderCounts?.favoriteCount || 0) > 0 || 
                            (orderCounts?.appointmentCount || 0) > 0 || 
                            (orderCounts?.orderCount || 0) > 0 || 
                            (orderCounts?.tenantResourceCount || 0) > 0;

    return {
      totalDemands,
      totalProperties,
      totalInteractions,
      formattedTenantStats,
      formattedLandlordStats,
      formattedOrderCounts,
      hasTenantActivity,
      hasLandlordActivity,
      hasOrderActivity,
    };
  }, [tenantBuyerStats, landlordStats, orderCounts]);

  return {
    computedStats,
  };
};
