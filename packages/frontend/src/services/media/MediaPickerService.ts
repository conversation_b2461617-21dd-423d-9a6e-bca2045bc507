/**
 * 企业级媒体选择器服务
 * 
 * 功能特性：
 * - 支持图片和视频选择
 * - 自动压缩和优化
 * - 权限管理
 * - 错误处理
 * - 类型安全
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月11日
 */

import * as ImagePicker from 'expo-image-picker';
import {} from 'react-native';
import FeedbackService from '../../shared/services/FeedbackService';

export interface MediaPickerOptions {
  mediaTypes: 'images' | 'videos' | 'all';
  allowsEditing?: boolean;
  quality?: number;
  allowsMultipleSelection?: boolean;
  selectionLimit?: number;
  videoMaxDuration?: number;
}

export interface MediaAsset {
  uri: string;
  type: 'image' | 'video';
  fileName?: string;
  fileSize?: number;
  width?: number;
  height?: number;
  duration?: number;
  mimeType?: string;
}

export interface MediaPickerResult {
  success: boolean;
  assets?: MediaAsset[];
  error?: string;
  cancelled?: boolean;
}

class MediaPickerService {
  /**
   * 请求相机权限
   */
  async requestCameraPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (status !== 'granted') {
        FeedbackService.showInfo('需要相机权限才能拍照，请在设置中开启相机权限');
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('请求相机权限失败:', error);
      return false;
    }
  }

  /**
   * 请求媒体库权限
   */
  async requestMediaLibraryPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        FeedbackService.showInfo('需要相册权限才能选择图片，请在设置中开启相册权限');
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('请求媒体库权限失败:', error);
      return false;
    }
  }

  /**
   * 从相册选择媒体文件
   */
  async pickFromLibrary(options: MediaPickerOptions = { mediaTypes: 'images' }): Promise<MediaPickerResult> {
    try {
      // 检查权限
      const hasPermission = await this.requestMediaLibraryPermissions();
      if (!hasPermission) {
        return { success: false, error: '没有相册权限' };
      }

      // 转换媒体类型
      let mediaTypes: ImagePicker.MediaTypeOptions;
      switch (options.mediaTypes) {
        case 'images':
          mediaTypes = ImagePicker.MediaTypeOptions.Images;
          break;
        case 'videos':
          mediaTypes = ImagePicker.MediaTypeOptions.Videos;
          break;
        case 'all':
          mediaTypes = ImagePicker.MediaTypeOptions.All;
          break;
        default:
          mediaTypes = ImagePicker.MediaTypeOptions.Images;
      }

      // 选择媒体文件
      const isMultipleSelection = options.allowsMultipleSelection ?? false;
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes,
        allowsEditing: isMultipleSelection ? false : (options.allowsEditing ?? true), // 多选时禁用编辑
        quality: options.quality ?? 0.8,
        allowsMultipleSelection: isMultipleSelection,
        selectionLimit: options.selectionLimit ?? 1,
        videoMaxDuration: options.videoMaxDuration ?? 300, // 5分钟 = 300秒
        exif: false, // 不包含EXIF数据以保护隐私
      });

      if (result.canceled) {
        return { success: false, cancelled: true };
      }

      // 转换结果格式
      const assets: MediaAsset[] = result.assets.map(asset => ({
        uri: asset.uri,
        type: asset.type === 'video' ? 'video' : 'image',
        fileName: asset.fileName || undefined,
        fileSize: asset.fileSize,
        width: asset.width,
        height: asset.height,
        duration: asset.duration || undefined,
        mimeType: asset.mimeType,
      }));

      return { success: true, assets };

    } catch (error) {
      console.error('从相册选择媒体失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '选择媒体失败' 
      };
    }
  }

  /**
   * 使用相机拍照或录像
   */
  async captureWithCamera(options: MediaPickerOptions = { mediaTypes: 'images' }): Promise<MediaPickerResult> {
    try {
      // 检查权限
      const hasPermission = await this.requestCameraPermissions();
      if (!hasPermission) {
        return { success: false, error: '没有相机权限' };
      }

      // 转换媒体类型
      let mediaTypes: ImagePicker.MediaTypeOptions;
      switch (options.mediaTypes) {
        case 'images':
          mediaTypes = ImagePicker.MediaTypeOptions.Images;
          break;
        case 'videos':
          mediaTypes = ImagePicker.MediaTypeOptions.Videos;
          break;
        case 'all':
          mediaTypes = ImagePicker.MediaTypeOptions.All;
          break;
        default:
          mediaTypes = ImagePicker.MediaTypeOptions.Images;
      }

      // 拍照或录像
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes,
        allowsEditing: options.allowsEditing ?? true,
        quality: options.quality ?? 0.8,
        videoMaxDuration: options.videoMaxDuration ?? 300, // 5分钟 = 300秒
        exif: false, // 不包含EXIF数据以保护隐私
      });

      if (result.canceled) {
        return { success: false, cancelled: true };
      }

      // 转换结果格式
      const assets: MediaAsset[] = result.assets.map(asset => ({
        uri: asset.uri,
        type: asset.type === 'video' ? 'video' : 'image',
        fileName: asset.fileName || undefined,
        fileSize: asset.fileSize,
        width: asset.width,
        height: asset.height,
        duration: asset.duration || undefined,
        mimeType: asset.mimeType,
      }));

      return { success: true, assets };

    } catch (error) {
      console.error('相机拍摄失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '拍摄失败' 
      };
    }
  }

  /**
   * 显示媒体选择器选项 - 修复版本，提供实际选择功能
   */
  async showMediaPicker(options: MediaPickerOptions = { mediaTypes: 'images' }): Promise<MediaPickerResult> {
    try {
      const mediaTypeText = options.mediaTypes === 'videos' ? '视频' : 
                           options.mediaTypes === 'all' ? '媒体文件' : '图片';
      
      // 简化版本：直接调用相册选择，不需要用户选择来源
      // 这样用户点击后直接打开相册，简化操作流程
      console.log(`[MediaPickerService] 直接打开相册选择${mediaTypeText}...`);
      
      return await this.pickFromLibrary(options);
      
    } catch (error) {
      console.error('[MediaPickerService] showMediaPicker失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '打开媒体选择器失败' 
      };
    }
  }

  /**
   * 验证媒体文件 - 支持横竖屏混合上传
   */
  validateMediaAsset(asset: MediaAsset, maxSizeInMB: number = 10): { valid: boolean; error?: string; info?: string } {
    // 检查文件大小
    if (asset.fileSize && asset.fileSize > maxSizeInMB * 1024 * 1024) {
      return { valid: false, error: `文件大小不能超过${maxSizeInMB}MB` };
    }

    // 检查图片尺寸
    if (asset.type === 'image') {
      if (asset.width && asset.height) {
        if (asset.width < 100 || asset.height < 100) {
          return { valid: false, error: '图片尺寸太小，最小100x100像素' };
        }
        if (asset.width > 4096 || asset.height > 4096) {
          return { valid: false, error: '图片尺寸太大，最大4096x4096像素' };
        }
      }
    }

    // 检查视频尺寸和方向 - 支持横竖屏混合
    if (asset.type === 'video') {
      // 检查视频时长 (duration是毫秒，需要转换为秒)
      if (asset.duration) {
        const durationInSeconds = asset.duration / 1000;
        if (durationInSeconds > 300) { // 5分钟 = 300秒
          return {
            valid: false,
            error: `视频时长${Math.round(durationInSeconds)}秒，不能超过5分钟(300秒)`
          };
        }
      }

      // 智能识别视频方向并给出友好提示
      if (asset.width && asset.height) {
        const isPortrait = asset.height > asset.width;

        if (isPortrait) {
          return {
            valid: true,
            info: `竖屏视频 ${asset.width}×${asset.height} - 将通过阿里云自动优化显示效果`
          };
        } else {
          return {
            valid: true,
            info: `横屏视频 ${asset.width}×${asset.height} - 推荐的房产展示格式`
          };
        }
      }
    }

    return { valid: true };
  }
}

// 导出单例
export const mediaPickerService = new MediaPickerService();
export default mediaPickerService;
