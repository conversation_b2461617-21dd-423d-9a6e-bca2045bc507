/**
 * 搜索栏主题配置
 * 定义不同页面搜索栏的样式主题
 */

import { Colors } from '../../theme/designSystem';
import { SearchBarStyleConfig, SearchBarTheme } from './types';

/**
 * 主题配置映射
 */
export const searchBarThemes: Record<SearchBarTheme, SearchBarStyleConfig> = {
  // 首页主题 - 基于HomepageTheme
  homepage: {
    container: {
      backgroundColor: Colors.background.primary,
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    searchBox: {
      backgroundColor: Colors.neutral[100],
      borderRadius: 20,
      height: 40,
      paddingHorizontal: 12,
    },
    text: {
      fontSize: 14,
      color: Colors.neutral[700],
      placeholderColor: Colors.neutral[500],
    },
    icon: {
      size: 18,
      color: Colors.neutral[500],
    },
  },

  // 房源页面主题
  property: {
    container: {
      backgroundColor: Colors.background.primary,
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    searchBox: {
      backgroundColor: Colors.neutral[100],
      borderRadius: 25,
      height: 40,
      paddingHorizontal: 16,
    },
    text: {
      fontSize: 14,
      color: Colors.neutral[700],
      placeholderColor: Colors.neutral[500],
    },
    icon: {
      size: 16,
      color: Colors.neutral[500],
    },
  },

  // 地图页面主题
  map: {
    container: {
      backgroundColor: Colors.background.primary,
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    searchBox: {
      backgroundColor: Colors.background.secondary,
      borderRadius: 8,
      height: 44,
      paddingHorizontal: 12,
    },
    text: {
      fontSize: 16,
      color: Colors.neutral[700],
      placeholderColor: Colors.neutral[500],
    },
    icon: {
      size: 20,
      color: Colors.neutral[500],
    },
  },

  // 极简主题
  minimal: {
    container: {
      backgroundColor: 'transparent',
      paddingHorizontal: 12,
      paddingVertical: 6,
    },
    searchBox: {
      backgroundColor: Colors.neutral[50],
      borderRadius: 18,
      height: 32,
      paddingHorizontal: 10,
    },
    text: {
      fontSize: 12,
      color: Colors.neutral[700],
      placeholderColor: Colors.neutral[400],
    },
    icon: {
      size: 14,
      color: Colors.neutral[400],
    },
  },
};

/**
 * 获取主题配置
 */
export const getSearchBarTheme = (theme: SearchBarTheme): SearchBarStyleConfig => {
  return searchBarThemes[theme];
};

/**
 * 动画配置
 */
export const animationConfigs = {
  // 轮播动画配置
  carousel: {
    interval: 3000, // 3秒轮播
    duration: 300,  // 切换动画时长
  },

  // 滚动隐藏动画配置
  scrollHide: {
    inputRange: [0, 80],
    opacityRange: [1, 0],
    translateYRange: [0, -20],
    scaleRange: [1, 0.95],
  },

  // 滚动显示动画配置
  scrollShow: {
    inputRange: [20, 60],
    opacityRange: [0, 1],
    scaleInputRange: [20, 50, 60],
    scaleRange: [0.3, 1.1, 1],
  },
};
