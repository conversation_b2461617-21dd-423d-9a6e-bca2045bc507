/**
 * API数据清理工具
 *
 * 解决Stage2问题：清理API请求中的undefined值，防止JSON序列化问题
 * 确保发送给后端的数据格式完全符合要求
 */

/**
 * 深度清理对象中的undefined值
 * @param obj 要清理的对象
 * @returns 清理后的对象
 */
export function cleanUndefinedValues<T extends Record<string, any>>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj
      .filter(item => item !== undefined)
      .map(item =>
        typeof item === 'object' && item !== null
          ? cleanUndefinedValues(item)
          : item
      ) as T;
  }

  if (typeof obj === 'object') {
    const cleaned: Record<string, any> = {};

    for (const [key, value] of Object.entries(obj)) {
      // 跳过undefined值
      if (value === undefined) {
        console.log(`[apiDataCleaner] 移除undefined字段: ${key}`);
        continue;
      }

      // 跳过null值（根据业务需求决定）
      if (value === null) {
        console.log(`[apiDataCleaner] 移除null字段: ${key}`);
        continue;
      }

      // 递归清理嵌套对象
      if (typeof value === 'object' && value !== null) {
        const cleanedValue = cleanUndefinedValues(value);
        // 只有非空对象才保留
        if (Array.isArray(cleanedValue)) {
          if (cleanedValue.length > 0) {
            cleaned[key] = cleanedValue;
          } else {
            console.log(`[apiDataCleaner] 移除空数组字段: ${key}`);
          }
        } else if (Object.keys(cleanedValue).length > 0) {
          cleaned[key] = cleanedValue;
        } else {
          console.log(`[apiDataCleaner] 移除空对象字段: ${key}`);
        }
      } else {
        cleaned[key] = value;
      }
    }

    return cleaned as T;
  }

  return obj;
}

/**
 * 验证对象是否可以安全进行JSON序列化
 * @param obj 要验证的对象
 * @returns 验证结果
 */
export function validateSerializable(obj: any): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];

  function checkValue(value: any, path: string) {
    if (typeof value === 'function') {
      issues.push(`${path}: 包含函数，不可序列化`);
    } else if (typeof value === 'symbol') {
      issues.push(`${path}: 包含Symbol，不可序列化`);
    } else if (value === undefined) {
      issues.push(`${path}: 包含undefined值`);
    } else if (
      typeof value === 'number' &&
      (isNaN(value) || !isFinite(value))
    ) {
      issues.push(`${path}: 包含NaN或Infinity`);
    } else if (typeof value === 'object' && value !== null) {
      if (Array.isArray(value)) {
        value.forEach((item, index) => checkValue(item, `${path}[${index}]`));
      } else {
        Object.entries(value).forEach(([key, val]) => {
          checkValue(val, path ? `${path}.${key}` : key);
        });
      }
    }
  }

  checkValue(obj, '');

  return {
    isValid: issues.length === 0,
    issues,
  };
}

/**
 * 完整的API数据预处理
 * 清理undefined值并验证序列化安全性
 * @param data API请求数据
 * @returns 处理后的数据
 */
export function prepareAPIData<T extends Record<string, any>>(
  data: T
): {
  cleanedData: T;
  isValid: boolean;
  issues: string[];
} {
  console.log('[apiDataCleaner] 开始预处理API数据...');
  console.log(
    '[apiDataCleaner] 原始数据:',
    JSON.stringify(
      data,
      (key, value) => {
        if (typeof value === 'function') return '[Function]';
        if (typeof value === 'undefined') return '[Undefined]';
        if (typeof value === 'symbol') return '[Symbol]';
        return value;
      },
      2
    )
  );

  // 第一步：清理undefined值
  const cleanedData = cleanUndefinedValues(data);
  console.log(
    '[apiDataCleaner] 清理后数据:',
    JSON.stringify(cleanedData, null, 2)
  );

  // 第二步：验证序列化安全性
  const validation = validateSerializable(cleanedData);

  console.log(
    `[apiDataCleaner] 预处理完成 - 有效: ${validation.isValid}, 问题数: ${validation.issues.length}`
  );
  if (validation.issues.length > 0) {
    console.warn('[apiDataCleaner] 发现问题:', validation.issues);
  }

  return {
    cleanedData,
    isValid: validation.isValid,
    issues: validation.issues,
  };
}

/**
 * 为房源发布API特别优化的数据清理器
 * @param propertyData 房源发布数据
 * @returns 清理后的数据
 */
export function cleanPropertyAPIData(propertyData: any): any {
  console.log('[apiDataCleaner] 开始清理房源发布数据...');

  // 专门处理房源数据的已知问题字段
  const { cleanedData, isValid, issues } = prepareAPIData(propertyData);

  // 特别检查房源发布中常见的问题字段
  if (cleanedData.features && typeof cleanedData.features === 'object') {
    console.log(
      '[apiDataCleaner] ⚠️ 发现features字段，数据库中不存在此字段，将移除'
    );
    delete cleanedData.features;
  }

  // 确保必要的价格字段格式正确
  if (cleanedData.rent_price !== undefined && cleanedData.rent_price !== null) {
    cleanedData.rent_price = Number(cleanedData.rent_price);
    if (isNaN(cleanedData.rent_price)) {
      delete cleanedData.rent_price;
      console.log('[apiDataCleaner] 移除无效的rent_price字段');
    }
  }

  if (cleanedData.sale_price !== undefined && cleanedData.sale_price !== null) {
    cleanedData.sale_price = Number(cleanedData.sale_price);
    if (isNaN(cleanedData.sale_price)) {
      delete cleanedData.sale_price;
      console.log('[apiDataCleaner] 移除无效的sale_price字段');
    }
  }

  if (
    cleanedData.transfer_price !== undefined &&
    cleanedData.transfer_price !== null
  ) {
    cleanedData.transfer_price = Number(cleanedData.transfer_price);
    if (isNaN(cleanedData.transfer_price)) {
      delete cleanedData.transfer_price;
      console.log('[apiDataCleaner] 移除无效的transfer_price字段');
    }
  }

  console.log('[apiDataCleaner] 房源数据清理完成');
  console.log(
    '[apiDataCleaner] 最终数据:',
    JSON.stringify(cleanedData, null, 2)
  );

  return cleanedData;
}
