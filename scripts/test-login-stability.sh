#!/bin/bash

# 验证码登录稳定性测试脚本
# 用于验证崩溃修复是否有效

echo "🧪 开始验证码登录稳定性测试..."

# 1. 检查前端开发服务器状态
echo "📱 检查前端开发服务器..."
if pgrep -f "expo start" > /dev/null; then
    echo "✅ Expo开发服务器正在运行"
else
    echo "❌ Expo开发服务器未运行，请运行: cd packages/frontend && npx expo start"
    exit 1
fi

# 2. 检查后端API健康状态
echo "🔗 检查后端API健康状态..."
BACKEND_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8082/health)
if [ "$BACKEND_HEALTH" = "200" ]; then
    echo "✅ 后端API健康"
else
    echo "❌ 后端API不健康 (HTTP $BACKEND_HEALTH)"
    exit 1
fi

# 3. 验证验证码API端点
echo "📞 测试验证码API端点..."
CODE_API_RESPONSE=$(curl -s "http://localhost:8082/api/v1/auth/can-send-code?phone_number=13800138000&code_type=login")
if echo "$CODE_API_RESPONSE" | grep -q "can_send"; then
    echo "✅ 验证码API响应正常"
else
    echo "❌ 验证码API响应异常: $CODE_API_RESPONSE"
    exit 1
fi

# 4. 检查TypeScript编译状态
echo "🔧 检查TypeScript编译状态..."
cd packages/frontend
if npx tsc --noEmit --skipLibCheck --jsx react-native --esModuleInterop src/domains/auth/screens/EnhancedSmsLoginScreen.tsx src/shared/hooks/useVerificationCode.ts src/shared/services/verificationCodeService.ts > /dev/null 2>&1; then
    echo "✅ 关键文件TypeScript编译正常"
else
    echo "❌ TypeScript编译存在错误"
    npx tsc --noEmit --skipLibCheck --jsx react-native --esModuleInterop src/domains/auth/screens/EnhancedSmsLoginScreen.tsx src/shared/hooks/useVerificationCode.ts src/shared/services/verificationCodeService.ts
    exit 1
fi

echo ""
echo "🎉 所有稳定性检查通过！"
echo ""
echo "📋 测试建议："
echo "1. 在APK中测试验证码登录流程"
echo "2. 尝试在弱网络环境下登录"
echo "3. 快速切换页面测试异步状态管理"
echo "4. 观察控制台日志中的错误处理信息"
echo ""
echo "🔍 如果仍然崩溃，请查看："
echo "- Expo开发者工具的错误日志"
echo "- 设备的JavaScript控制台输出"
echo "- React DevTools的组件状态"