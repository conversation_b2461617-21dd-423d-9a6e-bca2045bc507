/**
 * 前端存储清理脚本
 * 用于清除可能污染的用户数据
 */

// 在APK的开发者控制台中执行以下代码：

// 1. 清除所有认证相关的存储数据
async function clearAllAuthData() {
  try {
    console.log('🧹 开始清除所有认证数据...');
    
    // 清除AsyncStorage中的认证数据
    const keysToDelete = [
      'access_token',
      'refresh_token', 
      'user_info',
      'last_code_request_time'
    ];
    
    for (const key of keysToDelete) {
      try {
        await AsyncStorage.removeItem(key);
        console.log(`✅ 已清除: ${key}`);
      } catch (error) {
        console.warn(`⚠️ 清除${key}失败:`, error);
      }
    }
    
    // 清除内存缓存
    if (window.memoryCache) {
      window.memoryCache.clear();
      console.log('✅ 已清除内存缓存');
    }
    
    // 重置AuthStore状态
    if (window.useAuthStore) {
      window.useAuthStore.setState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      });
      console.log('✅ 已重置AuthStore状态');
    }
    
    console.log('🎉 所有认证数据已清除完成');
    console.log('💡 请重新登录以获取正确的用户信息');
    
  } catch (error) {
    console.error('❌ 清除认证数据失败:', error);
  }
}

// 2. 验证当前存储的用户信息
async function checkStoredUserInfo() {
  try {
    console.log('🔍 检查当前存储的用户信息...');
    
    const userInfoStr = await AsyncStorage.getItem('user_info');
    const accessToken = await AsyncStorage.getItem('access_token');
    
    console.log('📱 存储的用户信息:', userInfoStr);
    console.log('🔑 存储的访问令牌:', accessToken ? '存在' : '不存在');
    
    if (userInfoStr) {
      const userInfo = JSON.parse(userInfoStr);
      console.log('👤 解析的用户信息:', {
        id: userInfo.id,
        phone_number: userInfo.phone_number,
        nickname: userInfo.nickname
      });
    }
    
  } catch (error) {
    console.error('❌ 检查存储信息失败:', error);
  }
}

// 3. 执行完整的清理和验证流程
async function fullCleanupAndVerify() {
  console.log('🚀 开始完整的清理和验证流程...');
  
  // 步骤1: 检查当前状态
  await checkStoredUserInfo();
  
  // 步骤2: 清除所有数据
  await clearAllAuthData();
  
  // 步骤3: 验证清理结果
  console.log('\n📋 验证清理结果:');
  await checkStoredUserInfo();
  
  console.log('\n✅ 清理完成！现在可以重新登录测试。');
}

// 执行清理
fullCleanupAndVerify();
