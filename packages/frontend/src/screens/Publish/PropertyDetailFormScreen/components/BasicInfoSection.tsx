/**
 * 基础信息区块组件
 * 
 * 职责：
 * 1. 房源类型选择
 * 2. 面积输入
 * 3. 楼层信息
 * 4. 朝向选择
 * 5. 装修程度选择
 */

import * as React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';

interface BasicInfoSectionProps {
  // 表单数据
  subType: string;
  area: string;
  floor: string;
  totalFloors: string;
  orientation: string;
  decorationLevel: string;
  
  // 更新函数
  onSubTypeChange: (value: string) => void;
  onAreaChange: (value: string) => void;
  onFloorChange: (value: string) => void;
  onTotalFloorsChange: (value: string) => void;
  onOrientationChange: (value: string) => void;
  onDecorationLevelChange: (value: string) => void;
  
  // 下拉框状态
  showDropdown: string | null;
  onDropdownToggle: (field: string | null) => void;
  
  // 字段位置记录
  onFieldLayout: (fieldName: string, layout: { y: number; height: number }) => void;
  
  // 配置数据
  subTypeConfig: any;
  orientationOptions: any[];
  decorationLevelConfig: any;
  
  // 错误状态
  errors: Record<string, string>;
}

export const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({
  subType,
  area,
  floor,
  totalFloors,
  orientation,
  decorationLevel,
  onSubTypeChange,
  onAreaChange,
  onFloorChange,
  onTotalFloorsChange,
  onOrientationChange,
  onDecorationLevelChange,
  showDropdown,
  onDropdownToggle,
  onFieldLayout,
  subTypeConfig,
  orientationOptions,
  decorationLevelConfig,
  errors,
}) => {
  
  const renderDropdown = (fieldName: string, options: any[], currentValue: string, onChange: (value: string) => void) => {
    if (showDropdown !== fieldName) return null;
    
    return (
      <View style={styles.dropdownContainer}>
        <TouchableOpacity 
          style={styles.dropdownOverlay}
          onPress={() => onDropdownToggle(null)}
        >
          <View style={styles.dropdownContent}>
            {options.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.dropdownItem,
                  currentValue === option.value && styles.dropdownItemSelected
                ]}
                onPress={() => {
                  onChange(option.value);
                  onDropdownToggle(null);
                }}
              >
                <Text style={[
                  styles.dropdownItemText,
                  currentValue === option.value && styles.dropdownItemTextSelected
                ]}>
                  {option.label}
                </Text>
                {option.description && (
                  <Text style={styles.dropdownItemDesc}>{option.description}</Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* 房源类型 */}
      <View 
        style={styles.formSection}
        onLayout={({ nativeEvent }) => onFieldLayout('sub_type', nativeEvent.layout)}
      >
        <TouchableOpacity
          style={styles.formRow}
          onPress={() => onDropdownToggle(showDropdown === 'sub_type' ? null : 'sub_type')}
        >
          <Text style={styles.rowLabel}>类型<Text style={styles.required}> *</Text></Text>
          <View style={styles.rowRight}>
            <Text style={[styles.rowValue, !subType && styles.placeholder]}>
              {subType ? subTypeConfig[subType]?.label : '请选择房源类型'}
            </Text>
            <Ionicons name="chevron-forward" size={16} color="#C7C7CC" />
          </View>
        </TouchableOpacity>
        {errors.sub_type && (
          <Text style={styles.fieldError}>{errors.sub_type}</Text>
        )}
      </View>

      {/* 面积 */}
      <View 
        style={styles.formSection}
        onLayout={({ nativeEvent }) => onFieldLayout('area', nativeEvent.layout)}
      >
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>面积<Text style={styles.required}> *</Text></Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              value={area}
              onChangeText={onAreaChange}
              placeholder="请输入面积"
              placeholderTextColor="#C7C7CC"
              keyboardType="numeric"
            />
            <Text style={styles.inputUnit}>㎡</Text>
          </View>
          {errors.area && (
            <Text style={styles.fieldError}>{errors.area}</Text>
          )}
        </View>
      </View>

      {/* 楼层信息 */}
      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>楼层信息</Text>
        <View style={styles.floorRowContainer}>
          <View style={styles.floorInputContainer}>
            <Text style={styles.floorInputLabel}>所在楼层</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                value={floor}
                onChangeText={onFloorChange}
                placeholder="楼层"
                placeholderTextColor="#C7C7CC"
                keyboardType="numeric"
              />
            </View>
          </View>
          <View style={styles.floorInputContainer}>
            <Text style={styles.floorInputLabel}>总楼层</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                value={totalFloors}
                onChangeText={onTotalFloorsChange}
                placeholder="总层数"
                placeholderTextColor="#C7C7CC"
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>
      </View>

      {/* 朝向 */}
      <View style={styles.formSection}>
        <TouchableOpacity
          style={styles.formRow}
          onPress={() => onDropdownToggle(showDropdown === 'orientation' ? null : 'orientation')}
        >
          <Text style={styles.rowLabel}>朝向</Text>
          <View style={styles.rowRight}>
            <Text style={[styles.rowValue, !orientation && styles.placeholder]}>
              {orientation || '请选择朝向'}
            </Text>
            <Ionicons name="chevron-forward" size={16} color="#C7C7CC" />
          </View>
        </TouchableOpacity>
      </View>

      {/* 装修程度 */}
      <View style={styles.formSection}>
        <TouchableOpacity
          style={styles.formRow}
          onPress={() => onDropdownToggle(showDropdown === 'decoration_level' ? null : 'decoration_level')}
        >
          <Text style={styles.rowLabel}>装修</Text>
          <View style={styles.rowRight}>
            <Text style={[styles.rowValue, !decorationLevel && styles.placeholder]}>
              {decorationLevel ? decorationLevelConfig[decorationLevel]?.label : '请选择装修程度'}
            </Text>
            <Ionicons name="chevron-forward" size={16} color="#C7C7CC" />
          </View>
        </TouchableOpacity>
      </View>

      {/* 下拉框渲染 */}
      {showDropdown === 'sub_type' && renderDropdown('sub_type', 
        Object.values(subTypeConfig), subType, onSubTypeChange)}
      {showDropdown === 'orientation' && renderDropdown('orientation', 
        orientationOptions, orientation, onOrientationChange)}
      {showDropdown === 'decoration_level' && renderDropdown('decoration_level', 
        Object.values(decorationLevelConfig), decorationLevel, onDecorationLevelChange)}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: wp(16),
    marginBottom: hp(6),
  },
  sectionTitle: {
    fontSize: fp(15),
    fontWeight: '600',
    color: '#000000',
    paddingTop: hp(16),
    paddingBottom: hp(12),
  },
  formRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#C6C6C8',
    minHeight: 44,
  },
  rowLabel: {
    fontSize: 17,
    color: '#000000',
    width: 60,
    fontWeight: '400',
  },
  rowRight: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  rowValue: {
    fontSize: 17,
    color: '#000000',
    marginRight: 8,
  },
  placeholder: {
    color: '#C7C7CC',
  },
  required: {
    color: '#FF3B30',
  },
  inputGroup: {
    paddingVertical: hp(12),
  },
  inputLabel: {
    fontSize: fp(14),
    fontWeight: '500',
    color: '#000000',
    marginBottom: hp(8),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: wp(12),
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    minHeight: hp(48),
  },
  textInput: {
    flex: 1,
    fontSize: fp(16),
    color: '#000000',
    padding: 0,
  },
  inputUnit: {
    fontSize: fp(16),
    color: '#8E8E93',
    marginLeft: wp(8),
  },
  floorRowContainer: {
    flexDirection: 'row',
    gap: wp(12),
  },
  floorInputContainer: {
    flex: 1,
  },
  floorInputLabel: {
    fontSize: fp(12),
    color: '#8E8E93',
    marginBottom: hp(6),
    fontWeight: '400',
  },
  fieldError: {
    fontSize: fp(12),
    color: '#FF3B30',
    marginTop: hp(4),
    marginLeft: wp(2),
    fontWeight: '500',
  },
  // 下拉框样式
  dropdownContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    zIndex: 1000,
  },
  dropdownOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdownContent: {
    backgroundColor: '#FFFFFF',
    maxHeight: '80%',
    width: '100%',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    position: 'absolute',
    bottom: 0,
  },
  dropdownItem: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: '#F0F0F0',
  },
  dropdownItemSelected: {
    backgroundColor: '#F2F2F7',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#000000',
    fontWeight: '400',
  },
  dropdownItemTextSelected: {
    color: '#007AFF',
    fontWeight: '600',
  },
  dropdownItemDesc: {
    fontSize: 13,
    color: '#8E8E93',
    marginTop: 2,
  },
});

export default BasicInfoSection;