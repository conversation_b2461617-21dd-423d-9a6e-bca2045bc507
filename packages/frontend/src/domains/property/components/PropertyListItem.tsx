/**
 * 🏗️ 企业级架构：通用房源列表项组件
 * 基于DemandListItem的设计模式，适配房源数据结构
 * 遵循前端企业级架构规范 - 完整五层架构
 */

import React, { useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// 🏗️ 企业级架构：类型定义
export type PropertyStatus =
  | 'draft'      // 草稿
  | 'published'  // 已发布
  | 'inactive'   // 已下架
  | 'sold'       // 已售出
  | 'rented'     // 已出租
  | 'expired';   // 已过期

import { spacing, fontSize, borderRadius } from '../../../shared/utils/responsiveUtils';

// 🔧 API层：房源服务
import { PropertyAPI } from '../services/propertyAPI';

interface PropertyListItemProps {
  item: any; // 🔧 使用any类型以兼容API原始数据和现有Property接口
  onPress: (item: any) => void;
  onEdit: (item: any) => void;
  onDelete: (item: any) => void;
  onStatusChange?: (item: any, newStatus: PropertyStatus) => void;
  onRefresh?: () => void; // 添加刷新回调

  // 🔧 兼容现有MyPropertiesScreen的功能
  isDraft?: boolean; // 是否为草稿
  showActions?: boolean; // 是否显示操作按钮
  showStats?: boolean; // 是否显示统计信息
  customLayout?: 'default' | 'draft' | 'published'; // 自定义布局类型
}

export const PropertyListItem: React.FC<PropertyListItemProps> = ({
  item,
  onPress,
  onEdit,
  onDelete,
  onStatusChange,
  onRefresh,
}) => {
  // 🎨 UI层：状态显示文本
  const getStatusText = (status: PropertyStatus) => {
    switch (status) {
      case 'draft': return '草稿';
      case 'published': return '已发布';
      case 'inactive': return '已下架';
      case 'sold': return '已售出';
      case 'rented': return '已出租';
      case 'expired': return '已过期';
      default: return '未知';
    }
  };

  // 🎨 UI层：状态颜色
  const getStatusColor = (status: PropertyStatus) => {
    switch (status) {
      case 'draft': return '#999999';
      case 'published': return '#52c41a';
      case 'inactive': return '#faad14';
      case 'sold': return '#1890ff';
      case 'rented': return '#1890ff';
      case 'expired': return '#ff4d4f';
      default: return '#666666';
    }
  };

  // 🎨 UI层：房源类型显示
  const getPropertyTypeText = (type: string) => {
    switch (type) {
      case 'OFFICE': return '写字楼';
      case 'SHOP': return '商铺';
      case 'WAREHOUSE': return '仓库';
      case 'FACTORY': return '厂房';
      case 'APARTMENT': return '公寓';
      case 'HOUSE': return '别墅';
      default: return type;
    }
  };

  // 🎨 UI层：价格格式化
  const formatPrice = (item: any) => {
    if (item.rent_price) {
      return `${item.rent_price}元/月`;
    }
    if (item.sale_price) {
      return `${item.sale_price}万元`;
    }
    return '价格面议';
  };

  // 🎨 UI层：面积格式化
  const formatArea = (item: any) => {
    if (item.total_area) {
      return `${item.total_area}㎡`;
    }
    return '面积待定';
  };

  // 🔧 事件处理：状态变更
  const handleStatusChange = useCallback(async (newStatus: PropertyStatus) => {
    try {
      console.log(`[PropertyListItem] 🔄 变更房源状态: ${item.id} -> ${newStatus}`);
      
      // 调用API更新状态
      const result = await PropertyAPI.updatePropertyStatus(item.id, newStatus);
      
      if (result.success) {
        Alert.alert('成功', `房源状态已更新为${getStatusText(newStatus)}`);
        onStatusChange?.(item, newStatus);
        onRefresh?.(); // 触发列表刷新
      } else {
        Alert.alert('失败', result.message || '状态更新失败');
      }
    } catch (error: any) {
      console.error('[PropertyListItem] 💥 状态变更失败:', error);
      Alert.alert('错误', '网络错误，请稍后重试');
    }
  }, [item, onStatusChange, onRefresh]);

  // 🔧 事件处理：删除房源
  const handleDelete = useCallback(async () => {
    Alert.alert(
      '确认删除',
      '删除后无法恢复，确定要删除这个房源吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log(`[PropertyListItem] 🗑️ 删除房源: ${item.id}`);
              
              const result = await PropertyAPI.deleteProperty(item.id);
              
              if (result.success) {
                Alert.alert('成功', '房源已删除');
                onDelete(item);
                onRefresh?.(); // 触发列表刷新
              } else {
                Alert.alert('失败', result.message || '删除失败');
              }
            } catch (error: any) {
              console.error('[PropertyListItem] 💥 删除失败:', error);
              Alert.alert('错误', '网络错误，请稍后重试');
            }
          },
        },
      ]
    );
  }, [item, onDelete, onRefresh]);

  // 🎨 UI层：渲染操作按钮 - 恢复完整功能
  const renderActionButtons = () => {
    const actions = [];

    // 查看按钮（所有状态都可查看）
    actions.push(
      <TouchableOpacity
        key="view"
        style={[styles.actionButton, styles.viewButton]}
        onPress={() => onPress(item)}
      >
        <Text style={[styles.actionButtonText, styles.viewButtonText]}>查看</Text>
      </TouchableOpacity>
    );

    // 编辑按钮（草稿和下架状态可编辑）
    if (item.status === 'draft' || item.status === 'inactive') {
      actions.push(
        <TouchableOpacity
          key="edit"
          style={[styles.actionButton, styles.editButton]}
          onPress={() => onEdit(item)}
        >
          <Text style={[styles.actionButtonText, styles.editButtonText]}>编辑</Text>
        </TouchableOpacity>
      );
    }

    // 下架按钮（仅发布状态）
    if (item.status === 'published') {
      actions.push(
        <TouchableOpacity
          key="offline"
          style={styles.actionButton}
          onPress={() => handleStatusChange('inactive')}
        >
          <Text style={styles.actionButtonText}>下架</Text>
        </TouchableOpacity>
      );
    }

    // 上架按钮（仅下架状态）
    if (item.status === 'inactive') {
      actions.push(
        <TouchableOpacity
          key="online"
          style={styles.actionButton}
          onPress={() => handleStatusChange('published')}
        >
          <Text style={styles.actionButtonText}>上架</Text>
        </TouchableOpacity>
      );
    }

    // 删除按钮（草稿和下架状态可删除）
    if (item.status === 'draft' || item.status === 'inactive') {
      actions.push(
        <TouchableOpacity
          key="delete"
          style={[styles.actionButton, styles.deleteButton]}
          onPress={handleDelete}
        >
          <Text style={[styles.actionButtonText, styles.deleteButtonText]}>删除</Text>
        </TouchableOpacity>
      );
    }

    return actions;
  };

  return (
    <TouchableOpacity style={styles.container} onPress={() => onPress(item)}>
      {/* 主要内容区域 */}
      <View style={styles.content}>
        {/* 房源图片 */}
        <Image 
          source={{ 
            uri: item.cover_image || item.imageUrl || 'https://via.placeholder.com/80x60?text=房源' 
          }} 
          style={styles.propertyImage} 
        />

        {/* 房源信息 */}
        <View style={styles.propertyInfo}>
          {/* 标题和状态 */}
          <View style={styles.titleRow}>
            <Text style={styles.title} numberOfLines={1}>
              {item.title || `${getPropertyTypeText(item.property_type)} · ${item.building_name || '位置待完善'}`}
            </Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
              <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
            </View>
          </View>

          {/* 价格信息 */}
          <Text style={styles.price}>
            {formatPrice(item)}
          </Text>

          {/* 面积和描述 */}
          <Text style={styles.subtitle} numberOfLines={1}>
            {formatArea(item)}
            {item.description && ` · ${item.description}`}
          </Text>

          {/* 底部信息行 */}
          <View style={styles.bottomRow}>
            {/* 统计信息 */}
            <View style={styles.statsContainer}>
              <Text style={styles.statText}>浏览{item.view_count || 0}</Text>
              <Text style={styles.statText}>收藏{item.favorite_count || 0}</Text>
              <Text style={styles.statText}>咨询{item.inquiry_count || 0}</Text>
            </View>

            {/* 操作按钮 */}
            <View style={styles.actionContainer}>
              {renderActionButtons()}
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: spacing.md,
    marginVertical: spacing.sm,
    borderRadius: borderRadius.md,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  content: {
    flexDirection: 'row',
    padding: spacing.md,
  },
  propertyImage: {
    width: 80,
    height: 60,
    borderRadius: borderRadius.sm,
    marginRight: spacing.md,
  },
  propertyInfo: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.xs,
  },
  title: {
    flex: 1,
    fontSize: fontSize.base,
    fontWeight: '600',
    color: '#333333',
    marginRight: spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    borderRadius: borderRadius.sm,
  },
  statusText: {
    fontSize: fontSize.xs,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  price: {
    fontSize: fontSize.xl,
    fontWeight: '700',
    color: '#FF6B35',
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: fontSize.base,
    color: '#666666',
    marginBottom: spacing.sm,
  },
  bottomRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: fontSize.xs,
    color: '#999999',
    marginRight: spacing.md,
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    marginRight: spacing.lg,
    borderRadius: borderRadius.sm,
    backgroundColor: '#F5F5F5',
  },
  actionButtonText: {
    fontSize: fontSize.sm,
    color: '#666666',
    fontWeight: '500',
  },
  // 🎨 恢复完整的按钮样式
  viewButton: {
    backgroundColor: '#F3E5F5',
  },
  viewButtonText: {
    color: '#7B68EE',
    fontWeight: '600',
  },
  editButton: {
    backgroundColor: '#E3F2FD',
  },
  editButtonText: {
    color: '#1976D2',
    fontWeight: '600',
  },
  deleteButton: {
    backgroundColor: 'transparent',
  },
  deleteButtonText: {
    color: '#FF4D4F',
    fontWeight: '600',
  },
});
