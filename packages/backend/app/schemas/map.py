"""
地图相关的数据传输对象(DTO)

企业级架构 - Schema层
定义地图API的请求和响应数据结构，确保数据传输的类型安全和验证。
严格遵循单一职责原则，只处理数据结构定义和验证。
"""

from typing import List, Dict, Any, Optional
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

from app.core.constants import PropertyType, PropertyStatus


class LocationPoint(BaseModel):
    """地理位置点"""
    lat: float = Field(..., ge=-90, le=90, description="纬度")
    lng: float = Field(..., ge=-180, le=180, description="经度")
    
    class Config:
        json_schema_extra = {
            "example": {
                "lat": 22.547,
                "lng": 114.085
            }
        }


class PropertyLocationSearchRequest(BaseModel):
    """按地理位置搜索房源请求"""
    center_lat: float = Field(..., ge=-90, le=90, description="中心点纬度")
    center_lng: float = Field(..., ge=-180, le=180, description="中心点经度")
    radius_meters: int = Field(5000, ge=100, le=50000, description="搜索半径（米）")
    limit: int = Field(50, ge=1, le=1000, description="返回数量限制")
    offset: int = Field(0, ge=0, description="偏移量")
    include_distances: bool = Field(False, description="是否包含距离信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "center_lat": 22.547,
                "center_lng": 114.085,
                "radius_meters": 5000,
                "limit": 50,
                "offset": 0,
                "include_distances": True
            }
        }


class PropertyBoundsSearchRequest(BaseModel):
    """按地图边界搜索房源请求"""
    north_lat: float = Field(..., ge=-90, le=90, description="北边界纬度")
    south_lat: float = Field(..., ge=-90, le=90, description="南边界纬度")
    east_lng: float = Field(..., ge=-180, le=180, description="东边界经度")
    west_lng: float = Field(..., ge=-180, le=180, description="西边界经度")
    limit: int = Field(100, ge=1, le=1000, description="返回数量限制")
    offset: int = Field(0, ge=0, description="偏移量")
    
    @validator('north_lat')
    def validate_north_south(cls, v, values):
        if 'south_lat' in values and v <= values['south_lat']:
            raise ValueError('北边界必须大于南边界')
        return v
    
    @validator('east_lng')
    def validate_east_west(cls, v, values):
        if 'west_lng' in values and v <= values['west_lng']:
            raise ValueError('东边界必须大于西边界')
        return v
    
    class Config:
        json_schema_extra = {
            "example": {
                "north_lat": 22.560,
                "south_lat": 22.530,
                "east_lng": 114.100,
                "west_lng": 114.070,
                "limit": 100,
                "offset": 0
            }
        }


class PropertyDistanceRequest(BaseModel):
    """房源距离计算请求"""
    property_id: UUID = Field(..., description="房源ID")
    target_lat: float = Field(..., ge=-90, le=90, description="目标点纬度")
    target_lng: float = Field(..., ge=-180, le=180, description="目标点经度")
    
    class Config:
        json_schema_extra = {
            "example": {
                "property_id": "123e4567-e89b-12d3-a456-************",
                "target_lat": 22.547,
                "target_lng": 114.085
            }
        }


class PropertyLocationUpdateRequest(BaseModel):
    """房源地理位置更新请求"""
    latitude: float = Field(..., ge=-90, le=90, description="纬度")
    longitude: float = Field(..., ge=-180, le=180, description="经度")
    
    class Config:
        json_schema_extra = {
            "example": {
                "latitude": 22.547,
                "longitude": 114.085
            }
        }


class MapPropertyInfo(BaseModel):
    """地图房源信息"""
    id: str = Field(..., description="房源ID")
    title: str = Field(..., description="房源标题")
    property_type: PropertyType = Field(..., description="房源类型")
    sub_type: Optional[str] = Field(None, description="房源子类型")
    address: Optional[str] = Field(None, description="房源地址")
    location: LocationPoint = Field(..., description="地理位置")
    area: Optional[float] = Field(None, description="面积（平方米）")
    rent_price: Optional[float] = Field(None, description="租金价格")
    sale_price: Optional[float] = Field(None, description="售价")
    status: PropertyStatus = Field(..., description="房源状态")
    created_at: Optional[str] = Field(None, description="创建时间")
    distance_meters: Optional[float] = Field(None, description="距离（米）")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "title": "CBD核心商铺出租",
                "property_type": "SHOP",
                "sub_type": "临街商铺",
                "address": "深圳市福田区中心大道123号",
                "location": {
                    "lat": 22.547,
                    "lng": 114.085
                },
                "area": 120.5,
                "rent_price": 15000.0,
                "sale_price": None,
                "status": "ACTIVE",
                "created_at": "2024-01-15T10:30:00Z",
                "distance_meters": 1250.5
            }
        }


class SearchParams(BaseModel):
    """搜索参数"""
    center: Optional[LocationPoint] = Field(None, description="中心点")
    bounds: Optional[Dict[str, float]] = Field(None, description="边界")
    radius_meters: Optional[int] = Field(None, description="搜索半径（米）")
    limit: int = Field(..., description="返回数量限制")
    offset: int = Field(..., description="偏移量")


class SearchMetadata(BaseModel):
    """搜索元数据"""
    total_found: int = Field(..., description="找到的总数量")
    has_more: bool = Field(..., description="是否还有更多数据")
    include_distances: Optional[bool] = Field(None, description="是否包含距离信息")


class MapSearchResponse(BaseModel):
    """地图搜索响应"""
    properties: List[MapPropertyInfo] = Field(..., description="房源列表")
    search_params: SearchParams = Field(..., description="搜索参数")
    metadata: SearchMetadata = Field(..., description="搜索元数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "properties": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "title": "CBD核心商铺出租",
                        "property_type": "SHOP",
                        "sub_type": "临街商铺",
                        "address": "深圳市福田区中心大道123号",
                        "location": {
                            "lat": 22.547,
                            "lng": 114.085
                        },
                        "area": 120.5,
                        "rent_price": 15000.0,
                        "sale_price": None,
                        "status": "ACTIVE",
                        "created_at": "2024-01-15T10:30:00Z",
                        "distance_meters": 1250.5
                    }
                ],
                "search_params": {
                    "center": {
                        "lat": 22.547,
                        "lng": 114.085
                    },
                    "radius_meters": 5000,
                    "limit": 50,
                    "offset": 0
                },
                "metadata": {
                    "total_found": 1,
                    "has_more": False,
                    "include_distances": True
                }
            }
        }


class DistanceCalculationResponse(BaseModel):
    """距离计算响应"""
    property_id: str = Field(..., description="房源ID")
    target_point: LocationPoint = Field(..., description="目标点")
    distance_meters: float = Field(..., description="距离（米）")
    distance_km: float = Field(..., description="距离（公里）")
    distance_display: str = Field(..., description="友好显示格式")
    
    class Config:
        json_schema_extra = {
            "example": {
                "property_id": "123e4567-e89b-12d3-a456-************",
                "target_point": {
                    "lat": 22.547,
                    "lng": 114.085
                },
                "distance_meters": 1250.5,
                "distance_km": 1.251,
                "distance_display": "1.3公里"
            }
        }


class LocationUpdateResponse(BaseModel):
    """地理位置更新响应"""
    property_id: str = Field(..., description="房源ID")
    location: LocationPoint = Field(..., description="更新后的位置")
    updated: bool = Field(..., description="是否更新成功")
    message: str = Field(..., description="响应消息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "property_id": "123e4567-e89b-12d3-a456-************",
                "location": {
                    "lat": 22.547,
                    "lng": 114.085
                },
                "updated": True,
                "message": "地理位置更新成功"
            }
        }
