# 🚀 推广应用详细实施方案

## 📋 项目概述

本文档提供从零开始构建推广小程序的详细步骤，包括小程序注册、前后端设计、与现有系统集成等完整实施流程。

---

## 🎯 第一阶段：准备工作（第1周）

### 1.1 法律合规准备

#### 1.1.1 法律咨询清单
```yaml
法律咨询项目:
  个人信息保护法合规:
    - 评估拍摄电话号码的合法性
    - 确定数据收集边界
    - 制定用户授权机制
  
  肖像权风险评估:
    - 评估拍摄他人财产的法律风险
    - 制定风险缓释措施
  
  合规框架设计:
    - 设计用户告知机制
    - 建立数据删除流程
    - 制定合规操作手册
```

#### 1.1.2 合规文档准备
1. **用户授权协议模板**
2. **隐私政策模板**
3. **数据安全保护措施**
4. **风险准备金设立方案**

### 1.2 技术环境准备

#### 1.2.1 开发环境搭建
```bash
# 后端开发环境（基于现有项目）
cd /data/my-real-estate-app
python -m venv promotion_env
source promotion_env/bin/activate
pip install -r requirements.txt

# 小程序开发环境
# 1. 下载微信开发者工具：https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
# 2. 注册微信小程序账号：https://mp.weixin.qq.com/
# 3. 申请AppID
```

#### 1.2.2 云服务准备
```yaml
云服务清单:
  阿里云OSS:
    - 创建新的Bucket用于推广图片存储
    - 配置访问权限和跨域设置
    - 生成AccessKey和SecretKey
  
  OCR服务:
    - 阿里云OCR服务申请：https://vision.aliyun.com/
    - 开通文字识别API
    - 获取API Key和Secret
  
  草料二维码API:
    - 注册草料二维码开发者账号：https://cli.im/
    - 申请API接口权限
    - 获取API Key
```

---

## 📱 第二阶段：小程序注册与配置（第1-2周）

### 2.1 微信小程序注册流程

#### 2.1.1 注册步骤详解
```yaml
注册流程:
  步骤1: 访问微信公众平台
    url: "https://mp.weixin.qq.com/"
    点击: "立即注册"
  
  步骤2: 选择账号类型
    选择: "小程序"
    填写: "邮箱"和"密码"
  
  步骤3: 信息登记
    主体类型: "企业"（需要企业资质）
    企业名称: "您的公司名称"
    营业执照注册号: "统一社会信用代码"
  
  步骤4: 管理员信息
    管理员身份证: "上传身份证照片"
    管理员手机: "接收验证码"
    管理员微信: "扫码验证"
  
  步骤5: 完成认证
    支付: 300元认证费
    等待: 1-3个工作日审核
```

#### 2.1.2 小程序信息配置
```yaml
小程序配置:
  基本设置:
    小程序名称: "慧选址推广"
    小程序简介: "商业地产推广赚钱平台"
    服务类目: "工具 > 商务服务"
  
  开发设置:
    AppID: "自动生成"
    服务器域名: 
      - request合法域名: "https://your-api-domain.com"
      - uploadFile合法域名: "https://your-oss-domain.com"
      - downloadFile合法域名: "https://your-oss-domain.com"
  
  开发者权限:
    添加开发者微信号
    配置开发者权限
```

### 2.2 小程序开发环境配置

#### 2.2.1 微信开发者工具配置
```javascript
// project.config.json
{
  "description": "慧选址推广小程序",
  "packOptions": {
    "ignore": []
  },
  "miniprogramRoot": "miniprogram/",
  "compileType": "miniprogram",
  "projectname": "hui-site-selection-promotion",
  "setting": {
    "useCompilerPlugins": [
      "sass"
    ],
    "babelSetting": {
      "ignore": [],
      "disablePlugins": [],
      "outputPath": ""
    },
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true,
    "urlCheck": false,
    "coverView": true,
    "lazyloadPlaceholderEnable": false,
    "preloadBackgroundData": false,
    "minifiedWXSS": true,
    "autoAudits": false,
    "showShadowRootInWxmlPanel": true,
    "scopeDataCheck": false,
    "uglifyFileName": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true
  },
  "simulatorType": "wechat",
  "simulatorPluginLibVersion": {},
  "condition": false,
  "srcMiniprogramRoot": "miniprogram/",
  "appid": "your-appid-here",
  "libVersion": "2.32.3",
  "editorSetting": {
    "tabIndent": "insertSpaces",
    "tabSize": 2
  }
}
```

#### 2.2.2 项目目录结构
```
hui-site-selection-promotion/
├── miniprogram/                 # 小程序代码
│   ├── pages/                   # 页面
│   │   ├── index/              # 首页
│   │   ├── register/           # 注册页
│   │   ├── promotion/          # 推广页
│   │   ├── profile/            # 个人中心
│   │   └── result/             # 结果页
│   ├── components/             # 组件
│   │   ├── camera/             # 相机组件
│   │   ├── qr-scanner/         # 二维码扫描
│   │   └── image-upload/       # 图片上传
│   ├── utils/                  # 工具函数
│   │   ├── request.js          # 网络请求
│   │   ├── auth.js             # 认证工具
│   │   └── util.js             # 通用工具
│   ├── styles/                 # 样式文件
│   │   ├── common.wxss         # 通用样式
│   │   └── icon.wxss           # 图标样式
│   ├── images/                 # 图片资源
│   │   ├── icons/              # 图标
│   │   └── backgrounds/        # 背景图
│   └── app.js                  # 小程序入口
├── project.config.json         # 项目配置
├── sitemap.json                # 站点地图
└── package.json                # 依赖配置
```

---

## 🏗️ 第三阶段：后端系统设计（第2-3周）

### 3.1 数据库设计

#### 3.1.1 扩展现有数据库结构
```sql
-- 在现有数据库中添加推广相关表

-- 推广用户表（扩展现有users表）
ALTER TABLE users ADD COLUMN IF NOT EXISTS qr_code VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS commission_rate DECIMAL(5,2) DEFAULT 2.00;
ALTER TABLE users ADD COLUMN IF NOT EXISTS promotion_level VARCHAR(20) DEFAULT 'bronze';
ALTER TABLE users ADD COLUMN IF NOT EXISTS total_commission DECIMAL(10,2) DEFAULT 0.00;
ALTER TABLE users ADD COLUMN IF NOT EXISTS available_commission DECIMAL(10,2) DEFAULT 0.00;

-- 推广记录表
CREATE TABLE promotion_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    promoter_id BIGINT NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    ocr_result TEXT,
    ocr_confidence DECIMAL(3,2),
    manual_phone VARCHAR(20) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    rejection_reason VARCHAR(255),
    commission_amount DECIMAL(5,2) DEFAULT 2.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (promoter_id) REFERENCES users(id),
    INDEX idx_phone_number (phone_number),
    INDEX idx_promoter_id (promoter_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 佣金记录表
CREATE TABLE commission_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    promoter_id BIGINT NOT NULL,
    source_record_id BIGINT NOT NULL,
    amount DECIMAL(5,2) NOT NULL,
    level ENUM('direct', 'indirect') DEFAULT 'direct',
    status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending',
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (promoter_id) REFERENCES users(id),
    FOREIGN KEY (source_record_id) REFERENCES promotion_records(id),
    INDEX idx_promoter_id (promoter_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 推广关系表
CREATE TABLE promotion_relations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    referrer_id BIGINT NOT NULL,
    referee_id BIGINT NOT NULL,
    level INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id),
    FOREIGN KEY (referee_id) REFERENCES users(id),
    UNIQUE KEY uk_relation (referrer_id, referee_id),
    INDEX idx_referrer_id (referrer_id),
    INDEX idx_referee_id (referee_id)
);

-- 防作弊记录表
CREATE TABLE anti_cheat_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    action_type VARCHAR(50) NOT NULL,
    action_detail TEXT,
    ip_address VARCHAR(45),
    device_fingerprint VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at)
);
```

#### 3.1.2 数据库迁移文件
```python
# alembic/versions/add_promotion_tables.py
"""add promotion tables

Revision ID: add_promotion_tables
Revises: previous_revision
Create Date: 2025-08-04 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers
revision = 'add_promotion_tables'
down_revision = 'previous_revision'
branch_labels = None
depends_on = None

def upgrade():
    # 扩展users表
    op.add_column('users', sa.Column('qr_code', sa.String(255), nullable=True))
    op.add_column('users', sa.Column('commission_rate', sa.Numeric(5, 2), nullable=True, default=2.00))
    op.add_column('users', sa.Column('promotion_level', sa.String(20), nullable=True, default='bronze'))
    op.add_column('users', sa.Column('total_commission', sa.Numeric(10, 2), nullable=True, default=0.00))
    op.add_column('users', sa.Column('available_commission', sa.Numeric(10, 2), nullable=True, default=0.00))
    
    # 创建推广记录表
    op.create_table(
        'promotion_records',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('promoter_id', sa.BigInteger(), nullable=False),
        sa.Column('phone_number', sa.String(20), nullable=False),
        sa.Column('image_url', sa.String(500), nullable=False),
        sa.Column('ocr_result', sa.Text(), nullable=True),
        sa.Column('ocr_confidence', sa.Numeric(3, 2), nullable=True),
        sa.Column('manual_phone', sa.String(20), nullable=False),
        sa.Column('status', sa.Enum('pending', 'approved', 'rejected'), nullable=True, default='pending'),
        sa.Column('rejection_reason', sa.String(255), nullable=True),
        sa.Column('commission_amount', sa.Numeric(5, 2), nullable=True, default=2.00),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.ForeignKeyConstraint(['promoter_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_phone_number', 'promotion_records', ['phone_number'])
    op.create_index('idx_promoter_id', 'promotion_records', ['promoter_id'])
    op.create_index('idx_status', 'promotion_records', ['status'])
    op.create_index('idx_created_at', 'promotion_records', ['created_at'])
    
    # 创建佣金记录表
    op.create_table(
        'commission_records',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('promoter_id', sa.BigInteger(), nullable=False),
        sa.Column('source_record_id', sa.BigInteger(), nullable=False),
        sa.Column('amount', sa.Numeric(5, 2), nullable=False),
        sa.Column('level', sa.Enum('direct', 'indirect'), nullable=True, default='direct'),
        sa.Column('status', sa.Enum('pending', 'paid', 'cancelled'), nullable=True, default='pending'),
        sa.Column('paid_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.ForeignKeyConstraint(['promoter_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['source_record_id'], ['promotion_records.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_promoter_id', 'commission_records', ['promoter_id'])
    op.create_index('idx_status', 'commission_records', ['status'])
    op.create_index('idx_created_at', 'commission_records', ['created_at'])
    
    # 创建推广关系表
    op.create_table(
        'promotion_relations',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('referrer_id', sa.BigInteger(), nullable=False),
        sa.Column('referee_id', sa.BigInteger(), nullable=False),
        sa.Column('level', sa.Integer(), nullable=True, default=1),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.ForeignKeyConstraint(['referrer_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['referee_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('referrer_id', 'referee_id', name='uk_relation')
    )
    op.create_index('idx_referrer_id', 'promotion_relations', ['referrer_id'])
    op.create_index('idx_referee_id', 'promotion_relations', ['referee_id'])
    
    # 创建防作弊记录表
    op.create_table(
        'anti_cheat_logs',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('user_id', sa.BigInteger(), nullable=False),
        sa.Column('action_type', sa.String(50), nullable=False),
        sa.Column('action_detail', sa.Text(), nullable=True),
        sa.Column('ip_address', sa.String(45), nullable=True),
        sa.Column('device_fingerprint', sa.String(255), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_user_id', 'anti_cheat_logs', ['user_id'])
    op.create_index('idx_action_type', 'anti_cheat_logs', ['action_type'])
    op.create_index('idx_created_at', 'anti_cheat_logs', ['created_at'])

def downgrade():
    # 删除表
    op.drop_table('anti_cheat_logs')
    op.drop_table('promotion_relations')
    op.drop_table('commission_records')
    op.drop_table('promotion_records')
    
    # 删除users表扩展字段
    op.drop_column('users', 'available_commission')
    op.drop_column('users', 'total_commission')
    op.drop_column('users', 'promotion_level')
    op.drop_column('users', 'commission_rate')
    op.drop_column('users', 'qr_code')
```

### 3.2 后端API设计

#### 3.2.1 项目结构设计
```
packages/backend/app/
├── api/
│   ├── routes/
│   │   ├── promotion/
│   │   │   ├── __init__.py
│   │   │   ├── auth.py          # 认证相关API
│   │   │   ├── promotion.py     # 推广功能API
│   │   │   ├── commission.py    # 佣金系统API
│   │   │   └── qr_code.py      # 二维码API
│   │   └── ...
│   ├── deps.py                  # 依赖注入
│   └── ...
├── models/
│   ├── promotion/
│   │   ├── __init__.py
│   │   ├── user.py             # 推广用户模型
│   │   ├── record.py           # 推广记录模型
│   │   ├── commission.py       # 佣金模型
│   │   └── relation.py         # 推广关系模型
│   └── ...
├── services/
│   ├── promotion/
│   │   ├── __init__.py
│   │   ├── auth_service.py     # 认证服务
│   │   ├── promotion_service.py # 推广服务
│   │   ├── commission_service.py # 佣金服务
│   │   ├── qr_service.py      # 二维码服务
│   │   ├── ocr_service.py     # OCR服务
│   │   └── anti_cheat_service.py # 防作弊服务
│   └── ...
├── core/
│   ├── config.py               # 配置管理
│   └── security.py             # 安全相关
└── schemas/
    ├── promotion/
    │   ├── __init__.py
    │   ├── user.py             # 用户模式
    │   ├── record.py           # 推广记录模式
    │   └── commission.py       # 佣金模式
    └── ...
```

#### 3.2.2 核心API接口设计
```python
# packages/backend/app/api/routes/promotion/auth.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

router = APIRouter(prefix="/api/v1/promotion/auth", tags=["推广认证"])

@router.post("/register")
async def register_user(
    code: str,
    phone: str,
    referrer_code: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """用户注册"""
    # 1. 验证微信code
    wx_user_info = await verify_wechat_code(code)
    
    # 2. 检查用户是否已存在
    existing_user = await get_user_by_openid(wx_user_info['openid'])
    if existing_user:
        raise HTTPException(status_code=400, detail="用户已存在")
    
    # 3. 验证推广码
    referrer = None
    if referrer_code:
        referrer = await get_user_by_qr_code(referrer_code)
        if not referrer:
            raise HTTPException(status_code=400, detail="无效推广码")
    
    # 4. 创建用户
    user = await create_promotion_user(
        openid=wx_user_info['openid'],
        phone=phone,
        referrer_id=referrer.id if referrer else None
    )
    
    # 5. 生成专属二维码
    qr_code = await generate_user_qr_code(user.id)
    
    return {
        "success": True,
        "user_id": user.id,
        "qr_code": qr_code
    }

@router.post("/login")
async def login_user(
    code: str,
    db: AsyncSession = Depends(get_db)
):
    """用户登录"""
    # 1. 验证微信code
    wx_user_info = await verify_wechat_code(code)
    
    # 2. 获取用户信息
    user = await get_user_by_openid(wx_user_info['openid'])
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 3. 生成token
    token = generate_access_token(user.id)
    
    return {
        "success": True,
        "token": token,
        "user": {
            "id": user.id,
            "phone": user.phone,
            "level": user.promotion_level,
            "total_commission": user.total_commission,
            "available_commission": user.available_commission
        }
    }
```

```python
# packages/backend/app/api/routes/promotion/promotion.py
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

router = APIRouter(prefix="/api/v1/promotion", tags=["推广功能"])

@router.post("/submit")
async def submit_promotion(
    phone_number: str = Form(...),
    image: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """提交推广信息"""
    # 1. 防作弊检查
    await anti_cheat_check(current_user.id, phone_number)
    
    # 2. 上传图片到OSS
    image_url = await upload_image_to_oss(image)
    
    # 3. OCR识别
    ocr_result = await extract_phone_from_image(image_url)
    
    # 4. 验证号码一致性
    if ocr_result['number'] != phone_number:
        await log_anti_cheat(
            current_user.id, 
            "号码不一致", 
            f"OCR: {ocr_result['number']}, 手动: {phone_number}"
        )
        raise HTTPException(status_code=400, detail="图片号码与输入不一致")
    
    # 5. 检查号码是否已存在
    exists = await check_phone_exists_in_main_db(phone_number)
    if exists:
        await create_promotion_record(
            db=db,
            promoter_id=current_user.id,
            phone_number=phone_number,
            image_url=image_url,
            ocr_result=ocr_result,
            status="rejected",
            rejection_reason="号码已存在"
        )
        return {
            "success": False,
            "message": "系统已有该号码",
            "commission": 0
        }
    
    # 6. 创建推广记录
    record = await create_promotion_record(
        db=db,
        promoter_id=current_user.id,
        phone_number=phone_number,
        image_url=image_url,
        ocr_result=ocr_result,
        status="approved"
    )
    
    # 7. 处理佣金
    commission_result = await process_commission(record.id)
    
    return {
        "success": True,
        "message": "提交成功",
        "commission": commission_result['amount'],
        "record_id": record.id
    }

@router.get("/stats")
async def get_promotion_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取推广统计"""
    stats = await calculate_user_stats(current_user.id, db)
    
    return {
        "success": True,
        "stats": stats
    }

@router.get("/records")
async def get_promotion_records(
    page: int = 1,
    page_size: int = 20,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取推广记录"""
    records = await get_user_promotion_records(
        user_id=current_user.id,
        page=page,
        page_size=page_size,
        status=status,
        db=db
    )
    
    return {
        "success": True,
        "records": records
    }
```

#### 3.2.3 服务层设计
```python
# packages/backend/app/services/promotion/promotion_service.py
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from typing import List, Optional, Dict, Any
import asyncio
import aiohttp
import hashlib
import re

class PromotionService:
    def __init__(self):
        self.ocr_api_url = "https://ocrapi-advanced.taobao.com/ocrservice/advanced"
        self.ocr_app_code = "your-app-code"
        
    async def submit_promotion(
        self,
        promoter_id: int,
        phone_number: str,
        image_file,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """提交推广信息"""
        # 1. 上传图片
        image_url = await self._upload_image(image_file)
        
        # 2. OCR识别
        ocr_result = await self._extract_phone_from_image(image_url)
        
        # 3. 验证一致性
        if ocr_result['number'] != phone_number:
            raise ValueError("号码不一致")
        
        # 4. 检查重复
        exists = await self._check_phone_exists(phone_number)
        if exists:
            return {
                "success": False,
                "message": "号码已存在",
                "commission": 0
            }
        
        # 5. 创建记录
        record = await self._create_promotion_record(
            promoter_id, phone_number, image_url, ocr_result, db
        )
        
        return {
            "success": True,
            "message": "提交成功",
            "commission": 2.0,
            "record_id": record.id
        }
    
    async def _upload_image(self, image_file) -> str:
        """上传图片到OSS"""
        # 实现OSS上传逻辑
        import oss2
        auth = oss2.Auth('your-access-key', 'your-secret-key')
        bucket = oss2.Bucket(auth, 'your-endpoint', 'your-bucket')
        
        # 生成文件名
        import uuid
        file_name = f"promotion/{uuid.uuid4()}.jpg"
        
        # 上传文件
        result = bucket.put_object(file_name, image_file)
        return f"https://your-bucket.oss-cn-hangzhou.aliyuncs.com/{file_name}"
    
    async def _extract_phone_from_image(self, image_url: str) -> Dict[str, Any]:
        """OCR识别电话号码"""
        async with aiohttp.ClientSession() as session:
            headers = {
                'Authorization': f'APPCODE {self.ocr_app_code}',
                'Content-Type': 'application/json; charset=UTF-8'
            }
            
            data = {
                'image': image_url,
                'configure': {
                    'min_size': 16,
                    'output_prob': True
                }
            }
            
            async with session.post(
                self.ocr_api_url,
                headers=headers,
                json=data
            ) as response:
                result = await response.json()
                
                if result['success']:
                    # 从OCR结果中提取电话号码
                    text = result['text']
                    phone_pattern = re.compile(r'1[3-9]\d{9}')
                    match = phone_pattern.search(text)
                    
                    if match:
                        return {
                            'number': match.group(),
                            'confidence': 0.95
                        }
                    else:
                        raise ValueError("未识别到电话号码")
                else:
                    raise ValueError(f"OCR识别失败: {result['msg']}")
    
    async def _check_phone_exists(self, phone_number: str) -> bool:
        """检查号码是否已存在"""
        # 调用主业务数据库API检查
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://your-main-app.com/api/check-phone",
                json={"phone": phone_number}
            ) as response:
                result = await response.json()
                return result.get('exists', False)
    
    async def _create_promotion_record(
        self,
        promoter_id: int,
        phone_number: str,
        image_url: str,
        ocr_result: Dict[str, Any],
        db: AsyncSession
    ):
        """创建推广记录"""
        from app.models.promotion.record import PromotionRecord
        
        record = PromotionRecord(
            promoter_id=promoter_id,
            phone_number=phone_number,
            image_url=image_url,
            ocr_result=ocr_result,
            manual_phone=phone_number,
            status="approved"
        )
        
        db.add(record)
        await db.commit()
        await db.refresh(record)
        
        return record
```

### 3.3 与现有系统集成

#### 3.3.1 用户系统集成
```python
# packages/backend/app/services/promotion/auth_service.py
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.user import User
from app.models.promotion.user import PromotionUser
import httpx

class AuthService:
    def __init__(self):
        self.wx_app_id = "your-wx-app-id"
        self.wx_app_secret = "your-wx-app-secret"
        
    async def verify_wechat_code(self, code: str) -> Dict[str, Any]:
        """验证微信code"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://api.weixin.qq.com/sns/jscode2session",
                params={
                    "appid": self.wx_app_id,
                    "secret": self.wx_app_secret,
                    "js_code": code,
                    "grant_type": "authorization_code"
                }
            )
            
            result = response.json()
            
            if 'openid' in result:
                return result
            else:
                raise ValueError(f"微信验证失败: {result}")
    
    async def create_promotion_user(
        self,
        openid: str,
        phone: str,
        referrer_id: Optional[int] = None,
        db: AsyncSession = None
    ) -> User:
        """创建推广用户"""
        # 检查用户是否已存在
        existing_user = await db.execute(
            select(User).where(User.phone == phone)
        )
        if existing_user.scalar_one_or_none():
            raise ValueError("用户已存在")
        
        # 创建用户
        user = User(
            phone=phone,
            username=f"promo_{openid[:8]}",
            is_active=True
        )
        
        db.add(user)
        await db.commit()
        await db.refresh(user)
        
        # 添加推广相关字段
        user.qr_code = f"promo_{user.id}"
        user.commission_rate = 2.0
        user.promotion_level = "bronze"
        
        # 如果有推荐人，创建推广关系
        if referrer_id:
            from app.models.promotion.relation import PromotionRelation
            relation = PromotionRelation(
                referrer_id=referrer_id,
                referee_id=user.id,
                level=1
            )
            db.add(relation)
        
        await db.commit()
        await db.refresh(user)
        
        return user
    
    async def get_user_by_openid(self, openid: str, db: AsyncSession) -> Optional[User]:
        """根据openid获取用户"""
        result = await db.execute(
            select(User).where(User.username == f"promo_{openid[:8]}")
        )
        return result.scalar_one_or_none()
    
    async def get_user_by_qr_code(self, qr_code: str, db: AsyncSession) -> Optional[User]:
        """根据二维码获取用户"""
        result = await db.execute(
            select(User).where(User.qr_code == qr_code)
        )
        return result.scalar_one_or_none()
```

#### 3.3.2 主业务系统集成
```python
# packages/backend/app/services/promotion/integration_service.py
from sqlalchemy.ext.asyncio import AsyncSession
import httpx
from typing import Dict, Any

class IntegrationService:
    def __init__(self):
        self.main_app_base_url = "https://your-main-app.com/api"
        self.api_key = "your-integration-api-key"
        
    async def check_phone_exists(self, phone_number: str) -> bool:
        """检查号码是否已存在于主业务系统"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.main_app_base_url}/users/check-phone",
                headers={"Authorization": f"Bearer {self.api_key}"},
                json={"phone": phone_number}
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('exists', False)
            else:
                # 如果主系统不可用，默认为不存在
                return False
    
    async def sync_promotion_data(self, promotion_data: Dict[str, Any]) -> bool:
        """同步推广数据到主业务系统"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.main_app_base_url}/promotion/sync",
                headers={"Authorization": f"Bearer {self.api_key}"},
                json=promotion_data
            )
            
            return response.status_code == 200
    
    async def create_lead_in_main_system(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """在主系统创建潜在客户"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.main_app_base_url}/leads",
                headers={"Authorization": f"Bearer {self.api_key}"},
                json=lead_data
            )
            
            if response.status_code == 201:
                return response.json()
            else:
                raise Exception(f"创建潜在客户失败: {response.text}")
```

---

## 📱 第四阶段：小程序前端开发（第3-4周）

### 4.1 小程序页面设计

#### 4.1.1 首页设计
```html
<!-- miniprogram/pages/index/index.wxml -->
<view class="container">
  <view class="header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="title">慧选址推广</text>
    <text class="subtitle">商业地产推广赚钱平台</text>
  </view>
  
  <view class="main-buttons">
    <button class="btn-primary" bindtap="goToPromotion">
      <text>开始推广</text>
    </button>
    <button class="btn-secondary" bindtap="goToProfile">
      <text>个人中心</text>
    </button>
  </view>
  
  <view class="stats-card">
    <view class="stats-item">
      <text class="stats-number">{{userStats.totalCommission || 0}}</text>
      <text class="stats-label">总收益(元)</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{userStats.todayCount || 0}}</text>
      <text class="stats-label">今日提交</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{userStats.successRate || 0}}%</text>
      <text class="stats-label">成功率</text>
    </view>
  </view>
  
  <view class="quick-actions">
    <view class="action-item" bindtap="shareQrCode">
      <image class="action-icon" src="/images/icons/share.png"></image>
      <text>分享二维码</text>
    </view>
    <view class="action-item" bindtap="viewTutorial">
      <image class="action-icon" src="/images/icons/tutorial.png"></image>
      <text>使用教程</text>
    </view>
    <view class="action-item" bindtap="contactService">
      <image class="action-icon" src="/images/icons/service.png"></image>
      <text>联系客服</text>
    </view>
  </view>
</view>
```

```css
/* miniprogram/pages/index/index.wxss */
.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.main-buttons {
  margin-bottom: 60rpx;
}

.btn-primary {
  background-color: #ff6b35;
  color: white;
  border-radius: 50rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  padding: 20rpx;
}

.btn-secondary {
  background-color: white;
  color: #ff6b35;
  border: 2rpx solid #ff6b35;
  border-radius: 50rpx;
  font-size: 32rpx;
  padding: 20rpx;
}

.stats-card {
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
  display: block;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

.quick-actions {
  display: flex;
  justify-content: space-around;
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.action-item {
  text-align: center;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.action-item text {
  font-size: 24rpx;
  color: #666;
}
```

```javascript
// miniprogram/pages/index/index.js
const app = getApp()

Page({
  data: {
    userStats: {
      totalCommission: 0,
      todayCount: 0,
      successRate: 0
    },
    userInfo: null
  },

  onLoad: function (options) {
    this.checkLoginStatus()
    this.loadUserStats()
  },

  onShow: function () {
    this.loadUserStats()
  },

  checkLoginStatus: function () {
    const token = wx.getStorageSync('token')
    if (!token) {
      wx.redirectTo({
        url: '/pages/register/register'
      })
      return
    }
    
    // 获取用户信息
    this.setData({
      userInfo: app.globalData.userInfo
    })
  },

  loadUserStats: function () {
    const token = wx.getStorageSync('token')
    if (!token) return

    wx.request({
      url: `${app.globalData.apiBase}/api/v1/promotion/stats`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        if (res.data.success) {
          this.setData({
            userStats: res.data.stats
          })
        }
      },
      fail: (err) => {
        console.error('加载统计数据失败:', err)
      }
    })
  },

  goToPromotion: function () {
    wx.navigateTo({
      url: '/pages/promotion/promotion'
    })
  },

  goToProfile: function () {
    wx.navigateTo({
      url: '/pages/profile/profile'
    })
  },

  shareQrCode: function () {
    const qrCode = this.data.userInfo?.qr_code
    if (!qrCode) {
      wx.showToast({
        title: '请先注册',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '分享二维码',
      content: '是否生成您的专属推广二维码？',
      success: (res) => {
        if (res.confirm) {
          this.generateQrCode()
        }
      }
    })
  },

  generateQrCode: function () {
    const token = wx.getStorageSync('token')
    
    wx.showLoading({
      title: '生成中...'
    })

    wx.request({
      url: `${app.globalData.apiBase}/api/v1/promotion/qr-code/generate`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        wx.hideLoading()
        if (res.data.success) {
          this.showQrCode(res.data.qr_code_url)
        } else {
          wx.showToast({
            title: '生成失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  showQrCode: function (qrCodeUrl) {
    wx.previewImage({
      urls: [qrCodeUrl],
      current: qrCodeUrl
    })
  },

  viewTutorial: function () {
    wx.navigateTo({
      url: '/pages/tutorial/tutorial'
    })
  },

  contactService: function () {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  }
})
```

#### 4.1.2 推广页面设计
```html
<!-- miniprogram/pages/promotion/promotion.wxml -->
<view class="container">
  <view class="header">
    <text class="title">推广提交</text>
    <text class="subtitle">拍摄商业地产电话号码</text>
  </view>

  <view class="upload-section">
    <view class="upload-area" bindtap="chooseImage">
      <image 
        class="upload-image" 
        src="{{imageUrl || '/images/placeholder.png'}}" 
        mode="aspectFill"
      ></image>
      <view class="upload-hint" wx:if="{{!imageUrl}}">
        <image class="camera-icon" src="/images/icons/camera.png"></image>
        <text>点击拍摄或选择图片</text>
      </view>
    </view>
    
    <view class="upload-tips">
      <text class="tip-item">• 请确保电话号码清晰可见</text>
      <text class="tip-item">• 避免反光和模糊</text>
      <text class="tip-item">• 优先拍摄门口张贴的电话</text>
    </view>
  </view>

  <view class="input-section">
    <view class="input-group">
      <text class="input-label">电话号码</text>
      <input 
        class="input-field" 
        type="number" 
        maxlength="11" 
        placeholder="请输入图片中的电话号码" 
        value="{{phoneNumber}}"
        bindinput="onPhoneInput"
      />
    </view>
  </view>

  <view class="submit-section">
    <button 
      class="submit-btn" 
      bindtap="submitPromotion"
      disabled="{{!imageUrl || !phoneNumber || isSubmitting}}"
    >
      {{isSubmitting ? '提交中...' : '提交推广'}}
    </button>
    
    <view class="submit-info">
      <text class="info-text">有效号码奖励：¥2.00</text>
      <text class="info-text">审核时间：1-5分钟</text>
    </view>
  </view>

  <view class="recent-records" wx:if="{{recentRecords.length > 0}}">
    <view class="records-header">
      <text class="records-title">最近提交</text>
      <text class="records-more" bindtap="viewAllRecords">查看全部</text>
    </view>
    
    <view class="record-list">
      <view 
        class="record-item" 
        wx:for="{{recentRecords}}" 
        wx:key="id"
      >
        <view class="record-info">
          <text class="record-phone">{{item.phone_number}}</text>
          <text class="record-time">{{item.created_at}}</text>
        </view>
        <view class="record-status">
          <text class="status-{{item.status}}">{{getStatusText(item.status)}}</text>
          <text class="record-commission" wx:if="{{item.commission_amount > 0}}">
            +¥{{item.commission_amount}}
          </text>
        </view>
      </view>
    </view>
  </view>
</view>
```

```javascript
// miniprogram/pages/promotion/promotion.js
const app = getApp()

Page({
  data: {
    imageUrl: '',
    phoneNumber: '',
    isSubmitting: false,
    recentRecords: []
  },

  onLoad: function (options) {
    this.loadRecentRecords()
  },

  onShow: function () {
    this.loadRecentRecords()
  },

  chooseImage: function () {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['camera', 'album'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath
        this.setData({
          imageUrl: tempFilePath
        })
        
        // 自动OCR识别
        this.autoExtractPhone(tempFilePath)
      }
    })
  },

  autoExtractPhone: function (imagePath) {
    wx.showLoading({
      title: '识别中...'
    })

    wx.uploadFile({
      url: `${app.globalData.apiBase}/api/v1/promotion/ocr-extract`,
      filePath: imagePath,
      name: 'image',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        wx.hideLoading()
        const result = JSON.parse(res.data)
        if (result.success) {
          this.setData({
            phoneNumber: result.phone_number
          })
          wx.showToast({
            title: '识别成功',
            icon: 'success'
          })
        } else {
          wx.showToast({
            title: '识别失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        wx.showToast({
          title: '识别失败',
          icon: 'none'
        })
      }
    })
  },

  onPhoneInput: function (e) {
    this.setData({
      phoneNumber: e.detail.value
    })
  },

  submitPromotion: function () {
    if (!this.data.imageUrl || !this.data.phoneNumber) {
      wx.showToast({
        title: '请完善信息',
        icon: 'none'
      })
      return
    }

    if (!this.validatePhone(this.data.phoneNumber)) {
      wx.showToast({
        title: '手机号格式错误',
        icon: 'none'
      })
      return
    }

    this.setData({
      isSubmitting: true
    })

    wx.uploadFile({
      url: `${app.globalData.apiBase}/api/v1/promotion/submit`,
      filePath: this.data.imageUrl,
      name: 'image',
      formData: {
        phone_number: this.data.phoneNumber
      },
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        const result = JSON.parse(res.data)
        if (result.success) {
          wx.showModal({
            title: '提交成功',
            content: `获得奖励：¥${result.commission}`,
            showCancel: false,
            success: () => {
              this.resetForm()
              this.loadRecentRecords()
            }
          })
        } else {
          wx.showModal({
            title: '提交失败',
            content: result.message,
            showCancel: false
          })
        }
      },
      fail: (err) => {
        wx.showModal({
          title: '提交失败',
          content: '网络错误，请重试',
          showCancel: false
        })
      },
      complete: () => {
        this.setData({
          isSubmitting: false
        })
      }
    })
  },

  validatePhone: function (phone) {
    return /^1[3-9]\d{9}$/.test(phone)
  },

  resetForm: function () {
    this.setData({
      imageUrl: '',
      phoneNumber: ''
    })
  },

  loadRecentRecords: function () {
    const token = wx.getStorageSync('token')
    if (!token) return

    wx.request({
      url: `${app.globalData.apiBase}/api/v1/promotion/records`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      },
      data: {
        page: 1,
        page_size: 5
      },
      success: (res) => {
        if (res.data.success) {
          this.setData({
            recentRecords: res.data.records
          })
        }
      }
    })
  },

  viewAllRecords: function () {
    wx.navigateTo({
      url: '/pages/records/records'
    })
  },

  getStatusText: function (status) {
    const statusMap = {
      'pending': '审核中',
      'approved': '已通过',
      'rejected': '已拒绝'
    }
    return statusMap[status] || status
  }
})
```

### 4.2 工具函数设计

#### 4.2.1 网络请求工具
```javascript
// miniprogram/utils/request.js
const app = getApp()

const BASE_URL = 'https://your-api-domain.com'

const request = (options) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token')
    
    wx.request({
      url: `${BASE_URL}${options.url}`,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.success) {
            resolve(res.data)
          } else {
            reject(new Error(res.data.message || '请求失败'))
          }
        } else if (res.statusCode === 401) {
          // token过期，重新登录
          wx.removeStorageSync('token')
          wx.redirectTo({
            url: '/pages/register/register'
          })
          reject(new Error('登录已过期'))
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`))
        }
      },
      fail: (err) => {
        reject(new Error('网络错误'))
      }
    })
  })
}

const upload = (options) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token')
    
    wx.uploadFile({
      url: `${BASE_URL}${options.url}`,
      filePath: options.filePath,
      name: options.name || 'file',
      formData: options.formData || {},
      header: {
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          const data = JSON.parse(res.data)
          if (data.success) {
            resolve(data)
          } else {
            reject(new Error(data.message || '上传失败'))
          }
        } else {
          reject(new Error(`上传失败: ${res.statusCode}`))
        }
      },
      fail: (err) => {
        reject(new Error('网络错误'))
      }
    })
  })
}

module.exports = {
  request,
  upload,
  get: (url, data, options = {}) => request({ url, method: 'GET', data, ...options }),
  post: (url, data, options = {}) => request({ url, method: 'POST', data, ...options }),
  put: (url, data, options = {}) => request({ url, method: 'PUT', data, ...options }),
  delete: (url, options = {}) => request({ url, method: 'DELETE', ...options })
}
```

#### 4.2.2 认证工具
```javascript
// miniprogram/utils/auth.js
const request = require('./request')

const login = async (code, phone, referrerCode = '') => {
  try {
    const result = await request.post('/api/v1/promotion/auth/login', {
      code,
      phone,
      referrer_code: referrerCode
    })
    
    if (result.success) {
      wx.setStorageSync('token', result.token)
      wx.setStorageSync('userInfo', result.user)
      return result
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    throw error
  }
}

const register = async (code, phone, referrerCode = '') => {
  try {
    const result = await request.post('/api/v1/promotion/auth/register', {
      code,
      phone,
      referrer_code: referrerCode
    })
    
    if (result.success) {
      wx.setStorageSync('token', result.token)
      wx.setStorageSync('userInfo', result.user)
      return result
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    throw error
  }
}

const logout = () => {
  wx.removeStorageSync('token')
  wx.removeStorageSync('userInfo')
  wx.redirectTo({
    url: '/pages/register/register'
  })
}

const getCurrentUser = () => {
  return wx.getStorageSync('userInfo')
}

const isLoggedIn = () => {
  return !!wx.getStorageSync('token')
}

module.exports = {
  login,
  register,
  logout,
  getCurrentUser,
  isLoggedIn
}
```

---

## 🔧 第五阶段：系统集成与测试（第4周）

### 5.1 与现有系统集成

#### 5.1.1 数据库集成
```bash
# 1. 运行数据库迁移
cd /data/my-real-estate-app
source promotion_env/bin/activate
alembic upgrade head

# 2. 验证表结构
mysql -h localhost -u postgres -p postgres_main -e "
SHOW TABLES LIKE 'promotion%';
DESCRIBE promotion_records;
DESCRIBE commission_records;
DESCRIBE promotion_relations;
"
```

#### 5.1.2 API集成测试
```python
# test_integration.py
import requests
import json

def test_promotion_api():
    base_url = "https://your-api-domain.com"
    
    # 测试用户注册
    register_data = {
        "code": "test-wx-code",
        "phone": "13800138000",
        "referrer_code": "promo_1"
    }
    
    response = requests.post(
        f"{base_url}/api/v1/promotion/auth/register",
        json=register_data
    )
    
    print("注册测试:", response.json())
    
    # 测试推广提交
    if response.status_code == 200:
        token = response.json()['token']
        
        # 模拟文件上传
        files = {'image': open('test-phone-image.jpg', 'rb')}
        data = {'phone_number': '13800138001'}
        
        upload_response = requests.post(
            f"{base_url}/api/v1/promotion/submit",
            files=files,
            data=data,
            headers={'Authorization': f'Bearer {token}'}
        )
        
        print("推广提交测试:", upload_response.json())

if __name__ == "__main__":
    test_promotion_api()
```

### 5.2 小程序发布流程

#### 5.2.1 小程序审核准备
```yaml
审核准备清单:
  基础信息:
    - 小程序名称: "慧选址推广"
    - 小程序简介: "商业地产推广赚钱平台"
    - 服务类目: "工具 > 商务服务"
  
  功能页面:
    - 首页: 展示推广功能和统计数据
    - 注册页: 用户注册和登录
    - 推广页: 拍照上传电话号码
    - 个人中心: 查看收益和记录
  
  隐私政策:
    - 用户协议: 明确数据收集和使用
    - 隐私政策: 说明个人信息保护
    - 权限说明: 相机权限使用说明
  
  测试账号:
    - 准备3-5个测试账号
    - 覆盖不同使用场景
    - 准备测试数据
```

#### 5.2.2 提交审核流程
```yaml
审核提交流程:
  步骤1: 完善小程序信息
    - 填写小程序基本信息
    - 上传小程序图标
    - 设置服务类目
  
  步骤2: 上传代码
    - 在微信开发者工具中点击"上传"
    - 填写版本号和项目备注
    - 上传代码包
  
  步骤3: 提交审核
    - 在微信公众平台选择"版本管理"
    - 点击"提交审核"
    - 选择审核版本
    - 填写审核信息
  
  步骤4: 等待审核
    - 审核时间: 1-7个工作日
    - 审核状态: 审核中/审核通过/审核拒绝
    - 拒绝原因: 根据反馈修改
  
  步骤5: 发布上线
    - 审核通过后点击"发布"
    - 小程序正式上线
    - 用户可搜索使用
```

### 5.3 系统测试

#### 5.3.1 功能测试清单
```yaml
功能测试清单:
  用户注册登录:
    - [ ] 微信授权登录
    - [ ] 手机号验证
    - [ ] 推广码验证
    - [ ] 用户信息保存
  
  推广功能:
    - [ ] 相机拍照
    - [ ] 图片选择
    - [ ] OCR识别
    - [ ] 号码验证
    - [ ] 重复检查
    - [ ] 佣金计算
  
  二维码功能:
    - [ ] 二维码生成
    - [ ] 二维码分享
    - [ ] 推广关系绑定
  
  个人中心:
    - [ ] 收益统计
    - [ ] 推广记录
    - [ ] 提现功能
    - [ ] 邀请功能
  
  系统集成:
    - [ ] 与主业务系统数据同步
    - [ ] 用户信息一致性
    - [ ] 佣金发放准确性
```

#### 5.3.2 性能测试
```python
# performance_test.py
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

async def test_api_performance():
    """测试API性能"""
    base_url = "https://your-api-domain.com"
    
    # 模拟并发请求
    async def make_request(session, i):
        start_time = time.time()
        try:
            async with session.get(f"{base_url}/api/v1/health") as response:
                end_time = time.time()
                return {
                    'request_id': i,
                    'status': response.status,
                    'response_time': end_time - start_time
                }
        except Exception as e:
            return {
                'request_id': i,
                'status': 'error',
                'error': str(e)
            }
    
    # 创建100个并发请求
    async with aiohttp.ClientSession() as session:
        tasks = [make_request(session, i) for i in range(100)]
        results = await asyncio.gather(*tasks)
        
        # 统计结果
        successful_requests = [r for r in results if r['status'] == 200]
        failed_requests = [r for r in results if r['status'] != 200]
        
        if successful_requests:
            avg_response_time = sum(r['response_time'] for r in successful_requests) / len(successful_requests)
            print(f"成功请求数: {len(successful_requests)}")
            print(f"失败请求数: {len(failed_requests)}")
            print(f"平均响应时间: {avg_response_time:.2f}秒")
        else:
            print("所有请求都失败了")

if __name__ == "__main__":
    asyncio.run(test_api_performance())
```

---

## 🚀 第六阶段：上线与运营（第5周及以后）

### 6.1 上线准备

#### 6.1.1 生产环境部署
```bash
# 1. 部署后端服务
cd /data/my-real-estate-app
docker-compose -f docker-compose.yml up -d

# 2. 配置Nginx反向代理
cat > /etc/nginx/sites-available/promotion-api << 'EOF'
server {
    listen 443 ssl;
    server_name api.your-domain.com;
    
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 3. 重启Nginx
systemctl restart nginx
```

#### 6.1.2 监控配置
```yaml
监控配置清单:
  服务器监控:
    - CPU使用率
    - 内存使用率
    - 磁盘空间
    - 网络流量
  
  应用监控:
    - API响应时间
    - 错误率
    - 并发连接数
    - 数据库连接
  
  业务监控:
    - 用户注册数
    - 推广提交数
    - 佣金发放数
    - 成功率统计
  
  告警配置:
    - 服务器负载过高
    - API错误率超过阈值
    - 数据库连接异常
    - 业务异常波动
```

### 6.2 运营推广

#### 6.2.1 种子用户招募
```yaml
种子用户招募计划:
  目标人群:
    - 现有商业地产APP用户
    - 地产中介从业人员
    - 商业地产投资者
  
  招募渠道:
    - 现有APP内推广
    - 地产行业社群
    - 线下地推活动
    - 朋友圈转发
  
  激励政策:
    - 首周双倍佣金
    - 推荐奖励
    - 等级加速
    - 专属客服
  
  培训支持:
    - 使用教程视频
    - 常见问题解答
    - 一对一指导
    - 成功案例分享
```

#### 6.2.2 数据分析体系
```python
# analytics_dashboard.py
import pandas as pd
import matplotlib.pyplot as plt
import requests
from datetime import datetime, timedelta

class PromotionAnalytics:
    def __init__(self, api_base_url, api_key):
        self.api_base_url = api_base_url
        self.api_key = api_key
        
    def get_daily_stats(self, date):
        """获取每日统计数据"""
        response = requests.get(
            f"{self.api_base_url}/api/v1/analytics/daily",
            headers={'Authorization': f'Bearer {self.api_key}'},
            params={'date': date}
        )
        return response.json()
    
    def generate_weekly_report(self):
        """生成周报"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        # 获取一周数据
        daily_data = []
        current_date = start_date
        while current_date <= end_date:
            stats = self.get_daily_stats(current_date.strftime('%Y-%m-%d'))
            daily_data.append(stats)
            current_date += timedelta(days=1)
        
        # 生成报告
        df = pd.DataFrame(daily_data)
        
        # 计算关键指标
        total_submissions = df['submissions'].sum()
        total_commission = df['commission'].sum()
        success_rate = df['success_rate'].mean()
        
        report = {
            'period': f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}",
            'total_submissions': total_submissions,
            'total_commission': total_commission,
            'success_rate': success_rate,
            'daily_average': total_submissions / 7
        }
        
        return report
    
    def generate_charts(self, data):
        """生成图表"""
        df = pd.DataFrame(data)
        
        # 提交趋势图
        plt.figure(figsize=(12, 6))
        plt.plot(df['date'], df['submissions'], marker='o')
        plt.title('每日提交趋势')
        plt.xlabel('日期')
        plt.ylabel('提交数量')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('submission_trend.png')
        
        # 成功率趋势图
        plt.figure(figsize=(12, 6))
        plt.plot(df['date'], df['success_rate'], marker='o', color='green')
        plt.title('成功率趋势')
        plt.xlabel('日期')
        plt.ylabel('成功率(%)')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('success_rate_trend.png')

# 使用示例
analytics = PromotionAnalytics('https://your-api-domain.com', 'your-api-key')
weekly_report = analytics.generate_weekly_report()
print(weekly_report)
```

### 6.3 持续优化

#### 6.3.1 数据驱动优化
```yaml
优化策略:
  用户体验优化:
    - 分析用户流失节点
    - 优化关键转化路径
    - 提升操作便捷性
    - 减少等待时间
  
  业务流程优化:
    - 优化OCR识别准确率
    - 提升审核效率
    - 优化佣金发放速度
    - 完善防作弊机制
  
  技术架构优化:
    - 提升系统性能
    - 优化数据库查询
    - 增强系统稳定性
    - 扩展系统容量
  
  运营策略优化:
    - 优化用户激励政策
    - 提升用户活跃度
    - 扩大推广渠道
    - 优化成本结构
```

#### 6.3.2 版本迭代计划
```yaml
版本迭代计划:
  V1.1 (第6周):
    - 优化OCR识别准确率
    - 增加批量提交功能
    - 优化用户界面
    - 修复已知问题
  
  V1.2 (第8周):
    - 增加数据分析功能
    - 优化佣金结算流程
    - 增加推广素材库
    - 完善防作弊机制
  
  V1.3 (第12周):
    - 增加团队管理功能
    - 优化推广员等级体系
    - 增加营销活动功能
    - 完善数据分析
  
  V2.0 (第16周):
    - 重构前端架构
    - 优化后端性能
    - 增加AI智能推荐
    - 完善生态建设
```

---

## 📋 总结

### 实施时间表
| 阶段 | 时间 | 主要任务 | 产出物 |
|------|------|----------|--------|
| 第一阶段 | 第1周 | 准备工作 | 法律文档、开发环境 |
| 第二阶段 | 第1-2周 | 小程序注册 | 小程序账号、开发配置 |
| 第三阶段 | 第2-3周 | 后端开发 | API接口、数据库结构 |
| 第四阶段 | 第3-4周 | 前端开发 | 小程序页面、功能实现 |
| 第五阶段 | 第4周 | 集成测试 | 系统集成、测试报告 |
| 第六阶段 | 第5周 | 上线运营 | 生产部署、运营推广 |

### 关键成功因素
1. **法律合规**：确保所有操作符合法律法规
2. **技术稳定**：保证系统稳定运行和数据安全
3. **用户体验**：提供简洁易用的操作界面
4. **运营推广**：有效的用户招募和激励政策
5. **持续优化**：基于数据驱动的持续改进

### 风险控制
1. **法律风险**：定期合规审查，建立风险准备金
2. **技术风险**：完善的监控和备份机制
3. **运营风险**：建立用户反馈和投诉处理机制
4. **财务风险**：严格的成本控制和资金管理

通过以上详细的实施方案，您可以一步步构建完整的推广小程序系统，并与现有商业地产APP无缝集成。
