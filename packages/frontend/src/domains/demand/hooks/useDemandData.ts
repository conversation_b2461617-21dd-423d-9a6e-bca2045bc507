/**
 * 🏗️ 企业级架构：需求数据管理Hook
 * 遵循前端企业级架构规范 - Hook层
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { DemandAPI } from '../services/demandAPI';
import type { DemandResponse } from '../services/demandService';

interface DemandStatistics {
  total_demands: number;
  status_breakdown: Record<string, number>;
  type_breakdown: Record<string, number>;
  total_responses: number;
  total_matches: number;
  total_views: number;
  avg_response_rate: number;
}

interface UseDemandDataReturn {
  demands: DemandResponse[];
  statistics: DemandStatistics | null;
  loading: boolean;
  error: string | null;
  refreshDemands: () => Promise<void>;
  loadDemandsByType: (demandType: 'RENTAL' | 'PURCHASE') => Promise<DemandResponse[]>;
  loadDemandsByStatus: (status: string) => Promise<DemandResponse[]>;
}

export const useDemandData = (): UseDemandDataReturn => {
  const { state } = useAuth();
  const user = state.user;

  // 🏪 Store层：状态管理
  const [demands, setDemands] = useState<DemandResponse[]>([]);
  const [statistics, setStatistics] = useState<DemandStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 🌐 API层：加载统计数据
  const loadStatistics = useCallback(async () => {
    if (!user) return;

    try {
      const response = await DemandAPI.getDemandStatistics();
      if (response.success && response.data) {
        setStatistics(response.data);
      } else {
        console.warn('获取统计数据失败:', response.message);
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
      setError('加载统计数据失败');
    }
  }, [user]);

  // 🌐 API层：加载所有需求
  const loadAllDemands = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      const response = await DemandAPI.getMyDemands({
        size: 100,
        page: 1
      });

      if (response.success && response.data) {
        setDemands(response.data.items || []);
      } else {
        console.warn('获取需求列表失败:', response.message);
        setDemands([]);
        setError(response.message || '获取需求列表失败');
      }
    } catch (error) {
      console.error('加载需求列表失败:', error);
      setError('加载需求列表失败');
      setDemands([]);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // 🌐 API层：按类型加载需求
  const loadDemandsByType = useCallback(async (demandType: 'RENTAL' | 'PURCHASE'): Promise<DemandResponse[]> => {
    if (!user) return [];

    try {
      const response = await DemandAPI.getMyDemands({
        demand_type: demandType,
        size: 50,
        page: 1
      });

      if (response.success && response.data) {
        return response.data.items || [];
      } else {
        console.warn('获取需求列表失败:', response.message);
        return [];
      }
    } catch (error) {
      console.error('加载需求列表失败:', error);
      return [];
    }
  }, [user]);

  // 🌐 API层：按状态加载需求
  const loadDemandsByStatus = useCallback(async (status: string): Promise<DemandResponse[]> => {
    if (!user) return [];

    try {
      const response = await DemandAPI.getMyDemands({
        status: status.toUpperCase(),
        size: 50,
        page: 1
      });

      if (response.success && response.data) {
        return response.data.items || [];
      } else {
        console.warn('获取需求列表失败:', response.message);
        return [];
      }
    } catch (error) {
      console.error('加载需求列表失败:', error);
      return [];
    }
  }, [user]);

  // 🔧 Hook层：刷新数据
  const refreshDemands = useCallback(async () => {
    await Promise.all([
      loadAllDemands(),
      loadStatistics(),
    ]);
  }, [loadAllDemands, loadStatistics]);

  // 🔧 Hook层：初始化数据
  useEffect(() => {
    if (user) {
      refreshDemands();
    }
  }, [user, refreshDemands]);

  return {
    demands,
    statistics,
    loading,
    error,
    refreshDemands,
    loadDemandsByType,
    loadDemandsByStatus,
  };
};
