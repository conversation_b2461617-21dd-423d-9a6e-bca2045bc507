/**
 * 消息系统Zustand状态管理Store
 * 符合企业级架构规范，集成devtools、persist、subscribeWithSelector中间件
 */
import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { messageAPI } from '../services/messageAPI';
import { smsAPI } from '../services/smsAPI';
import { getMessageTransformers } from '../transformers/TransformerFactory';
import type { MessageCategory } from '../types/businessMessage.types';
import type {
  MessageQueryInput,
  MessageData,
  UserRoleInfo,
  PaymentUnlockInfo,
  InteractedUser,
  SMSCreditAccount,
} from '../types/message.types';

/**
 * 消息状态接口
 */
interface MessageState {
  // 基础状态
  activeRole: 'tenant_buyer' | 'landlord';
  activeCategory: MessageCategory;
  isDataEnabled: boolean;
  
  // 数据状态（使用类型安全的接口）
  userRoles: UserRoleInfo | null;
  messageData: MessageData[] | null;
  paymentInfo: PaymentUnlockInfo | null;
  interactedUsers: InteractedUser[] | null;
  smsAccount: SMSCreditAccount | null;
  
  // 加载状态
  isLoadingUserRoles: boolean;
  isLoadingMessages: boolean;
  isLoadingPayment: boolean;
  isLoadingUsers: boolean;
  isLoadingSMS: boolean;
  
  // 错误状态
  userRolesError: string | null;
  messagesError: string | null;
  paymentError: string | null;
  usersError: string | null;
  smsError: string | null;
  
  // UI状态
  refreshing: boolean;
  showPaymentModal: boolean;
}

/**
 * 消息操作接口
 */
interface MessageActions {
  // 基础操作
  setActiveRole: (role: 'tenant_buyer' | 'landlord') => void;
  setActiveCategory: (category: MessageCategory) => void;
  enableDataLoading: () => void;
  disableDataLoading: () => void;
  
  // 数据操作
  loadUserRoles: () => Promise<void>;
  loadMessages: () => Promise<void>;
  loadPaymentInfo: () => Promise<void>;
  loadInteractedUsers: () => Promise<void>;
  loadSMSAccount: () => Promise<void>;
  refreshAllData: () => Promise<void>;
  
  // UI操作
  setRefreshing: (refreshing: boolean) => void;
  setShowPaymentModal: (show: boolean) => void;
  
  // 重置操作
  resetErrors: () => void;
  resetData: () => void;
  resetAll: () => void;
}

/**
 * 初始状态
 */
const initialState: MessageState = {
  // 基础状态
  activeRole: 'tenant_buyer',
  activeCategory: 'all',
  isDataEnabled: false,
  
  // 数据状态
  userRoles: null,
  messageData: null,
  paymentInfo: null,
  interactedUsers: null,
  smsAccount: null,
  
  // 加载状态
  isLoadingUserRoles: false,
  isLoadingMessages: false,
  isLoadingPayment: false,
  isLoadingUsers: false,
  isLoadingSMS: false,
  
  // 错误状态
  userRolesError: null,
  messagesError: null,
  paymentError: null,
  usersError: null,
  smsError: null,
  
  // UI状态
  refreshing: false,
  showPaymentModal: false,
};

/**
 * 消息Store类型
 */
export type MessageStore = MessageState & MessageActions;

/**
 * 创建消息Store
 */
export const useMessageStore = create<MessageStore>()(
  devtools(
    persist(
      subscribeWithSelector((set, get) => ({
        ...initialState,

        // 基础操作
        setActiveRole: (role) => {
          console.log(`[MessageStore] 切换角色: ${role}`);
          set({ activeRole: role });
          // 移除手动调用updateComputedProperties，避免循环
        },

        setActiveCategory: (category) => {
          console.log(`[MessageStore] 切换分类: ${category}`);
          set({ activeCategory: category });
          // 移除手动调用updateComputedProperties，避免循环
        },

        enableDataLoading: () => {
          console.log('[MessageStore] 启用数据加载');
          set({ isDataEnabled: true });
        },

        disableDataLoading: () => {
          console.log('[MessageStore] 禁用数据加载');
          set({ isDataEnabled: false });
        },

        // 数据操作
        loadUserRoles: async () => {
          const { isDataEnabled } = get();
          if (!isDataEnabled) return;

          set({ isLoadingUserRoles: true, userRolesError: null });
          try {
            console.log('[MessageStore] 加载用户角色信息');
            const apiData = await messageAPI.getUserRoleInfo();

            // 使用转换层转换数据
            const transformers = getMessageTransformers();
            const transformResult = transformers.userRoleInfo.fromAPI(apiData);

            if (transformResult.success && transformResult.data) {
              set({ userRoles: transformResult.data, isLoadingUserRoles: false });
            } else {
              throw new Error(transformResult.error || '数据转换失败');
            }
          } catch (error) {
            console.error('[MessageStore] 加载用户角色失败:', error);
            set({
              userRolesError: error instanceof Error ? error.message : '加载失败',
              isLoadingUserRoles: false
            });
          }
          // 移除手动调用updateComputedProperties，避免循环
        },

        loadMessages: async () => {
          const { isDataEnabled, activeRole, activeCategory } = get();
          if (!isDataEnabled) return;

          set({ isLoadingMessages: true, messagesError: null });
          try {
            console.log(`[MessageStore] 加载消息列表: ${activeRole} - ${activeCategory}`);

            // 构建查询参数并使用转换层
            const queryInput: MessageQueryInput = {
              role: activeRole,
              category: activeCategory,
              limit: 20,
              skip: 0,
            };

            const transformers = getMessageTransformers();
            const queryTransformResult = transformers.messageQuery.toAPI(queryInput);

            if (!queryTransformResult.success || !queryTransformResult.data) {
              throw new Error(queryTransformResult.error || '查询参数转换失败');
            }

            // 调用API（这里简化处理，实际应该传递转换后的参数）
            const apiData = await messageAPI.getMessageList(activeRole, activeCategory);

            // 转换返回数据
            if (Array.isArray(apiData)) {
              const transformedMessages: MessageData[] = [];
              for (const item of apiData) {
                const transformResult = transformers.messageData.fromAPI(item);
                if (transformResult.success && transformResult.data) {
                  transformedMessages.push(transformResult.data);
                }
              }
              set({ messageData: transformedMessages, isLoadingMessages: false });
            } else {
              set({ messageData: [], isLoadingMessages: false });
            }
          } catch (error) {
            console.error('[MessageStore] 加载消息失败:', error);
            set({
              messagesError: error instanceof Error ? error.message : '加载失败',
              isLoadingMessages: false
            });
          }
          // 移除手动调用updateComputedProperties，避免循环
        },

        loadPaymentInfo: async () => {
          const { isDataEnabled, activeCategory } = get();
          if (!isDataEnabled) return;

          set({ isLoadingPayment: true, paymentError: null });
          try {
            console.log(`[MessageStore] 加载付费信息: ${activeCategory}`);
            const apiData = await messageAPI.getPaymentUnlockInfo(activeCategory);

            // 使用转换层转换数据
            const transformers = getMessageTransformers();
            const transformResult = transformers.paymentUnlockInfo.fromAPI(apiData);

            if (transformResult.success && transformResult.data) {
              set({ paymentInfo: transformResult.data, isLoadingPayment: false });
            } else {
              throw new Error(transformResult.error || '数据转换失败');
            }
          } catch (error) {
            console.error('[MessageStore] 加载付费信息失败:', error);
            set({
              paymentError: error instanceof Error ? error.message : '加载失败',
              isLoadingPayment: false
            });
          }
          // 移除手动调用updateComputedProperties，避免循环
        },

        loadInteractedUsers: async () => {
          const { isDataEnabled } = get();
          if (!isDataEnabled) return;

          set({ isLoadingUsers: true, usersError: null });
          try {
            console.log('[MessageStore] 加载互动用户');
            const apiData = await messageAPI.getInteractedUsers();

            // 使用转换层转换数据
            const transformers = getMessageTransformers();
            if (Array.isArray(apiData)) {
              const transformedUsers: InteractedUser[] = [];
              for (const item of apiData) {
                const transformResult = transformers.interactedUser.fromAPI(item);
                if (transformResult.success && transformResult.data) {
                  transformedUsers.push(transformResult.data);
                }
              }
              set({ interactedUsers: transformedUsers, isLoadingUsers: false });
            } else {
              set({ interactedUsers: [], isLoadingUsers: false });
            }
          } catch (error) {
            console.error('[MessageStore] 加载互动用户失败:', error);
            set({
              usersError: error instanceof Error ? error.message : '加载失败',
              isLoadingUsers: false
            });
          }
          // 移除手动调用updateComputedProperties，避免循环
        },

        loadSMSAccount: async () => {
          const { isDataEnabled } = get();
          if (!isDataEnabled) return;

          set({ isLoadingSMS: true, smsError: null });
          try {
            console.log('[MessageStore] 加载SMS账户信息');
            const apiData = await smsAPI.getSMSCreditAccount();

            // 使用转换层转换数据
            const transformers = getMessageTransformers();
            const transformResult = transformers.smsCreditAccount.fromAPI(apiData);

            if (transformResult.success && transformResult.data) {
              set({ smsAccount: transformResult.data, isLoadingSMS: false });
            } else {
              throw new Error(transformResult.error || '数据转换失败');
            }
          } catch (error) {
            console.error('[MessageStore] 加载SMS账户失败:', error);
            set({
              smsError: error instanceof Error ? error.message : '加载失败',
              isLoadingSMS: false
            });
          }
          // 移除手动调用updateComputedProperties，避免循环
        },

        refreshAllData: async () => {
          console.log('[MessageStore] 刷新所有数据');
          set({ refreshing: true });
          
          const actions = get();
          await Promise.all([
            actions.loadUserRoles(),
            actions.loadMessages(),
            actions.loadPaymentInfo(),
            actions.loadInteractedUsers(),
            actions.loadSMSAccount(),
          ]);
          
          set({ refreshing: false });
        },

        // UI操作
        setRefreshing: (refreshing) => {
          set({ refreshing });
        },

        setShowPaymentModal: (show) => {
          console.log(`[MessageStore] ${show ? '显示' : '隐藏'}付费弹窗`);
          set({ showPaymentModal: show });
        },

        // 重置操作
        resetErrors: () => {
          console.log('[MessageStore] 重置错误状态');
          set({
            userRolesError: null,
            messagesError: null,
            paymentError: null,
            usersError: null,
            smsError: null,
          });
        },

        resetData: () => {
          console.log('[MessageStore] 重置数据');
          set({
            userRoles: null,
            messageData: null,
            paymentInfo: null,
            interactedUsers: null,
            smsAccount: null,
          });
          // 移除手动调用updateComputedProperties，避免循环
        },

        resetAll: () => {
          console.log('[MessageStore] 重置所有状态');
          set(initialState);
        },
      })),
      {
        name: 'message-store', // 持久化存储键名
        partialize: (state) => ({
          // 只持久化必要的状态，不持久化加载状态和错误状态
          activeRole: state.activeRole,
          activeCategory: state.activeCategory,
          isDataEnabled: state.isDataEnabled,
        }),
        // 🔧 添加错误处理，避免存储警告
        onRehydrateStorage: () => (state, error) => {
          if (error) {
            console.warn('[MessageStore] 持久化恢复失败:', error);
          } else {
            console.log('[MessageStore] 持久化恢复成功');
          }
        },
      }
    ),
    {
      name: 'MessageStore', // DevTools中显示的名称
    }
  )
);
