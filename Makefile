.PHONY: help dev up down logs clean test migrate shell format lint check restart stop status

help: ## 显示帮助信息
	@echo "📋 慧选址商业地产APP - 容器化开发环境"
	@echo "============================================"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-15s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "🏗️ 常用开发流程："
	@echo "  1. make dev     # 启动开发环境"
	@echo "  2. make logs    # 查看实时日志"
	@echo "  3. make check   # 检查服务状态"
	@echo "  4. make down    # 停止开发环境"

dev: ## 启动开发环境（推荐）
	@echo "🚀 启动开发环境..."
	docker compose -f docker-compose.yml -f docker-compose.override.yml up -d
	@echo "⏳ 等待服务启动..."
	@sleep 10
	@echo "✅ 开发环境已启动："
	@echo "   🌐 后端API: http://localhost:8082"
	@echo "   📊 健康检查: http://localhost:8082/health"
	@echo "   💾 数据库: localhost:5432"
	@echo "   🔴 Redis: localhost:6379"

up: ## 启动生产环境
	@echo "🏭 启动生产环境..."
	docker compose up -d

down: ## 停止所有服务
	@echo "🛑 停止所有服务..."
	docker compose down

stop: ## 停止服务（保持数据）
	@echo "⏸️ 停止服务（保持数据）..."
	docker compose stop

restart: ## 重启后端服务
	@echo "🔄 重启后端服务..."
	docker compose restart backend
	@echo "⏳ 等待服务重启..."
	@sleep 5

logs: ## 查看后端实时日志
	@echo "📜 查看后端实时日志（Ctrl+C退出）..."
	docker compose logs -f backend

logs-all: ## 查看所有服务日志
	@echo "📜 查看所有服务实时日志（Ctrl+C退出）..."
	docker compose logs -f

clean: ## 清理容器和数据卷
	@echo "🧹 清理容器和数据卷..."
	docker compose down -v
	docker system prune -f
	@echo "✅ 清理完成"

test: ## 运行测试
	@echo "🧪 运行测试..."
	docker compose exec backend pytest -v

migrate: ## 运行数据库迁移
	@echo "🗄️ 运行数据库迁移..."
	docker compose exec backend alembic upgrade head

shell: ## 进入后端容器Shell
	@echo "🐚 进入后端容器Shell..."
	docker compose exec backend bash

format: ## 代码格式化
	@echo "✨ 代码格式化..."
	docker compose exec backend black .

lint: ## 代码检查
	@echo "🔍 代码检查..."
	docker compose exec backend ruff check .

check: ## 健康检查
	@echo "🔍 检查服务状态..."
	@echo ""
	@echo "📊 容器状态："
	@docker compose ps
	@echo ""
	@echo "🌐 后端健康检查："
	@curl -f http://localhost:8082/health 2>/dev/null && echo "✅ 后端服务正常" || echo "❌ 后端服务异常"
	@echo ""
	@echo "💾 数据库连接："
	@docker compose exec db pg_isready -U postgres 2>/dev/null && echo "✅ 数据库连接正常" || echo "❌ 数据库连接异常"
	@echo ""
	@echo "🔴 Redis连接："
	@docker compose exec redis redis-cli ping 2>/dev/null | grep -q "PONG" && echo "✅ Redis连接正常" || echo "❌ Redis连接异常"

status: ## 查看容器状态
	@echo "📊 容器状态："
	docker compose ps

# 高级开发命令
standardize: ## 执行容器标准化（清理非标准容器）
	@echo "🏗️ 执行容器标准化..."
	./scripts/container-standardize.sh

db-shell: ## 进入数据库Shell
	@echo "🗄️ 进入数据库Shell..."
	docker compose exec db psql -U postgres -d realestate

redis-shell: ## 进入Redis Shell
	@echo "🔴 进入Redis Shell..."
	docker compose exec redis redis-cli

# 测试环境命令
test-env: ## 启动测试环境（包含backend-test容器）
	@echo "🧪 启动测试环境..."
	docker compose --profile testing up -d

backup-db: ## 备份数据库
	@echo "💾 备份数据库..."
	@mkdir -p backups
	docker compose exec db pg_dump -U postgres realestate > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ 数据库已备份到 backups/ 目录"

# 故障排查命令
debug: ## 故障排查模式
	@echo "🐛 故障排查模式："
	@echo ""
	@echo "1. 容器状态："
	@docker compose ps
	@echo ""
	@echo "2. 最近的错误日志："
	@docker compose logs backend --tail 20 | grep -i error || echo "无错误日志"
	@echo ""
	@echo "3. 资源使用情况："
	@docker stats --no-stream
	@echo ""
	@echo "4. 端口占用："
	@netstat -tlnp | grep -E ':(8082|5432|6379)' || echo "端口未占用"

# 开发效率命令
install: ## 安装后端依赖
	@echo "📦 安装后端依赖..."
	docker compose exec backend pip install -e .

reinstall: ## 重新安装依赖（清理缓存）
	@echo "🔄 重新安装依赖..."
	docker compose exec backend pip install --no-cache-dir -e .

update-deps: ## 更新依赖包
	@echo "⬆️ 更新依赖包..."
	docker compose exec backend pip install --upgrade -r requirements.txt

# 代码质量命令
type-check: ## 类型检查
	@echo "🔍 TypeScript类型检查..."
	docker compose exec backend mypy app || echo "⚠️ mypy未安装"

security-check: ## 安全检查
	@echo "🔒 安全检查..."
	docker compose exec backend safety check || echo "⚠️ safety未安装"

# 数据管理命令
reset-db: ## 重置数据库（危险操作）
	@echo "⚠️ 重置数据库（将删除所有数据）"
	@echo "按Ctrl+C取消，或等待5秒继续..."
	@sleep 5
	docker compose down
	docker volume rm my-real-estate-app_pgdata || echo "数据卷不存在"
	docker compose up -d db redis
	@echo "等待数据库启动..."
	@sleep 10
	$(MAKE) migrate
	docker compose up -d backend

seed-db: ## 填充测试数据
	@echo "🌱 填充测试数据..."
	docker compose exec backend python -m app.scripts.init_demand_tags || echo "⚠️ 种子脚本需要检查"

# 性能监控命令
monitor: ## 实时监控资源使用
	@echo "📊 实时监控资源使用（Ctrl+C退出）..."
	docker stats

top: ## 查看容器进程
	@echo "👀 查看容器进程..."
	docker compose top

# 网络调试命令
network-info: ## 显示网络信息
	@echo "🌐 Docker网络信息："
	@docker network ls | grep my-real-estate-app
	@echo ""
	@echo "🔗 容器网络详情："
	@docker network inspect my-real-estate-app_default | grep -A 10 -B 5 "Containers" || echo "网络不存在"

# 快速重建命令
rebuild: ## 重建后端镜像
	@echo "🔨 重建后端镜像..."
	docker compose build backend --no-cache
	docker compose up -d backend

rebuild-all: ## 重建所有镜像
	@echo "🔨 重建所有镜像..."
	docker compose build --no-cache
	docker compose up -d

# 日志管理命令
logs-error: ## 查看错误日志
	@echo "❌ 查看错误日志..."
	docker compose logs backend | grep -i "error\|exception\|traceback" | tail -50

logs-clear: ## 清理容器日志
	@echo "🧹 清理容器日志..."
	docker container prune -f
	@echo "✅ 日志已清理"

# 容器管理增强
ps: ## 详细容器状态
	@echo "📋 详细容器状态："
	@docker compose ps -a
	@echo ""
	@echo "💾 数据卷使用："
	@docker volume ls | grep my-real-estate-app

images: ## 查看镜像信息
	@echo "🖼️ 相关镜像："
	@docker images | grep -E "my-real-estate-app|postgres|redis" || echo "无相关镜像"

# 快速修复命令
fix-permissions: ## 修复文件权限
	@echo "🔧 修复文件权限..."
	sudo chown -R $$USER:$$USER ./packages/backend/
	@echo "✅ 权限已修复"

fix-containers: ## 修复容器问题
	@echo "🔧 修复容器问题..."
	docker compose down
	docker system prune -f
	$(MAKE) dev

# 完整测试套件
test-all: ## 运行完整测试套件
	@echo "🧪 运行完整测试套件..."
	docker compose exec backend pytest tests/ -v --tb=short

test-unit: ## 运行单元测试
	@echo "🧪 运行单元测试..."
	docker compose exec backend pytest tests/unit/ -v

test-integration: ## 运行集成测试
	@echo "🧪 运行集成测试..."
	docker compose exec backend pytest tests/integration/ -v

test-api: ## 运行API测试
	@echo "🧪 运行API测试..."
	docker compose exec backend pytest tests/api/ -v