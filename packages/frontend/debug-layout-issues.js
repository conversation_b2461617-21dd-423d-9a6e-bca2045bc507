#!/usr/bin/env node

/**
 * PropertyDetailScreen布局问题调试脚本
 * 深度分析为什么样式修改不生效
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 PropertyDetailScreen布局问题深度调试');
console.log('==========================================');

const PROPERTY_DETAIL_FILE = path.join(__dirname, 'src/screens/Property/PropertyDetailScreen.tsx');

try {
  const content = fs.readFileSync(PROPERTY_DETAIL_FILE, 'utf8');
  
  console.log('📋 关键问题检查:');
  console.log('─'.repeat(50));
  
  // 1. 检查租金左对齐
  const rentLeftAlign = /justifyContent:\s*['"]flex-start['"]/.test(content);
  console.log(`1. 租金数字左对齐: ${rentLeftAlign ? '✅ 已修复' : '❌ 未修复'}`);
  
  // 2. 检查按钮内部边距
  const buttonPadding = content.match(/paddingHorizontal:\s*wp\((\d+)\)/g);
  console.log(`2. 按钮内部边距: ${buttonPadding ? buttonPadding.join(', ') : '❌ 未找到'}`);
  
  // 3. 检查本地视频配置
  const localVideo = /local:\/\/testvideo\.mp4/.test(content);
  console.log(`3. 本地视频配置: ${localVideo ? '✅ 已配置' : '❌ 未配置'}`);
  
  // 4. 检查面积右边距
  const areaRightPadding = /paddingRight:\s*wp\((\d+)\)/.test(content);
  console.log(`4. 面积右边距: ${areaRightPadding ? '✅ 已设置' : '❌ 未设置'}`);
  
  // 5. 检查DynamicMetric样式传递
  const dynamicMetricProps = /containerStyle=\{containerStyle\}/.test(content);
  console.log(`5. DynamicMetric样式传递: ${dynamicMetricProps ? '✅ 已实现' : '❌ 未实现'}`);
  
  console.log('\n🔧 详细分析:');
  console.log('─'.repeat(50));
  
  // 分析按钮边距值
  if (buttonPadding) {
    buttonPadding.forEach(match => {
      const value = match.match(/wp\((\d+)\)/)[1];
      const pixels = parseInt(value) * 2.67; // 假设wp(1) ≈ 2.67px
      console.log(`   ${match} ≈ ${pixels.toFixed(1)}px`);
      
      if (parseInt(value) < 3) {
        console.log(`   ⚠️  警告: wp(${value})太小，可能看不出效果`);
      }
    });
  }
  
  // 分析容器边距
  const containerPadding = content.match(/paddingHorizontal:\s*wp\(8\)/);
  if (containerPadding) {
    console.log(`   外层容器边距: wp(8) ≈ 21.3px`);
  }
  
  // 分析租金左边距
  const rentPadding = content.match(/paddingLeft:\s*wp\(0\)/);
  if (rentPadding) {
    console.log(`   租金左边距: wp(0) = 0px (完全贴边)`);
  }
  
  console.log('\n💡 可能的问题原因:');
  console.log('─'.repeat(50));
  
  // 检查可能的问题
  const issues = [];
  
  if (!rentLeftAlign) {
    issues.push('keyMetricsRowStandalone仍然使用center对齐');
  }
  
  if (buttonPadding && buttonPadding.some(p => parseInt(p.match(/wp\((\d+)\)/)[1]) < 3)) {
    issues.push('按钮内部边距值太小，视觉效果不明显');
  }
  
  if (!localVideo) {
    issues.push('本地视频仍使用require()而非local://前缀');
  }
  
  // 检查样式覆盖问题
  const styleOverrides = content.match(/style=\{\[.*\]\}/g);
  if (styleOverrides && styleOverrides.length > 5) {
    issues.push('可能存在样式覆盖冲突，内联样式过多');
  }
  
  if (issues.length === 0) {
    console.log('✅ 未发现明显问题，可能是以下原因:');
    console.log('   1. Metro bundler缓存问题');
    console.log('   2. 设备端缓存未清除');
    console.log('   3. DynamicMetric组件内部样式仍有强制设置');
    console.log('   4. 响应式函数wp()计算结果在当前设备上效果不明显');
  } else {
    console.log('❌ 发现以下问题:');
    issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
  }
  
  console.log('\n🚀 建议的修复步骤:');
  console.log('─'.repeat(50));
  console.log('1. 完全清除缓存: npx expo start --clear');
  console.log('2. 重启设备上的Expo Go或开发构建');
  console.log('3. 检查DynamicMetric组件是否移除了强制居中样式');
  console.log('4. 验证wp()函数在当前设备上的实际像素值');
  console.log('5. 使用React Native Debugger检查实际应用的样式');
  
  console.log('\n📱 设备调试建议:');
  console.log('─'.repeat(50));
  console.log('1. 在设备上长按屏幕，选择"Reload"');
  console.log('2. 摇晃设备，选择"Debug"查看样式');
  console.log('3. 使用Flipper或React Native Debugger检查元素');
  console.log('4. 对比修改前后的截图，确认视觉差异');
  
} catch (error) {
  console.error('❌ 读取文件失败:', error.message);
}

console.log('');
