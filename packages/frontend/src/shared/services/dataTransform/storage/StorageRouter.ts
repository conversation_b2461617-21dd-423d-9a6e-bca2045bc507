/**
 * 存储路由器 - 智能存储策略路由
 * 
 * 参考主流APP存储架构：
 * - 微信：根据内容敏感性自动选择存储桶
 * - 抖音：智能路由，优化存储成本和访问速度
 * - 淘宝：多存储桶策略，业务数据隔离
 * - 企业级最佳实践：双桶架构，敏感数据隔离
 * 
 * 核心功能：
 * - 根据业务类型自动选择存储桶
 * - 敏感数据自动路由到安全存储桶
 * - 支持多云存储提供商
 * - 智能存储策略选择
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月16日
 */

import { 
  IStorageRouter, 
  IStorageProvider, 
  StorageConfig, 
  BucketType, 
  StorageProvider,
  StorageStrategy,
  Permission
} from '../types/StorageTypes';
import { SecurityLevel, AccessLevel, MediaType, MediaCategory, MediaBusinessType } from '../transformers/MediaTransformer';

/**
 * 存储路由器实现
 */
export class StorageRouter implements IStorageRouter {
  private providers: Map<BucketType, IStorageProvider> = new Map();
  private configs: Map<string, StorageConfig> = new Map();

  constructor() {
    this.initializeConfigs();
  }
  
  /**
   * 初始化存储配置
   * 参考阿里云OSS双桶架构最佳实践
   */
  private initializeConfigs(): void {
    // 主业务桶配置 - 参考微信朋友圈、抖音视频存储策略
    const mainBucketConfig: StorageConfig = {
      provider: 'aliyun-oss',
      bucket: 'huixuanzhi-main',
      bucketType: 'main',
      region: 'cn-guangzhou',
      endpoint: 'oss-cn-guangzhou.aliyuncs.com',
      strategy: 'standard',
      permissions: ['read', 'write', 'delete'],
      encryption: false,
      lifecycle: [
        {
          id: 'temp-files-cleanup',
          prefix: 'temp/',
          enabled: true,
          transitions: [
            { days: 30, storageClass: 'infrequent' },
            { days: 90, storageClass: 'archive' }
          ],
          expiration: { days: 365 }
        }
      ]
    };
    
    // 敏感数据桶配置 - 参考银行级安全存储标准
    const sensitiveBucketConfig: StorageConfig = {
      provider: 'aliyun-oss',
      bucket: 'huixuanzhi-sensitive',
      bucketType: 'sensitive',
      region: 'cn-guangzhou',
      endpoint: 'oss-cn-guangzhou.aliyuncs.com',
      strategy: 'standard',
      permissions: ['read', 'write'], // 敏感数据不允许随意删除
      encryption: true, // 强制加密
      lifecycle: [
        {
          id: 'sensitive-long-term-storage',
          prefix: 'identity/',
          enabled: true,
          transitions: [
            { days: 90, storageClass: 'infrequent' },
            { days: 365, storageClass: 'archive' }
          ]
          // 敏感数据不自动过期
        }
      ]
    };
    
    // 简化的安全级别到存储配置映射
    const securityLevelMapping = {
      [SecurityLevel.NORMAL]: mainBucketConfig,      // 普通数据 -> 主桶
      [SecurityLevel.SENSITIVE]: sensitiveBucketConfig // 敏感数据 -> 敏感桶
    };

    // 初始化配置映射
    Object.entries(securityLevelMapping).forEach(([securityLevel, config]) => {
      this.configs.set(securityLevel, config);
    });
  }
  
  /**
   * 根据媒体分类路由到对应的存储提供商
   */
  route(category: MediaCategory): IStorageProvider {
    const config = this.getConfig(category.securityLevel);
    const provider = this.providers.get(config.bucketType);

    if (!provider) {
      throw new Error(`Storage provider not found for security level: ${category.securityLevel}`);
    }

    return provider;
  }
  
  /**
   * 根据对象键路由到对应的存储提供商
   * 参考阿里云OSS对象键命名规范
   */
  routeByPath(objectKey: string): IStorageProvider {
    // 根据对象键前缀判断存储桶类型
    const bucketType = this.determineBucketTypeByPath(objectKey);
    const provider = this.providers.get(bucketType);
    
    if (!provider) {
      throw new Error(`Storage provider not found for object key: ${objectKey}`);
    }
    
    return provider;
  }
  
  /**
   * 获取存储配置
   */
  getConfig(securityLevel: SecurityLevel): StorageConfig {
    const config = this.configs.get(securityLevel);

    if (!config) {
      // 默认使用普通数据桶配置
      console.warn(`No config found for security level: ${securityLevel}, using default normal bucket`);
      return this.configs.get(SecurityLevel.NORMAL)!;
    }

    return config;
  }

  /**
   * 生成对象路径
   */
  getObjectPath(category: MediaCategory, fileName: string): string {
    const { securityLevel, accessLevel, mediaType } = category;

    if (securityLevel === SecurityLevel.SENSITIVE) {
      // 敏感数据：sensitive-bucket/mediaType/fileName
      return `${mediaType}s/${fileName}`;
    }

    // 普通数据：main-bucket/accessLevel/mediaType/fileName
    return `${accessLevel}/${mediaType}s/${fileName}`;
  }

  /**
   * 注册存储提供商
   */
  registerProvider(type: BucketType, provider: IStorageProvider): void {
    this.providers.set(type, provider);
    console.log(`Storage provider registered for bucket type: ${type}`);
  }
  
  /**
   * 根据对象键路径确定存储桶类型
   * 参考主流APP的文件路径规范
   */
  private determineBucketTypeByPath(objectKey: string): BucketType {
    // 敏感数据路径前缀
    const sensitivePathPrefixes = [
      'sensitive/',
      'identity/',
      'business/',
      'property-cert/',
      'financial/'
    ];
    
    // 临时文件路径前缀
    const tempPathPrefixes = [
      'temp/',
      'upload-temp/',
      'processing/'
    ];
    
    // 检查是否为敏感数据
    if (sensitivePathPrefixes.some(prefix => objectKey.startsWith(prefix))) {
      return 'sensitive';
    }
    
    // 检查是否为临时文件
    if (tempPathPrefixes.some(prefix => objectKey.startsWith(prefix))) {
      return 'temp';
    }
    
    // 默认为主业务桶
    return 'main';
  }
  
  /**
   * 获取推荐的存储策略
   * 参考阿里云OSS存储类型最佳实践
   */
  getRecommendedStrategy(businessType: MediaBusinessType, fileSize: number): StorageStrategy {
    // 大文件使用标准存储
    if (fileSize > 100 * 1024 * 1024) { // 100MB
      return 'standard';
    }
    
    // 根据业务类型推荐存储策略
    switch (businessType) {
      case MediaBusinessType.PROPERTY_IMAGE:
      case MediaBusinessType.PROPERTY_VIDEO:
        // 房源媒体文件，访问频率高
        return 'standard';
        
      case MediaBusinessType.USER_AVATAR:
        // 用户头像，访问频率中等
        return 'standard';
        
      case MediaBusinessType.IDENTITY_CARD:
      case MediaBusinessType.BUSINESS_LICENSE:
      case MediaBusinessType.PROPERTY_CERT:
        // 证件类文件，访问频率低但需要长期保存
        return 'infrequent';
        
      case MediaBusinessType.FINANCIAL_DOC:
        // 财务文档，访问频率很低
        return 'archive';
        
      default:
        return 'standard';
    }
  }
  
  /**
   * 获取业务类型的权限配置
   * 参考企业级权限管理最佳实践
   */
  getBusinessPermissions(businessType: MediaBusinessType): Permission[] {
    switch (businessType) {
      case MediaBusinessType.IDENTITY_CARD:
      case MediaBusinessType.BUSINESS_LICENSE:
      case MediaBusinessType.PROPERTY_CERT:
      case MediaBusinessType.FINANCIAL_DOC:
        // 敏感数据：只读和写入，不允许删除
        return ['read', 'write'];
        
      case MediaBusinessType.PROPERTY_IMAGE:
      case MediaBusinessType.PROPERTY_VIDEO:
      case MediaBusinessType.USER_AVATAR:
        // 普通业务数据：完整权限
        return ['read', 'write', 'delete'];
        
      default:
        return ['read', 'write'];
    }
  }
  
  /**
   * 获取所有已注册的存储提供商
   */
  getRegisteredProviders(): Map<BucketType, IStorageProvider> {
    return new Map(this.providers);
  }
  
  /**
   * 获取存储桶使用统计
   */
  async getBucketUsageStats(): Promise<Record<BucketType, { objectCount: number; totalSize: number }>> {
    const stats: Record<BucketType, { objectCount: number; totalSize: number }> = {} as any;
    
    for (const [bucketType, provider] of this.providers) {
      try {
        const bucketStats = await provider.getStats();
        stats[bucketType] = {
          objectCount: bucketStats.totalObjects,
          totalSize: bucketStats.totalSize
        };
      } catch (error) {
        console.error(`Failed to get stats for bucket type: ${bucketType}`, error);
        stats[bucketType] = { objectCount: 0, totalSize: 0 };
      }
    }
    
    return stats;
  }
  
  /**
   * 验证存储配置
   */
  validateConfig(businessType: MediaBusinessType): boolean {
    try {
      const config = this.getConfig(businessType);
      const provider = this.providers.get(config.bucketType);
      
      return !!(config && provider && config.bucket && config.region);
    } catch (error) {
      console.error(`Config validation failed for business type: ${businessType}`, error);
      return false;
    }
  }
}
