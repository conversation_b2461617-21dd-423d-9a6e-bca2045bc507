#!/usr/bin/env node

/**
 * 导航生命周期测试脚本
 * 系统性测试组件挂载/卸载问题
 */

const fs = require('fs');

console.log('🧪 导航生命周期测试脚本');
console.log('========================');

// 1. 验证调试日志是否正确添加
const verifyDebugLogs = () => {
  console.log('\n📋 验证调试日志配置...');
  
  const mapScreenFile = 'packages/frontend/src/domains/map/screens/MapSearchScreen.tsx';
  const mapContainerFile = 'packages/frontend/src/shared/components/MapContainer.tsx';
  
  let allChecksPass = true;
  
  // 检查MapSearchScreen调试
  if (fs.existsSync(mapScreenFile)) {
    const content = fs.readFileSync(mapScreenFile, 'utf8');
    
    const hasDetailedMountLog = content.includes('组件挂载 - 开始初始化') && 
                               content.includes('挂载时间');
    const hasDetailedUnmountLog = content.includes('组件卸载 - 开始清理') && 
                                 content.includes('卸载时间') &&
                                 content.includes('卸载原因');
    
    console.log(`  MapSearchScreen详细挂载日志: ${hasDetailedMountLog ? '✅' : '❌'}`);
    console.log(`  MapSearchScreen详细卸载日志: ${hasDetailedUnmountLog ? '✅' : '❌'}`);
    
    if (!hasDetailedMountLog || !hasDetailedUnmountLog) allChecksPass = false;
  } else {
    console.log('  MapSearchScreen文件: ❌ 文件不存在');
    allChecksPass = false;
  }
  
  // 检查MapContainer调试
  if (fs.existsSync(mapContainerFile)) {
    const content = fs.readFileSync(mapContainerFile, 'utf8');
    
    const hasDetailedMountLog = content.includes('组件挂载，开始初始化') && 
                               content.includes('挂载时间') &&
                               content.includes('挂载时props');
    const hasDetailedUnmountLog = content.includes('组件卸载，清理所有异步操作') && 
                                 content.includes('卸载时间') &&
                                 content.includes('卸载原因');
    
    console.log(`  MapContainer详细挂载日志: ${hasDetailedMountLog ? '✅' : '❌'}`);
    console.log(`  MapContainer详细卸载日志: ${hasDetailedUnmountLog ? '✅' : '❌'}`);
    
    if (!hasDetailedMountLog || !hasDetailedUnmountLog) allChecksPass = false;
  } else {
    console.log('  MapContainer文件: ❌ 文件不存在');
    allChecksPass = false;
  }
  
  return allChecksPass;
};

// 2. 分析导航配置
const analyzeNavigationConfig = () => {
  console.log('\n🧭 分析导航配置...');
  
  const navFile = 'packages/frontend/src/navigation/MainTabNavigator.tsx';
  
  if (fs.existsSync(navFile)) {
    const content = fs.readFileSync(navFile, 'utf8');
    
    // 检查Map Tab配置
    const mapTabConfig = content.match(/name="Map"[\s\S]*?\/>/);
    if (mapTabConfig) {
      const configText = mapTabConfig[0];
      
      const usesComponent = configText.includes('component={MapSearchScreen}');
      const usesRenderFunction = configText.includes('{() => (') || configText.includes('children');
      
      console.log(`  Map Tab使用component属性: ${usesComponent ? '✅' : '❌'}`);
      console.log(`  Map Tab避免render函数: ${!usesRenderFunction ? '✅' : '❌'}`);
      
      if (usesRenderFunction) {
        console.log('  ⚠️  发现render函数，这可能导致组件卸载问题');
      }
      
      return usesComponent && !usesRenderFunction;
    } else {
      console.log('  ❌ 无法找到Map Tab配置');
      return false;
    }
  } else {
    console.log('  ❌ 导航配置文件不存在');
    return false;
  }
};

// 3. 生成测试指南
const generateTestGuide = () => {
  console.log('\n📋 实际测试指南:');
  console.log('================');
  
  console.log('\n🎯 测试步骤:');
  console.log('1. 启动应用，观察初始状态');
  console.log('2. 点击"地图找房"Tab，观察挂载日志:');
  console.log('   - [MapSearchScreen] 🚀 组件挂载 - 开始初始化');
  console.log('   - [MapContainer] 🚀 组件挂载，开始初始化');
  console.log('3. 等待地图加载完成');
  console.log('4. 点击"首页"Tab，观察卸载日志:');
  console.log('   - [MapSearchScreen] 🧹 组件卸载 - 开始清理');
  console.log('   - [MapContainer] 🧹 组件卸载，清理所有异步操作');
  console.log('5. 确认MapContainer相关日志不再出现');
  
  console.log('\n🔍 关键观察点:');
  console.log('✅ 正常情况: 切换到首页后，MapContainer日志完全停止');
  console.log('❌ 异常情况: 切换到首页后，MapContainer日志继续出现');
  
  console.log('\n🚨 问题诊断:');
  console.log('如果组件没有正确卸载，可能的原因:');
  console.log('1. React Navigation配置问题（render函数 vs component）');
  console.log('2. 组件内部有未清理的异步操作');
  console.log('3. 状态管理导致的引用保持');
  console.log('4. useEffect依赖数组问题');
  
  console.log('\n📊 预期结果:');
  console.log('- 进入地图页面: 看到挂载日志');
  console.log('- 离开地图页面: 看到卸载日志');
  console.log('- 在其他页面: MapContainer日志不再出现');
};

// 4. 执行完整测试
const runCompleteTest = () => {
  console.log('\n📊 导航生命周期测试报告:');
  console.log('============================');
  
  const debugLogsOk = verifyDebugLogs();
  const navigationConfigOk = analyzeNavigationConfig();
  
  console.log('\n🎯 测试结果汇总:');
  console.log(`✅ 调试日志配置: ${debugLogsOk ? '通过' : '失败'}`);
  console.log(`✅ 导航配置检查: ${navigationConfigOk ? '通过' : '失败'}`);
  
  if (debugLogsOk && navigationConfigOk) {
    console.log('\n🎉 配置检查通过！现在可以进行实际测试');
    generateTestGuide();
  } else {
    console.log('\n⚠️  配置检查失败，需要先修复配置问题');
    
    if (!debugLogsOk) {
      console.log('❌ 调试日志配置不完整，无法精确监控组件生命周期');
    }
    
    if (!navigationConfigOk) {
      console.log('❌ 导航配置有问题，可能导致组件卸载异常');
    }
  }
  
  console.log('\n✅ 测试脚本执行完成！');
};

// 执行测试
runCompleteTest();
