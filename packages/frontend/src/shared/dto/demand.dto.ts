/**
 * 需求数据转换层 (DTO - Data Transfer Object)
 * 负责前后端数据格式的转换，确保数据一致性和类型安全
 * 
 * @fileoverview 企业级DTO设计模式实现
 * <AUTHOR> Assistant
 * @since 2025-07-09
 */

import { 
  DemandForm, 
  PropertyType, 
  IndustryType, 
  PaymentMethod 
} from '../../domains/demand/types/demand.types';

// 后端数据格式接口定义
export interface DemandCreateRequest {
  demand_type: 'RENTAL' | 'PURCHASE';
  property_type: string;
  property_subtypes?: string[];
  target_regions?: string[];
  
  // 面积和价格范围
  area_min?: number;
  area_max?: number;
  area_unit: string;
  price_min?: number;
  price_max?: number;
  price_unit?: string;
  
  // 详细要求
  floor_preferences?: string[];
  orientations?: string[];
  decoration_levels?: string[];
  industry_types?: string[];
  
  // 联系信息
  contact_surname?: string;
  contact_gender?: string;
  contact_phone?: string;
  contact_wechat?: string;
  additional_contacts?: any;
  
  // 标签和描述
  custom_tags?: string[];
  description?: string;
  special_requirements?: string;
}

export interface DemandResponse {
  id: string;
  user_id: string;
  demand_type: 'RENTAL' | 'PURCHASE';
  property_type: string;
  property_subtypes?: string[];
  target_regions?: string[];
  
  // 面积和价格范围
  area_min?: number;
  area_max?: number;
  area_unit: string;
  price_min?: number;
  price_max?: number;
  price_unit?: string;
  
  // 详细要求
  floor_preferences?: string[];
  orientations?: string[];
  decoration_levels?: string[];
  industry_types?: string[];
  
  // 联系信息
  contact_surname?: string;
  contact_gender?: string;
  contact_phone?: string;
  contact_wechat?: string;
  additional_contacts?: any;
  
  // 标签和描述
  custom_tags?: string[];
  ai_generated_tags?: string[];
  description?: string;
  special_requirements?: string;
  
  // 状态和管理
  status: string;
  priority_level: number;
  is_promoted: boolean;
  promotion_start_date?: string;
  promotion_end_date?: string;
  
  // 统计数据
  view_count: number;
  match_count: number;
  response_count: number;
  
  // 时间戳
  created_at: string;
  updated_at: string;
  expires_at?: string;
  
  // 扩展属性
  all_tags?: string[];
  display_price_range?: string;
  display_area_range?: string;
  is_expired?: boolean;
  can_be_promoted?: boolean;
}

/**
 * 需求表单DTO转换器
 * 实现前后端数据格式的双向转换
 */
export class DemandFormDTO {
  /**
   * 前端表单数据转换为后端API格式
   * @param frontendData 前端表单数据
   * @returns 后端API请求格式
   */
  static toBackend(frontendData: DemandForm): DemandCreateRequest {
    return {
      // 基础信息转换
      demand_type: frontendData.demandType,
      property_type: Array.isArray(frontendData.propertyType) 
        ? frontendData.propertyType[0] 
        : frontendData.propertyType || '',
      property_subtypes: Array.isArray(frontendData.propertyType) && frontendData.propertyType.length > 1
        ? frontendData.propertyType.slice(1)
        : frontendData.layoutType || [],
      
      // 地理位置转换
      target_regions: frontendData.location?.districts || [],
      
      // 面积范围转换
      area_min: frontendData.areaRange?.min,
      area_max: frontendData.areaRange?.max,
      area_unit: frontendData.areaRange?.unit || '平方米',
      
      // 价格范围转换
      price_min: frontendData.budgetRange?.min,
      price_max: frontendData.budgetRange?.max,
      price_unit: frontendData.budgetRange?.unit || (frontendData.demandType === 'RENTAL' ? '元/月' : '万元'),
      
      // 详细要求转换
      floor_preferences: frontendData.floorPreference || [],
      orientations: frontendData.orientation || [],
      decoration_levels: frontendData.decoration ? [frontendData.decoration] : [],
      industry_types: frontendData.industryType || [],
      
      // 联系信息转换
      contact_surname: frontendData.contactInfo?.surname,
      contact_gender: frontendData.contactInfo?.gender,
      contact_phone: frontendData.contactInfo?.phone,
      contact_wechat: frontendData.contactInfo?.wechat,
      additional_contacts: frontendData.contactInfo?.additionalContacts || [],
      
      // 其他信息
      custom_tags: [], // 自定义标签，前端暂未实现
      description: frontendData.specialRequirements,
      special_requirements: frontendData.specialRequirements,
    };
  }

  /**
   * 后端响应数据转换为前端表单格式
   * @param backendData 后端响应数据
   * @returns 前端表单格式（包含元数据）
   */
  static toFrontend(backendData: DemandResponse): DemandForm & { id?: string; created_at?: string; updated_at?: string } {
    return {
      // 🔧 元数据字段（列表显示需要）
      id: backendData.id,
      created_at: backendData.created_at,
      updated_at: backendData.updated_at,

      // 基础信息转换
      demandType: backendData.demand_type,
      propertyType: backendData.property_type ? [backendData.property_type as PropertyType, ...(backendData.property_subtypes || []).map(t => t as PropertyType)] : [],
      
      // 地理位置转换
      location: {
        city: 'nanning', // 默认南宁
        districts: backendData.target_regions || [],
        landmarks: [],
        transportRequirements: [],
      },
      
      // 面积范围转换
      areaRange: {
        min: backendData.area_min || 50,
        max: backendData.area_max || 200,
        unit: backendData.area_unit || '平方米',
      },
      
      // 价格范围转换
      budgetRange: {
        min: backendData.price_min || (backendData.demand_type === 'RENTAL' ? 3000 : 100),
        max: backendData.price_max || (backendData.demand_type === 'RENTAL' ? 10000 : 500),
        unit: (backendData.price_unit || (backendData.demand_type === 'RENTAL' ? '元/月' : '万元')) as '元/月' | '万元',
      },
      
      // 详细要求转换
      industryType: (backendData.industry_types || []).map(t => t as IndustryType),
      layoutType: backendData.property_subtypes || [],
      floorPreference: backendData.floor_preferences || [],
      orientation: backendData.orientations || [],
      decoration: backendData.decoration_levels?.[0] || '',
      
      // 其他条件
      leaseTerm: '', // 后端暂未提供
      paymentMethod: 'FULL' as PaymentMethod, // 后端暂未提供，设置默认值
      transferFee: '', // 后端暂未提供
      
      // 联系信息转换
      contactInfo: {
        phone: backendData.contact_phone || '',
        surname: backendData.contact_surname || '',
        gender: backendData.contact_gender as any,
        wechat: backendData.contact_wechat || '',
        preferredContactTime: 'ANYTIME' as any,
        contactMethod: 'BOTH' as any,
        additionalContacts: backendData.additional_contacts || [],
      },
      
      // 特殊要求
      specialRequirements: backendData.special_requirements || '',
      
      // 同意条款
      agreeToRules: true, // 已提交的需求默认已同意
    };
  }

  /**
   * 验证前端数据完整性
   * @param frontendData 前端表单数据
   * @param isDraft 是否为草稿模式（草稿模式跳过必填项验证）
   * @returns 验证结果
   */
  static validateFrontendData(frontendData: DemandForm, isDraft: boolean = false): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    console.log('[DemandFormDTO] 🔍 开始验证前端数据:', { isDraft, demandType: frontendData.demandType });

    const errors: string[] = [];
    const warnings: string[] = [];

    // 🔧 草稿模式跳过必填字段验证
    if (!isDraft) {
      console.log('[DemandFormDTO] 🔍 非草稿模式，进行必填项验证');
      // 必填字段验证
      if (!frontendData.demandType) {
        errors.push('需求类型不能为空');
      }

      if (!frontendData.propertyType || frontendData.propertyType.length === 0) {
        errors.push('房源类型不能为空');
      }

      if (!frontendData.location?.districts || frontendData.location.districts.length === 0) {
        errors.push('目标区域不能为空');
      }

      if (!frontendData.industryType || frontendData.industryType.length === 0) {
        errors.push('行业类型不能为空');
      }

      if (!frontendData.contactInfo?.phone) {
        errors.push('联系电话不能为空');
      }

      if (!frontendData.contactInfo?.surname) {
        errors.push('联系人姓氏不能为空');
      }

      if (!frontendData.contactInfo?.gender) {
        errors.push('联系人性别不能为空');
      }
    } else {
      console.log('[DemandFormDTO] 🔧 草稿模式，跳过必填项验证');
    }

    // 可选字段警告
    if (!frontendData.areaRange?.min || !frontendData.areaRange?.max) {
      warnings.push('建议填写面积范围以获得更精准的推荐');
    }

    if (!frontendData.budgetRange?.min || !frontendData.budgetRange?.max) {
      warnings.push('建议填写预算范围以获得更精准的推荐');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 数据清理和标准化
   * @param frontendData 前端表单数据
   * @returns 清理后的数据
   */
  static sanitizeData(frontendData: DemandForm): DemandForm {
    return {
      ...frontendData,
      // 清理空数组
      propertyType: frontendData.propertyType?.filter(type => type && type.trim()) || [],
      industryType: frontendData.industryType?.filter(type => type && type.trim()) || [],
      layoutType: frontendData.layoutType?.filter(type => type && type.trim()) || [],
      
      // 清理空字符串
      specialRequirements: frontendData.specialRequirements?.trim() || '',
      
      // 标准化联系信息
      contactInfo: frontendData.contactInfo ? {
        ...frontendData.contactInfo,
        phone: frontendData.contactInfo.phone?.replace(/\D/g, '') || '', // 只保留数字
        surname: frontendData.contactInfo.surname?.trim() || '',
        wechat: frontendData.contactInfo.wechat?.trim() || '',
      } : {
        phone: '',
        surname: '',
        gender: 'male' as const,
        preferredContactTime: 'ANYTIME' as const,
        contactMethod: 'PHONE' as const,
      },
    };
  }

  /**
   * 生成数据摘要（用于缓存键值）
   * @param frontendData 前端表单数据
   * @returns 数据摘要字符串
   */
  static generateDataSummary(frontendData: DemandForm): string {
    const key = [
      frontendData.demandType,
      frontendData.propertyType?.join(','),
      frontendData.location?.districts?.join(','),
      frontendData.contactInfo?.phone,
    ].filter(Boolean).join('|');
    
    return Buffer.from(key).toString('base64').substring(0, 16);
  }
}

/**
 * 需求列表DTO转换器
 */
export class DemandListDTO {
  /**
   * 转换需求列表响应
   * @param backendResponse 后端列表响应
   * @returns 前端格式的需求列表
   */
  static toFrontend(backendResponse: {
    demands: DemandResponse[];
    total_count: number;
    has_more: boolean;
    status_summary?: Record<string, number>;
    type_summary?: Record<string, number>;
  }) {
    return {
      items: backendResponse.demands.map(demand => DemandFormDTO.toFrontend(demand)),
      totalCount: backendResponse.total_count,
      hasMore: backendResponse.has_more,
      statusSummary: backendResponse.status_summary || {},
      typeSummary: backendResponse.type_summary || {},
    };
  }
}

/**
 * 标签推荐DTO转换器
 */
export class TagRecommendationDTO {
  /**
   * 转换标签推荐请求
   * @param frontendData 前端表单数据
   * @param page 页码
   * @param pageSize 每页大小
   * @returns 后端标签推荐请求格式
   */
  static toBackendRequest(frontendData: Partial<DemandForm>, page = 0, pageSize = 5) {
    return {
      demand_type: frontendData.demandType,
      property_type: Array.isArray(frontendData.propertyType) 
        ? frontendData.propertyType[0] 
        : frontendData.propertyType,
      target_regions: frontendData.location?.districts,
      area_min: frontendData.areaRange?.min,
      area_max: frontendData.areaRange?.max,
      price_min: frontendData.budgetRange?.min,
      price_max: frontendData.budgetRange?.max,
      industry_types: frontendData.industryType,
    };
  }

  /**
   * 转换标签推荐响应
   * @param backendResponse 后端响应
   * @returns 前端格式的标签推荐
   */
  static toFrontendResponse(backendResponse: {
    recommended_tags: string[];
    popular_tags: string[];
    related_tags: string[];
    location_tags: string[];
    property_tags: string[];
    business_tags: string[];
    price_tags: string[];
  }) {
    return {
      recommendedTags: backendResponse.recommended_tags || [],
      popularTags: backendResponse.popular_tags || [],
      relatedTags: backendResponse.related_tags || [],
      locationTags: backendResponse.location_tags || [],
      propertyTags: backendResponse.property_tags || [],
      businessTags: backendResponse.business_tags || [],
      priceTags: backendResponse.price_tags || [],
    };
  }
}