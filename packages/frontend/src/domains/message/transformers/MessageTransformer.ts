/**
 * 消息数据转换器
 * 继承BaseTransformer，实现消息系统的数据转换逻辑
 * 符合企业级架构规范
 */
import { BaseTransformer } from '../../../shared/services/dataTransform/core/BaseTransformer';
import { z } from 'zod';
import type {
  MessageQueryInput,
  MessageQueryOutput,
  MessageData,
  MessageDataAPI,
  MessageAttachment,
  MessageAttachmentAPI,
  UserRoleInfo,
  UserRoleInfoAPI,
  PaymentUnlockInfo,
  PaymentUnlockInfoAPI,
  InteractedUser,
  InteractedUserAPI,
  SMSCreditAccount,
  SMSCreditAccountAPI,
  TransformResult,
  TransformOptions,
  MessageQueryInputSchema,
  MessageDataSchema,
} from '../types/message.types';

/**
 * 消息查询转换器
 */
export class MessageQueryTransformer extends BaseTransformer<MessageQueryInput, MessageQueryOutput> {
  constructor() {
    super('MessageQueryTransformer', '1.0.0');
  }

  protected initializeValidationRules(): void {
    this.addValidationRule('messageQuery', {
      name: 'messageQuery',
      schema: MessageQueryInputSchema,
      required: true
    });
  }

  toAPI(input: MessageQueryInput, options?: TransformOptions): TransformResult<MessageQueryOutput> {
    return this.safeTransform(() => {
      // 输入验证
      if (options?.validateInput !== false) {
        const validationResult = this.validateInput(input, 'messageQuery');
        if (!validationResult.success) {
          throw new Error(`输入验证失败: ${validationResult.error}`);
        }
      }

      // 数据转换
      const output: MessageQueryOutput = {
        user_role: input.role,
        message_category: input.category,
        pagination: {
          offset: input.skip || 0,
          limit: input.limit || 20,
        }
      };

      // 可选过滤条件
      if (input.status || input.startDate || input.endDate) {
        output.filters = {};
        
        if (input.status) {
          output.filters.status = input.status;
        }
        
        if (input.startDate || input.endDate) {
          output.filters.date_range = {
            start: input.startDate || '',
            end: input.endDate || '',
          };
        }
      }

      return output;
    }, 'MessageQueryTransformer.toAPI', options);
  }

  fromAPI(apiData: MessageQueryOutput, options?: TransformOptions): TransformResult<MessageQueryInput> {
    return this.safeTransform(() => {
      const input: MessageQueryInput = {
        role: apiData.user_role as any,
        category: apiData.message_category as any,
        skip: apiData.pagination.offset,
        limit: apiData.pagination.limit,
      };

      if (apiData.filters) {
        if (apiData.filters.status) {
          input.status = apiData.filters.status as any;
        }
        
        if (apiData.filters.date_range) {
          input.startDate = apiData.filters.date_range.start;
          input.endDate = apiData.filters.date_range.end;
        }
      }

      return input;
    }, 'MessageQueryTransformer.fromAPI', options);
  }
}

/**
 * 消息数据转换器
 */
export class MessageDataTransformer extends BaseTransformer<MessageData, MessageDataAPI> {
  constructor() {
    super('MessageDataTransformer', '1.0.0');
  }

  protected initializeValidationRules(): void {
    this.addValidationRule('messageData', {
      name: 'messageData',
      schema: MessageDataSchema,
      required: true
    });
  }

  toAPI(input: MessageData, options?: TransformOptions): TransformResult<MessageDataAPI> {
    return this.safeTransform(() => {
      // 输入验证
      if (options?.validateInput !== false) {
        const validationResult = this.validateInput(input, 'messageData');
        if (!validationResult.success) {
          throw new Error(`输入验证失败: ${validationResult.error}`);
        }
      }

      // 转换附件
      const attachments = input.attachments?.map(att => this.transformAttachmentToAPI(att));

      const output: MessageDataAPI = {
        message_id: input.id,
        message_title: input.title,
        message_content: input.content,
        message_category: input.category,
        message_status: input.status,
        message_priority: input.priority,
        sender_id: input.senderId,
        sender_name: input.senderName,
        sender_avatar: input.senderAvatar,
        recipient_id: input.recipientId,
        recipient_role: input.recipientRole,
        property_id: input.propertyId,
        property_title: input.propertyTitle,
        created_at: input.createdAt.toISOString(),
        updated_at: input.updatedAt.toISOString(),
        read_at: input.readAt?.toISOString(),
        replied_at: input.repliedAt?.toISOString(),
        attachments,
        metadata: input.metadata,
      };

      return output;
    }, 'MessageDataTransformer.toAPI', options);
  }

  fromAPI(apiData: MessageDataAPI, options?: TransformOptions): TransformResult<MessageData> {
    return this.safeTransform(() => {
      // 转换附件
      const attachments = apiData.attachments?.map(att => this.transformAttachmentFromAPI(att));

      const input: MessageData = {
        id: apiData.message_id,
        title: apiData.message_title,
        content: apiData.message_content,
        category: apiData.message_category as any,
        status: apiData.message_status as any,
        priority: apiData.message_priority as any,
        senderId: apiData.sender_id,
        senderName: apiData.sender_name,
        senderAvatar: apiData.sender_avatar,
        recipientId: apiData.recipient_id,
        recipientRole: apiData.recipient_role as any,
        propertyId: apiData.property_id,
        propertyTitle: apiData.property_title,
        createdAt: new Date(apiData.created_at),
        updatedAt: new Date(apiData.updated_at),
        readAt: apiData.read_at ? new Date(apiData.read_at) : undefined,
        repliedAt: apiData.replied_at ? new Date(apiData.replied_at) : undefined,
        attachments,
        metadata: apiData.metadata,
      };

      return input;
    }, 'MessageDataTransformer.fromAPI', options);
  }

  private transformAttachmentToAPI(attachment: MessageAttachment): MessageAttachmentAPI {
    return {
      attachment_id: attachment.id,
      attachment_type: attachment.type,
      attachment_url: attachment.url,
      attachment_name: attachment.name,
      attachment_size: attachment.size,
      attachment_mime_type: attachment.mimeType,
      created_at: attachment.createdAt.toISOString(),
    };
  }

  private transformAttachmentFromAPI(apiAttachment: MessageAttachmentAPI): MessageAttachment {
    return {
      id: apiAttachment.attachment_id,
      type: apiAttachment.attachment_type as any,
      url: apiAttachment.attachment_url,
      name: apiAttachment.attachment_name,
      size: apiAttachment.attachment_size,
      mimeType: apiAttachment.attachment_mime_type,
      createdAt: new Date(apiAttachment.created_at),
    };
  }
}

/**
 * 用户角色信息转换器
 */
export class UserRoleInfoTransformer extends BaseTransformer<UserRoleInfo, UserRoleInfoAPI> {
  constructor() {
    super('UserRoleInfoTransformer', '1.0.0');
  }

  protected initializeValidationRules(): void {
    // 用户角色信息验证规则
    this.addValidationRule('userRoleInfo', {
      name: 'userRoleInfo',
      schema: z.object({
        userId: z.string(),
        roles: z.array(z.enum(['tenant_buyer', 'landlord'])),
        permissions: z.array(z.string()),
      }),
      required: true
    });
  }

  toAPI(input: UserRoleInfo, options?: TransformOptions): TransformResult<UserRoleInfoAPI> {
    return this.safeTransform(() => {
      const output: UserRoleInfoAPI = {
        user_id: input.userId,
        user_roles: input.roles,
        user_permissions: input.permissions,
        tenant_buyer_profile: input.tenantBuyerProfile ? {
          is_verified: input.tenantBuyerProfile.verified,
          credit_score: input.tenantBuyerProfile.creditScore,
          user_preferences: input.tenantBuyerProfile.preferences,
        } : undefined,
        landlord_profile: input.landlordProfile ? {
          is_verified: input.landlordProfile.verified,
          property_count: input.landlordProfile.propertyCount,
          user_rating: input.landlordProfile.rating,
        } : undefined,
      };

      return output;
    }, 'UserRoleInfoTransformer.toAPI', options);
  }

  fromAPI(apiData: UserRoleInfoAPI, options?: TransformOptions): TransformResult<UserRoleInfo> {
    return this.safeTransform(() => {
      const input: UserRoleInfo = {
        userId: apiData.user_id,
        roles: apiData.user_roles as any[],
        permissions: apiData.user_permissions,
        tenantBuyerProfile: apiData.tenant_buyer_profile ? {
          verified: apiData.tenant_buyer_profile.is_verified,
          creditScore: apiData.tenant_buyer_profile.credit_score,
          preferences: apiData.tenant_buyer_profile.user_preferences,
        } : undefined,
        landlordProfile: apiData.landlord_profile ? {
          verified: apiData.landlord_profile.is_verified,
          propertyCount: apiData.landlord_profile.property_count,
          rating: apiData.landlord_profile.user_rating,
        } : undefined,
      };

      return input;
    }, 'UserRoleInfoTransformer.fromAPI', options);
  }
}

/**
 * 付费解锁信息转换器
 */
export class PaymentUnlockInfoTransformer extends BaseTransformer<PaymentUnlockInfo, PaymentUnlockInfoAPI> {
  constructor() {
    super('PaymentUnlockInfoTransformer', '1.0.0');
  }

  protected initializeValidationRules(): void {
    this.addValidationRule('paymentUnlockInfo', {
      name: 'paymentUnlockInfo',
      schema: z.object({
        category: z.enum(['all', 'inquiry', 'appointment', 'system']),
        isUnlocked: z.boolean(),
        requiredCredits: z.number().min(0),
        currentCredits: z.number().min(0),
        unlockPrice: z.number().min(0),
        features: z.array(z.string()),
      }),
      required: true
    });
  }

  toAPI(input: PaymentUnlockInfo, options?: TransformOptions): TransformResult<PaymentUnlockInfoAPI> {
    return this.safeTransform(() => {
      const output: PaymentUnlockInfoAPI = {
        message_category: input.category,
        is_unlocked: input.isUnlocked,
        required_credits: input.requiredCredits,
        current_credits: input.currentCredits,
        unlock_price: input.unlockPrice,
        valid_until: input.validUntil?.toISOString(),
        unlock_features: input.features,
      };

      return output;
    }, 'PaymentUnlockInfoTransformer.toAPI', options);
  }

  fromAPI(apiData: PaymentUnlockInfoAPI, options?: TransformOptions): TransformResult<PaymentUnlockInfo> {
    return this.safeTransform(() => {
      const input: PaymentUnlockInfo = {
        category: apiData.message_category as any,
        isUnlocked: apiData.is_unlocked,
        requiredCredits: apiData.required_credits,
        currentCredits: apiData.current_credits,
        unlockPrice: apiData.unlock_price,
        validUntil: apiData.valid_until ? new Date(apiData.valid_until) : undefined,
        features: apiData.unlock_features,
      };

      return input;
    }, 'PaymentUnlockInfoTransformer.fromAPI', options);
  }
}

/**
 * 互动用户信息转换器
 */
export class InteractedUserTransformer extends BaseTransformer<InteractedUser, InteractedUserAPI> {
  constructor() {
    super('InteractedUserTransformer', '1.0.0');
  }

  protected initializeValidationRules(): void {
    this.addValidationRule('interactedUser', {
      name: 'interactedUser',
      schema: z.object({
        userId: z.string(),
        userName: z.string(),
        userRole: z.enum(['tenant_buyer', 'landlord']),
        lastMessageAt: z.date(),
        unreadCount: z.number().min(0),
        isOnline: z.boolean(),
      }),
      required: true
    });
  }

  toAPI(input: InteractedUser, options?: TransformOptions): TransformResult<InteractedUserAPI> {
    return this.safeTransform(() => {
      const output: InteractedUserAPI = {
        user_id: input.userId,
        user_name: input.userName,
        user_avatar: input.userAvatar,
        user_role: input.userRole,
        last_message_at: input.lastMessageAt.toISOString(),
        unread_count: input.unreadCount,
        is_online: input.isOnline,
        property_id: input.propertyId,
        property_title: input.propertyTitle,
      };

      return output;
    }, 'InteractedUserTransformer.toAPI', options);
  }

  fromAPI(apiData: InteractedUserAPI, options?: TransformOptions): TransformResult<InteractedUser> {
    return this.safeTransform(() => {
      const input: InteractedUser = {
        userId: apiData.user_id,
        userName: apiData.user_name,
        userAvatar: apiData.user_avatar,
        userRole: apiData.user_role as any,
        lastMessageAt: new Date(apiData.last_message_at),
        unreadCount: apiData.unread_count,
        isOnline: apiData.is_online,
        propertyId: apiData.property_id,
        propertyTitle: apiData.property_title,
      };

      return input;
    }, 'InteractedUserTransformer.fromAPI', options);
  }
}

/**
 * SMS积分账户转换器
 */
export class SMSCreditAccountTransformer extends BaseTransformer<SMSCreditAccount, SMSCreditAccountAPI> {
  constructor() {
    super('SMSCreditAccountTransformer', '1.0.0');
  }

  protected initializeValidationRules(): void {
    this.addValidationRule('smsCreditAccount', {
      name: 'smsCreditAccount',
      schema: z.object({
        userId: z.string(),
        currentCredits: z.number().min(0),
        totalCredits: z.number().min(0),
        usedCredits: z.number().min(0),
        packages: z.array(z.object({
          id: z.string(),
          name: z.string(),
          credits: z.number().min(0),
          price: z.number().min(0),
          validDays: z.number().min(0),
          isActive: z.boolean(),
          description: z.string(),
        })),
      }),
      required: true
    });
  }

  toAPI(input: SMSCreditAccount, options?: TransformOptions): TransformResult<SMSCreditAccountAPI> {
    return this.safeTransform(() => {
      const output: SMSCreditAccountAPI = {
        user_id: input.userId,
        current_credits: input.currentCredits,
        total_credits: input.totalCredits,
        used_credits: input.usedCredits,
        last_recharge_at: input.lastRechargeAt?.toISOString(),
        expires_at: input.expiresAt?.toISOString(),
        credit_packages: input.packages.map(pkg => ({
          package_id: pkg.id,
          package_name: pkg.name,
          package_credits: pkg.credits,
          package_price: pkg.price,
          valid_days: pkg.validDays,
          is_active: pkg.isActive,
          package_description: pkg.description,
        })),
      };

      return output;
    }, 'SMSCreditAccountTransformer.toAPI', options);
  }

  fromAPI(apiData: SMSCreditAccountAPI, options?: TransformOptions): TransformResult<SMSCreditAccount> {
    return this.safeTransform(() => {
      const input: SMSCreditAccount = {
        userId: apiData.user_id,
        currentCredits: apiData.current_credits,
        totalCredits: apiData.total_credits,
        usedCredits: apiData.used_credits,
        lastRechargeAt: apiData.last_recharge_at ? new Date(apiData.last_recharge_at) : undefined,
        expiresAt: apiData.expires_at ? new Date(apiData.expires_at) : undefined,
        packages: apiData.credit_packages.map(pkg => ({
          id: pkg.package_id,
          name: pkg.package_name,
          credits: pkg.package_credits,
          price: pkg.package_price,
          validDays: pkg.valid_days,
          isActive: pkg.is_active,
          description: pkg.package_description,
        })),
      };

      return input;
    }, 'SMSCreditAccountTransformer.fromAPI', options);
  }
}
