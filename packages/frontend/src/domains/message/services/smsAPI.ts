/**
 * SMS积分API服务
 * 处理SMS积分管理相关的API请求
 */

import { apiClient as client } from '../../../shared/services/client';

export interface SMSCreditAccount {
  id: string;
  userId: string;
  balance: number;
  creditsBalance: number; // 新增积分余额字段
  totalSpent: number;
  lastRechargeTime?: string;
  lastRechargeAmount?: number;
  monthlyUsage: number;
  subscriptionSmsTotal: number; // 套餐短信总数
  subscriptionSmsBalance: number; // 套餐短信余额
  autoRechargeEnabled: boolean; // 自动充值开关
  autoRechargeThreshold: number; // 自动充值阈值
  autoRechargeAmount: number; // 自动充值金额
  status: 'active' | 'inactive' | 'suspended';
}

export interface SMSRechargeOrder {
  id: string;
  amount: number;
  credits: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  createdAt: string;
  completedAt?: string;
}

export interface SMSUsageRecord {
  id: string;
  type: 'send_message' | 'unlock_contact' | 'premium_feature';
  credits: number;
  description: string;
  createdAt: string;
  propertyId?: string;
  contactId?: string;
}

export class SMSAPI {
  /**
   * 获取用户SMS积分账户信息
   */
  async getSMSCreditAccount(): Promise<SMSCreditAccount | null> {
    try {
      const response = await client.get('/sms/account');
      return response.data || null;
    } catch (error) {
      console.error('获取SMS积分账户失败:', error);
      // 🔧 API容错：返回默认SMS账户信息
      return {
        id: 'default-sms-account',
        userId: 'current-user',
        balance: 0,
        creditsBalance: 0,
        totalSpent: 0,
        monthlyUsage: 0,
        subscriptionSmsTotal: 100,
        subscriptionSmsBalance: 100,
        autoRechargeEnabled: false,
        autoRechargeThreshold: 10,
        autoRechargeAmount: 50,
        status: 'active'
      };
    }
  }

  /**
   * 获取SMS使用记录
   */
  async getSMSUsageRecords(limit: number = 20, offset: number = 0): Promise<SMSUsageRecord[]> {
    try {
      const response = await client.get('/sms/usage', {
        params: { limit, offset }
      });
      return response.data || [];
    } catch (error) {
      console.error('获取SMS使用记录失败:', error);
      return [];
    }
  }

  /**
   * 创建SMS充值订单
   */
  async createRechargeOrder(amount: number, credits: number): Promise<SMSRechargeOrder | null> {
    try {
      const response = await client.post('/sms/recharge', {
        amount,
        credits
      });
      return response.data || null;
    } catch (error) {
      console.error('创建SMS充值订单失败:', error);
      return null;
    }
  }

  /**
   * 获取充值订单状态
   */
  async getRechargeOrder(orderId: string): Promise<SMSRechargeOrder | null> {
    try {
      const response = await client.get(`/sms/recharge/${orderId}`);
      return response.data || null;
    } catch (error) {
      console.error('获取充值订单状态失败:', error);
      return null;
    }
  }

  /**
   * 使用SMS积分发送消息
   */
  async sendSMSMessage(phoneNumber: string, message: string, type: string = 'message'): Promise<boolean> {
    try {
      await client.post('/sms/send', {
        phoneNumber,
        message,
        type
      });
      return true;
    } catch (error) {
      console.error('发送SMS消息失败:', error);
      return false;
    }
  }

  /**
   * 使用SMS积分解锁联系方式
   */
  async unlockContact(contactId: string): Promise<boolean> {
    try {
      await client.post('/sms/unlock-contact', {
        contactId
      });
      return true;
    } catch (error) {
      console.error('解锁联系方式失败:', error);
      return false;
    }
  }

  /**
   * 获取SMS积分价格套餐
   */
  async getSMSPricingPackages(): Promise<any[]> {
    try {
      const response = await client.get('/sms/pricing');
      return response.data || [];
    } catch (error) {
      console.error('获取SMS价格套餐失败:', error);
      return [];
    }
  }

  /**
   * 检查SMS积分余额是否足够
   */
  async checkSMSBalance(requiredCredits: number): Promise<boolean> {
    try {
      const account = await this.getSMSCreditAccount();
      return account ? account.balance >= requiredCredits : false;
    } catch (error) {
      console.error('检查SMS余额失败:', error);
      return false;
    }
  }

  /**
   * SMS积分充值（PaymentUnlockModal需要的方法）
   */
  async rechargeCredits(amount: number, credits: number): Promise<{ success: boolean; orderId?: string; error?: string }> {
    try {
      const order = await this.createRechargeOrder(amount, credits);
      if (order) {
        return {
          success: true,
          orderId: order.id
        };
      }
      return {
        success: false,
        error: '创建充值订单失败'
      };
    } catch (error) {
      console.error('SMS积分充值失败:', error);
      return {
        success: false,
        error: 'SMS积分充值失败'
      };
    }
  }
}

// 导出单例实例
export const smsAPI = new SMSAPI();