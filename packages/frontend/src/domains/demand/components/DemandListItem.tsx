/**
 * 🏗️ 企业级架构：需求列表项组件
 * 遵循前端企业级架构规范 - UI组件层
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// 📊 类型定义
import type { DemandResponse } from '../services/demandService';
import type { DemandStatus } from '../types/demand.types';

// 🌐 API层：服务
import { DemandAPI } from '../services/demandAPI';
import FeedbackService from '../../../shared/services/FeedbackService';
import { queryClient } from '../../../shared/services/queryClient';

// 响应式工具
import { wp, hp, fp } from '../../../shared/utils/responsiveUtils';

// 🌐 转换工具：中文显示
import { getIndustryTypesDisplayString } from '../utils/industryTypeUtils';
import { getRegionsDisplayString } from '../utils/regionUtils';
import { getPropertyTypeDisplayName } from '../../property/config/propertyTypeConfigs';

interface DemandListItemProps {
  item: any; // 🔧 使用any类型以兼容API原始数据
  onPress: (item: any) => void;
  onEdit: (item: any) => void;
  onDelete: (item: any) => void;
  onStatusChange: (item: any, newStatus: DemandStatus) => void;
  onRefresh?: () => void; // 添加刷新回调
}

export const DemandListItem: React.FC<DemandListItemProps> = ({
  item,
  onPress,
  onEdit,
  onDelete,
  onStatusChange,
  onRefresh,
}) => {
  // 🎨 UI层：状态显示文本
  const getStatusText = (status: DemandStatus) => {
    switch (status) {
      case 'DRAFT': return '草稿';
      case 'ACTIVE': return '已发布';
      case 'OFFLINE': return '已下架';
      case 'COMPLETED': return '已完成';
      case 'EXPIRED': return '已过期';
      default: return '未知';
    }
  };

  // 🎨 UI层：状态颜色
  const getStatusColor = (status: DemandStatus) => {
    switch (status) {
      case 'DRAFT': return '#999999';
      case 'ACTIVE': return '#52c41a';
      case 'OFFLINE': return '#faad14';
      case 'COMPLETED': return '#1890ff';
      case 'EXPIRED': return '#ff4d4f';
      default: return '#666666';
    }
  };

  // 🎨 UI层：格式化时间
  const formatTime = (timeStr?: string) => {
    if (!timeStr) return '';
    return new Date(timeStr).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 🎨 UI层：格式化价格范围
  const formatPriceRange = (item: any) => {
    // 优先使用后端计算的显示范围
    if (item.display_price_range) {
      return item.display_price_range;
    }
    
    // 后备方案：前端计算
    if (!item.price_min && !item.price_max) return '价格面议';
    
    const unit = item.price_unit || '元';
    if (item.price_min && item.price_max) {
      return `${item.price_min}-${item.price_max}${unit}`;
    }
    if (item.price_min) return `${item.price_min}${unit}起`;
    if (item.price_max) return `${item.price_max}${unit}以下`;
    return '价格面议';
  };

  // 🎨 UI层：格式化面积范围
  const formatAreaRange = (item: any) => {
    // 优先使用后端计算的显示范围
    if (item.display_area_range) {
      return item.display_area_range;
    }

    // 后备方案：前端计算
    if (!item.area_min && !item.area_max) return '面积不限';

    const unit = item.area_unit || '㎡';
    if (item.area_min && item.area_max) {
      return `${item.area_min}-${item.area_max}${unit}`;
    }
    if (item.area_min) return `${item.area_min}${unit}起`;
    if (item.area_max) return `${item.area_max}${unit}以下`;
    return '面积不限';
  };

  // 🔧 业务逻辑：处理删除
  const handleDelete = async () => {
    console.log('[DemandListItem] 🗑️ 准备删除需求:', item.id);
    Alert.alert(
      '确认删除',
      '删除后无法恢复，确定要删除这条需求吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('[DemandListItem] 🔄 开始删除需求:', item.id);
              // 🔧 移除showLoading调用，因为FeedbackService没有这个方法

              const response = await DemandAPI.deleteDemand(item.id);
              console.log('[DemandListItem] 📡 删除API响应:', response);

              if (response.success) {
                console.log('[DemandListItem] ✅ 删除成功');

                // 使React Query缓存失效，强制重新获取数据
                queryClient.invalidateQueries({
                  queryKey: ['demand', 'list']
                });

                FeedbackService.showSuccess('删除成功');
                onDelete(item);
                onRefresh?.(); // 刷新列表
              } else {
                console.error('[DemandListItem] ❌ 删除失败:', response.message);
                FeedbackService.showError(response.message || '删除失败');
              }
            } catch (error) {
              console.error('[DemandListItem] 💥 删除异常:', error);
              console.error('[DemandListItem] 💥 错误详情:', JSON.stringify(error, null, 2));
              FeedbackService.showError('删除失败，请重试');
            }
          },
        },
      ]
    );
  };

  // 🔧 业务逻辑：处理状态变更
  const handleStatusChange = async (newStatus: DemandStatus) => {
    try {
      // 🔧 移除showLoading调用，因为FeedbackService没有这个方法
      const response = await DemandAPI.updateDemandStatus(item.id, newStatus);

      if (response.success) {
        const statusText = getStatusText(newStatus);
        FeedbackService.showSuccess(`已${statusText}`);
        onStatusChange(item, newStatus);
        onRefresh?.(); // 刷新列表
      } else {
        FeedbackService.showError(response.message || '更新失败');
      }
    } catch (error) {
      console.error('更新需求状态失败:', error);
      FeedbackService.showError('更新失败，请重试');
    }
  };

  // 🎨 UI层：渲染操作按钮（闲鱼风格 - 简洁文字按钮）
  const renderActionButtons = () => {
    const actions = [];

    // 编辑按钮（草稿和下架状态可编辑）
    if (item.status === 'DRAFT' || item.status === 'OFFLINE') {
      actions.push(
        <TouchableOpacity
          key="edit"
          style={styles.actionButton}
          onPress={() => onEdit(item)}
        >
          <Text style={styles.actionButtonText}>编辑</Text>
        </TouchableOpacity>
      );
    }

    // 下架按钮（仅活跃状态）
    if (item.status === 'ACTIVE') {
      actions.push(
        <TouchableOpacity
          key="offline"
          style={styles.actionButton}
          onPress={() => handleStatusChange('OFFLINE')}
        >
          <Text style={styles.actionButtonText}>下架</Text>
        </TouchableOpacity>
      );
    }

    // 重新上架按钮（仅下架状态）
    if (item.status === 'OFFLINE') {
      actions.push(
        <TouchableOpacity
          key="reactivate"
          style={styles.actionButton}
          onPress={() => handleStatusChange('ACTIVE')}
        >
          <Text style={styles.actionButtonText}>上架</Text>
        </TouchableOpacity>
      );
    }

    // 删除按钮（所有状态都可以删除）
    actions.push(
      <TouchableOpacity
        key="delete"
        style={styles.actionButton}
        onPress={handleDelete}
      >
        <Text style={[styles.actionButtonText, styles.deleteButtonText]}>删除</Text>
      </TouchableOpacity>
    );

    return actions;
  };

  return (
    <TouchableOpacity style={styles.container} onPress={() => onPress(item)}>
      {/* 主要内容区域 */}
      <View style={styles.content}>
        {/* 标题和状态 */}
        <View style={styles.titleRow}>
          <Text style={styles.title} numberOfLines={1}>
            {getPropertyTypeDisplayName(item.property_type)} · {getRegionsDisplayString(item.target_regions) || '区域不限'}
          </Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
            <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
          </View>
        </View>

        {/* 价格信息 */}
        <Text style={styles.price}>
          {formatPriceRange(item)}
        </Text>

        {/* 面积和描述 */}
        <Text style={styles.subtitle} numberOfLines={1}>
          {formatAreaRange(item)}
          {item.description && ` · ${item.description}`}
        </Text>

        {/* 底部信息行 */}
        <View style={styles.bottomRow}>
          {/* 统计信息 */}
          <View style={styles.statsContainer}>
            <Text style={styles.statText}>浏览{item.view_count || 0}</Text>
            <Text style={styles.statText}>响应{item.response_count || 0}</Text>
            {item.match_count !== undefined && (
              <Text style={styles.statText}>匹配{item.match_count}</Text>
            )}
          </View>

          {/* 操作按钮 */}
          <View style={styles.actionContainer}>
            {renderActionButtons()}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    marginHorizontal: 0, // 延伸到屏幕边缘
    marginBottom: hp(8), // 减少间距，更紧凑
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
    borderBottomWidth: 0.5,
    borderBottomColor: '#f0f0f0',
  },
  content: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(8),
  },
  title: {
    flex: 1,
    fontSize: fp(16),
    fontWeight: '600',
    color: '#333333',
    marginRight: wp(12),
  },
  statusBadge: {
    borderRadius: 10,
    paddingHorizontal: wp(6),
    paddingVertical: hp(2),
  },
  statusText: {
    fontSize: fp(11),
    fontWeight: '500',
    color: '#ffffff',
  },
  price: {
    fontSize: fp(18),
    fontWeight: '700',
    color: '#FF6B35',
    marginBottom: hp(6),
  },
  subtitle: {
    fontSize: fp(14),
    color: '#666666',
    marginBottom: hp(12),
    lineHeight: fp(20),
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: fp(12),
    color: '#999999',
    marginRight: wp(12),
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    paddingHorizontal: wp(8),
    paddingVertical: hp(4),
    marginLeft: wp(8),
  },
  actionButtonText: {
    fontSize: fp(13),
    color: '#666666',
    fontWeight: '500',
  },
  deleteButtonText: {
    color: '#ff4d4f',
  },
});
