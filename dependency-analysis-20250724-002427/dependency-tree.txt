=== 依赖树分析 ===
依赖树深度分析:
commercial-real-estate-app@1.0.0 /data/my-real-estate-app
├─┬ @expo/cli@0.18.31
│ ├── @babel/runtime@7.27.6
│ ├── @expo/code-signing-certificates@0.0.5
│ ├── @expo/config-plugins@8.0.11
│ ├── @expo/config@9.0.4
│ ├── @expo/devcert@1.2.0
│ ├── @expo/env@0.3.0
│ ├── @expo/image-utils@0.5.1
│ ├── @expo/json-file@8.3.3
│ ├── @expo/metro-config@0.18.11
│ ├── @expo/osascript@2.2.4
│ ├── @expo/package-manager@1.8.4
│ ├── @expo/plist@0.1.3
│ ├── @expo/prebuild-config@7.0.9
│ ├── @expo/rudder-sdk-node@1.1.1
│ ├── @expo/spawn-async@1.7.2
│ ├── @expo/xcpretty@4.3.2
│ ├── @react-native/dev-middleware@0.74.85
│ ├── @urql/core@2.3.6

⚠️  深层依赖可能存在安全风险
💡 建议定期审查间接依赖

