/**
 * MapContainer 纯高德原生定位功能测试示例
 * 
 * 展示如何使用新的纯高德原生定位架构
 * 2025年7月24日重构：基于专家建议的纯高德方案
 */

import React, { useState, useCallback, useEffect } from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';
import { MapContainer, LocationService } from './MapContainer';
import type { LocationResult } from '../services/LocationService';

// 示例房源数据
const sampleProperties = [
  {
    id: '1',
    latitude: 22.8167,
    longitude: 108.3669,
    title: '万象城公寓',
    price: 3500,
    area: 85,
    propertyType: 'apartment',
  },
  {
    id: '2',
    latitude: 22.8200,
    longitude: 108.3700,
    title: '青秀山别墅',
    price: 8800,
    area: 180,
    propertyType: 'villa',
  },
];

interface MapCenter {
  latitude: number;
  longitude: number;
}

export const MapTestWithPureAmapLocation: React.FC = () => {
  // 地图状态管理
  const [mapCenter, setMapCenter] = useState<MapCenter>({
    latitude: 22.8167,
    longitude: 108.3669,
  });
  const [userLocation, setUserLocation] = useState<MapCenter | null>(null);
  const [locationStatus, setLocationStatus] = useState<string>('等待定位...');
  const [locationAccuracy, setLocationAccuracy] = useState<number | null>(null);

  /**
   * 监听LocationService的位置更新
   */
  useEffect(() => {
    console.log('🔗 [MapTest] 注册LocationService监听器');
    
    const unsubscribe = LocationService.onLocationUpdate((result: LocationResult) => {
      console.log('📍 [MapTest] 收到LocationService位置更新:', result);
      
      if (result.success && result.coordinates) {
        setUserLocation({
          latitude: result.coordinates.latitude,
          longitude: result.coordinates.longitude,
        });
        setLocationAccuracy(result.accuracy || null);
        setLocationStatus(`定位成功 - ${result.city?.name || '未知城市'}`);
      } else {
        setLocationStatus(`定位失败: ${result.error || '未知错误'}`);
      }
    });

    // 获取初始位置状态
    const currentState = LocationService.getCurrentLocationState();
    if (currentState.hasLocation && currentState.coordinates) {
      setUserLocation(currentState.coordinates);
      setLocationAccuracy(currentState.accuracy || null);
      setLocationStatus(`已有位置 - ${currentState.city?.name || '未知城市'}`);
    }

    return () => {
      console.log('🔗 [MapTest] 取消LocationService监听器');
      unsubscribe();
    };
  }, []);

  /**
   * 处理高德原生定位更新
   */
  const handleNativeLocationUpdate = useCallback((location: MapCenter) => {
    console.log('🎯 [MapTest] 收到高德原生定位更新:', location);
    
    setUserLocation(location);
    setLocationStatus('高德原生定位更新');
    
    // 可以选择自动移动地图中心
    // setMapCenter(location);
  }, []);

  /**
   * 手动获取当前位置
   */
  const handleManualGetLocation = useCallback(async () => {
    try {
      console.log('📍 [MapTest] 手动获取当前位置...');
      setLocationStatus('正在获取位置...');
      
      const locationResult = await LocationService.getCurrentLocation();
      
      if (locationResult.success && locationResult.coordinates) {
        const newLocation = locationResult.coordinates;
        
        setMapCenter(newLocation);
        setUserLocation(newLocation);
        setLocationAccuracy(locationResult.accuracy || null);
        
        Alert.alert('获取位置成功', `当前位置：${locationResult.city?.name || '未知城市'}`);
        setLocationStatus(`手动获取成功 - ${locationResult.city?.name || '未知城市'}`);
      } else {
        Alert.alert('获取位置失败', locationResult.error || '无法获取当前位置');
        setLocationStatus(`获取失败: ${locationResult.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('❌ [MapTest] 手动获取位置失败:', error);
      Alert.alert('获取位置失败', '定位服务异常');
      setLocationStatus('获取失败: 服务异常');
    }
  }, []);

  /**
   * 清除缓存
   */
  const handleClearCache = useCallback(async () => {
    try {
      await LocationService.clearCache();
      setUserLocation(null);
      setLocationAccuracy(null);
      setLocationStatus('缓存已清除');
      Alert.alert('提示', '定位缓存已清除');
    } catch (error) {
      console.error('❌ [MapTest] 清除缓存失败:', error);
      Alert.alert('错误', '清除缓存失败');
    }
  }, []);

  /**
   * 处理房源标记点击
   */
  const handleMarkerPress = useCallback((marker: any) => {
    console.log('🏠 [MapTest] 房源标记被点击:', marker.title);
    
    // 移动到房源位置
    setMapCenter({
      latitude: marker.latitude,
      longitude: marker.longitude,
    });
    
    Alert.alert('房源信息', `${marker.title}\n价格：${marker.price}元/月\n面积：${marker.area}㎡`);
  }, []);

  /**
   * 处理地图长按
   */
  const handleMapLongPress = useCallback((coordinate: MapCenter) => {
    console.log('👆 [MapTest] 地图长按:', coordinate);
    
    Alert.alert(
      '搜索此区域',
      `是否在此位置(${coordinate.latitude.toFixed(4)}, ${coordinate.longitude.toFixed(4)})附近搜索房源？`,
      [
        { text: '取消', style: 'cancel' },
        { 
          text: '搜索', 
          onPress: () => {
            setMapCenter(coordinate);
            console.log('🔍 开始搜索房源...');
          }
        },
      ]
    );
  }, []);

  return (
    <View style={styles.container}>
      {/* 地图容器 */}
      <MapContainer
        center={mapCenter}
        markers={sampleProperties}
        zoomLevel={15}
        // 高德原生定位配置
        enableNativeLocation={true}
        showNativeLocationButton={true}  // Android显示原生定位按钮
        enableLongPressSearch={true}
        // 事件回调
        onUserLocationChanged={handleNativeLocationUpdate}
        onMarkerPress={handleMarkerPress}
        onLongPress={handleMapLongPress}
        onMapReady={() => {
          console.log('🗺️ [MapTest] 地图准备就绪');
          setLocationStatus('地图已加载，等待定位...');
        }}
      />

      {/* 控制面板 */}
      <View style={styles.controlPanel}>
        {/* 定位状态显示 */}
        <View style={styles.statusPanel}>
          <Text style={styles.statusText}>
            定位状态: {locationStatus}
          </Text>
          {locationAccuracy && (
            <Text style={styles.accuracyText}>
              定位精度: ±{locationAccuracy.toFixed(0)}米
            </Text>
          )}
          {userLocation && (
            <Text style={styles.locationText}>
              当前位置: {userLocation.latitude.toFixed(6)}, {userLocation.longitude.toFixed(6)}
            </Text>
          )}
        </View>

        {/* 控制按钮 */}
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={styles.button}
            onPress={handleManualGetLocation}
          >
            <Text style={styles.buttonText}>获取位置</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.button}
            onPress={handleClearCache}
          >
            <Text style={styles.buttonText}>清除缓存</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.button}
            onPress={() => {
              // 回到南宁市中心
              const nanningCenter = { latitude: 22.8167, longitude: 108.3669 };
              setMapCenter(nanningCenter);
            }}
          >
            <Text style={styles.buttonText}>回到中心</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  controlPanel: {
    position: 'absolute',
    bottom: 50,
    left: 16,
    right: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  statusPanel: {
    marginBottom: 12,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  accuracyText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  locationText: {
    fontSize: 10,
    color: '#999',
    fontFamily: 'monospace',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    backgroundColor: '#FF4F19',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

// 使用说明文档
export const PURE_AMAP_USAGE_DOCUMENTATION = `
# MapContainer 纯高德原生定位功能使用指南

## 🎯 核心架构

### 1. 纯高德原生定位
- **完全基于react-native-amap3d**: 不再使用Expo Location
- **MapView onLocation回调**: 直接接收高德SDK的定位数据
- **LocationService处理**: 统一处理定位数据、城市识别和缓存
- **无权限申请**: 权限管理完全交给高德地图SDK

### 2. 数据流程
\`\`\`
高德地图SDK定位 → MapView onLocation → LocationService.handleNativeLocationUpdate → 
缓存 + 城市识别 → 通知监听器 → 组件状态更新
\`\`\`

### 3. 定位策略
- **实时定位**: 优先使用高德SDK的实时定位数据
- **缓存定位**: 没有实时数据时使用缓存
- **默认位置**: 最后使用南宁默认位置

## 📋 使用步骤

### 1. 基础集成
\`\`\`tsx
import { MapContainer, LocationService } from '@/shared/components/MapContainer';

<MapContainer
  enableNativeLocation={true}      // 开启高德原生定位
  showNativeLocationButton={true}  // 显示原生定位按钮
  onUserLocationChanged={handleLocationUpdate}
/>
\`\`\`

### 2. 监听位置更新
\`\`\`tsx
useEffect(() => {
  const unsubscribe = LocationService.onLocationUpdate((result) => {
    if (result.success) {
      console.log('位置更新:', result.coordinates);
      console.log('城市:', result.city?.name);
      console.log('精度:', result.accuracy);
    }
  });
  
  return unsubscribe;
}, []);
\`\`\`

### 3. 获取当前位置
\`\`\`tsx
const getCurrentLocation = async () => {
  const result = await LocationService.getCurrentLocation();
  if (result.success) {
    console.log('当前位置:', result.coordinates);
  }
};
\`\`\`

## ⚡ 性能优势

### 1. 内存节省
- **单一SDK**: 不再同时加载Expo Location和高德定位
- **减少冲突**: 避免多个定位系统的冲突

### 2. 权限简化
- **统一权限**: 高德地图SDK统一管理定位权限
- **用户体验**: 减少权限申请弹窗

### 3. 定位精度
- **原生精度**: 直接使用高德SDK的高精度定位
- **优化算法**: 高德的定位算法更适合国内环境

## 🔧 故障排查

### 1. 定位不工作
- 检查app.json中的高德API Key配置
- 确认设备GPS和网络连接正常
- 查看控制台的高德定位日志

### 2. 权限问题
- 高德地图SDK会自动处理权限申请
- 无需手动申请位置权限
- 用户拒绝权限时会有相应提示

### 3. 缓存问题
- 使用LocationService.clearCache()清除缓存
- 重启应用重新获取定位

## 📊 测试验证

### 1. 功能测试
- 地图加载正常
- 定位数据获取正常
- 位置更新回调正常
- 缓存机制正常

### 2. 性能测试
- 内存使用对比
- 定位响应时间
- 电量消耗情况
`;