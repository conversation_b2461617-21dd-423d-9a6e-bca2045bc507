/**
 * 消息数据管理Hook
 * 封装所有API调用逻辑，符合企业级架构规范
 */
import { useQuery } from '@tanstack/react-query';
import { messageAPI } from '../services/messageAPI';
import { smsAPI } from '../services/smsAPI';
import type { MessageCategory } from '../types/businessMessage.types';

/**
 * 消息数据Hook接口
 */
interface UseMessageDataProps {
  activeRole: 'tenant_buyer' | 'landlord';
  activeCategory: MessageCategory;
  enabled?: boolean; // 控制是否启用API调用
}

/**
 * 消息数据Hook返回值
 */
interface UseMessageDataReturn {
  // 数据
  userRoles: any;
  messageData: any;
  paymentInfo: any;
  interactedUsers: any;
  smsAccount: any;
  
  // 加载状态
  isLoadingUserRoles: boolean;
  isLoadingMessages: boolean;
  isLoadingPayment: boolean;
  isLoadingUsers: boolean;
  isLoadingSMS: boolean;
  
  // 错误状态
  userRolesError: Error | null;
  messagesError: Error | null;
  paymentError: Error | null;
  usersError: Error | null;
  smsError: Error | null;
  
  // 操作方法
  refetchUserRoles: () => void;
  refetchMessages: () => void;
  refetchPayment: () => void;
  refetchUsers: () => void;
  refetchSMS: () => void;
}

/**
 * 消息数据管理Hook
 * 
 * @param props - Hook配置参数
 * @returns 消息数据和操作方法
 */
export const useMessageData = ({
  activeRole,
  activeCategory,
  enabled = false, // 默认不启用，等待业务触发
}: UseMessageDataProps): UseMessageDataReturn => {
  
  // 用户角色信息查询
  const {
    data: userRoles,
    isLoading: isLoadingUserRoles,
    error: userRolesError,
    refetch: refetchUserRoles,
  } = useQuery({
    queryKey: ['user-roles'],
    queryFn: () => messageAPI.getUserRoleInfo(),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    enabled,
  });

  // 消息列表查询
  const {
    data: messageData,
    isLoading: isLoadingMessages,
    error: messagesError,
    refetch: refetchMessages,
  } = useQuery({
    queryKey: ['business-messages', activeRole, activeCategory],
    queryFn: () => messageAPI.getMessageList(activeRole, activeCategory),
    staleTime: 2 * 60 * 1000, // 2分钟缓存
    enabled,
  });

  // 付费解锁信息查询
  const {
    data: paymentInfo,
    isLoading: isLoadingPayment,
    error: paymentError,
    refetch: refetchPayment,
  } = useQuery({
    queryKey: ['payment-unlock', activeCategory],
    queryFn: () => messageAPI.getPaymentUnlockInfo(activeCategory),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    enabled,
  });

  // 互动用户查询
  const {
    data: interactedUsers,
    isLoading: isLoadingUsers,
    error: usersError,
    refetch: refetchUsers,
  } = useQuery({
    queryKey: ['interacted-users'],
    queryFn: () => messageAPI.getInteractedUsers(),
    staleTime: 10 * 60 * 1000, // 10分钟缓存
    enabled,
  });

  // SMS积分账户查询
  const {
    data: smsAccount,
    isLoading: isLoadingSMS,
    error: smsError,
    refetch: refetchSMS,
  } = useQuery({
    queryKey: ['sms-account'],
    queryFn: () => smsAPI.getSMSCreditAccount(),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    enabled,
  });

  return {
    // 数据
    userRoles,
    messageData,
    paymentInfo,
    interactedUsers,
    smsAccount,
    
    // 加载状态
    isLoadingUserRoles,
    isLoadingMessages,
    isLoadingPayment,
    isLoadingUsers,
    isLoadingSMS,
    
    // 错误状态
    userRolesError,
    messagesError,
    paymentError,
    usersError,
    smsError,
    
    // 操作方法
    refetchUserRoles,
    refetchMessages,
    refetchPayment,
    refetchUsers,
    refetchSMS,
  };
};
