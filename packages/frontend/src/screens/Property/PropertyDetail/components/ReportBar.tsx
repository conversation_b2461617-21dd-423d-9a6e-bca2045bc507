/**
 * 举报纠错滚动条组件
 * 
 * 功能：
 * - 显示举报纠错信息
 * - 提供举报链接
 * - 响应式设计贴边显示
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import FeedbackService from '@shared/services/FeedbackService';
import { PropertyDetailDesignSystem } from '@property/styles/propertyDetailDesignSystem';
import { fp } from '@shared/utils/responsive';

const { typography, spacing } = PropertyDetailDesignSystem;

export const ReportBar: React.FC = () => {
  const handleReportPress = () => {
    FeedbackService.showInfo('跳转到举报纠错页面');
  };

  return (
    <View style={styles.reportBar}>
      <Text style={styles.reportText} numberOfLines={1}>
        房源内容不实，可点此举报或纠错！
        <Text
          style={styles.reportLink}
          onPress={handleReportPress}
        >
          点击这里
        </Text>
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  // 举报纠错滚动条 - 响应式设计贴到屏幕边缘，与下方无缝连接
  reportBar: {
    backgroundColor: '#FFD700', // 黄色底色
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xs,
    marginBottom: 0, // 移除底部间距，与标题容器无缝连接
  },

  // 举报文字
  reportText: {
    ...typography.small,
    color: '#2E2F29', // 深灰
    fontSize: fp(11),
    textAlign: 'center',
  },

  // 举报链接
  reportLink: {
    textDecorationLine: 'underline', // 下划线
    fontWeight: '600',
  },
});