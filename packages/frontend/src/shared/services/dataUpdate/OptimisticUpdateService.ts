/**
 * OptimisticUpdateService - 乐观更新服务
 * @fileoverview 实现乐观更新和错误回滚
 * <AUTHOR> Assistant
 * @since 2025-08-01
 *
 * 🎯 功能：
 * - 立即更新UI状态
 * - 支持错误时自动回滚
 * - 保存原始数据用于回滚
 */

import { QueryClient } from '@tanstack/react-query';

// 实体类型定义
export type EntityType = 'demand' | 'property' | 'user';

// 乐观更新配置
export interface OptimisticUpdateConfig<T = any> {
  entityType: EntityType;
  entityId: string;
  updates: Partial<T>;
  queryKey: string[];
  updateFn?: (oldData: T, updates: Partial<T>) => T;
}

// 回滚数据存储
interface RollbackData {
  queryKey: string[];
  originalData: any;
  timestamp: number;
}

/**
 * 乐观更新服务类 - 单例模式
 */
class OptimisticUpdateServiceClass {
  private static instance: OptimisticUpdateServiceClass | null = null;
  private queryClient: QueryClient | null = null;
  private rollbackStore: Map<string, RollbackData> = new Map();
  private readonly ROLLBACK_TIMEOUT = 30000; // 30秒后自动清理回滚数据
  private isInitialized = false;

  private constructor() {
    // 私有构造函数，防止外部实例化
  }

  /**
   * 获取单例实例（按需初始化）
   */
  public static getInstance(
    queryClient?: QueryClient
  ): OptimisticUpdateServiceClass {
    if (!OptimisticUpdateServiceClass.instance) {
      OptimisticUpdateServiceClass.instance =
        new OptimisticUpdateServiceClass();
    }

    // 如果提供了queryClient且还未初始化，则进行初始化
    if (queryClient && !OptimisticUpdateServiceClass.instance.isInitialized) {
      OptimisticUpdateServiceClass.instance.initialize(queryClient);
    }

    return OptimisticUpdateServiceClass.instance;
  }

  /**
   * 初始化服务
   */
  private initialize(queryClient: QueryClient): void {
    if (this.isInitialized) return;

    this.queryClient = queryClient;
    this.isInitialized = true;
    console.log('⚡ [OptimisticUpdate] 按需初始化乐观更新服务');

    // 启动清理定时器
    this.startCleanupTimer();
  }

  /**
   * 执行乐观更新
   */
  async applyOptimisticUpdate<T>(
    config: OptimisticUpdateConfig<T>
  ): Promise<string> {
    if (!this.queryClient) {
      console.warn('⚡ [OptimisticUpdate] QueryClient未初始化');
      throw new Error('QueryClient未初始化');
    }

    const { entityType, entityId, updates, queryKey, updateFn } = config;
    const rollbackId = this.generateRollbackId(entityType, entityId);

    console.log(
      `⚡ [OptimisticUpdate] 执行乐观更新: ${entityType}/${entityId}`,
      updates
    );

    try {
      // 获取当前数据
      const currentData = this.queryClient.getQueryData<T>(queryKey);

      if (!currentData) {
        console.warn(
          `⚡ [OptimisticUpdate] 未找到缓存数据: ${queryKey.join('/')}`
        );
        return rollbackId;
      }

      // 保存原始数据用于回滚
      this.saveRollbackData(rollbackId, queryKey, currentData);

      // 计算新数据
      const newData = updateFn
        ? updateFn(currentData, updates)
        : { ...currentData, ...updates };

      // 立即更新缓存
      this.queryClient.setQueryData(queryKey, newData);

      console.log(`⚡ [OptimisticUpdate] 乐观更新完成: ${rollbackId}`);
      return rollbackId;
    } catch (error) {
      console.error(`⚡ [OptimisticUpdate] 乐观更新失败:`, error);
      throw error;
    }
  }

  /**
   * 确认更新（清理回滚数据）
   */
  confirmUpdate(rollbackId: string, newData?: any): void {
    console.log(`⚡ [OptimisticUpdate] 确认更新: ${rollbackId}`);

    const rollbackData = this.rollbackStore.get(rollbackId);
    if (rollbackData && newData) {
      // 用服务器返回的真实数据更新缓存
      this.queryClient?.setQueryData(rollbackData.queryKey, newData);
    }

    // 清理回滚数据
    this.rollbackStore.delete(rollbackId);
  }

  /**
   * 回滚更新
   */
  async rollbackUpdate(rollbackId: string): Promise<void> {
    if (!this.queryClient) {
      console.warn('⚡ [OptimisticUpdate] QueryClient未初始化');
      return;
    }

    const rollbackData = this.rollbackStore.get(rollbackId);
    if (!rollbackData) {
      console.warn(`⚡ [OptimisticUpdate] 未找到回滚数据: ${rollbackId}`);
      return;
    }

    console.log(`⚡ [OptimisticUpdate] 回滚更新: ${rollbackId}`);

    try {
      // 恢复原始数据
      this.queryClient.setQueryData(
        rollbackData.queryKey,
        rollbackData.originalData
      );

      // 清理回滚数据
      this.rollbackStore.delete(rollbackId);

      console.log(`⚡ [OptimisticUpdate] 回滚完成: ${rollbackId}`);
    } catch (error) {
      console.error(`⚡ [OptimisticUpdate] 回滚失败:`, error);
    }
  }

  /**
   * 状态更新的专用方法
   */
  async updateStatus(
    entityType: EntityType,
    entityId: string,
    newStatus: string,
    additionalUpdates?: any
  ): Promise<string> {
    // 构建查询键
    const queryKey = this.buildQueryKey(entityType, entityId);

    // 构建更新数据
    const updates = {
      status: newStatus,
      frontend_status: this.mapToFrontendStatus(newStatus),
      updated_at: new Date().toISOString(),
      ...additionalUpdates,
    };

    // 执行乐观更新
    return this.applyOptimisticUpdate({
      entityType,
      entityId,
      updates,
      queryKey,
      updateFn: (oldData: any, updates: any) => ({
        ...oldData,
        ...updates,
        // 特殊处理状态显示
        status_display: this.getStatusDisplay(newStatus, entityType),
      }),
    });
  }

  /**
   * 列表项更新的专用方法
   */
  async updateListItem<T>(
    listQueryKey: string[],
    entityId: string,
    updates: Partial<T>,
    findFn?: (item: T) => boolean
  ): Promise<string> {
    if (!this.queryClient) {
      throw new Error('QueryClient未初始化');
    }

    const rollbackId = this.generateRollbackId('list', entityId);

    try {
      // 获取列表数据
      const listData = this.queryClient.getQueryData<T[]>(listQueryKey);

      if (!listData || !Array.isArray(listData)) {
        console.warn(
          `⚡ [OptimisticUpdate] 列表数据无效: ${listQueryKey.join('/')}`
        );
        return rollbackId;
      }

      // 保存原始数据
      this.saveRollbackData(rollbackId, listQueryKey, listData);

      // 更新列表项
      const newListData = listData.map(item => {
        const shouldUpdate = findFn
          ? findFn(item)
          : (item as any).id === entityId;

        return shouldUpdate ? { ...item, ...updates } : item;
      });

      // 更新缓存
      this.queryClient.setQueryData(listQueryKey, newListData);

      return rollbackId;
    } catch (error) {
      console.error(`⚡ [OptimisticUpdate] 列表更新失败:`, error);
      throw error;
    }
  }

  /**
   * 获取回滚统计信息
   */
  getRollbackStats(): { count: number; oldestTimestamp: number | null } {
    const count = this.rollbackStore.size;
    let oldestTimestamp: number | null = null;

    for (const data of this.rollbackStore.values()) {
      if (oldestTimestamp === null || data.timestamp < oldestTimestamp) {
        oldestTimestamp = data.timestamp;
      }
    }

    return { count, oldestTimestamp };
  }

  /**
   * 清理所有回滚数据
   */
  clearAllRollbackData(): void {
    console.log('⚡ [OptimisticUpdate] 清理所有回滚数据');
    this.rollbackStore.clear();
  }

  // === 私有方法 ===

  /**
   * 生成回滚ID
   */
  private generateRollbackId(entityType: string, entityId: string): string {
    return `${entityType}_${entityId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 保存回滚数据
   */
  private saveRollbackData(
    rollbackId: string,
    queryKey: string[],
    originalData: any
  ): void {
    this.rollbackStore.set(rollbackId, {
      queryKey,
      originalData: JSON.parse(JSON.stringify(originalData)), // 深拷贝
      timestamp: Date.now(),
    });
  }

  /**
   * 构建查询键
   */
  private buildQueryKey(entityType: EntityType, entityId: string): string[] {
    const keyMap = {
      demand: ['demandDetail', entityId],
      property: ['propertyDetail', entityId],
      user: ['userProfile', entityId],
    };

    return keyMap[entityType] || [entityType, entityId];
  }

  /**
   * 映射到前端状态
   */
  private mapToFrontendStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      ACTIVE: 'active',
      OFFLINE: 'inactive', // 需求的下架状态
      INACTIVE: 'inactive', // 房源的下架状态
      DRAFT: 'draft',
      COMPLETED: 'completed',
      EXPIRED: 'expired',
      SOLD: 'sold',
      RENTED: 'rented',
    };

    return statusMap[status] || status.toLowerCase();
  }

  /**
   * 获取状态显示文本
   */
  private getStatusDisplay(status: string, entityType: EntityType): string {
    const statusDisplayMap: { [key: string]: string } = {
      ACTIVE: '已发布',
      OFFLINE: '已下架', // 需求的下架状态
      INACTIVE: '已下架', // 房源的下架状态
      DRAFT: '草稿',
      COMPLETED: '已完成',
      EXPIRED: '已过期',
      SOLD: '已售出',
      RENTED: '已出租',
    };

    return statusDisplayMap[status] || status;
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    setInterval(() => {
      const now = Date.now();
      const expiredKeys: string[] = [];

      for (const [key, data] of this.rollbackStore.entries()) {
        if (now - data.timestamp > this.ROLLBACK_TIMEOUT) {
          expiredKeys.push(key);
        }
      }

      if (expiredKeys.length > 0) {
        console.log(
          `⚡ [OptimisticUpdate] 清理过期回滚数据: ${expiredKeys.length}个`
        );
        expiredKeys.forEach(key => this.rollbackStore.delete(key));
      }
    }, 60000); // 每分钟检查一次
  }
}

// 导出单例访问器
export const OptimisticUpdateService = {
  getInstance: (queryClient?: QueryClient) =>
    OptimisticUpdateServiceClass.getInstance(queryClient),
  // 兼容性方法，直接代理到实例
  applyOptimisticUpdate: (...args: any[]) =>
    OptimisticUpdateServiceClass.getInstance().applyOptimisticUpdate(...args),
  confirmUpdate: (...args: any[]) =>
    OptimisticUpdateServiceClass.getInstance().confirmUpdate(...args),
  rollbackUpdate: (...args: any[]) =>
    OptimisticUpdateServiceClass.getInstance().rollbackUpdate(...args),
  updateStatus: (...args: any[]) =>
    OptimisticUpdateServiceClass.getInstance().updateStatus(...args),
  updateListItem: (...args: any[]) =>
    OptimisticUpdateServiceClass.getInstance().updateListItem(...args),
  getRollbackStats: () =>
    OptimisticUpdateServiceClass.getInstance().getRollbackStats(),
  clearAllRollbackData: () =>
    OptimisticUpdateServiceClass.getInstance().clearAllRollbackData(),
};

// React Hook 用于在组件中使用乐观更新
import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';

/**
 * React Hook：使用乐观更新服务
 */
export const useOptimisticUpdate = () => {
  const queryClient = useQueryClient();

  // 按需获取服务实例
  const serviceInstance = useMemo(() => {
    return OptimisticUpdateServiceClass.getInstance(queryClient);
  }, [queryClient]);

  const updateStatus = useCallback(
    (
      entityType: EntityType,
      entityId: string,
      newStatus: string,
      additionalUpdates?: any
    ) => {
      return serviceInstance.updateStatus(
        entityType,
        entityId,
        newStatus,
        additionalUpdates
      );
    },
    [serviceInstance]
  );

  const confirmUpdate = useCallback(
    (rollbackId: string, newData?: any) => {
      serviceInstance.confirmUpdate(rollbackId, newData);
    },
    [serviceInstance]
  );

  const rollbackUpdate = useCallback(
    (rollbackId: string) => {
      return serviceInstance.rollbackUpdate(rollbackId);
    },
    [serviceInstance]
  );

  return {
    updateStatus,
    confirmUpdate,
    rollbackUpdate,
    getRollbackStats: serviceInstance.getRollbackStats.bind(serviceInstance),
  };
};
