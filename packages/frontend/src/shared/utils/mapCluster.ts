/**
 * 地图聚合工具函数
 * 职责：将房源数据按楼盘聚合，生成聚合标记数据
 */

export interface PropertyMarker {
  id: string;
  latitude: number;
  longitude: number;
  title: string;
  price?: number;
  area?: number;
  propertyType?: string;
  dealType?: 'rent' | 'sale'; // 租赁或出售
  buildingName?: string; // 楼盘名称
  district?: string; // 区域
}

export interface ClusterData {
  id: string;
  latitude: number;
  longitude: number;
  rentCount: number;
  saleCount: number;
  buildingName: string;
  district: string;
  properties: PropertyMarker[];
}

/**
 * 按楼盘聚合房源数据
 * @param properties 房源数据数组
 * @param selectedDealType 选中的交易类型筛选
 * @returns 聚合后的数据数组
 */
export const clusterPropertiesByBuilding = (
  properties: PropertyMarker[],
  selectedDealType: 'rent' | 'sale' | null = null
): ClusterData[] => {
  // 按楼盘名称分组
  const buildingGroups = new Map<string, PropertyMarker[]>();

  properties.forEach(property => {
    const buildingKey =
      property.buildingName ||
      property.title ||
      `${property.latitude}_${property.longitude}`;

    if (!buildingGroups.has(buildingKey)) {
      buildingGroups.set(buildingKey, []);
    }
    buildingGroups.get(buildingKey)!.push(property);
  });

  // 转换为聚合数据
  const clusters: ClusterData[] = [];

  buildingGroups.forEach((buildingProperties, buildingName) => {
    // 计算租赁和出售数量
    const rentCount = buildingProperties.filter(
      p => p.dealType === 'rent'
    ).length;
    const saleCount = buildingProperties.filter(
      p => p.dealType === 'sale'
    ).length;

    // 根据筛选条件决定是否显示
    let shouldShow = true;
    if (selectedDealType === 'rent' && rentCount === 0) {
      shouldShow = false;
    }
    if (selectedDealType === 'sale' && saleCount === 0) {
      shouldShow = false;
    }

    if (!shouldShow) return;

    // 使用第一个房源的位置作为聚合标记位置
    const firstProperty = buildingProperties[0];

    clusters.push({
      id: `cluster_${buildingName}`,
      latitude: firstProperty.latitude,
      longitude: firstProperty.longitude,
      rentCount: selectedDealType === 'sale' ? 0 : rentCount,
      saleCount: selectedDealType === 'rent' ? 0 : saleCount,
      buildingName,
      district: firstProperty.district || '未知区域',
      properties: buildingProperties,
    });
  });

  return clusters;
};

/**
 * 根据地图缩放级别决定是否需要进一步聚合
 * @param clusters 聚合数据
 * @param zoomLevel 地图缩放级别
 * @returns 是否需要区域级聚合
 */
export const shouldUseDistrictClustering = (
  clusters: ClusterData[],
  zoomLevel: number
): boolean => {
  // 缩放级别较小（地图范围较大）且聚合点较多时，使用区域级聚合
  return zoomLevel < 12 && clusters.length > 20;
};

/**
 * 按区域聚合数据（用于大范围地图显示）
 * @param clusters 楼盘级聚合数据
 * @returns 区域级聚合数据
 */
export const clusterByDistrict = (clusters: ClusterData[]): ClusterData[] => {
  const districtGroups = new Map<string, ClusterData[]>();

  clusters.forEach(cluster => {
    const district = cluster.district;
    if (!districtGroups.has(district)) {
      districtGroups.set(district, []);
    }
    districtGroups.get(district)!.push(cluster);
  });

  const districtClusters: ClusterData[] = [];

  districtGroups.forEach((districtClusters, district) => {
    // 计算区域中心点（简单平均）
    const avgLat =
      districtClusters.reduce((sum, c) => sum + c.latitude, 0) /
      districtClusters.length;
    const avgLng =
      districtClusters.reduce((sum, c) => sum + c.longitude, 0) /
      districtClusters.length;

    // 计算总数量
    const totalRentCount = districtClusters.reduce(
      (sum, c) => sum + c.rentCount,
      0
    );
    const totalSaleCount = districtClusters.reduce(
      (sum, c) => sum + c.saleCount,
      0
    );

    // 合并所有房源
    const allProperties = districtClusters.flatMap(c => c.properties);

    districtClusters.push({
      id: `district_${district}`,
      latitude: avgLat,
      longitude: avgLng,
      rentCount: totalRentCount,
      saleCount: totalSaleCount,
      buildingName: district,
      district,
      properties: allProperties,
    });
  });

  return districtClusters;
};

/**
 * 生成模拟房源数据（用于测试）
 * @param center 中心点坐标
 * @param count 生成数量
 * @returns 模拟房源数据
 */
export const generateMockProperties = (
  center: { latitude: number; longitude: number },
  count: number = 50
): PropertyMarker[] => {
  const properties: PropertyMarker[] = [];
  const buildingNames = [
    '万象城',
    '青秀万达',
    '航洋国际',
    '梦之岛',
    '朝阳广场',
    '南宁百货',
    '华润万象汇',
    '印象城',
    '凤岭儿童公园',
    '青秀山',
  ];

  const districts = ['青秀区', '兴宁区', '江南区', '良庆区', '邕宁区'];
  const propertyTypes = ['写字楼', '商铺', '厂房', '仓库'];

  for (let i = 0; i < count; i++) {
    // 在中心点周围随机分布
    const latOffset = (Math.random() - 0.5) * 0.1; // 约11km范围
    const lngOffset = (Math.random() - 0.5) * 0.1;

    properties.push({
      id: `property_${i}`,
      latitude: center.latitude + latOffset,
      longitude: center.longitude + lngOffset,
      title: `${buildingNames[i % buildingNames.length]}${Math.floor(i / buildingNames.length) + 1}号楼`,
      price: Math.floor(Math.random() * 50000) + 1000,
      area: Math.floor(Math.random() * 500) + 50,
      propertyType:
        propertyTypes[Math.floor(Math.random() * propertyTypes.length)],
      dealType: Math.random() > 0.5 ? 'rent' : 'sale',
      buildingName: buildingNames[i % buildingNames.length],
      district: districts[Math.floor(Math.random() * districts.length)],
    });
  }

  return properties;
};
