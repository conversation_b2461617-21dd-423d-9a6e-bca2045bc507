/**
 * FormContent 样式定义
 * @fileoverview 表单内容组件的样式
 * <AUTHOR> Assistant
 * @since 2025-08-01
 */

import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../../../shared/utils/responsiveUtils';

export const styles = StyleSheet.create({
  // === 主容器样式 ===
  container: {
    flex: 1,
  },

  // === 表单卡片样式 ===
  formCard: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: wp(20),
    borderTopRightRadius: wp(20),
    marginTop: hp(20),
    paddingHorizontal: wp(20),
    paddingTop: hp(30),
    paddingBottom: hp(40),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    minHeight: hp(600), // 确保有足够的高度
  },

  // === 区块间距样式 ===
  sectionSpacing: {
    marginBottom: hp(30),
  },

  // === 分隔线样式 ===
  sectionDivider: {
    height: 1,
    backgroundColor: '#F0F0F0',
    marginVertical: hp(25),
    marginHorizontal: wp(-20), // 延伸到卡片边缘
  },
});
