# 认证状态修复测试

## 修复内容

### 1. 开发环境默认状态改为未登录
**文件**: `packages/frontend/src/domains/auth/services/authStore.ts`
**修改**: 第471-481行，将开发环境默认状态从已登录改为未登录

**修改前**:
```typescript
// 默认开发环境行为：自动Mock认证
set({
  user: MOCK_DEV_USER,
  isAuthenticated: true,
  isLoading: false,
  error: null,
});
```

**修改后**:
```typescript
// 默认开发环境行为：显示未登录状态（真实用户体验）
set({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
});
```

### 2. 优化退出登录逻辑
**文件**: `packages/frontend/src/domains/auth/services/auth.ts`
**修改**: 第202-224行，只在有token时才调用后端API

**修改前**:
```typescript
async logout(): Promise<void> {
  try {
    if (!this.useMockAPI) {
      await apiClient.post('/auth/logout'); // 总是调用API
    }
  } catch (error) {
    console.warn('Backend logout failed:', error);
  } finally {
    await this.clearLocalAuthData();
  }
}
```

**修改后**:
```typescript
async logout(): Promise<void> {
  try {
    const token = await storage.getString(STORAGE_KEYS.ACCESS_TOKEN);
    
    if (token && !this.useMockAPI) {
      // 只在有token时调用后端API
      await apiClient.post('/auth/logout');
    } else {
      console.log('[Auth] 未找到访问令牌，跳过后端登出调用');
    }
  } catch (error) {
    console.warn('Backend logout failed:', error);
  } finally {
    await this.clearLocalAuthData();
  }
}
```

### 3. 后端SMS登录API支持前端参数
**文件**: `packages/backend/app/schemas/user.py`
**修改**: 第82-90行，添加code_type参数支持

**文件**: `packages/backend/app/api/v1/auth.py`
**修改**: 第223-224行和258行，使用动态code_type

## 预期效果

### 开发环境
1. ✅ 应用启动时显示未登录状态
2. ✅ 首页显示登录/注册按钮
3. ✅ 右上角不再显示已登录的用户信息
4. ✅ 可以正常进行验证码登录流程

### 退出登录
1. ✅ 未登录状态下不会触发API调用
2. ✅ 已登录状态下正常调用后端API
3. ✅ 无论API调用成功与否都会清除本地数据

### API兼容性
1. ✅ 前端SMS登录请求包含code_type参数
2. ✅ 后端正确处理并使用code_type
3. ✅ API路径匹配: `/api/v1/auth/login/sms`

## 测试步骤

1. **重新加载应用**
   - 关闭并重新打开React Native应用
   - 观察首页是否显示未登录状态

2. **验证码登录测试**
   ```bash
   # 测试请求验证码API
   curl -X POST "http://localhost:8082/api/v1/auth/request-code" \
     -H "Content-Type: application/json" \
     -d '{"phone_number": "13800138000"}'
   
   # 测试验证码登录API  
   curl -X POST "http://localhost:8082/api/v1/auth/login/sms" \
     -H "Content-Type: application/json" \
     -d '{"phone_number": "13800138000", "verification_code": "123456", "code_type": "login"}'
   ```

3. **退出登录测试**
   - 在未登录状态下触发退出操作
   - 观察控制台是否显示跳过API调用的日志

## 故障排除

如果修改未生效：
1. 清除React Native缓存: `npx expo start --clear`
2. 重新安装依赖: `npm install`
3. 重启开发服务器
4. 在真机上重新安装应用

如果还有问题：
1. 检查控制台日志确认代码是否执行
2. 验证.env文件配置
3. 确认Docker容器是否正常运行