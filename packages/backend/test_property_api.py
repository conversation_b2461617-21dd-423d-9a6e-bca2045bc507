import asyncio
import aiohttp
import json

async def test_property_publish():
    test_data = {
        'title': '高端商务写字楼出租',
        'property_type': 'OFFICE',
        'sub_type': '写字楼',
        'total_area': 120.0,
        'transaction_types': ['RENT'],
        'address': '广西南宁市青秀区民族大道100号',
        'features': {
            'has_elevator': True,
            'has_parking': True,
            'has_air_conditioning': True
        },
        'prices': [
            {
                'transaction_type': 'RENT',
                'rent_price': 8000.0,
                'rent_payment_method': '月付',
                'rent_deposit_months': 2
            }
        ],
        'tags': ['地铁口', '精装修', '商务区']
    }
    
    # 获取认证token (使用手机号登录)
    login_data = {
        'phone_number': '18377100296',
        'password': '123456'
    }
    
    async with aiohttp.ClientSession() as session:
        # 登录
        async with session.post(
            'http://backend/api/v1/auth/login/phone',
            json=login_data,
            headers={'Content-Type': 'application/json'}
        ) as resp:
            if resp.status != 200:
                print(f'登录失败: {resp.status}')
                print(await resp.text())
                return
            
            login_result = await resp.json()
            token = login_result['access_token']
            print(f'登录成功，token: {token[:20]}...')
        
        # 发布房源
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        async with session.post(
            'http://backend/api/v1/properties/',
            json=test_data,
            headers=headers
        ) as resp:
            print(f'房源发布API响应状态: {resp.status}')
            response_text = await resp.text()
            print(f'响应内容: {response_text}')
            
            if resp.status == 201:
                result = json.loads(response_text)
                print('✅ 房源发布成功！')
                print(f'房源ID: {result.get("id")}')
                print(f'房源标题: {result.get("title")}')
                print(f'地址: {result.get("address")}')
                print(f'特性: {result.get("features")}')
                print(f'标签: {result.get("tags")}')
            else:
                print('❌ 房源发布失败')

asyncio.run(test_property_publish())