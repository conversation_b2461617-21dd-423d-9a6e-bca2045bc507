import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';

export const baseStyles = StyleSheet.create({
  // 基础容器样式
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: hp(100),
  },
  
  // 基础文本样式
  errorText: {
    color: '#FF3B30',
    fontSize: fp(12),
    marginTop: hp(4),
    marginLeft: wp(4),
  },
  warningText: {
    color: '#FF9500',
    fontSize: fp(12),
    marginTop: hp(4),
    marginLeft: wp(4),
  },
  
  // 基础输入框样式
  inputError: {
    borderColor: '#FF3B30',
    borderWidth: 1,
  },
  
  // 基础布局样式
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  column: {
    flexDirection: 'column',
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  
  // 基础间距样式
  marginSmall: {
    margin: wp(8),
  },
  marginMedium: {
    margin: wp(16),
  },
  marginLarge: {
    margin: wp(24),
  },
  paddingSmall: {
    padding: wp(8),
  },
  paddingMedium: {
    padding: wp(16),
  },
  paddingLarge: {
    padding: wp(24),
  },
  
  // 基础边框样式
  borderRadius: {
    borderRadius: wp(8),
  },
  borderRadiusSmall: {
    borderRadius: wp(4),
  },
  borderRadiusLarge: {
    borderRadius: wp(12),
  },
  
  // 基础阴影样式
  shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  shadowLight: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
});
