/**
 * 房源表单使用企业级组件的示例
 * 
 * 展示如何用新的企业级表单系统替换现有的表单实现
 * 这个文件可以作为将来重构SimplePropertyForm的参考
 */

import React, { useCallback } from 'react';
import { View, Text } from 'react-native';
import {
  EnterpriseFormContainer,
  EnterpriseFormInput,
  useEnterpriseFormValidation,
  CommonFormStyles,
  type FormFieldError,
} from '../../../../shared/components/Form';

// 表单数据类型（简化版）
interface PropertyFormData {
  title: string;
  property_certificate_address: string;
  area: number;
  description: string;
  rent_price: string;
  transaction_types: string[];
  rent_payment_method: string;
}

export const PropertyFormExample: React.FC = () => {
  // 企业级表单验证配置
  const formValidation = useEnterpriseFormValidation<PropertyFormData>({
    fields: {
      title: {
        required: true,
        minLength: 8, // 新要求：8个字符
        maxLength: 100,
      },
      property_certificate_address: {
        required: true,
        minLength: 5,
      },
      area: {
        required: true,
        min: 0.1,
        custom: (value: any) => {
          const numValue = typeof value === 'number' ? value : parseFloat(value);
          if (isNaN(numValue) || numValue <= 0) {
            return { valid: false, message: '请输入有效的面积' };
          }
          return { valid: true };
        }
      },
      description: {
        required: true,
        minLength: 20,
        maxLength: 2000,
      },
      transaction_types: {
        required: true,
        custom: (value: string[]) => {
          if (!Array.isArray(value) || value.length === 0) {
            return { valid: false, message: '请至少选择一种交易类型' };
          }
          return { valid: true };
        }
      },
      rent_payment_method: {
        custom: (value: string, allValues?: PropertyFormData) => {
          // 只有在包含租赁交易时才验证支付方式
          if (allValues?.transaction_types?.includes('RENT') && !value) {
            return { valid: false, message: '请选择租金支付方式' };
          }
          return { valid: true };
        }
      }
    },
    defaultValues: {
      title: '',
      property_certificate_address: '',
      area: 0,
      description: '',
      rent_price: '',
      transaction_types: [],
      rent_payment_method: 'QUARTERLY',
    },
    realTimeValidation: true,
    validateOnBlur: true,
    showWarnings: true,
  });

  const {
    values,
    errors,
    warnings,
    setValue,
    getFieldErrors,
    submitForm,
  } = formValidation;

  // 表单验证函数
  const validateForm = useCallback((): FormFieldError[] => {
    const fieldErrors = getFieldErrors();
    
    // 可以添加自定义业务验证
    const customErrors: FormFieldError[] = [];
    
    // 验证交易类型和对应价格的业务逻辑
    if (values.transaction_types.includes('RENT') && !values.rent_price) {
      customErrors.push({
        fieldName: 'rent_price',
        message: '请填写租金',
        priority: 1,
      });
    }
    
    return [...fieldErrors, ...customErrors];
  }, [values, getFieldErrors]);

  // 处理表单提交
  const handleSubmit = useCallback(async () => {
    const success = await submitForm(async (formData) => {
      console.log('提交数据:', formData);
      // 实际的API调用会在这里
      // await publishAPI.createProperty(transformedData);
    });

    if (success) {
      console.log('房源发布成功');
    }
  }, [submitForm]);

  return (
    <EnterpriseFormContainer
      onValidate={validateForm}
      onSubmit={handleSubmit}
      enableAutoValidation={true}
      keyboardOptions={{
        extraOffset: 30,
        enableAutoScroll: true,
        scrollAnimationDuration: 300,
      }}
    >
      {/* 基础信息区块 */}
      <View style={CommonFormStyles.formSection}>
        <Text style={CommonFormStyles.sectionTitle}>基础信息</Text>
        <Text style={CommonFormStyles.sectionSubtitle}>
          来自房产证识别的信息，可手动修改
        </Text>
        
        <EnterpriseFormInput
          fieldName="property_certificate_address"
          label="地址"
          required={true}
          value={values.property_certificate_address}
          onValueChange={(value) => setValue('property_certificate_address', value)}
          error={errors.property_certificate_address}
          warning={warnings.property_certificate_address}
          placeholder="请输入详细地址"
          multiline={true}
          numberOfLines={2}
          hint="请填写完整的房产证地址"
        />
        
        <EnterpriseFormInput
          fieldName="area"
          label="建筑面积"
          required={true}
          value={values.area.toString()}
          onValueChange={(value) => setValue('area', parseFloat(value) || 0)}
          error={errors.area}
          warning={warnings.area}
          placeholder="房产证上的建筑面积"
          keyboardType="numeric"
          hint="单位：平方米"
        />
      </View>
      
      {/* 详细描述区块 */}
      <View style={CommonFormStyles.formSection}>
        <Text style={CommonFormStyles.sectionTitle}>详细描述</Text>
        
        <EnterpriseFormInput
          fieldName="title"
          label="房源标题"
          required={true}
          value={values.title}
          onValueChange={(value) => setValue('title', value)}
          error={errors.title}
          warning={warnings.title}
          placeholder="请输入房源标题"
          maxLength={100}
          hint="标题至少需要8个字符，用于吸引客户注意"
        />
        
        <EnterpriseFormInput
          fieldName="description"
          label="房源描述"
          required={true}
          value={values.description}
          onValueChange={(value) => setValue('description', value)}
          error={errors.description}
          warning={warnings.description}
          placeholder="请详细描述房源特色、周边配套、交通情况等..."
          multiline={true}
          numberOfLines={6}
          maxLength={2000}
          hint="详细描述有助于提高房源吸引力，至少需要20个字符"
        />
      </View>
    </EnterpriseFormContainer>
  );
};

export default PropertyFormExample;