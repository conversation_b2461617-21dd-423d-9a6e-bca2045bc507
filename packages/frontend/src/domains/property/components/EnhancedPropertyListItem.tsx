/**
 * 🏗️ 企业级架构：增强版房源列表项组件
 * 兼容现有MyPropertiesScreen的复杂功能，同时提供通用接口
 * 支持草稿模式、统计信息、自定义操作等
 */

import React, { useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Alert,
} from 'react-native';

// 🏗️ 企业级架构：样式工具
import { spacing, fontSize, borderRadius } from '../../../shared/utils/responsiveUtils';

// 🔧 兼容现有Property接口 - 使用any类型以兼容不同的Property定义
interface PropertyItem {
  id: string;
  type?: 'rental' | 'sale';
  title: string;
  price: string;
  area: string;
  location: string;
  description?: string;
  imageUrl: string;
  status: 'published' | 'draft' | 'inactive';
  exposureCount: number;
  favoriteCount: number;
  inquiryCount: number;
  viewCount: number;
  tenantMatches: number;
  isPremiumOwner: boolean;
  priceRange?: { min: number; max: number };
  areaRange?: { min: number; max: number };
  createdAt: Date;
  updatedAt: Date;
  draftData?: any;
  [key: string]: any; // 允许其他属性
}

interface EnhancedPropertyListItemProps {
  item: PropertyItem;
  onPress: (item: PropertyItem) => void;
  onEdit?: (item: PropertyItem) => void;
  onDelete?: (item: PropertyItem) => void;
  formatDateTime?: (date: Date) => string;
  
  // 🎨 UI配置
  showActions?: boolean;
  showStats?: boolean;
  customLayout?: 'default' | 'enhanced';
}

export const EnhancedPropertyListItem: React.FC<EnhancedPropertyListItemProps> = ({
  item,
  onPress,
  onEdit,
  onDelete,
  formatDateTime,
  showActions = true,
  showStats = true,
  customLayout = 'enhanced',
}) => {
  const isDraft = item.status === 'draft';

  // 🔧 事件处理：编辑草稿
  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit(item);
    }
  }, [item, onEdit]);

  // 🔧 事件处理：删除草稿
  const handleDelete = useCallback(() => {
    if (onDelete) {
      Alert.alert(
        '确认删除',
        '您确定要删除此草稿吗？此操作不可撤销。',
        [
          { text: '取消', style: 'cancel' },
          {
            text: '删除',
            style: 'destructive',
            onPress: () => onDelete(item),
          },
        ]
      );
    }
  }, [item, onDelete]);

  // 🎨 UI层：格式化时间
  const formatTime = (date: Date) => {
    if (formatDateTime) {
      return formatDateTime(date);
    }
    return date.toLocaleDateString();
  };

  return (
    <TouchableOpacity
      style={styles.propertyItem}
      onPress={() => onPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.propertyContent}>
        {/* 房源图片 */}
        <Image 
          source={{ 
            uri: item.imageUrl || 'https://via.placeholder.com/80x80?text=房源' 
          }} 
          style={styles.propertyImage} 
        />

        {/* 房源信息 */}
        <View style={styles.propertyInfo}>
          {/* 标题和类型标签 */}
          <View style={styles.propertyHeader}>
            <View style={[
              styles.propertyTypeTag, 
              isDraft && styles.draftPropertyTypeTag
            ]}>
              <Text style={[
                styles.propertyTypeText, 
                isDraft && styles.draftPropertyTypeText
              ]}>
                {isDraft ? '草稿' : (item.type === 'rental' ? '出租' : item.type === 'sale' ? '出售' : '房源')}
              </Text>
            </View>
            <Text style={styles.propertyTitle} numberOfLines={1}>
              {item.title}
            </Text>
          </View>

          {/* 草稿模式内容 */}
          {isDraft ? (
            <>
              <Text style={styles.draftPromptText}>
                快完善房源信息，发布赚米吧~
              </Text>
              <View style={styles.draftBottomRow}>
                <Text style={styles.draftTimestamp}>
                  {formatTime(item.updatedAt)}
                </Text>
                {showActions && (
                  <View style={styles.draftActionsContainer}>
                    <TouchableOpacity 
                      style={styles.draftActionButton} 
                      onPress={handleDelete}
                    >
                      <Text style={styles.draftActionButtonText}>删除</Text>
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={[styles.draftActionButton, styles.editButton]} 
                      onPress={handleEdit}
                    >
                      <Text style={[styles.draftActionButtonText, styles.editButtonText]}>
                        编辑
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            </>
          ) : (
            <>
              {/* 已发布房源内容 */}
              <Text style={styles.propertyPrice}>¥ {item.price}</Text>
              <Text style={styles.propertyDetails}>
                {item.area} · {item.location}
              </Text>
              
              {/* 统计信息 */}
              {showStats && (
                <View style={styles.propertyStats}>
                  <Text style={styles.propertyStat}>曝光{item.exposureCount}</Text>
                  <Text style={styles.propertyStat}>浏览{item.viewCount}</Text>
                  <Text style={styles.propertyStat}>想要{item.favoriteCount}</Text>
                  <Text style={[
                    styles.propertyStat, 
                    item.isPremiumOwner && item.tenantMatches > 0 && styles.tenantResourceStat
                  ]}>
                    租客资源{item.tenantMatches}
                  </Text>
                </View>
              )}
            </>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  propertyItem: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: spacing.md,
    marginVertical: spacing.sm,
    borderRadius: borderRadius.base,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  propertyContent: {
    flexDirection: 'row',
    padding: spacing.md,
  },
  propertyImage: {
    width: 80,
    height: 80,
    borderRadius: borderRadius.base,
    marginRight: spacing.md,
  },
  propertyInfo: {
    flex: 1,
  },
  propertyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  propertyTypeTag: {
    backgroundColor: '#4A90E2',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    borderRadius: borderRadius.base,
    marginRight: spacing.sm,
  },
  draftPropertyTypeTag: {
    backgroundColor: '#999999',
  },
  propertyTypeText: {
    fontSize: fontSize.xs,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  draftPropertyTypeText: {
    color: '#FFFFFF',
  },
  propertyTitle: {
    flex: 1,
    fontSize: fontSize.base,
    fontWeight: '600',
    color: '#333333',
  },
  
  // 草稿样式
  draftPromptText: {
    fontSize: fontSize.base,
    color: '#666666',
    marginBottom: spacing.sm,
  },
  draftBottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  draftTimestamp: {
    fontSize: fontSize.xs,
    color: '#999999',
  },
  draftActionsContainer: {
    flexDirection: 'row',
  },
  draftActionButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    marginLeft: spacing.sm,
    borderRadius: borderRadius.base,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  editButton: {
    backgroundColor: '#4A90E2',
    borderColor: '#4A90E2',
  },
  draftActionButtonText: {
    fontSize: fontSize.base,
    color: '#666666',
  },
  editButtonText: {
    color: '#FFFFFF',
  },
  
  // 已发布房源样式
  propertyPrice: {
    fontSize: fontSize.xl,
    fontWeight: '700',
    color: '#FF6B35',
    marginBottom: spacing.xs,
  },
  propertyDetails: {
    fontSize: fontSize.base,
    color: '#666666',
    marginBottom: spacing.sm,
  },
  propertyStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  propertyStat: {
    fontSize: fontSize.xs,
    color: '#999999',
    marginRight: spacing.md,
  },
  tenantResourceStat: {
    color: '#4A90E2',
    fontWeight: '500',
  },
});

export default EnhancedPropertyListItem;
