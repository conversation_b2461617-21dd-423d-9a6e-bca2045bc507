/**
 * 环境配置测试脚本
 * 用于验证前端环境变量配置是否正确
 */

// 模拟Expo环境变量加载
require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '.env.development' });

console.log('=== 环境配置测试 ===\n');

console.log('当前环境变量:');
console.log('EXPO_PUBLIC_FRONTEND_API_URL:', process.env.EXPO_PUBLIC_FRONTEND_API_URL);
console.log('EXPO_PUBLIC_FRONTEND_ENV:', process.env.EXPO_PUBLIC_FRONTEND_ENV);
console.log('NODE_ENV:', process.env.NODE_ENV);

console.log('\n=== 模拟前端API配置 ===');

// 模拟前端配置逻辑
const __DEV__ = process.env.NODE_ENV !== 'production';
console.log('__DEV__:', __DEV__);

const API_CONFIG = {
  BASE_URL: __DEV__
    ? process.env.EXPO_PUBLIC_FRONTEND_API_URL || 'http://8.134.250.136:8082/api/v1'
    : process.env.EXPO_PUBLIC_FRONTEND_API_URL || 'https://your-production-api.com/api/v1',
  API_VERSION: '',
};

console.log('最终API配置:');
console.log('BASE_URL:', API_CONFIG.BASE_URL);
console.log('API_VERSION:', API_CONFIG.API_VERSION);

console.log('\n=== 测试API端点 ===');
const testEndpoints = [
  '/messages/list',
  '/messages/user-roles',
  '/sms/account'
];

testEndpoints.forEach(endpoint => {
  const fullUrl = `${API_CONFIG.BASE_URL}${endpoint}`;
  console.log(`${endpoint.padEnd(20)} -> ${fullUrl}`);
});

console.log('\n=== 验证结果 ===');
if (API_CONFIG.BASE_URL.includes('/api/v1')) {
  console.log('✅ BASE_URL包含正确的API路径');
} else {
  console.log('❌ BASE_URL缺少/api/v1路径');
}

if (API_CONFIG.BASE_URL.includes(':8082')) {
  console.log('✅ BASE_URL包含正确的端口号');
} else {
  console.log('❌ BASE_URL缺少端口号');
}
