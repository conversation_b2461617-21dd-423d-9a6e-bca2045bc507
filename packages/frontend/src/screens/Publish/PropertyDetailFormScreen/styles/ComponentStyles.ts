import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';

export const componentStyles = StyleSheet.create({
  // 头部导航样式
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF'
  },
  backButton: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center'
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#000000'
  },
  headerRight: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center'
  },

  // 底部操作按钮
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: wp(20),
    paddingVertical: hp(16),
    paddingBottom: hp(32),
    backgroundColor: '#FFFFFF',
    borderTopWidth: 0.5,
    borderTopColor: '#E5E5EA'
  },
  bottomButtonContainer: {
    flexDirection: 'row',
    paddingHorizontal: wp(20),
    paddingVertical: hp(16),
    paddingBottom: hp(32),
    backgroundColor: '#FFFFFF',
    borderTopWidth: 0.5,
    borderTopColor: '#E5E5EA'
  },
  publishButton: {
    flex: 1,
    backgroundColor: '#FF6B35',
    borderRadius: wp(12),
    paddingVertical: hp(16),
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#FF6B35',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.2,
    shadowRadius: 4
  },
  draftButton: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    borderRadius: wp(12),
    paddingVertical: hp(16),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E5E5EA',
    marginRight: wp(12)
  },
  buttonDisabled: {
    opacity: 0.6
  },

  // 移除重复的按钮样式定义，保留原有的样式
  publishButtonText: {
    fontSize: fp(17),
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 0.5
  },
  draftButtonText: {
    fontSize: fp(17),
    fontWeight: '600',
    color: '#007AFF',
    letterSpacing: 0.5
  },
  
  // 草稿状态
  draftStatusContainer: {
    paddingHorizontal: wp(20),
    paddingBottom: hp(8),
    backgroundColor: '#FFFFFF'
  },
  draftStatusText: {
    fontSize: fp(12),
    color: '#8E8E93',
    textAlign: 'center'
  },
  
  // 内容容器
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF'
  },
  contentContainer: {
    flex: 1
  },
  scrollContent: {
    paddingVertical: hp(8) // 减少上下间距
  },
  
  // 分割线
  separator: {
    height: 0.5,
    backgroundColor: '#C6C6C8',
    marginLeft: wp(16)
  },
  sectionSeparator: {
    height: hp(8),
    backgroundColor: '#F2F2F7'
  },
  
  // 加载状态
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF'
  },
  loadingText: {
    fontSize: fp(16),
    color: '#8E8E93',
    marginTop: hp(16)
  },
  
  // 空状态
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(32)
  },
  emptyIcon: {
    width: wp(80),
    height: wp(80),
    marginBottom: hp(16)
  },
  emptyTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: hp(8)
  },
  emptyDescription: {
    fontSize: fp(14),
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: fp(20)
  },
  
  // 模态框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: wp(16),
    paddingVertical: hp(24),
    paddingHorizontal: wp(20),
    marginHorizontal: wp(32),
    maxHeight: '80%'
  },
  modalTitle: {
    fontSize: fp(18),
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: hp(16)
  },
  modalContent: {
    maxHeight: hp(400)
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(20)
  },
  modalButton: {
    flex: 1,
    paddingVertical: hp(12),
    borderRadius: wp(8),
    alignItems: 'center',
    justifyContent: 'center'
  },
  modalButtonPrimary: {
    backgroundColor: '#007AFF',
    marginLeft: wp(8)
  },
  modalButtonSecondary: {
    backgroundColor: '#F2F2F7',
    marginRight: wp(8)
  },
  modalButtonText: {
    fontSize: fp(16),
    fontWeight: '600'
  },
  modalButtonTextPrimary: {
    color: '#FFFFFF'
  },
  modalButtonTextSecondary: {
    color: '#007AFF'
  },
  
  // 提示气泡
  tooltip: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingVertical: hp(8),
    paddingHorizontal: wp(12),
    borderRadius: wp(6),
    zIndex: 1000
  },
  tooltipText: {
    fontSize: fp(12),
    color: '#FFFFFF',
    textAlign: 'center'
  },
  tooltipArrow: {
    position: 'absolute',
    width: 0,
    height: 0,
    borderLeftWidth: wp(6),
    borderRightWidth: wp(6),
    borderTopWidth: hp(6),
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: 'rgba(0, 0, 0, 0.8)'
  }
});
