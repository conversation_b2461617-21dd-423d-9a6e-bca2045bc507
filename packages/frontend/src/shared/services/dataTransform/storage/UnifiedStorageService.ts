/**
 * 统一存储服务 - 企业级存储抽象层核心
 * 
 * 参考主流APP存储架构：
 * - 微信：统一存储接口，智能路由到不同存储桶
 * - 抖音：多云存储支持，无缝切换存储提供商
 * - 淘宝：企业级权限控制，细粒度访问管理
 * - 阿里云：双桶架构，敏感数据隔离存储
 * 
 * 核心功能：
 * - 统一存储接口，屏蔽底层差异
 * - 智能路由，根据业务类型选择存储策略
 * - 权限控制，企业级安全管理
 * - 多云支持，避免供应商锁定
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月16日
 */

import {
  IStorageProvider,
  UploadOptions,
  UploadResult,
  DownloadOptions,
  DownloadResult,
  BatchUploadOptions,
  BatchUploadResult,
  BatchDeleteResult,
  STSCredentials,
  ObjectMetadata,
  StorageStats,
  Permission,
  StorageEvent,
  StorageEventType,
  IStorageEventListener,
  IStorageMonitor
} from '../types/StorageTypes';
import { MediaBusinessType } from '../transformers/MediaTransformer';
import { StorageRouter } from './StorageRouter';
import { PermissionController } from './PermissionController';
import { AliyunOSSProvider } from './providers/AliyunOSSProvider';

/**
 * 存储监控器实现
 */
class StorageMonitor implements IStorageMonitor {
  private listeners: IStorageEventListener[] = [];
  private metrics = {
    uploadCount: 0,
    downloadCount: 0,
    errorCount: 0,
    responseTimes: [] as number[]
  };
  
  addEventListener(listener: IStorageEventListener): void {
    this.listeners.push(listener);
  }
  
  removeEventListener(listener: IStorageEventListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }
  
  async emitEvent(event: StorageEvent): Promise<void> {
    // 更新指标
    this.updateMetrics(event);
    
    // 通知所有监听器
    const promises = this.listeners.map(listener => 
      listener.onEvent(event).catch(error => 
        console.error('Storage event listener error:', error)
      )
    );
    
    await Promise.all(promises);
  }
  
  async getMetrics(): Promise<{
    uploadCount: number;
    downloadCount: number;
    errorCount: number;
    averageResponseTime: number;
  }> {
    const averageResponseTime = this.metrics.responseTimes.length > 0
      ? this.metrics.responseTimes.reduce((a, b) => a + b, 0) / this.metrics.responseTimes.length
      : 0;
    
    return {
      uploadCount: this.metrics.uploadCount,
      downloadCount: this.metrics.downloadCount,
      errorCount: this.metrics.errorCount,
      averageResponseTime
    };
  }
  
  private updateMetrics(event: StorageEvent): void {
    switch (event.type) {
      case 'upload':
        this.metrics.uploadCount++;
        break;
      case 'download':
        this.metrics.downloadCount++;
        break;
      case 'error':
        this.metrics.errorCount++;
        break;
    }
    
    // 记录响应时间（如果有的话）
    if (event.metadata?.responseTime) {
      this.metrics.responseTimes.push(event.metadata.responseTime);
      // 只保留最近1000次的响应时间
      if (this.metrics.responseTimes.length > 1000) {
        this.metrics.responseTimes.shift();
      }
    }
  }
}

/**
 * 统一存储服务实现
 */
export class UnifiedStorageService implements IStorageProvider {
  private router: StorageRouter;
  private permissionController: PermissionController;
  private monitor: StorageMonitor;
  private currentUserId?: string;
  
  constructor() {
    this.router = new StorageRouter();
    this.permissionController = new PermissionController();
    this.monitor = new StorageMonitor();
    
    this.initializeProviders();
  }
  
  /**
   * 初始化存储提供商
   */
  private initializeProviders(): void {
    // 初始化阿里云OSS提供商
    const mainOSSProvider = new AliyunOSSProvider({
      accessKeyId: process.env.EXPO_PUBLIC_ALIBABA_CLOUD_ACCESS_KEY_ID || '',
      accessKeySecret: process.env.EXPO_PUBLIC_ALIBABA_CLOUD_ACCESS_KEY_SECRET || '',
      region: 'cn-guangzhou',
      bucket: 'huixuanzhi-main',
      endpoint: 'oss-cn-guangzhou.aliyuncs.com'
    }, 'https://cdn.huixuanzhi.com');
    
    const sensitiveOSSProvider = new AliyunOSSProvider({
      accessKeyId: process.env.EXPO_PUBLIC_ALIBABA_CLOUD_ACCESS_KEY_ID || '',
      accessKeySecret: process.env.EXPO_PUBLIC_ALIBABA_CLOUD_ACCESS_KEY_SECRET || '',
      region: 'cn-guangzhou',
      bucket: 'huixuanzhi-sensitive',
      endpoint: 'oss-cn-guangzhou.aliyuncs.com'
    });
    
    // 注册存储提供商
    this.router.registerProvider('main', mainOSSProvider);
    this.router.registerProvider('sensitive', sensitiveOSSProvider);
    
    console.log('Storage providers initialized');
  }
  
  /**
   * 设置当前用户ID（用于权限控制）
   */
  setCurrentUser(userId: string): void {
    this.currentUserId = userId;
  }
  
  /**
   * 上传文件
   */
  async upload(file: File, path: string, options?: UploadOptions): Promise<UploadResult> {
    const startTime = Date.now();
    
    try {
      // 1. 推断业务类型
      const businessType = this.inferBusinessTypeFromPath(path);
      
      // 2. 权限检查
      if (this.currentUserId) {
        await this.permissionController.checkUploadPermission(
          this.currentUserId, 
          path, 
          businessType
        );
      }
      
      // 3. 路由到对应的存储提供商
      const provider = this.router.route(businessType);
      
      // 4. 执行上传
      const result = await provider.upload(file, path, options);
      
      // 5. 触发事件
      await this.monitor.emitEvent({
        type: 'upload',
        objectKey: path,
        userId: this.currentUserId,
        timestamp: new Date(),
        metadata: {
          businessType,
          fileSize: file.size,
          responseTime: Date.now() - startTime
        }
      });
      
      return result;
      
    } catch (error) {
      // 触发错误事件
      await this.monitor.emitEvent({
        type: 'error',
        objectKey: path,
        userId: this.currentUserId,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Upload failed'
      });
      
      throw error;
    }
  }
  
  /**
   * 下载文件
   */
  async download(objectKey: string, options?: DownloadOptions): Promise<DownloadResult> {
    const startTime = Date.now();
    
    try {
      // 1. 权限检查
      if (this.currentUserId) {
        await this.permissionController.checkViewPermission(objectKey, this.currentUserId);
      }
      
      // 2. 路由到对应的存储提供商
      const provider = this.router.routeByPath(objectKey);
      
      // 3. 执行下载
      const result = await provider.download(objectKey, options);
      
      // 4. 触发事件
      await this.monitor.emitEvent({
        type: 'download',
        objectKey,
        userId: this.currentUserId,
        timestamp: new Date(),
        metadata: {
          responseTime: Date.now() - startTime
        }
      });
      
      return result;
      
    } catch (error) {
      // 触发错误事件
      await this.monitor.emitEvent({
        type: 'error',
        objectKey,
        userId: this.currentUserId,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Download failed'
      });
      
      throw error;
    }
  }
  
  /**
   * 删除文件
   */
  async delete(objectKey: string): Promise<boolean> {
    try {
      // 1. 权限检查
      if (this.currentUserId) {
        await this.permissionController.checkDeletePermission(objectKey, this.currentUserId);
      }
      
      // 2. 路由到对应的存储提供商
      const provider = this.router.routeByPath(objectKey);
      
      // 3. 执行删除
      const result = await provider.delete(objectKey);
      
      // 4. 触发事件
      await this.monitor.emitEvent({
        type: 'delete',
        objectKey,
        userId: this.currentUserId,
        timestamp: new Date()
      });
      
      return result;
      
    } catch (error) {
      // 触发错误事件
      await this.monitor.emitEvent({
        type: 'error',
        objectKey,
        userId: this.currentUserId,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Delete failed'
      });
      
      return false;
    }
  }
  
  /**
   * 检查文件是否存在
   */
  async exists(objectKey: string): Promise<boolean> {
    try {
      const provider = this.router.routeByPath(objectKey);
      return await provider.exists(objectKey);
    } catch (error) {
      console.error('Exists check failed:', error);
      return false;
    }
  }
  
  /**
   * 批量上传
   */
  async batchUpload(files: File[], paths: string[], options?: BatchUploadOptions): Promise<BatchUploadResult> {
    // 按业务类型分组
    const groupedUploads = this.groupUploadsByBusinessType(files, paths);
    
    const allResults: UploadResult[] = [];
    const allFailed: { file: File; error: string }[] = [];
    
    // 并发处理不同业务类型的上传
    const groupPromises = Object.entries(groupedUploads).map(async ([businessType, uploads]) => {
      const provider = this.router.route(businessType as MediaBusinessType);
      const result = await provider.batchUpload(
        uploads.map(u => u.file),
        uploads.map(u => u.path),
        options
      );
      
      allResults.push(...result.results);
      allFailed.push(...result.failed);
    });
    
    await Promise.all(groupPromises);
    
    return {
      success: allFailed.length === 0,
      results: allResults,
      failed: allFailed,
      totalCount: files.length,
      successCount: allResults.length,
      failedCount: allFailed.length
    };
  }
  
  /**
   * 批量删除
   */
  async batchDelete(objectKeys: string[]): Promise<BatchDeleteResult> {
    // 按存储提供商分组
    const groupedDeletes = this.groupDeletesByProvider(objectKeys);
    
    const allDeleted: string[] = [];
    const allFailed: { objectKey: string; error: string }[] = [];
    
    // 并发处理不同提供商的删除
    const groupPromises = Object.entries(groupedDeletes).map(async ([providerType, keys]) => {
      const provider = this.router.getRegisteredProviders().get(providerType as any);
      if (provider) {
        const result = await provider.batchDelete(keys);
        allDeleted.push(...result.deleted);
        allFailed.push(...result.failed);
      }
    });
    
    await Promise.all(groupPromises);
    
    return {
      success: allFailed.length === 0,
      deleted: allDeleted,
      failed: allFailed,
      totalCount: objectKeys.length,
      deletedCount: allDeleted.length,
      failedCount: allFailed.length
    };
  }
  
  /**
   * 获取查看URL
   */
  async getViewUrl(objectKey: string, expires?: number): Promise<string> {
    try {
      // 权限检查
      if (this.currentUserId) {
        await this.permissionController.checkViewPermission(objectKey, this.currentUserId);
      }
      
      const provider = this.router.routeByPath(objectKey);
      return await provider.getViewUrl(objectKey, expires);
    } catch (error) {
      throw new Error(`Failed to get view URL: ${error}`);
    }
  }
  
  /**
   * 获取下载URL
   */
  async getDownloadUrl(objectKey: string, expires?: number): Promise<string> {
    try {
      const provider = this.router.routeByPath(objectKey);
      return await provider.getDownloadUrl(objectKey, expires);
    } catch (error) {
      throw new Error(`Failed to get download URL: ${error}`);
    }
  }
  
  /**
   * 生成STS凭证
   */
  async generateSTS(permissions: Permission[], expires?: number): Promise<STSCredentials> {
    // 默认使用主存储桶的提供商
    const provider = this.router.getRegisteredProviders().get('main');
    if (!provider) {
      throw new Error('Main storage provider not found');
    }
    
    return await provider.generateSTS(permissions, expires);
  }
  
  /**
   * 生成预签名URL
   */
  async generatePresignedUrl(objectKey: string, operation: 'GET' | 'PUT', expires?: number): Promise<string> {
    const provider = this.router.routeByPath(objectKey);
    return await provider.generatePresignedUrl(objectKey, operation, expires);
  }
  
  /**
   * 获取对象元数据
   */
  async getMetadata(objectKey: string): Promise<ObjectMetadata> {
    const provider = this.router.routeByPath(objectKey);
    return await provider.getMetadata(objectKey);
  }
  
  /**
   * 更新对象元数据
   */
  async updateMetadata(objectKey: string, metadata: Record<string, string>): Promise<boolean> {
    const provider = this.router.routeByPath(objectKey);
    return await provider.updateMetadata(objectKey, metadata);
  }
  
  /**
   * 列出对象
   */
  async listObjects(prefix?: string, maxKeys?: number): Promise<ObjectMetadata[]> {
    // 如果没有指定前缀，需要从所有提供商获取
    if (!prefix) {
      const allObjects: ObjectMetadata[] = [];
      const providers = this.router.getRegisteredProviders();
      
      for (const provider of providers.values()) {
        try {
          const objects = await provider.listObjects(prefix, maxKeys);
          allObjects.push(...objects);
        } catch (error) {
          console.error('Failed to list objects from provider:', error);
        }
      }
      
      return allObjects;
    }
    
    // 根据前缀路由到对应的提供商
    const provider = this.router.routeByPath(prefix);
    return await provider.listObjects(prefix, maxKeys);
  }
  
  /**
   * 获取存储统计
   */
  async getStats(): Promise<StorageStats> {
    const providers = this.router.getRegisteredProviders();
    let totalObjects = 0;
    let totalSize = 0;
    const bucketStats: Record<string, any> = {};
    
    for (const [bucketType, provider] of providers) {
      try {
        const stats = await provider.getStats();
        totalObjects += stats.totalObjects;
        totalSize += stats.totalSize;
        bucketStats[bucketType] = stats.bucketStats;
      } catch (error) {
        console.error(`Failed to get stats from ${bucketType} provider:`, error);
      }
    }
    
    return {
      totalObjects,
      totalSize,
      bucketStats
    };
  }
  
  /**
   * 获取监控器
   */
  getMonitor(): IStorageMonitor {
    return this.monitor;
  }
  
  // 辅助方法
  
  private inferBusinessTypeFromPath(path: string): MediaBusinessType {
    if (path.includes('/identity/')) return MediaBusinessType.IDENTITY_CARD;
    if (path.includes('/business/')) return MediaBusinessType.BUSINESS_LICENSE;
    if (path.includes('/property-cert/')) return MediaBusinessType.PROPERTY_CERT;
    if (path.includes('/financial/')) return MediaBusinessType.FINANCIAL_DOC;
    if (path.includes('/avatar/')) return MediaBusinessType.USER_AVATAR;
    if (path.includes('/video/')) return MediaBusinessType.PROPERTY_VIDEO;
    
    return MediaBusinessType.PROPERTY_IMAGE;
  }
  
  private groupUploadsByBusinessType(files: File[], paths: string[]): Record<string, { file: File; path: string }[]> {
    const groups: Record<string, { file: File; path: string }[]> = {};
    
    files.forEach((file, index) => {
      const path = paths[index];
      const businessType = this.inferBusinessTypeFromPath(path);
      
      if (!groups[businessType]) {
        groups[businessType] = [];
      }
      
      groups[businessType].push({ file, path });
    });
    
    return groups;
  }
  
  private groupDeletesByProvider(objectKeys: string[]): Record<string, string[]> {
    const groups: Record<string, string[]> = {};
    
    objectKeys.forEach(objectKey => {
      const bucketType = this.router.routeByPath(objectKey);
      const providerType = this.getProviderTypeByBucketType(objectKey);
      
      if (!groups[providerType]) {
        groups[providerType] = [];
      }
      
      groups[providerType].push(objectKey);
    });
    
    return groups;
  }
  
  private getProviderTypeByBucketType(objectKey: string): string {
    if (objectKey.includes('/sensitive/') || objectKey.includes('/identity/') || objectKey.includes('/business/')) {
      return 'sensitive';
    }
    return 'main';
  }
}
