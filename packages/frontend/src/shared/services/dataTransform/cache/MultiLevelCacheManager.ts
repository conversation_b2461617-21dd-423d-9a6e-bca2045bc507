/**
 * 多层缓存管理器
 * 
 * 核心功能：
 * 1. 统一管理L1(内存) + L2(Redis) + L3(CDN)三层缓存
 * 2. 智能缓存策略路由
 * 3. 自动缓存一致性管理
 * 4. 性能监控和统计
 * 
 * 缓存层级：
 * - L1 内存缓存：最快访问，容量小，应用重启丢失
 * - L2 Redis缓存：快速访问，容量中等，持久化存储
 * - L3 CDN缓存：边缘缓存，容量大，全球分发
 * 
 * 设计模式：
 * - 策略模式：不同数据类型使用不同缓存策略
 * - 观察者模式：缓存事件监听和通知
 * - 装饰器模式：缓存统计和监控
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月16日
 */

import {
  ICacheProvider,
  IMultiLevelCacheManager,
  ICacheEventListener,
  CacheLevel,
  CacheDataType,
  CacheResult,
  CacheStats,
  CacheEvent,
  CacheEventType,
  InvalidationPattern,
  CacheDataTypeConfigs
} from '../types/CacheTypes';

/**
 * 多层缓存管理器实现
 */
export class MultiLevelCacheManager implements IMultiLevelCacheManager {
  private providers: Map<CacheLevel, ICacheProvider> = new Map();
  private eventListeners: ICacheEventListener[] = [];
  private stats: Map<CacheLevel, CacheStats> = new Map();
  
  constructor() {
    this.initializeStats();
  }

  /**
   * 注册缓存提供商
   */
  registerProvider(level: CacheLevel, provider: ICacheProvider): void {
    this.providers.set(level, provider);
    console.log(`Cache provider registered for level: ${level}`);
  }

  /**
   * 添加事件监听器
   */
  addEventListener(listener: ICacheEventListener): void {
    this.eventListeners.push(listener);
  }

  /**
   * 获取数据（多层查找）
   */
  async get<T>(key: string, dataType: CacheDataType): Promise<CacheResult<T>> {
    const startTime = Date.now();
    const configs = CacheDataTypeConfigs[dataType];
    
    // 按优先级顺序查找（L1 -> L2 -> L3）
    for (const config of configs) {
      const provider = this.providers.get(config.level);
      if (!provider) continue;

      try {
        const result = await provider.get<T>(key);
        const responseTime = Date.now() - startTime;

        if (result.success && result.data !== undefined) {
          // 缓存命中
          this.emitEvent({
            type: CacheEventType.HIT,
            key,
            level: config.level,
            timestamp: Date.now(),
            responseTime
          });

          // 回填到更高级别的缓存
          await this.backfillCache(key, result.data, dataType, config.level);

          return {
            ...result,
            level: config.level,
            responseTime
          };
        }
      } catch (error) {
        this.emitEvent({
          type: CacheEventType.ERROR,
          key,
          level: config.level,
          timestamp: Date.now(),
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    // 所有缓存层都未命中
    const responseTime = Date.now() - startTime;
    this.emitEvent({
      type: CacheEventType.MISS,
      key,
      level: CacheLevel.L1_MEMORY, // 默认记录到L1
      timestamp: Date.now(),
      responseTime
    });

    return {
      success: false,
      fromCache: false,
      responseTime
    };
  }

  /**
   * 设置数据（多层写入）
   */
  async set<T>(key: string, value: T, dataType: CacheDataType, ttl?: number): Promise<boolean> {
    const configs = CacheDataTypeConfigs[dataType];
    const results: boolean[] = [];

    // 并行写入所有配置的缓存层
    const writePromises = configs.map(async (config) => {
      const provider = this.providers.get(config.level);
      if (!provider) return false;

      try {
        const effectiveTTL = ttl || config.ttl;
        const success = await provider.set(key, value, effectiveTTL);
        
        if (success) {
          this.emitEvent({
            type: CacheEventType.SET,
            key,
            level: config.level,
            timestamp: Date.now()
          });
        }

        return success;
      } catch (error) {
        this.emitEvent({
          type: CacheEventType.ERROR,
          key,
          level: config.level,
          timestamp: Date.now(),
          error: error instanceof Error ? error.message : String(error)
        });
        return false;
      }
    });

    const writeResults = await Promise.all(writePromises);
    return writeResults.some(result => result); // 至少一个成功就算成功
  }

  /**
   * 删除数据（多层删除）
   */
  async delete(key: string, dataType: CacheDataType): Promise<boolean> {
    const configs = CacheDataTypeConfigs[dataType];
    const results: boolean[] = [];

    // 并行删除所有配置的缓存层
    const deletePromises = configs.map(async (config) => {
      const provider = this.providers.get(config.level);
      if (!provider) return false;

      try {
        const success = await provider.delete(key);
        
        if (success) {
          this.emitEvent({
            type: CacheEventType.DELETE,
            key,
            level: config.level,
            timestamp: Date.now()
          });
        }

        return success;
      } catch (error) {
        this.emitEvent({
          type: CacheEventType.ERROR,
          key,
          level: config.level,
          timestamp: Date.now(),
          error: error instanceof Error ? error.message : String(error)
        });
        return false;
      }
    });

    const deleteResults = await Promise.all(deletePromises);
    return deleteResults.some(result => result);
  }

  /**
   * 失效缓存（支持模式匹配）
   */
  async invalidate(pattern: InvalidationPattern): Promise<number> {
    let totalInvalidated = 0;

    const invalidatePromises = pattern.levels.map(async (level) => {
      const provider = this.providers.get(level);
      if (!provider) return 0;

      try {
        const count = await provider.invalidate(pattern.pattern);
        
        this.emitEvent({
          type: CacheEventType.INVALIDATE,
          key: pattern.pattern,
          level,
          timestamp: Date.now(),
          metadata: { count }
        });

        return count;
      } catch (error) {
        this.emitEvent({
          type: CacheEventType.ERROR,
          key: pattern.pattern,
          level,
          timestamp: Date.now(),
          error: error instanceof Error ? error.message : String(error)
        });
        return 0;
      }
    });

    const results = await Promise.all(invalidatePromises);
    totalInvalidated = results.reduce((sum, count) => sum + count, 0);

    return totalInvalidated;
  }

  /**
   * 预热缓存
   */
  async warmup<T>(key: string, loader: () => Promise<T>, dataType: CacheDataType): Promise<boolean> {
    try {
      // 检查是否已存在
      const existing = await this.get<T>(key, dataType);
      if (existing.success) {
        return true; // 已存在，无需预热
      }

      // 加载数据
      const data = await loader();
      
      // 写入缓存
      return await this.set(key, data, dataType);
    } catch (error) {
      console.error(`Cache warmup failed for key: ${key}`, error);
      return false;
    }
  }

  /**
   * 获取综合统计
   */
  async getStats(): Promise<Map<CacheLevel, CacheStats>> {
    const statsMap = new Map<CacheLevel, CacheStats>();

    const statsPromises = Array.from(this.providers.entries()).map(async ([level, provider]) => {
      try {
        const stats = await provider.stats();
        statsMap.set(level, stats);
      } catch (error) {
        console.error(`Failed to get stats for level: ${level}`, error);
      }
    });

    await Promise.all(statsPromises);
    return statsMap;
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<Map<CacheLevel, boolean>> {
    const healthMap = new Map<CacheLevel, boolean>();

    const healthPromises = Array.from(this.providers.entries()).map(async ([level, provider]) => {
      try {
        const isHealthy = await provider.health();
        healthMap.set(level, isHealthy);
      } catch (error) {
        console.error(`Health check failed for level: ${level}`, error);
        healthMap.set(level, false);
      }
    });

    await Promise.all(healthPromises);
    return healthMap;
  }

  /**
   * 回填缓存到更高级别
   */
  private async backfillCache<T>(
    key: string, 
    value: T, 
    dataType: CacheDataType, 
    currentLevel: CacheLevel
  ): Promise<void> {
    const configs = CacheDataTypeConfigs[dataType];
    const currentIndex = configs.findIndex(config => config.level === currentLevel);
    
    // 回填到更高级别的缓存（索引更小的）
    const backfillPromises = configs.slice(0, currentIndex).map(async (config) => {
      const provider = this.providers.get(config.level);
      if (!provider) return;

      try {
        await provider.set(key, value, config.ttl);
      } catch (error) {
        console.error(`Backfill failed for level: ${config.level}`, error);
      }
    });

    await Promise.all(backfillPromises);
  }

  /**
   * 发送缓存事件
   */
  private emitEvent(event: CacheEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener.onEvent(event);
      } catch (error) {
        console.error('Cache event listener error:', error);
      }
    });
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    Object.values(CacheLevel).forEach(level => {
      this.stats.set(level, {
        hitCount: 0,
        missCount: 0,
        hitRate: 0,
        totalRequests: 0,
        averageResponseTime: 0,
        cacheSize: 0,
        maxSize: 0,
        evictionCount: 0
      });
    });
  }
}

/**
 * 缓存事件监听器实现 - 用于监控和日志
 */
export class CacheEventLogger implements ICacheEventListener {
  onEvent(event: CacheEvent): void {
    const { type, key, level, timestamp, responseTime, error } = event;

    switch (type) {
      case CacheEventType.HIT:
        console.log(`[CACHE HIT] ${level} - ${key} (${responseTime}ms)`);
        break;
      case CacheEventType.MISS:
        console.log(`[CACHE MISS] ${level} - ${key} (${responseTime}ms)`);
        break;
      case CacheEventType.SET:
        console.log(`[CACHE SET] ${level} - ${key}`);
        break;
      case CacheEventType.DELETE:
        console.log(`[CACHE DELETE] ${level} - ${key}`);
        break;
      case CacheEventType.INVALIDATE:
        console.log(`[CACHE INVALIDATE] ${level} - ${key}`);
        break;
      case CacheEventType.ERROR:
        console.error(`[CACHE ERROR] ${level} - ${key}: ${error}`);
        break;
    }
  }
}
