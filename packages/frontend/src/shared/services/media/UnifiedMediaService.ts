/**
 * 统一媒体服务 - 企业级抽象层
 * 
 * 功能特性：
 * - 统一的媒体上传/下载/删除接口
 * - 智能压缩和水印处理
 * - 支持多种业务场景（房源、身份证、营业执照等）
 * - 符合统一转换层架构
 * - CDN/OSS双模式支持
 * 
 * 设计原则：
 * - 单一职责：每个方法只负责一个功能
 * - 开闭原则：易于扩展新的媒体类型
 * - 依赖倒置：依赖抽象而非具体实现
 * - 接口隔离：不同业务场景使用不同接口
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月15日
 */

import { enterpriseMediaService } from './EnterpriseMediaService';
import { STSCredentials, UploadProgress, UploadResult } from './types';
import { Transformers } from '../dataTransform';
import { MediaUploadRequest } from '../dataTransform/transformers/MediaTransformer';
import { MediaSize } from '../dataTransform/types/MediaTypes';

// 业务场景枚举
export enum MediaBusinessType {
  PROPERTY_IMAGE = 'property_image',      // 房源图片
  PROPERTY_VIDEO = 'property_video',      // 房源视频
  IDENTITY_CARD = 'identity_card',        // 身份证
  BUSINESS_LICENSE = 'business_license',  // 营业执照
  PROPERTY_CERT = 'property_cert',        // 房产证
  USER_AVATAR = 'user_avatar',           // 用户头像
  FINANCIAL_DOC = 'financial_doc',       // 财务文档
  OTHER_DOC = 'other_doc'                // 其他文档
}

// 压缩配置
export interface CompressionConfig {
  enabled: boolean;
  quality: number;        // 压缩质量 0-100
  maxWidth: number;       // 最大宽度
  maxHeight: number;      // 最大高度
  format?: 'jpeg' | 'webp' | 'png';  // 输出格式
}

// 水印配置
export interface WatermarkConfig {
  enabled: boolean;
  text: string;           // 水印文字
  opacity: number;        // 透明度 0-1
  position: 'center' | 'bottom-right' | 'top-left' | 'top-right' | 'bottom-left';
  color: string;          // 颜色
  fontSize: number;       // 字体大小
}

// 媒体处理配置
export interface MediaProcessingConfig {
  compression: CompressionConfig;
  watermark: WatermarkConfig;
  generateThumbnail: boolean;
  enableContentSecurity: boolean;  // 内容安全检查
}

// 统一上传选项
export interface UnifiedUploadOptions {
  businessType: MediaBusinessType;
  propertyId?: string;
  userId?: string;
  onProgress?: (progress: UploadProgress) => void;
  processing?: Partial<MediaProcessingConfig>;
}

// 统一上传结果
export interface UnifiedUploadResult extends UploadResult {
  mediaId?: string;
  thumbnailUrl?: string;
  processedUrl?: string;
  originalUrl?: string;
  watermarkedUrl?: string;
  businessType: MediaBusinessType;
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
}

export class UnifiedMediaService {
  private static instance: UnifiedMediaService;
  
  // 默认处理配置
  private readonly defaultConfigs: Record<MediaBusinessType, MediaProcessingConfig> = {
    [MediaBusinessType.PROPERTY_IMAGE]: {
      compression: { enabled: true, quality: 85, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
      watermark: { enabled: true, text: '慧选址', opacity: 0.4, position: 'center', color: '#FFFFFF', fontSize: 48 },
      generateThumbnail: true,
      enableContentSecurity: true
    },
    [MediaBusinessType.PROPERTY_VIDEO]: {
      compression: { enabled: true, quality: 80, maxWidth: 1280, maxHeight: 720 },
      watermark: { enabled: true, text: '慧选址', opacity: 0.4, position: 'center', color: '#FFFFFF', fontSize: 36 },
      generateThumbnail: true,
      enableContentSecurity: true
    },
    [MediaBusinessType.IDENTITY_CARD]: {
      compression: { enabled: true, quality: 95, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
      watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
      generateThumbnail: false,
      enableContentSecurity: true
    },
    [MediaBusinessType.BUSINESS_LICENSE]: {
      compression: { enabled: true, quality: 95, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
      watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
      generateThumbnail: false,
      enableContentSecurity: true
    },
    [MediaBusinessType.PROPERTY_CERT]: {
      compression: { enabled: true, quality: 95, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
      watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
      generateThumbnail: false,
      enableContentSecurity: true
    },
    [MediaBusinessType.USER_AVATAR]: {
      compression: { enabled: true, quality: 90, maxWidth: 512, maxHeight: 512, format: 'jpeg' },
      watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
      generateThumbnail: true,
      enableContentSecurity: false
    },
    [MediaBusinessType.FINANCIAL_DOC]: {
      compression: { enabled: true, quality: 95, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
      watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
      generateThumbnail: false,
      enableContentSecurity: true
    },
    [MediaBusinessType.OTHER_DOC]: {
      compression: { enabled: true, quality: 85, maxWidth: 1920, maxHeight: 1080, format: 'jpeg' },
      watermark: { enabled: false, text: '', opacity: 0, position: 'center', color: '', fontSize: 0 },
      generateThumbnail: false,
      enableContentSecurity: false
    }
  };
  
  /**
   * 单例模式
   */
  public static getInstance(): UnifiedMediaService {
    if (!UnifiedMediaService.instance) {
      UnifiedMediaService.instance = new UnifiedMediaService();
    }
    return UnifiedMediaService.instance;
  }
  
  /**
   * 统一文件上传 - 支持所有业务场景
   */
  async uploadFile(
    file: { uri: string; name: string; type: string; size: number },
    options: UnifiedUploadOptions
  ): Promise<UnifiedUploadResult> {
    try {
      console.log(`[UnifiedMediaService] 开始上传文件: ${file.name}, 业务类型: ${options.businessType}`);
      
      // 1. 获取处理配置
      const processingConfig = this.getProcessingConfig(options.businessType, options.processing);
      
      // 2. 文件预处理（压缩、格式转换）
      const processedFile = await this.preprocessFile(file, processingConfig);
      
      // 3. 使用企业级媒体服务上传
      const uploadResult = await enterpriseMediaService.uploadFile(processedFile, {
        propertyId: options.propertyId,
        onProgress: options.onProgress
      });
      
      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '上传失败');
      }
      
      // 4. 后处理（水印、缩略图生成）
      const postProcessResult = await this.postProcessFile(
        uploadResult.objectKey!,
        processingConfig,
        options.businessType
      );
      
      // 5. 构建统一结果
      const result: UnifiedUploadResult = {
        ...uploadResult,
        businessType: options.businessType,
        processingStatus: 'completed',
        thumbnailUrl: postProcessResult.thumbnailUrl,
        processedUrl: postProcessResult.processedUrl,
        originalUrl: uploadResult.url,
        watermarkedUrl: postProcessResult.watermarkedUrl
      };
      
      console.log(`[UnifiedMediaService] 文件上传完成: ${file.name}`);
      return result;
      
    } catch (error) {
      console.error(`[UnifiedMediaService] 文件上传失败: ${file.name}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败',
        businessType: options.businessType,
        processingStatus: 'failed'
      };
    }
  }
  
  /**
   * 批量文件上传
   */
  async uploadFiles(
    files: Array<{ uri: string; name: string; type: string; size: number }>,
    options: UnifiedUploadOptions
  ): Promise<UnifiedUploadResult[]> {
    console.log(`[UnifiedMediaService] 开始批量上传 ${files.length} 个文件`);
    
    const results: UnifiedUploadResult[] = [];
    
    // 并发上传，但限制并发数量
    const concurrency = 3;
    for (let i = 0; i < files.length; i += concurrency) {
      const batch = files.slice(i, i + concurrency);
      const batchPromises = batch.map(file => this.uploadFile(file, options));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }
    
    console.log(`[UnifiedMediaService] 批量上传完成，成功: ${results.filter(r => r.success).length}/${files.length}`);
    return results;
  }
  
  /**
   * 获取查看URL - 支持不同尺寸和处理效果
   */
  async getViewUrl(
    objectKey: string,
    options: {
      size?: 'thumbnail' | 'medium' | 'large' | 'original';
      withWatermark?: boolean;
      businessType?: MediaBusinessType;
    } = {}
  ): Promise<string> {
    const { size = 'medium', withWatermark = false, businessType } = options;
    
    // 如果需要水印且有业务类型，构建水印URL
    if (withWatermark && businessType) {
      const config = this.defaultConfigs[businessType];
      if (config.watermark.enabled) {
        return await this.buildWatermarkedUrl(objectKey, size, config.watermark);
      }
    }
    
    // 否则使用标准URL
    return await enterpriseMediaService.getViewUrl(objectKey, size);
  }
  
  /**
   * 删除文件
   */
  async deleteFile(objectKey: string): Promise<{ success: boolean; error?: string }> {
    return await enterpriseMediaService.deleteFile(objectKey);
  }
  
  /**
   * 获取处理配置
   */
  private getProcessingConfig(
    businessType: MediaBusinessType,
    customConfig?: Partial<MediaProcessingConfig>
  ): MediaProcessingConfig {
    const defaultConfig = this.defaultConfigs[businessType];
    
    if (!customConfig) {
      return defaultConfig;
    }
    
    return {
      compression: { ...defaultConfig.compression, ...customConfig.compression },
      watermark: { ...defaultConfig.watermark, ...customConfig.watermark },
      generateThumbnail: customConfig.generateThumbnail ?? defaultConfig.generateThumbnail,
      enableContentSecurity: customConfig.enableContentSecurity ?? defaultConfig.enableContentSecurity
    };
  }
  
  /**
   * 文件预处理（压缩、格式转换）
   */
  private async preprocessFile(
    file: { uri: string; name: string; type: string; size: number },
    config: MediaProcessingConfig
  ): Promise<{ uri: string; name: string; type: string; size: number }> {
    // 如果不需要压缩，直接返回原文件
    if (!config.compression.enabled) {
      return file;
    }
    
    // TODO: 实现客户端压缩逻辑
    // 这里可以集成图片/视频压缩库
    console.log(`[UnifiedMediaService] 文件预处理: ${file.name}`);
    
    return file; // 暂时返回原文件
  }
  
  /**
   * 文件后处理（水印、缩略图）
   */
  private async postProcessFile(
    objectKey: string,
    config: MediaProcessingConfig,
    businessType: MediaBusinessType
  ): Promise<{
    thumbnailUrl?: string;
    processedUrl?: string;
    watermarkedUrl?: string;
  }> {
    const result: {
      thumbnailUrl?: string;
      processedUrl?: string;
      watermarkedUrl?: string;
    } = {};
    
    // 生成缩略图URL
    if (config.generateThumbnail) {
      result.thumbnailUrl = await enterpriseMediaService.getViewUrl(objectKey, 'thumbnail');
    }
    
    // 生成水印URL
    if (config.watermark.enabled) {
      result.watermarkedUrl = await this.buildWatermarkedUrl(objectKey, 'medium', config.watermark);
    }
    
    // 生成处理后的URL
    result.processedUrl = await enterpriseMediaService.getViewUrl(objectKey, 'medium');
    
    return result;
  }
  
  /**
   * 构建水印URL - 使用OSS图片处理功能
   */
  private async buildWatermarkedUrl(
    objectKey: string,
    size: MediaSize,
    watermarkConfig: WatermarkConfig
  ): Promise<string> {
    const baseUrl = await enterpriseMediaService.getViewUrl(objectKey, size);
    
    // 构建OSS水印处理参数
    const watermarkParams = this.buildOSSWatermarkParams(watermarkConfig);
    
    // 如果URL已有参数，用&连接，否则用?
    const separator = baseUrl.includes('?') ? '&' : '?';
    
    return `${baseUrl}${separator}${watermarkParams}`;
  }
  
  /**
   * 构建OSS水印处理参数
   */
  private buildOSSWatermarkParams(config: WatermarkConfig): string {
    // 将文字转换为base64
    const textBase64 = Buffer.from(config.text, 'utf8').toString('base64');
    
    // 位置映射
    const positionMap = {
      'center': 'center',
      'top-left': 'nw',
      'top-right': 'ne',
      'bottom-left': 'sw',
      'bottom-right': 'se'
    };
    
    // 构建水印参数
    const params = [
      'x-oss-process=image/watermark',
      `text_${textBase64}`,
      `color_${config.color.replace('#', '')}`,
      `size_${config.fontSize}`,
      `g_${positionMap[config.position]}`,
      `t_${Math.round(config.opacity * 100)}`
    ];
    
    return params.join(',');
  }
}

// 导出单例实例
export const unifiedMediaService = UnifiedMediaService.getInstance();
