/**
 * react-native-amap3d类型声明文件
 */

declare module 'react-native-amap3d' {
  import { Component } from 'react';
  import { ViewProps } from 'react-native';

  export interface MapViewProps extends ViewProps {
    // 地图类型
    mapType?: 'standard' | 'satellite' | 'hybrid';
    
    // 初始状态（严格按照官方CameraPosition类型）
    initialCameraPosition?: {
      target?: { latitude: number; longitude: number };
      zoom?: number;
      bearing?: number;  // 朝向、旋转角度
      tilt?: number;     // 倾斜角度
    };
    
    // 定位相关属性（官方标准属性名）
    myLocationEnabled?: boolean;          // 是否显示当前定位
    myLocationButtonEnabled?: boolean;    // 是否显示定位按钮(仅Android)
    showsUserLocation?: boolean;          // 是否显示用户位置蓝点
    
    // 地图显示控制
    indoorViewEnabled?: boolean;          // 是否显示室内地图
    buildingsEnabled?: boolean;           // 是否显示3D建筑
    labelsEnabled?: boolean;              // 是否显示标注
    compassEnabled?: boolean;             // 是否显示指南针
    zoomControlsEnabled?: boolean;        // 是否显示放大缩小按钮(仅Android)
    scaleControlsEnabled?: boolean;       // 是否显示比例尺
    trafficEnabled?: boolean;             // 是否显示路况
    
    // 缩放控制
    maxZoom?: number;                     // 最大缩放级别
    minZoom?: number;                     // 最小缩放级别
    zoomLevel?: number;                   // 缩放级别
    
    // 手势控制
    zoomGesturesEnabled?: boolean;        // 是否启用缩放手势
    scrollGesturesEnabled?: boolean;      // 是否启用滑动手势
    rotateGesturesEnabled?: boolean;      // 是否启用旋转手势
    tiltGesturesEnabled?: boolean;        // 是否启用倾斜手势
    
    // 定位精度控制
    distanceFilter?: number;              // 设定定位的最小更新距离(iOS)
    headingFilter?: number;               // 设定最小更新角度，默认为 1 度(iOS)
    
    // 地图中心设置
    center?: {
      latitude: number;
      longitude: number;
    };
    
    // 事件回调
    onPress?: (event: any) => void;            // 点击事件
    onPressPoi?: (event: any) => void;         // 标注点击事件
    onLongPress?: (event: any) => void;        // 长按事件
    onCameraMove?: (event: any) => void;       // 地图状态改变事件
    onCameraIdle?: (event: any) => void;       // 地图状态改变事件(停止后触发)
    onLoad?: (event: any) => void;             // 地图初始化完成事件
    onLocation?: (event: any) => void;         // 地图定位更新事件
    onRegionChangeComplete?: (region: any) => void;  // 区域变化完成事件
    
    children?: React.ReactNode;
  }

  export class MapView extends Component<MapViewProps> {
    // 动画移动到指定区域
    animateToRegion(region: {
      latitude: number;
      longitude: number;
      latitudeDelta: number;
      longitudeDelta: number;
    }, duration?: number): void;
    
    // 移动摄像头
    moveCamera(cameraPosition: any, duration?: number): void;
  }

  export interface MarkerProps extends ViewProps {
    coordinate: {
      latitude: number;
      longitude: number;
    };
    title?: string;
    description?: string;
    onPress?: () => void;
    children?: React.ReactNode;
  }

  export class Marker extends Component<MarkerProps> {}

  export interface CircleProps extends ViewProps {
    center: {
      latitude: number;
      longitude: number;
    };
    radius: number;
    strokeColor?: string;
    fillColor?: string;
    strokeWidth?: number;
  }

  export class Circle extends Component<CircleProps> {}

  export default MapView;
}

declare module 'supercluster' {
  export interface ClusterFeature {
    type: 'Feature';
    properties: {
      cluster: boolean;
      cluster_id?: number;
      point_count?: number;
      point_count_abbreviated?: string;
    };
    geometry: {
      type: 'Point';
      coordinates: [number, number];
    };
  }

  export interface PointFeature {
    type: 'Feature';
    properties: any;
    geometry: {
      type: 'Point';
      coordinates: [number, number];
    };
  }

  export interface Options {
    radius?: number;
    maxZoom?: number;
    minZoom?: number;
    extent?: number;
    nodeSize?: number;
    log?: boolean;
    generateId?: boolean;
    reduce?: (accumulated: any, props: any) => void;
    map?: (props: any) => any;
  }

  export default class Supercluster {
    constructor(options?: Options);
    load(points: PointFeature[]): Supercluster;
    getClusters(bbox: number[], zoom: number): (ClusterFeature | PointFeature)[];
    getChildren(clusterId: number): (ClusterFeature | PointFeature)[];
    getLeaves(clusterId: number, limit?: number, offset?: number): PointFeature[];
    getClusterExpansionZoom(clusterId: number): number;
  }
}