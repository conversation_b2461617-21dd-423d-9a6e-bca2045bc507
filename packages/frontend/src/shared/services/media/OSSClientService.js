/**
 * OSS客户端服务 - JavaScript版本
 * 
 * 暂时使用JS版本来避免Metro模块解析问题
 */

import { Alert } from 'react-native';

class OSSClientService {
  constructor() {
    this.client = null;
    this.currentCredentials = null;
  }

  /**
   * 初始化OSS客户端
   */
  async initClient(credentials) {
    try {
      // 动态导入阿里云OSS SDK
      const { default: OSS } = await import('ali-oss');
      
      this.client = new OSS({
        region: credentials.region || 'oss-cn-guangzhou',
        accessKeyId: credentials.access_key_id,
        accessKeySecret: credentials.access_key_secret,
        stsToken: credentials.security_token,
        bucket: credentials.bucket,
        secure: true
      });
      
      this.currentCredentials = credentials;
      console.log('✅ OSS客户端初始化成功');
      
    } catch (error) {
      console.error('❌ OSS客户端初始化失败:', error);
      throw new Error(`OSS客户端初始化失败: ${error}`);
    }
  }

  /**
   * 检查并更新凭证
   */
  async ensureValidClient(credentials) {
    const needsUpdate = !this.client || 
                       !this.currentCredentials ||
                       this.currentCredentials.access_key_id !== credentials.access_key_id ||
                       this.currentCredentials.security_token !== credentials.security_token;

    if (needsUpdate) {
      await this.initClient(credentials);
    }
  }

  /**
   * 上传文件到OSS
   */
  async uploadFile(file, credentials, options = {}) {
    try {
      await this.ensureValidClient(credentials);
      
      console.log(`🚀 开始上传文件: ${file.name}`);
      
      // 生成OSS对象键
      const objectKey = this.generateObjectKey(file, options);
      
      // 准备上传数据
      const uploadData = await this.prepareUploadData(file);
      
      // 上传到OSS
      const startTime = Date.now();
      const result = await this.client.put(objectKey, uploadData, {
        headers: options.headers || {},
        progress: (p, checkpoint) => {
          const progress = {
            percentage: Math.round(p * 100),
            loaded: Math.round(file.size * p),
            total: file.size,
            speed: this.calculateSpeed(Math.round(file.size * p), startTime),
            remainingTime: this.calculateRemainingTime(p, startTime)
          };
          
          if (options.onProgress) {
            options.onProgress(progress);
          }
        }
      });

      const uploadResult = {
        success: true,
        objectKey,
        url: result.url,
        size: file.size,
        uploadTime: Date.now() - startTime,
        etag: result.etag
      };

      console.log(`✅ 文件上传成功: ${file.name} -> ${objectKey}`);
      return uploadResult;

    } catch (error) {
      console.error(`❌ 文件上传失败: ${file.name}`, error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      };
    }
  }

  /**
   * 删除OSS文件
   */
  async deleteFile(objectKey, credentials) {
    try {
      await this.ensureValidClient(credentials);
      
      console.log(`🗑️ 删除文件: ${objectKey}`);
      
      await this.client.delete(objectKey);
      
      console.log(`✅ 文件删除成功: ${objectKey}`);
      
      return {
        success: true,
        objectKey
      };

    } catch (error) {
      console.error(`❌ 文件删除失败: ${objectKey}`, error);
      
      return {
        success: false,
        objectKey,
        error: error instanceof Error ? error.message : '删除失败'
      };
    }
  }

  /**
   * 生成OSS对象键
   */
  generateObjectKey(file, options) {
    const timestamp = new Date().toISOString().slice(0, 10);
    const randomId = Math.random().toString(36).substr(2, 9);
    const extension = this.getFileExtension(file.name);
    
    const folder = options.folder || 'uploads';
    const prefix = options.propertyId ? `property/${options.propertyId}` : 'general';
    
    return `${folder}/${prefix}/${timestamp}/${randomId}${extension}`;
  }

  /**
   * 准备上传数据
   */
  async prepareUploadData(file) {
    // 在React Native中，我们需要使用文件URI
    if (file.uri) {
      return {
        uri: file.uri,
        type: file.type,
        name: file.name
      };
    }
    
    // 如果有数据内容
    if (file.data) {
      return file.data;
    }
    
    throw new Error('文件数据无效');
  }

  /**
   * 获取文件扩展名
   */
  getFileExtension(filename) {
    const lastDot = filename.lastIndexOf('.');
    return lastDot >= 0 ? filename.slice(lastDot) : '';
  }

  /**
   * 计算上传速度 (bytes/second)
   */
  calculateSpeed(loaded, startTime) {
    const elapsed = (Date.now() - startTime) / 1000;
    return elapsed > 0 ? loaded / elapsed : 0;
  }

  /**
   * 计算剩余时间 (seconds)
   */
  calculateRemainingTime(progress, startTime) {
    if (progress <= 0) return 0;
    
    const elapsed = (Date.now() - startTime) / 1000;
    const totalTime = elapsed / progress;
    return Math.max(0, totalTime - elapsed);
  }
}

// 导出单例实例
export const ossClientService = new OSSClientService();