"""
房源服务层的SQLModel版本适配器

为了保持向后兼容，将现有的PropertyService重新导出并提供适配的接口
"""
from typing import List, Optional, Tuple
from sqlmodel import Session
from app.services.property.property_service import PropertyService as BasePropertyService
from app.schemas.property.init_sqlmodel import (
    PropertyCreate, PropertyUpdate, PropertyStatusUpdate, VerificationStatusUpdate
)
from app.models.property.property import Property


class PropertyService:
    """SQLModel版本的房源服务类，适配现有的路由接口"""
    
    @staticmethod
    async def create_property(
        db: Session,
        property_data: PropertyCreate,
        owner_id: str
    ) -> Property:
        """创建房源"""
        service = BasePropertyService(session=db)
        return await service.create_property(property_data, owner_id)
    
    @staticmethod
    async def get_property(
        db: Session,
        property_id: str
    ) -> Optional[dict]:
        """获取单个房源，返回与数据库结构一致的dict格式"""
        from sqlmodel import text
        
        # 按照统一转换层架构：直接查询数据库，保持数据格式一致性
        sql = """
        SELECT
            id, title, property_type, sub_type, address, building_name,
            floor, total_floors, total_area, usable_area,
            rent_price, sale_price, transfer_price, deposit_months,
            decoration_level, delivery_status, status, verification_status,
            description, owner_id, created_at, updated_at
        FROM properties
        WHERE id = :property_id
        """
        
        try:
            result = await db.execute(text(sql), {"property_id": property_id})
            row = result.first()
            
            if not row:
                return None
                
            # 从价格字段推导交易类型（与get_properties保持一致）
            transaction_types = []
            rent_price = float(row[10]) if row[10] else None
            sale_price = float(row[11]) if row[11] else None
            transfer_price = float(row[12]) if row[12] else None

            if rent_price and rent_price > 0:
                transaction_types.append("RENT")
            if sale_price and sale_price > 0:
                transaction_types.append("SALE")
            if transfer_price and transfer_price > 0:
                transaction_types.append("TRANSFER")

            # 返回与get_properties相同格式的dict，并添加Schema要求的字段
            property_dict = {
                'id': str(row[0]),
                'title': row[1],
                'property_type': row[2],
                'sub_type': row[3],
                'address': row[4],
                'building_name': row[5],
                'floor': row[6],
                'total_floors': row[7],
                'total_area': float(row[8]) if row[8] else None,
                'usable_area': float(row[9]) if row[9] else None,
                'rent_price': rent_price,
                'sale_price': sale_price,
                'transfer_price': transfer_price,
                'deposit_months': row[13],
                'decoration_level': row[14],
                'delivery_status': row[15],
                'status': row[16],
                'verification_status': row[17],
                'description': row[18],
                'owner_id': str(row[19]),
                'created_at': row[20].isoformat() if row[20] else None,
                'updated_at': row[21].isoformat() if row[21] else None,
                'transaction_types': transaction_types,
                # 为了兼容PropertyResponse Schema，添加空的features字段
                'features': [],
                'prices': []
            }
            
            return property_dict
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error querying single property {property_id}: {e}")
            return None
    
    @staticmethod
    async def get_properties(
        db: Session,
        skip: int = 0,
        limit: int = 10,
        property_type=None,
        transaction_type=None,
        min_area: Optional[float] = None,
        max_area: Optional[float] = None,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        status=None,
        search_term: Optional[str] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc",
        owner_id=None
    ) -> Tuple[List[dict], int]:
        """
        获取房源列表，返回(properties, total)
        按照企业级架构：使用数据库作为单一事实来源
        """
        from sqlmodel import text
        from app.core.constants import PropertyStatus

        # 按照项目架构：直接查询数据库表，保持数据库作为单一事实来源
        base_sql = """
        SELECT
            id, title, property_type, sub_type, address, building_name,
            floor, total_floors, total_area, usable_area,
            rent_price, sale_price, transfer_price, deposit_months,
            decoration_level, delivery_status, status, verification_status,
            description, owner_id, created_at, updated_at
        FROM properties
        """

        count_sql = "SELECT COUNT(*) FROM properties"

        # 构建WHERE条件
        where_conditions = []
        params = {}

        # 房源类型过滤
        if property_type:
            where_conditions.append("property_type = :property_type")
            params['property_type'] = property_type

        # 状态过滤（默认只显示活跃状态）
        if status:
            where_conditions.append("status = :status")
            params['status'] = status
        else:
            where_conditions.append("status = :status")
            params['status'] = PropertyStatus.ACTIVE.value  # 使用枚举值

        # 面积过滤
        if min_area:
            where_conditions.append("total_area >= :min_area")
            params['min_area'] = min_area
        if max_area:
            where_conditions.append("total_area <= :max_area")
            params['max_area'] = max_area

        # 价格过滤（检查租金、售价、转让费）
        if min_price or max_price:
            price_conditions = []
            if min_price:
                price_conditions.extend([
                    "(rent_price IS NOT NULL AND rent_price >= :min_price)",
                    "(sale_price IS NOT NULL AND sale_price >= :min_price)",
                    "(transfer_price IS NOT NULL AND transfer_price >= :min_price)"
                ])
                params['min_price'] = min_price
            if max_price:
                price_conditions.extend([
                    "(rent_price IS NOT NULL AND rent_price <= :max_price)",
                    "(sale_price IS NOT NULL AND sale_price <= :max_price)",
                    "(transfer_price IS NOT NULL AND transfer_price <= :max_price)"
                ])
                params['max_price'] = max_price
            if price_conditions:
                where_conditions.append(f"({' OR '.join(price_conditions)})")

        # 业主过滤
        if owner_id:
            where_conditions.append("owner_id = :owner_id")
            params['owner_id'] = owner_id

        # 关键词搜索
        if search_term:
            where_conditions.append("(title ILIKE :search_term OR description ILIKE :search_term OR address ILIKE :search_term)")
            params['search_term'] = f"%{search_term}%"

        # 构建完整的WHERE子句
        where_clause = ""
        if where_conditions:
            where_clause = " WHERE " + " AND ".join(where_conditions)

        # 排序
        if sort_by == "created_at":
            order_clause = " ORDER BY created_at"
        elif sort_by == "price":
            order_clause = " ORDER BY rent_price"
        elif sort_by == "area":
            order_clause = " ORDER BY total_area"
        else:
            order_clause = " ORDER BY created_at"

        if sort_order == "asc":
            order_clause += " ASC"
        else:
            order_clause += " DESC"

        # 分页
        limit_clause = f" LIMIT {limit} OFFSET {skip}"

        # 构建完整的查询
        final_query = base_sql + where_clause + order_clause + limit_clause
        final_count_query = count_sql + where_clause

        try:
            # 执行查询
            result = await db.execute(text(final_query), params)
            rows = result.all()

            count_result = await db.execute(text(final_count_query), params)
            total = count_result.scalar()

            # 按照项目转换层架构：将数据库结果转换为标准格式
            properties = []
            for row in rows:
                # 从价格字段推导交易类型
                transaction_types = []
                rent_price = float(row[10]) if row[10] else None
                sale_price = float(row[11]) if row[11] else None
                transfer_price = float(row[12]) if row[12] else None

                if rent_price and rent_price > 0:
                    transaction_types.append("RENT")
                if sale_price and sale_price > 0:
                    transaction_types.append("SALE")
                if transfer_price and transfer_price > 0:
                    transaction_types.append("TRANSFER")

                property_dict = {
                    'id': str(row[0]),
                    'title': row[1],
                    'property_type': row[2],
                    'sub_type': row[3],
                    'address': row[4],
                    'building_name': row[5],
                    'floor': row[6],
                    'total_floors': row[7],
                    'total_area': float(row[8]) if row[8] else None,
                    'usable_area': float(row[9]) if row[9] else None,
                    'rent_price': rent_price,
                    'sale_price': sale_price,
                    'transfer_price': transfer_price,
                    'deposit_months': row[13],
                    'decoration_level': row[14],
                    'delivery_status': row[15],
                    'status': row[16],
                    'verification_status': row[17],
                    'description': row[18],
                    'owner_id': str(row[19]),
                    'created_at': row[20].isoformat() if row[20] else None,
                    'updated_at': row[21].isoformat() if row[21] else None,
                    'transaction_types': transaction_types  # 推导的交易类型
                }
                properties.append(property_dict)

            return properties, total

        except Exception as e:
            # 企业级错误处理
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error querying properties: {e}")
            return [], 0
    
    @staticmethod
    async def update_property(
        db: Session,
        property_id: str,
        property_data: PropertyUpdate
    ) -> Optional[Property]:
        """更新房源"""
        service = BasePropertyService(session=db)
        # 需要适配方法签名，这里暂时返回None
        return None
    
    @staticmethod
    async def delete_property(
        db: Session,
        property_id: str
    ) -> bool:
        """删除房源"""
        service = BasePropertyService(session=db)
        # 需要适配方法签名，这里暂时返回False
        return False
    
    @staticmethod
    async def update_property_status(
        db: Session,
        property_id: str,
        status_data: PropertyStatusUpdate
    ) -> Optional[Property]:
        """更新房源状态"""
        service = BasePropertyService(session=db)
        # 需要适配方法签名，这里暂时返回None
        return None
    
    @staticmethod
    async def update_verification_status(
        db: Session,
        property_id: str,
        status_data: VerificationStatusUpdate
    ) -> Optional[Property]:
        """更新审核状态"""
        service = BasePropertyService(session=db)
        # 需要适配方法签名，这里暂时返回None
        return None
    
    def __init__(self, session):
        """实例初始化，兼容BasePropertyService的接口"""
        self.session = session
    
    async def get_tag_recommendations(
        self,
        property_type: str,
        count: int = 8
    ) -> List[str]:
        """
        获取指定房源类型的推荐标签（委托给BasePropertyService）
        
        Args:
            property_type: 房源类型 (例如 "OFFICE", "SHOP" 或 "商铺", "写字楼")
            count: 需要获取的标签数量
            
        Returns:
            随机推荐标签列表
        """
        base_service = BasePropertyService(session=self.session)
        return await base_service.get_tag_recommendations(property_type, count)