/**
 * 表单验证与键盘感知滚动整合Hook
 * 
 * 基于React Native和React Hook Form官方最佳实践：
 * 1. 整合键盘响应表单和表单验证功能
 * 2. 提供一站式表单解决方案
 * 3. 支持错误字段自动滚动定位
 */

import { useCallback, useMemo } from 'react';
import FeedbackService from '../services/FeedbackService';
import { useKeyboardAwareForm, UseKeyboardAwareFormOptions } from './useKeyboardAwareForm';
import type { FormFieldError } from './useKeyboardAwareForm';

export interface FormFieldValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => {
    valid: boolean;
    hasWarning?: boolean;
    errorMessage?: string;
    warningMessage?: string;
  };
}

export interface FormValidationWithScrollOptions extends UseKeyboardAwareFormOptions {
  /**
   * 字段验证规则
   */
  fieldRules?: Record<string, FormFieldValidationRule>;
  
  /**
   * 是否显示验证错误弹窗
   * @default true
   */
  showValidationAlert?: boolean;
  
  /**
   * 验证错误弹窗标题
   * @default "请完善必填信息"
   */
  validationAlertTitle?: string;
  
  /**
   * 验证错误弹窗按钮文本
   * @default "确定"
   */
  validationAlertButtonText?: string;
}

export interface UseFormValidationWithScrollReturn {
  /**
   * 键盘响应表单相关功能
   */
  formControl: ReturnType<typeof useKeyboardAwareForm>;
  
  /**
   * 验证单个字段
   */
  validateField: (fieldName: string, value: any) => {
    valid: boolean;
    hasWarning: boolean;
    errorMessage?: string;
    warningMessage?: string;
  };
  
  /**
   * 验证多个字段并滚动到第一个错误
   */
  validateFieldsWithScroll: (fields: Record<string, any>) => Promise<{
    valid: boolean;
    errors: FormFieldError[];
    firstErrorField?: string;
  }>;
  
  /**
   * 验证表单并显示错误提示
   */
  validateAndShowErrors: (fields: Record<string, any>) => Promise<boolean>;
}

/**
 * 表单验证与键盘感知滚动整合Hook
 * 
 * 提供企业级表单解决方案，整合验证、键盘响应、错误滚动等功能
 */
export const useFormValidationWithScroll = (
  options: FormValidationWithScrollOptions = {}
): UseFormValidationWithScrollReturn => {
  const {
    fieldRules = {},
    showValidationAlert = true,
    validationAlertTitle = "请完善必填信息",
    validationAlertButtonText = "确定",
    ...keyboardAwareOptions
  } = options;

  // 初始化键盘响应表单
  const formControl = useKeyboardAwareForm(keyboardAwareOptions);

  // 验证单个字段
  const validateField = useCallback((fieldName: string, value: any) => {
    const rule = fieldRules[fieldName];
    if (!rule) {
      return { valid: true, hasWarning: false };
    }

    let errorMessage = '';
    let warningMessage = '';

    // 必填验证
    if (rule.required && (!value || (typeof value === 'string' && value.trim().length === 0))) {
      warningMessage = `请填写${getFieldDisplayName(fieldName)}`;
    }

    // 长度验证
    if (value && typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        if (value.length === 0) {
          warningMessage = `请填写${getFieldDisplayName(fieldName)}`;
        } else {
          warningMessage = `${getFieldDisplayName(fieldName)}至少需要${rule.minLength}个字符，当前${value.length}个字符`;
        }
      }
      
      if (rule.maxLength && value.length > rule.maxLength) {
        errorMessage = `${getFieldDisplayName(fieldName)}最多${rule.maxLength}个字符`;
      }
    }

    // 正则验证
    if (rule.pattern && value && typeof value === 'string' && !rule.pattern.test(value)) {
      errorMessage = `${getFieldDisplayName(fieldName)}格式不正确`;
    }

    // 自定义验证
    if (rule.custom) {
      const customResult = rule.custom(value);
      if (!customResult.valid) {
        errorMessage = customResult.errorMessage || `${getFieldDisplayName(fieldName)}验证失败`;
      }
      if (customResult.hasWarning) {
        warningMessage = customResult.warningMessage || '';
      }
    }

    return { 
      valid: !errorMessage, 
      hasWarning: !!warningMessage,
      errorMessage,
      warningMessage
    };
  }, [fieldRules]);

  // 验证多个字段并滚动到第一个错误
  const validateFieldsWithScroll = useCallback(async (fields: Record<string, any>) => {
    const errors: FormFieldError[] = [];
    
    // 验证所有字段
    Object.entries(fields).forEach(([fieldName, value]) => {
      const result = validateField(fieldName, value);
      
      if (!result.valid || result.hasWarning) {
        errors.push({
          fieldName,
          message: result.errorMessage || result.warningMessage || '验证失败',
          priority: result.valid ? 2 : 1, // 错误优先级高于警告
        });
      }
    });

    // 如果有错误，显示错误状态并滚动到第一个错误
    if (errors.length > 0) {
      formControl.showFieldErrors(errors);
      await formControl.scrollToFirstError(errors);
      
      return {
        valid: false,
        errors,
        firstErrorField: errors[0]?.fieldName,
      };
    }

    // 清除之前的错误状态
    formControl.clearFieldErrors();
    
    return {
      valid: true,
      errors: [],
    };
  }, [validateField, formControl]);

  // 验证表单并显示错误提示
  const validateAndShowErrors = useCallback(async (fields: Record<string, any>): Promise<boolean> => {
    const result = await validateFieldsWithScroll(fields);
    
    if (!result.valid && showValidationAlert) {
      FeedbackService.showInfo(validationAlertTitle);
    }
    
    return result.valid;
  }, [validateFieldsWithScroll, showValidationAlert, validationAlertTitle, validationAlertButtonText]);

  return {
    formControl,
    validateField,
    validateFieldsWithScroll,
    validateAndShowErrors,
  };
};

/**
 * 获取字段显示名称
 * 可以根据项目需求自定义字段名称映射
 */
function getFieldDisplayName(fieldName: string): string {
  const fieldNameMap: Record<string, string> = {
    title: '标题',
    property_certificate_address: '房产证地址',
    sub_type: '房源类型',
    area: '面积',
    floor: '楼层',
    orientation: '朝向',
    decoration_level: '装修程度',
    description: '房源描述',
    rent_price: '租金',
    sale_price: '售价',
    transfer_price: '转让价格',
    transaction_types: '交易方式',
  };
  
  return fieldNameMap[fieldName] || fieldName;
}

export default useFormValidationWithScroll;