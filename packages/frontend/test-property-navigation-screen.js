/**
 * 🗺️ 专门测试PropertyNavigationScreen bundling
 * 验证地图导航页面是否能正常编译
 */

const { exec } = require('child_process');
const fs = require('fs');

console.log('🗺️ [Test] 开始测试PropertyNavigationScreen bundling...');

// 创建一个临时的entry文件，只导入PropertyNavigationScreen
const testEntryContent = `
/**
 * 临时测试入口文件 - 只测试PropertyNavigationScreen
 */
import React from 'react';
import { AppRegistry } from 'react-native';
import { PropertyNavigationScreen } from './src/screens/Property/PropertyNavigationScreen';

// 简单的测试组件
const TestApp = () => {
  return React.createElement(PropertyNavigationScreen);
};

AppRegistry.registerComponent('testApp', () => TestApp);
`;

const testEntryPath =
  '/data/my-real-estate-app/packages/frontend/test-entry.js';

try {
  // 写入测试入口文件
  fs.writeFileSync(testEntryPath, testEntryContent);
  console.log('✅ [Test] 测试入口文件已创建');

  // 执行bundling测试
  console.log('🔄 [Test] 开始bundling测试...');

  const bundleCommand = `cd /data/my-real-estate-app/packages/frontend && npx react-native bundle --platform android --dev false --entry-file test-entry.js --bundle-output /tmp/property-navigation-test-bundle.js`;

  exec(bundleCommand, { timeout: 60000 }, (error, stdout, stderr) => {
    // 清理测试文件
    if (fs.existsSync(testEntryPath)) {
      fs.unlinkSync(testEntryPath);
      console.log('🧹 [Test] 测试入口文件已清理');
    }

    if (error) {
      console.log('❌ [Test] PropertyNavigationScreen bundling失败:');
      console.log('Error:', error.message);
      console.log('Stderr:', stderr);

      // 检查是否是LocationData相关错误
      if (
        stderr.includes('LocationData') &&
        stderr.includes('already declared')
      ) {
        console.log('🚨 [Test] 确认：LocationData重复声明问题仍存在！');
      } else {
        console.log('ℹ️ [Test] 错误不是LocationData相关，可能是其他依赖问题');
      }
    } else {
      console.log('🎉 [Test] PropertyNavigationScreen bundling成功！');
      console.log('✅ [Test] LocationData重复声明问题已修复');
      console.log('✅ [Test] 地图导航页面可以正常编译');

      // 检查bundle文件大小
      const bundlePath = '/tmp/property-navigation-test-bundle.js';
      if (fs.existsSync(bundlePath)) {
        const stats = fs.statSync(bundlePath);
        const sizeKB = Math.round(stats.size / 1024);
        console.log(`📦 [Test] Bundle大小: ${sizeKB}KB`);

        // 清理bundle文件
        fs.unlinkSync(bundlePath);
      }
    }

    console.log('\n🔚 [Test] PropertyNavigationScreen bundling测试完成');
  });
} catch (error) {
  console.error('❌ [Test] 测试准备阶段失败:', error);
}
