/**
 * 高德地图隐私合规组件
 * 根据专家建议和官方要求实现的隐私同意流程
 */

import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  Modal, 
  TouchableOpacity, 
  StyleSheet, 
  Linking, 
  Platform, 
  BackHandler, 
  Alert 
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 安全导入高德地图SDK
let AMapSdk: any = null;
try {
  const amapModule = require('react-native-amap3d');
  AMapSdk = amapModule.AMapSdk;
  console.log('[AmapPrivacy] ✅ react-native-amap3d模块加载成功');
  console.log('[AmapPrivacy] - 模块内容:', Object.keys(amapModule));
} catch (error) {
  console.error('[AmapPrivacy] ❌ react-native-amap3d模块加载失败:', error);
}

const PRIVACY_AGREEMENT_KEY = 'amap_privacy_agreed_v1';

interface AmapPrivacyComplianceProps {
  children: React.ReactNode;
  onInitialized?: (success: boolean) => void;
}

export const AmapPrivacyCompliance: React.FC<AmapPrivacyComplianceProps> = ({
  children,
  onInitialized
}) => {
  const [showPrivacyDialog, setShowPrivacyDialog] = useState(false);
  const [isSDKInitialized, setIsSDKInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkPrivacyAgreement();
  }, []);

  /**
   * 检查用户是否已经同意过隐私政策
   * 优化：不强制弹窗，让应用正常启动
   */
  const checkPrivacyAgreement = async () => {
    try {
      const hasAgreed = await AsyncStorage.getItem(PRIVACY_AGREEMENT_KEY);
      console.log('[AmapPrivacy] 检查隐私同意状态:', hasAgreed);
      
      if (hasAgreed === 'true') {
        // 已同意，直接初始化SDK
        await initializeAmapSDK();
      } else {
        // 未同意，但不阻塞应用启动 - 应用仍然应该正常运行
        console.log('[AmapPrivacy] 用户未同意隐私政策，应用正常启动，地图功能稍后按需初始化');
        setIsLoading(false);
        // 通知父组件：应用可以正常启动（即使SDK未初始化）
        onInitialized?.(true);
      }
    } catch (error) {
      console.error('[AmapPrivacy] 检查隐私同意状态失败:', error);
      setIsLoading(false);
      // 即使检查失败，也让应用正常启动
      onInitialized?.(true);
    }
  };

  /**
   * 当用户需要使用地图功能时主动调用
   */
  const requestPrivacyAgreementForMap = async (): Promise<boolean> => {
    try {
      const hasAgreed = await AsyncStorage.getItem(PRIVACY_AGREEMENT_KEY);
      
      if (hasAgreed === 'true') {
        return true; // 已同意
      }
      
      // 显示优雅的同意对话框
      return new Promise((resolve) => {
        setShowPrivacyDialog(true);
        // 临时保存resolve函数，在用户操作后调用
        (window as any)._privacyResolve = resolve;
      });
    } catch (error) {
      console.error('[AmapPrivacy] 请求隐私同意失败:', error);
      return false;
    }
  };

  /**
   * 初始化高德地图SDK
   */
  const initializeAmapSDK = async () => {
    try {
      console.log('[AmapPrivacy] 🗺️ 开始初始化高德地图SDK...');
      
      // 检查AMapSdk模块可用性
      if (!AMapSdk) {
        throw new Error('AMapSdk模块未正确加载，请检查react-native-amap3d安装');
      }
      
      if (typeof AMapSdk.init !== 'function') {
        throw new Error('AMapSdk.init不是函数，请检查react-native-amap3d版本');
      }
      
      // 调试信息
      console.log('[AmapPrivacy] 🔍 调试信息:');
      console.log('[AmapPrivacy] - AMapSdk类型:', typeof AMapSdk);
      console.log('[AmapPrivacy] - AMapSdk.init类型:', typeof AMapSdk.init);
      console.log('[AmapPrivacy] - Platform.OS:', Platform.OS);
      
      const apiKey = Platform.select({
        android: process.env.EXPO_PUBLIC_AMAP_FRONTEND_KEY || "7623c396c3d7dcfa3b17f032db28a0bc",
        ios: process.env.EXPO_PUBLIC_AMAP_FRONTEND_KEY || "7623c396c3d7dcfa3b17f032db28a0bc",
      });
      console.log('[AmapPrivacy] - 使用的API Key:', apiKey?.substring(0, 8) + '...');
      
      // Android平台检查包名和SHA1（调试信息）
      if (Platform.OS === 'android') {
        console.log('[AmapPrivacy] - Android包名: com.huixuanzhi123.commercialrealestate');
        console.log('[AmapPrivacy] - API Key应该与此包名绑定');
      }
      
      // 执行初始化
      console.log('[AmapPrivacy] 🔄 开始调用AMapSdk.init...');
      await AMapSdk.init(apiKey);
      
      console.log('[AmapPrivacy] ✅ 高德地图SDK初始化成功');
      setIsSDKInitialized(true);
      setIsLoading(false);
      onInitialized?.(true);
      
    } catch (error) {
      console.error('[AmapPrivacy] ❌ 高德地图SDK初始化失败:', error);
      console.error('[AmapPrivacy] 错误详情:', {
        message: (error as Error)?.message,
        name: (error as Error)?.name,
        code: (error as any)?.code,
        stack: (error as Error)?.stack?.substring(0, 500)
      });
      setIsLoading(false);
      
      // 🔧 关键修复：即使SDK初始化失败，也要让应用正常启动
      // 应用其他功能不应该受到地图功能失败的影响
      console.log('[AmapPrivacy] 🏠 应用将正常启动，地图功能暂时不可用');
      onInitialized?.(true); // 改为 true，允许应用正常启动
      
      const errorMessage = (error as Error)?.message || '未知错误';
      Alert.alert(
        '地图功能暂时不可用',
        `地图服务初始化失败：${errorMessage}。您可以继续使用其他功能，稍后重试地图功能。`,
        [{ text: '知道了' }]
      );
    }
  };

  /**
   * 用户点击同意
   */
  const handleAgree = async () => {
    try {
      console.log('[AmapPrivacy] 用户同意隐私政策');
      
      // 保存用户同意状态
      await AsyncStorage.setItem(PRIVACY_AGREEMENT_KEY, 'true');
      
      // 关闭对话框
      setShowPrivacyDialog(false);
      setIsLoading(true);
      
      // 初始化SDK
      await initializeAmapSDK();
      
      // 如果有等待的Promise，resolve it
      if ((window as any)._privacyResolve) {
        (window as any)._privacyResolve(true);
        delete (window as any)._privacyResolve;
      }
      
    } catch (error) {
      console.error('[AmapPrivacy] 保存同意状态失败:', error);
      Alert.alert('错误', '保存设置失败，请重试。');
    }
  };

  /**
   * 用户点击不同意
   */
  const handleDisagree = () => {
    console.log('[AmapPrivacy] 用户拒绝隐私政策');
    
    // 关闭对话框，返回拒绝状态
    setShowPrivacyDialog(false);
    setIsLoading(false);
    
    // 如果有等待的Promise，resolve false
    if ((window as any)._privacyResolve) {
      (window as any)._privacyResolve(false);
      delete (window as any)._privacyResolve;
    }
    
    // 显示温和的提示
    Alert.alert(
      '提示',
      '您可以继续使用其他功能，如需使用地图功能，随时可以在设置中同意隐私政策。',
      [{ text: '知道了' }]
    );
  };

  /**
   * 打开隐私政策链接
   */
  const openPrivacyPolicy = (url: string) => {
    Linking.openURL(url).catch(err => {
      console.error('[AmapPrivacy] 打开链接失败:', err);
      Alert.alert('错误', '无法打开链接，请稍后重试。');
    });
  };

  /**
   * 渲染隐私同意对话框
   */
  const renderPrivacyDialog = () => (
    <Modal
      visible={showPrivacyDialog}
      transparent={true}
      animationType="fade"
      onRequestClose={() => {
        // Android返回键处理
        handleDisagree();
      }}
    >
      <View style={styles.dialogContainer}>
        <View style={styles.dialog}>
          <Text style={styles.title}>服务协议和隐私政策</Text>
          
          <Text style={styles.content}>
            欢迎使用我们的应用！为了给您提供地图定位服务，我们的应用集成了高德地图SDK。
            {'\n\n'}
            在使用前，请您务必仔细阅读并同意：
            {'\n\n'}
            <Text 
              style={styles.link}
              onPress={() => openPrivacyPolicy('https://your-privacy-policy-url.com')}
            >
              《用户服务协议》
            </Text>
            {' 和 '}
            <Text 
              style={styles.link}
              onPress={() => openPrivacyPolicy('https://your-privacy-policy-url.com')}
            >
              《隐私政策》
            </Text>
            {'\n\n'}
            我们还使用了高德地图服务，请您同时了解：
            {'\n'}
            <Text 
              style={styles.link}
              onPress={() => openPrivacyPolicy('https://lbs.amap.com/pages/privacy/')}
            >
              《高德地图开放平台隐私权政策》
            </Text>
            {'\n\n'}
            这些服务将会收集您的位置信息以提供地图和导航功能。您可以随时在设置中管理这些权限。
          </Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.disagreeButton]}
              onPress={handleDisagree}
            >
              <Text style={styles.disagreeButtonText}>不同意</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, styles.agreeButton]}
              onPress={handleAgree}
            >
              <Text style={styles.agreeButtonText}>同意并继续</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  /**
   * 渲染加载状态
   */
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <Text style={styles.loadingText}>正在初始化地图服务...</Text>
    </View>
  );

  // 如果正在加载，显示加载界面
  if (isLoading) {
    return (
      <>
        {renderLoading()}
        {renderPrivacyDialog()}
      </>
    );
  }

  // 🔧 关键修复：总是渲染子组件，让应用正常启动
  // 无论SDK是否初始化成功，都不应该阻止应用的其他功能
  return (
    <>
      {children}
      {showPrivacyDialog && renderPrivacyDialog()}
    </>
  );
};

const styles = StyleSheet.create({
  dialogContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  dialog: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    color: '#333333',
  },
  content: {
    fontSize: 14,
    lineHeight: 22,
    color: '#666666',
    marginBottom: 24,
  },
  link: {
    color: '#007AFF',
    textDecorationLine: 'underline',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  disagreeButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  agreeButton: {
    backgroundColor: '#007AFF',
  },
  disagreeButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '500',
  },
  agreeButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
});