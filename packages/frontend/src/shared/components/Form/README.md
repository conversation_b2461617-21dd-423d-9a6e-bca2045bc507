# 企业级表单组件系统

## 概览

本表单组件系统为房地产应用提供了完整的企业级表单解决方案，包含以下特性：

- ✅ 键盘响应式布局管理
- ✅ 实时表单验证与错误提示  
- ✅ 自动滚动到错误字段
- ✅ 红色错误提示和输入框边框
- ✅ 防抖验证优化
- ✅ 统一的样式规范
- ✅ TypeScript严格类型支持
- ✅ 内存和性能优化

## 核心组件

### 1. EnterpriseFormContainer
表单容器组件，提供键盘管理和滚动功能。

```tsx
import { EnterpriseFormContainer } from '@/shared/components/Form';

<EnterpriseFormContainer
  onValidate={validateForm}
  onSubmit={handleSubmit}
  enableAutoValidation={true}
  keyboardOptions={{
    extraOffset: 30,
    enableAutoScroll: true,
    scrollAnimationDuration: 300,
  }}
>
  {/* 表单内容 */}
</EnterpriseFormContainer>
```

### 2. EnterpriseFormInput
企业级输入框组件，支持错误显示和键盘适配。

```tsx
import { EnterpriseFormInput } from '@/shared/components/Form';

<EnterpriseFormInput
  fieldName="title"
  label="房源标题"
  required={true}
  value={values.title}
  onValueChange={(value) => setValue('title', value)}
  error={errors.title}
  warning={warnings.title}
  placeholder="请输入房源标题"
  maxLength={100}
  hint="标题至少需要8个字符"
/>
```

### 3. useEnterpriseFormValidation
表单验证Hook，提供完整的验证逻辑。

```tsx
import { useEnterpriseFormValidation } from '@/shared/components/Form';

const formValidation = useEnterpriseFormValidation({
  fields: {
    title: {
      required: true,
      minLength: 8,
      maxLength: 100,
    },
    area: {
      required: true,
      min: 0.1,
      custom: (value) => {
        const numValue = parseFloat(value);
        if (isNaN(numValue) || numValue <= 0) {
          return { valid: false, message: '请输入有效的面积' };
        }
        return { valid: true };
      }
    },
  },
  defaultValues: {
    title: '',
    area: '',
  },
});
```

## 验证规则

支持以下验证规则：

- `required`: 必填字段
- `minLength`/`maxLength`: 字符串长度限制
- `min`/`max`: 数值范围限制  
- `pattern`: 正则表达式验证
- `custom`: 自定义验证函数

## 错误处理

### 自动错误提示
- 字段下方显示红色错误信息
- 输入框边框变红
- 错误信息带有淡入淡出动画

### 自动滚动
- 提交时自动滚动到第一个错误字段
- 按优先级排序（必填字段优先）
- 平滑的滚动动画

### 防抖优化
- 输入时防抖验证，避免频繁验证
- 300ms延迟，提供良好的用户体验

## 键盘适配

### 自动键盘避让
- iOS使用`KeyboardAvoidingView`的`padding`行为
- Android使用`height`行为
- 自动计算安全区域

### 智能滚动
- 输入框获焦时自动滚动到可见区域
- 考虑键盘高度和额外偏移量
- 等待键盘动画完成后再滚动

## 样式规范

### 颜色系统
```typescript
const colors = {
  primary: '#007AFF',      // 主色调
  error: '#FF3B30',        // 错误色
  warning: '#FF9500',      // 警告色
  text: '#000000',         // 主文本
  textSecondary: '#8E8E93', // 次要文本
  background: '#F8F9FA',   // 输入框背景
  border: '#E5E5E5',       // 边框色
};
```

### 响应式尺寸
使用`wp()`, `hp()`, `fp()`函数确保不同设备适配：
```typescript
import { wp, hp, fp } from '@/shared/utils/responsiveUtils';
```

## 使用示例

### 完整表单示例

```tsx
import React from 'react';
import {
  EnterpriseFormContainer,
  EnterpriseFormInput,
  useEnterpriseFormValidation,
} from '@/shared/components/Form';

export const PropertyForm: React.FC = () => {
  const formValidation = useEnterpriseFormValidation({
    fields: {
      title: { required: true, minLength: 8, maxLength: 100 },
      description: { required: true, minLength: 20, maxLength: 2000 },
      area: { required: true, min: 0.1 },
    },
    defaultValues: {
      title: '',
      description: '',
      area: 0,
    },
  });

  const {
    values,
    errors,
    warnings,
    setValue,
    validateAllFields,
    getFieldErrors,
    submitForm,
  } = formValidation;

  const validateForm = () => {
    return getFieldErrors();
  };

  const handleSubmit = async () => {
    const success = await submitForm(async (data) => {
      console.log('提交数据:', data);
      // 实际的API调用
    });

    if (success) {
      console.log('提交成功');
    }
  };

  return (
    <EnterpriseFormContainer
      onValidate={validateForm}
      onSubmit={handleSubmit}
      enableAutoValidation={true}
    >
      <EnterpriseFormInput
        fieldName="title"
        label="房源标题"
        required={true}
        value={values.title}
        onValueChange={(value) => setValue('title', value)}
        error={errors.title}
        warning={warnings.title}
        placeholder="请输入房源标题"
        hint="标题至少需要8个字符"
      />

      <EnterpriseFormInput
        fieldName="description"
        label="房源描述"
        required={true}
        value={values.description}
        onValueChange={(value) => setValue('description', value)}
        error={errors.description}
        placeholder="请详细描述房源..."
        multiline={true}
        numberOfLines={6}
        hint="详细描述有助于提高房源吸引力"
      />

      <EnterpriseFormInput
        fieldName="area"
        label="建筑面积"
        required={true}
        value={values.area.toString()}
        onValueChange={(value) => setValue('area', parseFloat(value) || 0)}
        error={errors.area}
        placeholder="请输入面积"
        keyboardType="numeric"
        hint="单位：平方米"
      />
    </EnterpriseFormContainer>
  );
};
```

## 最佳实践

### 1. 验证规则设计
- 优先使用内置验证规则
- 复杂业务逻辑使用`custom`验证
- 错误信息要清晰明确

### 2. 性能优化
- 避免在render中创建新的验证规则对象
- 使用`useMemo`缓存验证配置
- 合理设置防抖延迟

### 3. 用户体验
- 提供清晰的错误提示
- 使用hint文本指导用户
- 合理的必填字段标识

### 4. 错误处理
- 区分错误和警告
- 提供补救措施的提示
- 避免过于严格的验证

## 技术架构

### 依赖关系
```
EnterpriseFormContainer
├── useKeyboardAwareForm (键盘管理)
├── EnterpriseFormInput (输入组件)
└── useEnterpriseFormValidation (验证逻辑)
    ├── useState (状态管理)
    ├── useCallback (性能优化)
    └── useMemo (缓存优化)
```

### 数据流
```
用户输入 → setValue → 防抖验证 → 更新错误状态 → UI重新渲染
                ↓
             实时验证 → 错误动画 → 自动滚动
```

## 总结

这套企业级表单组件系统已经集成到房地产应用的房源发布功能中，显著提升了用户体验：

1. **8个字符标题验证** - 提高房源标题质量
2. **租金支付方式选择** - 完整的业务流程支持  
3. **红色错误提示** - 清晰的视觉反馈
4. **自动滚动定位** - 快速找到需要修改的字段
5. **键盘响应布局** - 确保输入框始终可见
6. **企业级架构** - 可复用、可维护、高性能

该系统已经可以应用到其他表单页面，形成统一的用户体验标准。