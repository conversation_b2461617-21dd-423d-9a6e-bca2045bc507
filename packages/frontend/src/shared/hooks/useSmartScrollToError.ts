/**
 * 企业级智能滚动定位Hook
 * 
 * 基于主流APP最佳实践：
 * 1. 使用measure()获取绝对位置，而不是onLayout的相对位置
 * 2. 支持条件渲染字段的延迟布局记录
 * 3. 智能计算滚动位置，确保错误字段在视野中央
 * 4. 支持动画和用户体验优化
 * 
 * 参考实现：
 * - 微信表单验证滚动定位
 * - 支付宝错误字段自动定位
 * - React Native官方ScrollView最佳实践
 * 
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @since 2025-07-30
 */

import { useRef, useCallback, useState, useEffect } from 'react';
import { ScrollView, Dimensions, Keyboard, Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import FeedbackService from '../services/FeedbackService';

// ===== 类型定义 =====

interface FieldError {
  fieldName: string;
  message: string;
}

interface FieldLayout {
  y: number;
  height: number;
  measured: boolean;
}

interface ScrollToErrorOptions {
  /** 滚动动画时长 */
  animationDuration?: number;
  /** 目标字段距离屏幕顶部的偏移量 */
  topOffset?: number;
  /** 是否显示错误提示 */
  showErrorMessage?: boolean;
  /** 错误提示文案 */
  errorMessage?: string;
}

// ===== 响应式常量定义 =====

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// 🔧 简化配置，使用固定偏移量
const CONFIG = {
  TOP_OFFSET: 120, // 固定120px偏移，确保错误字段可见
  ANIMATION_DURATION: 300,
  MEASURE_RETRY_DELAY: 50,
  MAX_MEASURE_RETRIES: 10,
};

// ===== 主Hook =====

export const useSmartScrollToError = () => {
  // ===== Refs =====

  const scrollViewRef = useRef<ScrollView>(null);
  const fieldRefs = useRef<Record<string, any>>({});
  const fieldLayouts = useRef<Record<string, FieldLayout>>({});

  // ===== 响应式状态管理 =====

  const [isScrolling, setIsScrolling] = useState(false);
  const [currentScrollY, setCurrentScrollY] = useState(0);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [screenDimensions, setScreenDimensions] = useState(Dimensions.get('window'));

  // 获取安全区域
  const insets = useSafeAreaInsets();

  // ===== 响应式计算 =====

  // 🔧 简化偏移量计算
  const calculateResponsiveTopOffset = useCallback(() => {
    // 简化：使用固定偏移量，而不是复杂的响应式计算
    return CONFIG.TOP_OFFSET;
  }, []); // 移除所有依赖

  // ===== 响应式监听器 =====

  useEffect(() => {
    // 监听屏幕尺寸变化
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenDimensions(window);
      console.log(`[SmartScroll] 📱 屏幕尺寸变化:`, window);
    });

    // 监听键盘事件
    const keyboardWillShow = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event) => {
        setKeyboardHeight(event.endCoordinates.height);
        console.log(`[SmartScroll] ⌨️ 键盘弹起: ${event.endCoordinates.height}px`);
      }
    );

    const keyboardWillHide = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
        console.log(`[SmartScroll] ⌨️ 键盘隐藏`);
      }
    );

    return () => {
      subscription?.remove();
      keyboardWillShow.remove();
      keyboardWillHide.remove();
    };
  }, []);

  // ===== 核心功能：字段引用管理 =====
  
  /**
   * 注册字段引用
   */
  const registerField = useCallback((fieldName: string, ref: any) => {
    if (ref) {
      fieldRefs.current[fieldName] = ref;
      console.log(`[SmartScroll] 📝 注册字段引用: ${fieldName}`);
    }
  }, []);

  /**
   * 测量字段位置（使用measureInWindow获取绝对位置）
   */
  const measureField = useCallback(async (fieldName: string): Promise<FieldLayout | null> => {
    return new Promise((resolve) => {
      const fieldRef = fieldRefs.current[fieldName];

      if (!fieldRef) {
        console.warn(`[SmartScroll] ⚠️ 字段引用不存在: ${fieldName}`);
        resolve(null);
        return;
      }

      if (!fieldRef.measureInWindow) {
        console.warn(`[SmartScroll] ⚠️ 字段不支持measureInWindow: ${fieldName}`);
        resolve(null);
        return;
      }

      let retryCount = 0;
      
      const attemptMeasure = () => {
        // 🔧 关键修复：优先使用measureInWindow获取绝对位置
        fieldRef.measureInWindow((winX: number, winY: number, winWidth: number, winHeight: number) => {
          if (winHeight > 0) {
            // 🎯 使用measureInWindow获取的绝对位置（相对于屏幕）
            const layout: FieldLayout = {
              y: winY, // 这是相对于屏幕的绝对位置
              height: winHeight,
              measured: true,
            };

            fieldLayouts.current[fieldName] = layout;
            console.log(`[SmartScroll] 📏 测量成功(measureInWindow): ${fieldName}`, {
              y: winY,
              height: winHeight,
              width: winWidth,
              position: winY < 0 ? '屏幕上方' : winY > SCREEN_HEIGHT ? '屏幕下方' : '屏幕内'
            });
            resolve(layout);
          } else {
            // 如果measureInWindow失败，尝试measure作为备用
            fieldRef.measure((x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
              if (height > 0) {
                // 🔧 使用pageY（绝对位置）而不是y（相对位置）
                const layout: FieldLayout = {
                  y: pageY, // 使用pageY获取绝对位置
                  height,
                  measured: true,
                };

                fieldLayouts.current[fieldName] = layout;
                console.log(`[SmartScroll] 📏 测量成功(measure-pageY): ${fieldName}`, {
                  relativeY: y,
                  absoluteY: pageY,
                  height,
                  width
                });
                resolve(layout);
              } else if (retryCount < CONFIG.MAX_MEASURE_RETRIES) {
                // 重试测量
                retryCount++;
                console.log(`[SmartScroll] 🔄 重试测量: ${fieldName} (${retryCount}/${CONFIG.MAX_MEASURE_RETRIES})`);
                setTimeout(attemptMeasure, CONFIG.MEASURE_RETRY_DELAY);
              } else {
                // 测量失败
                console.error(`[SmartScroll] ❌ 测量失败: ${fieldName}`);
                resolve(null);
              }
            });
          }
        });
      };

      attemptMeasure();
    });
  }, []);

  /**
   * 批量测量所有字段
   */
  const measureAllFields = useCallback(async (fieldNames: string[]): Promise<void> => {
    console.log(`[SmartScroll] 📏 开始批量测量字段:`, fieldNames);
    
    const measurePromises = fieldNames.map(fieldName => measureField(fieldName));
    await Promise.all(measurePromises);
    
    console.log(`[SmartScroll] ✅ 批量测量完成`);
  }, [measureField]);

  // ===== 核心功能：智能滚动 =====
  
  /**
   * 计算最佳滚动位置 - 简化版本
   */
  const calculateScrollPosition = useCallback((fieldLayout: FieldLayout, topOffset: number, currentScrollY: number = 0): number => {
    const fieldY = fieldLayout.y;
    const fieldHeight = fieldLayout.height;

    let targetY: number;

    if (fieldY < 0) {
      // 🔧 字段在屏幕上方：向上滚动
      // 如果字段在上方-153px，我们需要向上滚动153px + topOffset
      targetY = Math.max(0, currentScrollY + fieldY - topOffset);
    } else if (fieldY > SCREEN_HEIGHT - topOffset) {
      // 🔧 字段在屏幕下方：向下滚动
      targetY = currentScrollY + fieldY - topOffset;
    } else {
      // 🔧 字段在屏幕内：微调到合适位置
      targetY = currentScrollY + fieldY - topOffset;
    }

    // 确保不会滚动到负数位置
    targetY = Math.max(0, targetY);

    return targetY;
  }, []);

  /**
   * 滚动到指定字段 - 响应式版本
   */
  const scrollToField = useCallback(async (
    fieldName: string,
    options: ScrollToErrorOptions = {}
  ): Promise<boolean> => {
    const {
      animationDuration = CONFIG.ANIMATION_DURATION,
    } = options;

    // 🎯 使用响应式计算的偏移量，而不是固定值
    const responsiveTopOffset = calculateResponsiveTopOffset();

    console.log(`[SmartScroll] 🎯 开始滚动到字段: ${fieldName}`);

    if (!scrollViewRef.current) {
      console.error(`[SmartScroll] ❌ ScrollView引用不存在`);
      return false;
    }

    setIsScrolling(true);

    try {
      // 1. 测量字段位置
      const fieldLayout = await measureField(fieldName);

      if (!fieldLayout) {
        console.error(`[SmartScroll] ❌ 无法获取字段布局: ${fieldName}`);
        return false;
      }

      // 2. 🎯 响应式滚动策略：基于绝对位置计算滚动目标
      let targetY: number;
      const submitButtonSpace = 100; // 固定发布按钮空间
      const keyboardVisibleHeight = screenDimensions.height - keyboardHeight - insets.top - insets.bottom;

      // 🔧 关键修复：现在fieldLayout.y是绝对位置（相对于屏幕）
      // 需要转换为相对于ScrollView的滚动位置

      if (fieldLayout.y >= 0) {
        // fieldLayout.y是绝对位置，需要计算滚动目标
        // 目标：让字段显示在距离屏幕顶部responsiveTopOffset的位置
        const desiredScreenPosition = insets.top + responsiveTopOffset;
        targetY = Math.max(0, fieldLayout.y - desiredScreenPosition + currentScrollY);

        // 🔧 键盘弹起时的特殊处理：确保字段在发布按钮上方
        if (keyboardHeight > 0) {
          // 重新计算：字段应该显示在键盘上方的可见区域内
          const keyboardTop = screenDimensions.height - keyboardHeight;
          const maxFieldBottom = keyboardTop - submitButtonSpace - 20; // 20px缓冲
          const desiredFieldTop = maxFieldBottom - fieldLayout.height;

          // 如果当前目标位置会让字段被遮挡，重新计算
          const fieldTopAfterScroll = fieldLayout.y - targetY;
          if (fieldTopAfterScroll > desiredFieldTop) {
            const adjustment = fieldTopAfterScroll - desiredFieldTop;
            targetY += adjustment;
            console.log(`[SmartScroll] 🔧 键盘模式调整滚动: +${adjustment}px`);
          }
        }

        console.log(`[SmartScroll] 📍 绝对位置滚动计算（考虑发布按钮）:`, {
          fieldAbsoluteY: fieldLayout.y,
          fieldHeight: fieldLayout.height,
          currentScrollY,
          responsiveOffset: responsiveTopOffset,
          desiredScreenPosition,
          keyboardHeight,
          submitButtonSpace,
          keyboardVisibleHeight,
          targetY,
          fieldWillBeAt: fieldLayout.y - targetY,
        });
      } else {
        // 字段在屏幕上方（负坐标）
        const distanceAboveScreen = Math.abs(fieldLayout.y);
        targetY = Math.max(0, currentScrollY - distanceAboveScreen - responsiveTopOffset);

        // 🔧 键盘弹起时确保不会滚动过头
        if (keyboardHeight > 0) {
          const maxScroll = currentScrollY + (keyboardVisibleHeight - submitButtonSpace) * 0.3; // 最多滚动30%
          targetY = Math.min(targetY, maxScroll);
        }

        console.log(`[SmartScroll] 🔼 屏幕上方字段滚动（考虑发布按钮）:`, {
          fieldAbsoluteY: fieldLayout.y,
          distanceAbove: distanceAboveScreen,
          responsiveOffset: responsiveTopOffset,
          currentScrollY,
          keyboardHeight,
          submitButtonSpace,
          targetY,
        });
      }

      // 3. 执行滚动
      scrollViewRef.current.scrollTo({
        y: targetY,
        animated: true,
      });

      console.log(`[SmartScroll] ✅ 滚动执行成功: ${fieldName} -> ${targetY}px`);

      // 5. 等待滚动动画完成
      await new Promise(resolve => setTimeout(resolve, animationDuration));

      return true;

    } catch (error) {
      console.error(`[SmartScroll] ❌ 滚动失败: ${fieldName}`, error);
      return false;
    } finally {
      setIsScrolling(false);
    }
  }, [measureField, calculateResponsiveTopOffset, currentScrollY, screenDimensions, keyboardHeight, insets]);

  /**
   * 滚动到第一个错误字段 - 响应式版本
   */
  const scrollToFirstError = useCallback(async (
    errors: FieldError[],
    options: ScrollToErrorOptions = {}
  ): Promise<boolean> => {
    const {
      showErrorMessage = true,
      errorMessage = '请完善标记为红色的必填信息',
    } = options;

    if (!errors.length) {
      console.log(`[SmartScroll] ℹ️ 没有错误字段，无需滚动`);
      return true;
    }

    console.log(`[SmartScroll] 🔍 开始处理错误字段:`, errors.map(e => e.fieldName));

    try {
      // 1. 批量测量所有错误字段
      await measureAllFields(errors.map(e => e.fieldName));

      // 2. 按位置排序，找到最上方的错误字段
      const sortedErrors = errors
        .map(error => ({
          ...error,
          layout: fieldLayouts.current[error.fieldName],
        }))
        .filter(error => error.layout && error.layout.measured)
        .sort((a, b) => (a.layout?.y || 0) - (b.layout?.y || 0));

      if (!sortedErrors.length) {
        console.warn(`[SmartScroll] ⚠️ 没有可测量的错误字段`);
        return false;
      }

      const firstError = sortedErrors[0];
      console.log(`[SmartScroll] 🎯 第一个错误字段: ${firstError.fieldName}`);

      // 3. 滚动到第一个错误字段
      const success = await scrollToField(firstError.fieldName, options);

      // 4. 显示错误提示
      if (success && showErrorMessage) {
        FeedbackService.showError(errorMessage);
      }

      return success;

    } catch (error) {
      console.error(`[SmartScroll] ❌ 滚动到错误字段失败:`, error);
      return false;
    }
  }, [measureAllFields, scrollToField]);

  // ===== 工具函数 =====

  /**
   * 清除所有字段布局缓存
   */
  const clearFieldLayouts = useCallback(() => {
    fieldLayouts.current = {};
    console.log(`[SmartScroll] 🗑️ 字段布局缓存已清除`);
  }, []);

  /**
   * 获取字段布局信息（调试用）
   */
  const getFieldLayout = useCallback((fieldName: string): FieldLayout | null => {
    return fieldLayouts.current[fieldName] || null;
  }, []);

  /**
   * 更新当前滚动位置
   */
  const updateScrollPosition = useCallback((scrollY: number) => {
    setCurrentScrollY(scrollY);
  }, []);

  // ===== 返回接口 =====

  return {
    // ScrollView引用
    scrollViewRef,

    // 字段管理
    registerField,
    measureField,
    measureAllFields,

    // 滚动功能
    scrollToField,
    scrollToFirstError,

    // 状态
    isScrolling,

    // 工具函数
    clearFieldLayouts,
    getFieldLayout,
    updateScrollPosition,
  };
};

export default useSmartScrollToError;
