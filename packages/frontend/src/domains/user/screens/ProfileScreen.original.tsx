/**
 * 个人中心页面
 * 包含用户信息、功能菜单、退出登录等
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,


} from 'react-native';
import FeedbackService from '../../../shared/services/FeedbackService';
import { Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '../../auth/services/authStore'; // 直接导入避免路径解析问题
import { useAuth } from '../../../contexts/AuthContext'; // ✅ 导入AuthContext用于正确的退出登录
import { SmartLogoutModal } from '../components/SmartLogoutModal';
import { TenantBuyerStatsCard } from '../components/TenantBuyerStatsCard';
import { LandlordStatsCard } from '../components/LandlordStatsCard';
// 所有子页面现在通过React Navigation管理，不需要直接导入
import { LayoutFactory, useLayoutPerformance } from '@shared';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { RootStackParamList } from '../../../navigation/types';
import { useQuery } from '@tanstack/react-query';
// 导入自定义头像组件
import { UserAvatar } from '../components/UserAvatar';
// 导入订单系统Hook
import { useOrder } from '../../../contexts/OrderContext';
// 导入企业级响应式工具函数
import {
  wp,
  hp,
  fp,
  spacing,
  fontSize,
  borderRadius,


} from '@shared/utils/responsiveUtils';

// 个人中心页面的Props接口定义
interface ProfileScreenProps {
  onShowOneClickLogin?: () => void; // 显示一键登录弹窗的回调函数（可选）
}

const ProfileScreen: React.FC<ProfileScreenProps> = ({
  onShowOneClickLogin, // 显示一键登录弹窗的回调函数（可选）
}) => {
  // 从认证状态管理中获取用户信息和相关方法
  const { user } = useAuthStore();
  const { signOut } = useAuth(); // ✅ 使用AuthContext的signOut方法，确保浮动登录栏正确恢复
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  
  // 获取订单状态
  const { state: orderState } = useOrder();

  // 企业级布局性能监控
  useLayoutPerformance('Profile');

  // 组件焦点状态监控
  React.useEffect(() => {
    console.log('[ProfileScreen] 👤 个人中心页面已挂载');
    return () => {
      console.log('[ProfileScreen] 👋 个人中心页面已卸载');
    };
  }, []);

  console.log('[ProfileScreen] 👤 组件渲染 - 用户状态:', !!user);

  // 页面状态管理
  const [_showQuickLogin, _setShowQuickLogin] = useState(false); // 是否显示一键登录弹窗
  const [showSmartLogout, setShowSmartLogout] = useState(false); // 是否显示智能退出弹窗
  const [currentScreen, setCurrentScreen] = useState<
    'profile' | 'settings' | 'account' | 'device-management' | 'security-settings' | 'role-verification' | 'feedback' | 'notifications' | 'personalization'
  >('profile'); // 当前显示的页面

  // 监听页面焦点，当用户从其他底部导航栏选项返回时，自动返回到主页面
  useFocusEffect(
    React.useCallback(() => {
      // 当页面获得焦点时，重置到主页面
      console.log('[ProfileScreen] 📱 页面获得焦点，当前屏幕:', currentScreen);
      if (currentScreen !== 'profile') {
        console.log('[ProfileScreen] 🔄 自动返回到用户中心主页');
        setCurrentScreen('profile');
      }
    }, [currentScreen])
  );

  // 检查用户是否已登录 - 根据user对象是否存在判断
  const isLoggedIn = !!user;

  // 获取租买需求统计数据 - 简化版
  const { data: tenantBuyerStats } = useQuery({
    queryKey: ['tenant-buyer-stats'],
    queryFn: async () => {
      if (!isLoggedIn) return null;
      // TODO: 实际API调用
      return {
        rentalDemands: 1,
        purchaseDemands: 2,
        interestedProperties: 15,
      };
    },
    enabled: isLoggedIn,
  });

  // 获取业主房源统计数据 - 简化版
  const { data: landlordStats } = useQuery({
    queryKey: ['landlord-stats'],
    queryFn: async () => {
      if (!isLoggedIn) return null;
      // TODO: 实际API调用
      return {
        rentalProperties: 2,
        saleProperties: 1,
        totalFavorites: 15,
        totalInquiries: 25,
      };
    },
    enabled: isLoggedIn,
  });

  // ✅ 确认退出登录的异步处理函数 - 使用AuthContext确保浮动登录栏正确恢复
  const handleLogoutConfirm = async () => {
    try {
      signOut(); // ✅ 使用AuthContext的signOut方法，确保浮动登录栏正确恢复
      setShowSmartLogout(false); // 关闭智能退出弹窗

      console.log('[ProfileScreen] 退出登录成功，浮动登录栏应该已恢复显示');

      // 延迟显示一键登录弹窗 - 给用户500ms的过渡时间
      setTimeout(() => {
        _setShowQuickLogin(true);
      }, 500);
    } catch (error) {
      console.error('退出登录失败:', error); // 记录错误日志
      FeedbackService.showError('退出登录失败，请重试'); // 显示错误提示
    }
  };

  // 休眠模式处理函数 - 用户选择休眠而非完全退出
  const handleSleepMode = () => {
    FeedbackService.showInfo('您的账号已进入休眠状态，将暂停推送通知，但保留所有数据。您可以随时在设置中恢复。');
  };

  // 个性化推荐设置处理函数 - 跳转到设置页面
  const handleCustomizeRecommendations = () => {
    navigation.navigate('Settings'); // 切换到设置页面
  };

  // 禁用通知处理函数 - 关闭推送通知
  const handleDisableNotifications = () => {
    FeedbackService.showInfo('已为您关闭推送通知，您可以在设置中重新开启。');
  };

  // 处理需要登录的功能点击
  const handleRequireLoginAction = (actionName: string) => {
    if (!isLoggedIn) {
      // 访客用户点击任何功能都弹出一键登录
      if (onShowOneClickLogin) {
        onShowOneClickLogin();
      } else {
        _setShowQuickLogin(true);
      }
      return;
    }

    // 已登录用户的功能处理
    if (actionName === '设置') {
      navigation.navigate('Settings');
    } else if (actionName === '求租资源') {
      (navigation as any).navigate('TenantResource');
    } else if (actionName === '我的订单') {
      // TODO: 导航到订单页面
      console.log('导航到我的订单页面');
    } else if (actionName === '我的收藏') {
      // TODO: 导航到收藏页面
      console.log('导航到我的收藏页面');
    } else if (actionName === '预约看房') {
      // TODO: 导航到预约看房页面
      console.log('导航到预约看房页面');
    } else {
      console.log(`执行功能: ${actionName}`);
    }
  };

  // 页面导航处理
  const handleSettings = () => {
    navigation.navigate('Settings');
  };

  // ✅ handleSmartLogout 已移除，退出登录功能通过设置页面访问

  // 处理统计卡片点击
  const handleStatsCardPress = (cardType: 'tenant_buyer' | 'landlord') => {
    if (!isLoggedIn) {
      if (onShowOneClickLogin) {
        onShowOneClickLogin();
      } else {
        _setShowQuickLogin(true);
      }
      return;
    }

    if (cardType === 'tenant_buyer') {
      (navigation as any).navigate('MyDemands');
    } else {
      (navigation as any).navigate('MyProperties');
    }
  };

  // 处理具体统计项点击
  const handleStatsItemPress = (cardType: 'tenant_buyer' | 'landlord', itemType: string) => {
    console.log(`点击${cardType}的${itemType}统计项`);
    // TODO: 导航到对应的详细页面
  };

  // 处理新人引导
  const handleShowOnboarding = () => {
    // 先导航到首页
    (navigation as any).reset({
      index: 0,
      routes: [{ name: 'MainTabs', params: { screen: 'Home' } }],
    });
    
    // 延迟导航到新人引导屏幕，确保首页完全加载
    setTimeout(() => {
      (navigation as any).navigate('Onboarding');
    }, 500);
  };

  // 删除了未使用的登录相关函数

  // 功能菜单项
  const menuItems = [
    {
      id: 'favorites',
      title: '我的收藏',
      useNumber: true, // 使用纯数字显示
      number: isLoggedIn ? orderState.favoriteCount : 0,
      color: '#333333', // 默认黑色
      activeColor: '#FF6B35', // 有变化时橙色
      onPress: () => handleRequireLoginAction('我的收藏'),
    },
    {
      id: 'appointments',
      title: '预约看房',
      useNumber: true, // 使用纯数字显示
      number: isLoggedIn ? orderState.appointmentCount : 0,
      color: '#333333', // 默认黑色
      activeColor: '#FF6B35', // 有变化时橙色
      onPress: () => handleRequireLoginAction('预约看房'),
    },
    {
      id: 'demands',
      title: '我的订单',
      useNumber: true, // 使用纯数字显示
      number: isLoggedIn ? orderState.orderCount : 0,
      color: '#333333', // 默认黑色
      activeColor: '#FF6B35', // 有变化时橙色
      onPress: () => handleRequireLoginAction('我的订单'),
    },
    {
      id: 'tenant_resources',
      title: '求租资源',
      useNumber: true, // 使用纯数字显示
      number: isLoggedIn ? orderState.tenantResourceCount : 0,
      color: '#333333', // 默认黑色
      activeColor: '#FF6B35', // 有变化时橙色
      onPress: () => handleRequireLoginAction('求租资源'),
    },
  ];

  // 更多功能菜单
  const moreMenuItems = [
    {
      id: 'history',
      title: '浏览记录',
      icon: 'time-outline',
      color: '#ff4f19',
      onPress: () => handleRequireLoginAction('浏览记录'),
    },
    {
      id: 'onboarding',
      title: '新人引导',
      icon: 'school-outline',
      color: '#ff4f19',
      onPress: handleShowOnboarding, // 新人引导不需要登录
    },
    {
      id: 'rewards',
      title: '兑换中心',
      icon: 'gift-outline',
      color: '#ff4f19',
      onPress: () => handleRequireLoginAction('兑换中心'),
    },
    {
      id: 'evaluation',
      title: '选址评估',
      icon: 'location-outline',
      color: '#ff4f19',
      onPress: () => handleRequireLoginAction('选址评估'),
    },
    {
      id: 'calculator',
      title: '费用评估',
      icon: 'calculator-outline',
      color: '#ff4f19',
      onPress: () => handleRequireLoginAction('费用评估'),
    },
    {
      id: 'feedback',
      title: '意见反馈',
      icon: 'chatbubble-outline',
      color: '#ff4f19',
      onPress: () => console.log('意见反馈'), // 意见反馈不需要登录
    },
    {
      id: 'help',
      title: '常见问题',
      icon: 'help-circle-outline',
      color: '#ff4f19',
      onPress: () => console.log('常见问题'), // 常见问题不需要登录
    },
  ];

  // 所有子页面现在使用React Navigation管理，不再需要条件渲染

  return (
    <LayoutFactory
      pageType="Profile"
      statusBarStyle="dark-content"
      backgroundColor="#FFFFFF"
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 用户信息栏 - 2025年无边框设计 */}
        <View style={styles.userSection}>
          <View style={styles.userInfo}>
            <View style={styles.avatarContainer}>
              {/* 使用自定义头像组件替换原有的Image */}
              <UserAvatar
                userId={isLoggedIn ? user?.id || user?.phone_number || 'guest' : 'guest'}
                userPhone={isLoggedIn ? user?.phone_number : undefined}
                userRole={isLoggedIn ? 'tenant' : undefined} // 默认为租户角色
                size={120}
                onPress={() => {
                  console.log('头像被点击 - 可以添加头像选择功能');
                }}
              />
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>
                {isLoggedIn ? `用户${user.phone_number?.slice(-4) || '****'}` : '访客用户'}
              </Text>
              <Text style={styles.userType}>
                {isLoggedIn ? '商业地产租户' : '点击登录享受更多服务'}
              </Text>
              <Text style={styles.userPhone}>
                手机：
                {isLoggedIn && user.phone_number
                  ? `${user.phone_number.slice(
                      0,
                      3
                    )}****${user.phone_number.slice(-4)}`
                  : '未绑定'}
              </Text>
            </View>
          </View>

          {/* 右上角设置和客服 */}
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => {
                if (isLoggedIn) {
                  handleSettings();
                } else {
                  handleRequireLoginAction('设置');
                }
              }}
            >
              <Ionicons name="settings-outline" size={wp(24)} color="#666666" />
              <Text style={styles.actionText}>设置</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="headset-outline" size={wp(24)} color="#666666" />
              <Text style={styles.actionText}>客服</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 核心功能区 - 去掉白色容器，缩小间距 */}
        <View style={styles.menuSection}>
          {menuItems.map(item => (
            <TouchableOpacity
              key={item.id}
              style={styles.menuItem}
              onPress={item.onPress}
            >
              <View style={styles.iconContainer}>
                {(item as any).useNumber ? (
                  // 显示纯数字，有数量时使用激活颜色
                  <Text style={[
                    styles.numberText, 
                    { color: (item as any).number > 0 ? (item as any).activeColor : item.color }
                  ]}>
                    {(item as any).number}
                  </Text>
                ) : (
                  // 显示图标
                  <Ionicons
                    name={(item as any).icon}
                    size={28}
                    color={item.color}
                  />
                )}
                {(item as any).badge && (
                  <View style={styles.badge}>
                    <Text style={styles.badgeText}>{(item as any).badge}</Text>
                  </View>
                )}
              </View>
              <Text style={[
                styles.menuText,
                (item as any).useNumber && (item as any).number > 0 
                  ? { color: (item as any).activeColor }
                  : { color: item.color }
              ]}>{item.title}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* 双角色统计卡片区域 */}
        {isLoggedIn && (
          <View style={styles.statsSection}>
            {/* 租买需求统计卡片 */}
            {tenantBuyerStats && (
              <TenantBuyerStatsCard
                stats={tenantBuyerStats}
                onCardPress={() => handleStatsCardPress('tenant_buyer')}
                onStatsPress={(itemType) => handleStatsItemPress('tenant_buyer', itemType)}
              />
            )}

            {/* 业主服务统计卡片 */}
            {landlordStats && (
              <LandlordStatsCard
                stats={landlordStats}
                onCardPress={() => handleStatsCardPress('landlord')}
                onStatsPress={(itemType) => handleStatsItemPress('landlord', itemType)}
              />
            )}
          </View>
        )}

        {/* 更多功能 */}
        <View style={styles.moreSection}>
          <Text style={styles.sectionTitle}>更多功能</Text>
          <View style={styles.moreGrid}>
            {moreMenuItems.map(item => (
              <TouchableOpacity
                key={item.id}
                style={styles.moreItem}
                onPress={item.onPress}
              >
                <Ionicons
                  name={item.icon as any}
                  size={32}
                  color={item.color}
                />
                <Text style={styles.moreItemText}>{item.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 移除明显的退出登录按钮，改为在设置中深层访问 */}

        {/* 底部空白区域 */}
        <View style={styles.bottomSpace} />
      </ScrollView>

      {/* 智能退出引导弹窗 */}
      <SmartLogoutModal
        visible={showSmartLogout}
        onClose={() => setShowSmartLogout(false)}
        onConfirmLogout={handleLogoutConfirm}
        onSleepMode={handleSleepMode}
        onCustomizeRecommendations={handleCustomizeRecommendations}
        onDisableNotifications={handleDisableNotifications}
      />

    </LayoutFactory>
  );
};

// 企业级响应式设计样式 - 2025年极简设计
// 定义统一的模块间距 - 原间距的二分之一
const UNIFIED_MODULE_SPACING = spacing.md / 2; // wp(8) - 统一模块间距

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF', // 纯白色背景
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#FFFFFF', // 滚动视图也是白色背景
  },
  // 用户信息栏 - 2025无边框设计
  userSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: spacing.lg, // wp(24) - 企业级响应式间距
    paddingVertical: spacing.lg, // wp(24)
    marginBottom: UNIFIED_MODULE_SPACING, // 统一模块间距
    // 移除底部边框 - 2025年无边框设计
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    width: wp(70), // 响应式头像容器
    height: wp(70),
    borderRadius: wp(35),
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  avatar: {
    width: wp(66), // 响应式头像尺寸
    height: wp(66),
    borderRadius: wp(33),
  },
  userDetails: {
    marginLeft: spacing.md, // wp(16) - 企业级间距
    flex: 1,
  },
  userName: {
    fontSize: fontSize.lg, // fp(18) - 响应式字体
    fontWeight: '600',
    color: '#333333',
    marginBottom: spacing.xs / 2, // wp(2)
  },
  userType: {
    fontSize: fontSize.sm, // fp(14)
    color: '#666666',
    marginBottom: spacing.xs / 4, // wp(1)
  },
  userPhone: {
    fontSize: fontSize.xs, // fp(12)
    color: '#999999',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md, // wp(16) - 响应式间隙
  },
  actionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.sm, // wp(8)
    minWidth: wp(44), // Material Design最小触摸区域
    minHeight: wp(44),
  },
  actionText: {
    fontSize: fontSize.xs, // fp(12)
    color: '#666666',
    marginTop: spacing.xs / 2, // wp(2)
  },
  menuSection: {
    // 去掉白色容器背景，直接使用网格布局
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg, // wp(24) - 保持水平内边距
    marginBottom: UNIFIED_MODULE_SPACING, // 统一模块间距
  },
  menuItem: {
    width: '22%', // 更合理的宽度分配
    alignItems: 'center',
    paddingVertical: spacing.sm, // wp(8)
    minHeight: wp(44), // Material Design触摸标准
  },
  iconContainer: {
    position: 'relative',
    marginBottom: spacing.sm, // wp(8)
  },
  badge: {
    position: 'absolute',
    top: -wp(6),
    right: -wp(6),
    backgroundColor: '#FF4444',
    borderRadius: borderRadius.full, // 圆形徽章
    minWidth: wp(20),
    height: wp(20),
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xs / 2, // wp(2)
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: fontSize.xs, // fp(12)
    fontWeight: '600',
  },
  menuText: {
    fontSize: fontSize.xs, // fp(12)
    color: '#333333', // 默认黑色
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: fp(16), // 响应式行高
  },
  numberText: {
    fontSize: fontSize['2xl'], // fp(24) - 大号数字
    fontWeight: 'bold',
    textAlign: 'center',
    minWidth: 28, // 确保与图标大小一致
    minHeight: 28,
    textAlignVertical: 'center',
  },
  moreSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: spacing.md, // wp(16)
    paddingVertical: spacing.md, // wp(16)
    marginBottom: UNIFIED_MODULE_SPACING, // 统一模块间距
    // 移除负边距，避免样式冲突
    marginTop: 0,
    borderRadius: borderRadius.md, // rp(12)
    marginHorizontal: spacing.md, // wp(16)
  },
  sectionTitle: {
    fontSize: fontSize.base, // fp(16)
    fontWeight: '600',
    color: '#333333',
    marginBottom: spacing.md, // wp(16)
    paddingHorizontal: spacing.xs, // wp(4)
  },
  moreGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  moreItem: {
    width: '30%',
    backgroundColor: '#F8F9FA',
    borderRadius: borderRadius.md, // rp(12)
    paddingVertical: spacing.md, // wp(16)
    paddingHorizontal: spacing.sm, // wp(8)
    alignItems: 'center',
    marginBottom: spacing.md, // wp(16)
    minHeight: wp(80), // 保证触摸区域
  },
  moreItemText: {
    fontSize: fontSize.sm, // fp(14)
    color: '#333333',
    marginTop: spacing.sm, // wp(8)
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: fp(18), // 响应式行高
  },
  logoutSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: spacing.md, // wp(16)
    borderRadius: borderRadius.md, // rp(12)
    marginBottom: UNIFIED_MODULE_SPACING, // 统一模块间距
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md, // wp(16)
    paddingHorizontal: spacing.lg, // wp(24)
    minHeight: wp(44), // Material Design最小触摸区域
  },
  logoutText: {
    color: '#FF4444',
    fontSize: fontSize.base, // fp(16)
    fontWeight: '600',
    marginLeft: spacing.sm, // wp(8)
  },
  bottomSpace: {
    height: hp(100), // 响应式底部间距
  },

  // 统计卡片区域
  statsSection: {
    // 强制减少与下方模块的间距
    marginBottom: UNIFIED_MODULE_SPACING / 2,
  },
});

export { ProfileScreen };
