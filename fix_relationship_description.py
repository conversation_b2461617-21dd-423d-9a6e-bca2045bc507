#!/usr/bin/env python3
"""
修复SQLModel Relationship中的description参数
SQLModel的Relationship不支持description参数，需要移除
"""

import os
import re
import glob

def fix_relationship_description(file_path):
    """修复单个文件中的Relationship description参数"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 匹配Relationship中的description参数
    # 模式1: description="xxx"
    pattern1 = r',\s*description="[^"]*"'
    content = re.sub(pattern1, '', content)
    
    # 模式2: description='xxx'
    pattern2 = r',\s*description=\'[^\']*\''
    content = re.sub(pattern2, '', content)
    
    # 模式3: 单独一行的description
    pattern3 = r'\s*description="[^"]*",?\s*\n'
    content = re.sub(pattern3, '\n', content)
    
    pattern4 = r'\s*description=\'[^\']*\',?\s*\n'
    content = re.sub(pattern4, '\n', content)
    
    # 如果内容有变化，写回文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Fixed: {file_path}")
        return True
    return False

def main():
    """主函数"""
    model_files = glob.glob('packages/backend/app/models/**/*.py', recursive=True)
    
    fixed_count = 0
    for file_path in model_files:
        if fix_relationship_description(file_path):
            fixed_count += 1
    
    print(f"Total files fixed: {fixed_count}")

if __name__ == "__main__":
    main()
