"""
房源咨询服务

基于企业级消息系统的核心业务逻辑：
1. 免费名额管理（每套房3个免费咨询）
2. 付费验证机制
3. 消息分类和状态管理
4. 业主查看权限控制
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlmodel import select, func
from sqlmodel.ext.asyncio.session import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.models.property_inquiry import (
    PropertyInquiry, PropertyFreeSlotQuota, 
    InquiryType, InquiryStatus
)
from app.models.sms_credit import SMSCreditAccount
from app.models.user import User
from app.models.property.property import Property
from app.services.notification_service import NotificationService
from app.core.db import get_async_session
from app.core.config import settings

import logging
logger = logging.getLogger(__name__)


class PropertyInquiryService:
    """房源咨询服务"""
    
    def __init__(self, db: AsyncSession, notification_service: NotificationService):
        self.db = db
        self.notification_service = notification_service
    
    async def create_inquiry(
        self,
        property_id: UUID,
        tenant_id: UUID,
        message_content: str,
        inquiry_type: InquiryType = InquiryType.MESSAGED
    ) -> Dict[str, Any]:
        """
        创建房源咨询记录
        
        Args:
            property_id: 房源ID
            tenant_id: 租客ID
            message_content: 咨询内容
            inquiry_type: 咨询类型
            
        Returns:
            Dict: 创建结果
        """
        try:
            # 获取房源信息
            property_stmt = select(Property).where(Property.id == property_id)
            property_result = await self.db.exec(property_stmt)
            property_obj = property_result.first()

            if not property_obj:
                return {
                    "success": False,
                    "error": "房源不存在",
                    "code": "PROPERTY_NOT_FOUND"
                }

            landlord_id = property_obj.owner_id
            
            # 检查是否为自己的房源
            if landlord_id == tenant_id:
                return {
                    "success": False,
                    "error": "不能咨询自己的房源",
                    "code": "SELF_INQUIRY_NOT_ALLOWED"
                }
            
            # 检查是否已经咨询过
            existing_inquiry_result = await self.db.exec(
                select(PropertyInquiry)
                .where(
                    PropertyInquiry.property_id == property_id,
                    PropertyInquiry.tenant_id == tenant_id,
                    PropertyInquiry.inquiry_type == inquiry_type
                )
            )
            existing_inquiry = existing_inquiry_result.first()
            
            if existing_inquiry:
                return {
                    "success": False,
                    "error": "您已经咨询过这套房源",
                    "code": "INQUIRY_ALREADY_EXISTS",
                    "existing_inquiry_id": existing_inquiry.id
                }
            
            # 获取或创建免费名额配额
            quota = await self._get_or_create_quota(property_id)
            
            # 检查免费名额
            is_free_slot = quota.has_free_slots()
            free_slot_number = None
            requires_payment = not is_free_slot
            
            if is_free_slot:
                # 使用免费名额
                if quota.use_slot():
                    free_slot_number = quota.used_slots
                    if quota.first_inquiry_at is None:
                        quota.first_inquiry_at = datetime.utcnow()
                    self.db.add(quota)
                else:
                    # 名额已用完
                    is_free_slot = False
                    requires_payment = True
            
            # 创建咨询记录
            inquiry = PropertyInquiry(
                property_id=property_id,
                tenant_id=tenant_id,
                landlord_id=landlord_id,
                inquiry_type=inquiry_type,
                message_content=message_content,
                is_free_slot=is_free_slot,
                free_slot_number=free_slot_number,
                requires_payment=requires_payment,
                status=InquiryStatus.PENDING
            )
            
            self.db.add(inquiry)
            await self.db.commit()
            await self.db.refresh(inquiry)
            
            # 发送通知给业主
            await self._send_inquiry_notification(inquiry)
            
            return {
                "success": True,
                "inquiry_id": inquiry.id,
                "is_free_slot": is_free_slot,
                "requires_payment": requires_payment,
                "free_slot_number": free_slot_number,
                "remaining_slots": quota.remaining_slots
            }
            
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"创建咨询记录失败: {str(e)}")
            return {
                "success": False,
                "error": "数据库操作失败",
                "code": "DATABASE_ERROR"
            }
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建咨询记录异常: {str(e)}")
            return {
                "success": False,
                "error": "系统内部错误",
                "code": "INTERNAL_ERROR"
            }
    
    async def view_inquiry(
        self,
        inquiry_id: str,
        landlord_id: UUID
    ) -> Dict[str, Any]:
        """
        业主查看咨询详情（付费验证）
        
        Args:
            inquiry_id: 咨询记录ID
            landlord_id: 业主ID
            
        Returns:
            Dict: 查看结果
        """
        try:
            # 获取咨询记录
            inquiry_result = await self.db.exec(
                select(PropertyInquiry)
                .where(PropertyInquiry.id == inquiry_id)
            )
            inquiry = inquiry_result.first()
            
            if not inquiry:
                return {
                    "success": False,
                    "error": "咨询记录不存在",
                    "code": "INQUIRY_NOT_FOUND"
                }
            
            # 验证业主权限
            if inquiry.landlord_id != landlord_id:
                return {
                    "success": False,
                    "error": "没有权限查看此咨询",
                    "code": "PERMISSION_DENIED"
                }
            
            # 检查是否需要付费
            if inquiry.requires_payment:
                payment_check = await self._check_payment_permission(landlord_id)
                if not payment_check["can_view"]:
                    return {
                        "success": False,
                        "error": "需要升级套餐才能查看此咨询",
                        "code": "PAYMENT_REQUIRED",
                        "payment_info": payment_check
                    }
            
            # 标记为已查看
            if inquiry.landlord_viewed_at is None:
                inquiry.landlord_viewed_at = datetime.utcnow()
                if inquiry.status == InquiryStatus.PENDING:
                    inquiry.status = InquiryStatus.VIEWED
                self.db.add(inquiry)
                self.db.commit()
            
            # 获取租客信息
            tenant = self.db.exec(
                select(User).where(User.id == inquiry.tenant_id)
            ).first()
            
            # 获取房源信息
            property_obj = self.db.exec(
                select(Property).where(Property.id == inquiry.property_id)
            ).first()
            
            return {
                "success": True,
                "inquiry": {
                    "id": inquiry.id,
                    "inquiry_type": inquiry.inquiry_type,
                    "message_content": inquiry.message_content,
                    "status": inquiry.status,
                    "is_free_slot": inquiry.is_free_slot,
                    "free_slot_number": inquiry.free_slot_number,
                    "created_at": inquiry.created_at.isoformat(),
                    "landlord_viewed_at": inquiry.landlord_viewed_at.isoformat() if inquiry.landlord_viewed_at else None,
                    "tenant": {
                        "id": str(tenant.id),
                        "name": tenant.name,
                        "phone": tenant.phone,
                        "avatar": tenant.avatar
                    } if tenant else None,
                    "property": {
                        "id": str(property_obj.id),
                        "title": property_obj.title,
                        "price": property_obj.price,
                        "location": property_obj.location
                    } if property_obj else None
                }
            }
            
        except Exception as e:
            logger.error(f"查看咨询详情失败: {str(e)}")
            return {
                "success": False,
                "error": "系统内部错误",
                "code": "INTERNAL_ERROR"
            }
    
    async def get_landlord_inquiries(
        self,
        landlord_id: UUID,
        property_id: Optional[UUID] = None,
        inquiry_type: Optional[InquiryType] = None,
        status: Optional[InquiryStatus] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """
        获取业主的咨询列表
        
        Args:
            landlord_id: 业主ID
            property_id: 房源ID（可选）
            inquiry_type: 咨询类型（可选）
            status: 咨询状态（可选）
            page: 页码
            page_size: 每页数量
            
        Returns:
            Dict: 咨询列表
        """
        try:
            # 构建查询条件
            stmt = select(PropertyInquiry).where(
                PropertyInquiry.landlord_id == landlord_id
            )
            
            if property_id:
                stmt = stmt.where(PropertyInquiry.property_id == property_id)
            
            if inquiry_type:
                stmt = stmt.where(PropertyInquiry.inquiry_type == inquiry_type)
            
            if status:
                stmt = stmt.where(PropertyInquiry.status == status)
            
            # 排序
            stmt = stmt.order_by(PropertyInquiry.created_at.desc())
            
            # 分页
            offset = (page - 1) * page_size
            inquiries = self.db.exec(stmt.offset(offset).limit(page_size)).all()
            
            # 获取总数
            count_stmt = select(func.count()).select_from(
                stmt.alias()
            )
            total = self.db.exec(count_stmt).first()
            
            # 组装返回数据
            inquiry_list = []
            for inquiry in inquiries:
                # 获取租客信息
                tenant = self.db.exec(
                    select(User).where(User.id == inquiry.tenant_id)
                ).first()
                
                # 获取房源信息
                property_obj = self.db.exec(
                    select(Property).where(Property.id == inquiry.property_id)
                ).first()
                
                inquiry_data = {
                    "id": inquiry.id,
                    "inquiry_type": inquiry.inquiry_type,
                    "message_content": inquiry.message_content[:100] + "..." if len(inquiry.message_content) > 100 else inquiry.message_content,
                    "status": inquiry.status,
                    "is_free_slot": inquiry.is_free_slot,
                    "requires_payment": inquiry.requires_payment,
                    "created_at": inquiry.created_at.isoformat(),
                    "landlord_viewed_at": inquiry.landlord_viewed_at.isoformat() if inquiry.landlord_viewed_at else None,
                    "tenant": {
                        "id": str(tenant.id),
                        "name": tenant.name,
                        "avatar": tenant.avatar
                    } if tenant else None,
                    "property": {
                        "id": str(property_obj.id),
                        "title": property_obj.title,
                        "price": property_obj.price,
                        "location": property_obj.location
                    } if property_obj else None
                }
                
                inquiry_list.append(inquiry_data)
            
            return {
                "success": True,
                "inquiries": inquiry_list,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total,
                    "total_pages": (total + page_size - 1) // page_size
                }
            }
            
        except Exception as e:
            logger.error(f"获取业主咨询列表失败: {str(e)}")
            return {
                "success": False,
                "error": "系统内部错误",
                "code": "INTERNAL_ERROR"
            }
    
    async def get_inquiry_stats(
        self,
        landlord_id: UUID,
        property_id: Optional[UUID] = None
    ) -> Dict[str, Any]:
        """
        获取咨询统计数据
        
        Args:
            landlord_id: 业主ID
            property_id: 房源ID（可选）
            
        Returns:
            Dict: 统计数据
        """
        try:
            # 构建基础查询
            base_stmt = select(PropertyInquiry).where(
                PropertyInquiry.landlord_id == landlord_id
            )
            
            if property_id:
                base_stmt = base_stmt.where(PropertyInquiry.property_id == property_id)
            
            # 总咨询数
            total_stmt = select(func.count()).select_from(base_stmt.alias())
            total_inquiries = self.db.exec(total_stmt).first()
            
            # 未读咨询数
            unread_stmt = select(func.count()).select_from(
                base_stmt.where(PropertyInquiry.landlord_viewed_at.is_(None)).alias()
            )
            unread_inquiries = self.db.exec(unread_stmt).first()
            
            # 按类型分组统计
            type_stats = {}
            for inquiry_type in InquiryType:
                type_stmt = select(func.count()).select_from(
                    base_stmt.where(PropertyInquiry.inquiry_type == inquiry_type).alias()
                )
                count = self.db.exec(type_stmt).first()
                type_stats[inquiry_type.value] = count
            
            # 付费咨询数
            paid_stmt = select(func.count()).select_from(
                base_stmt.where(PropertyInquiry.requires_payment == True).alias()
            )
            paid_inquiries = self.db.exec(paid_stmt).first()
            
            return {
                "success": True,
                "stats": {
                    "total_inquiries": total_inquiries,
                    "unread_inquiries": unread_inquiries,
                    "paid_inquiries": paid_inquiries,
                    "type_stats": type_stats,
                    "response_rate": self._calculate_response_rate(landlord_id),
                    "conversion_rate": self._calculate_conversion_rate(landlord_id)
                }
            }
            
        except Exception as e:
            logger.error(f"获取咨询统计失败: {str(e)}")
            return {
                "success": False,
                "error": "系统内部错误",
                "code": "INTERNAL_ERROR"
            }
    
    async def _get_or_create_quota(self, property_id: UUID) -> PropertyFreeSlotQuota:
        """获取或创建房源免费名额配额"""
        quota_result = await self.db.exec(
            select(PropertyFreeSlotQuota)
            .where(PropertyFreeSlotQuota.property_id == property_id)
        )
        quota = quota_result.first()

        if not quota:
            quota = PropertyFreeSlotQuota(
                property_id=property_id,
                total_slots=3,
                used_slots=0,
                remaining_slots=3
            )
            self.db.add(quota)
            await self.db.commit()
            await self.db.refresh(quota)

        return quota
    
    async def _check_payment_permission(self, landlord_id: UUID) -> Dict[str, Any]:
        """检查业主的付费权限"""
        # 获取用户的短信积分账户
        account_result = await self.db.exec(
            select(SMSCreditAccount)
            .where(SMSCreditAccount.user_id == landlord_id)
        )
        account = account_result.first()
        
        if not account:
            return {
                "can_view": False,
                "reason": "需要升级399元套餐查看咨询详情",
                "upgrade_url": "/subscription/upgrade"
            }
        
        # 检查是否有有效的套餐
        has_premium = account.subscription_sms_balance > 0 or account.credits_balance > 0
        
        if has_premium:
            return {
                "can_view": True,
                "reason": None
            }
        else:
            return {
                "can_view": False,
                "reason": "套餐已过期或积分不足，请续费或充值",
                "upgrade_url": "/subscription/upgrade"
            }
    
    async def _send_inquiry_notification(self, inquiry: PropertyInquiry):
        """发送咨询通知给业主"""
        try:
            # 获取业主信息
            landlord = self.db.exec(
                select(User).where(User.id == inquiry.landlord_id)
            ).first()
            
            if not landlord:
                logger.error(f"业主不存在: {inquiry.landlord_id}")
                return
            
            # 获取房源信息
            property_obj = self.db.exec(
                select(Property).where(Property.id == inquiry.property_id)
            ).first()
            
            if not property_obj:
                logger.error(f"房源不存在: {inquiry.property_id}")
                return
            
            # 通过现有通知服务发送通知
            notification_data = {
                "inquiry_id": inquiry.id,
                "property_title": property_obj.title,
                "inquiry_type": inquiry.inquiry_type.value,
                "message_preview": inquiry.message_content[:50] + "..." if len(inquiry.message_content) > 50 else inquiry.message_content
            }
            
            await self.notification_service.send_notification(
                user_id=str(inquiry.landlord_id),
                notification_type="inquiry_received",
                title="收到新的房源咨询",
                content=f"您的房源 {property_obj.title} 收到新咨询",
                data=notification_data
            )
            
        except Exception as e:
            logger.error(f"发送咨询通知失败: {str(e)}")
    
    def _calculate_response_rate(self, landlord_id: UUID) -> float:
        """计算回复率"""
        try:
            # 获取总咨询数
            total_stmt = select(func.count()).where(
                PropertyInquiry.landlord_id == landlord_id
            )
            total = self.db.exec(total_stmt).first()
            
            if total == 0:
                return 0.0
            
            # 获取已回复数
            replied_stmt = select(func.count()).where(
                PropertyInquiry.landlord_id == landlord_id,
                PropertyInquiry.status.in_([InquiryStatus.REPLIED, InquiryStatus.CONTACT_EXCHANGED])
            )
            replied = self.db.exec(replied_stmt).first()
            
            return round((replied / total) * 100, 2)
            
        except Exception:
            return 0.0
    
    def _calculate_conversion_rate(self, landlord_id: UUID) -> float:
        """计算转化率（交换联系方式）"""
        try:
            # 获取总咨询数
            total_stmt = select(func.count()).where(
                PropertyInquiry.landlord_id == landlord_id
            )
            total = self.db.exec(total_stmt).first()
            
            if total == 0:
                return 0.0
            
            # 获取已交换联系方式数
            converted_stmt = select(func.count()).where(
                PropertyInquiry.landlord_id == landlord_id,
                PropertyInquiry.contact_exchanged == True
            )
            converted = self.db.exec(converted_stmt).first()
            
            return round((converted / total) * 100, 2)
            
        except Exception:
            return 0.0