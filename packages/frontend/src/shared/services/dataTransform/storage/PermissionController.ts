/**
 * 权限控制器 - 企业级存储权限管理
 * 
 * 参考主流APP权限控制策略：
 * - 微信：用户文件隔离，敏感数据加密访问
 * - 支付宝：多层权限验证，敏感操作二次确认
 * - 银行APP：最小权限原则，细粒度权限控制
 * - 阿里云RAM：基于角色的访问控制(RBAC)
 * 
 * 核心功能：
 * - 用户文件隔离保护
 * - 敏感数据访问权限验证
 * - 基于业务类型的权限策略
 * - STS临时凭证权限控制
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月16日
 */

import { 
  IPermissionController, 
  Permission, 
  StorageConfig 
} from '../types/StorageTypes';
import { MediaBusinessType } from '../transformers/MediaTransformer';

/**
 * 用户角色类型
 */
export enum UserRole {
  GUEST = 'guest',           // 游客
  USER = 'user',             // 普通用户
  VIP = 'vip',               // VIP用户
  AGENT = 'agent',           // 经纪人
  ADMIN = 'admin',           // 管理员
  SUPER_ADMIN = 'super_admin' // 超级管理员
}

/**
 * 权限策略
 */
interface PermissionPolicy {
  businessType: MediaBusinessType;
  userRole: UserRole;
  permissions: Permission[];
  conditions?: {
    timeRange?: { start: string; end: string };
    ipWhitelist?: string[];
    maxFileSize?: number;
    allowedExtensions?: string[];
  };
}

/**
 * 权限控制器实现
 */
export class PermissionController implements IPermissionController {
  private policies: Map<string, PermissionPolicy> = new Map();
  private userRoleCache: Map<string, UserRole> = new Map();
  
  constructor() {
    this.initializeDefaultPolicies();
  }
  
  /**
   * 初始化默认权限策略
   * 参考企业级权限管理最佳实践
   */
  private initializeDefaultPolicies(): void {
    const defaultPolicies: PermissionPolicy[] = [
      // 房源图片 - 普通用户
      {
        businessType: MediaBusinessType.PROPERTY_IMAGE,
        userRole: UserRole.USER,
        permissions: ['read', 'write', 'delete'],
        conditions: {
          maxFileSize: 20 * 1024 * 1024, // 20MB
          allowedExtensions: ['jpg', 'jpeg', 'png', 'webp']
        }
      },
      
      // 房源视频 - 普通用户
      {
        businessType: MediaBusinessType.PROPERTY_VIDEO,
        userRole: UserRole.USER,
        permissions: ['read', 'write', 'delete'],
        conditions: {
          maxFileSize: 500 * 1024 * 1024, // 500MB
          allowedExtensions: ['mp4', 'mov', 'avi']
        }
      },
      
      // 用户头像 - 普通用户
      {
        businessType: MediaBusinessType.USER_AVATAR,
        userRole: UserRole.USER,
        permissions: ['read', 'write', 'delete'],
        conditions: {
          maxFileSize: 5 * 1024 * 1024, // 5MB
          allowedExtensions: ['jpg', 'jpeg', 'png']
        }
      },
      
      // 身份证 - 实名认证用户
      {
        businessType: MediaBusinessType.IDENTITY_CARD,
        userRole: UserRole.USER,
        permissions: ['read', 'write'], // 不允许删除
        conditions: {
          maxFileSize: 10 * 1024 * 1024, // 10MB
          allowedExtensions: ['jpg', 'jpeg', 'png', 'pdf']
        }
      },
      
      // 营业执照 - 企业用户/经纪人
      {
        businessType: MediaBusinessType.BUSINESS_LICENSE,
        userRole: UserRole.AGENT,
        permissions: ['read', 'write'],
        conditions: {
          maxFileSize: 10 * 1024 * 1024, // 10MB
          allowedExtensions: ['jpg', 'jpeg', 'png', 'pdf']
        }
      },
      
      // 房产证 - 房主认证
      {
        businessType: MediaBusinessType.PROPERTY_CERT,
        userRole: UserRole.USER,
        permissions: ['read', 'write'],
        conditions: {
          maxFileSize: 15 * 1024 * 1024, // 15MB
          allowedExtensions: ['jpg', 'jpeg', 'png', 'pdf']
        }
      },
      
      // 财务文档 - 高级用户
      {
        businessType: MediaBusinessType.FINANCIAL_DOC,
        userRole: UserRole.VIP,
        permissions: ['read', 'write'],
        conditions: {
          maxFileSize: 50 * 1024 * 1024, // 50MB
          allowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx']
        }
      },
      
      // 管理员权限 - 所有业务类型
      ...Object.values(MediaBusinessType).map(businessType => ({
        businessType,
        userRole: UserRole.ADMIN,
        permissions: ['read', 'write', 'delete', 'admin'] as Permission[]
      }))
    ];
    
    // 注册默认策略
    defaultPolicies.forEach(policy => {
      const key = this.getPolicyKey(policy.businessType, policy.userRole);
      this.policies.set(key, policy);
    });
  }
  
  /**
   * 检查上传权限
   */
  async checkUploadPermission(
    userId: string, 
    path: string, 
    businessType: MediaBusinessType
  ): Promise<void> {
    try {
      // 1. 验证用户身份
      const userRole = await this.getUserRole(userId);
      
      // 2. 检查基础权限
      const hasWritePermission = await this.validatePermission(userId, path, 'write');
      if (!hasWritePermission) {
        throw new Error('用户没有上传权限');
      }
      
      // 3. 检查业务类型权限
      const policy = this.getPolicy(businessType, userRole);
      if (!policy || !policy.permissions.includes('write')) {
        throw new Error(`用户角色 ${userRole} 没有 ${businessType} 类型文件的上传权限`);
      }
      
      // 4. 检查路径权限（用户文件隔离）
      if (!this.isUserOwnedPath(userId, path)) {
        throw new Error('用户只能上传到自己的目录');
      }
      
      // 5. 检查敏感数据权限
      if (this.isSensitiveBusinessType(businessType)) {
        await this.checkSensitiveDataPermission(userId, businessType);
      }
      
      console.log(`Upload permission granted for user ${userId}, path: ${path}, type: ${businessType}`);
      
    } catch (error) {
      console.error('Upload permission check failed:', error);
      throw error;
    }
  }
  
  /**
   * 检查查看权限
   */
  async checkViewPermission(objectKey: string, userId?: string): Promise<void> {
    try {
      // 1. 公开文件无需权限验证
      if (this.isPublicPath(objectKey)) {
        return;
      }
      
      // 2. 需要登录的文件
      if (!userId) {
        throw new Error('查看此文件需要登录');
      }
      
      // 3. 检查用户文件所有权
      if (!this.isUserOwnedPath(userId, objectKey)) {
        // 4. 检查是否有管理员权限
        const userRole = await this.getUserRole(userId);
        if (userRole !== UserRole.ADMIN && userRole !== UserRole.SUPER_ADMIN) {
          throw new Error('用户只能查看自己的文件');
        }
      }
      
      // 5. 检查敏感数据访问权限
      if (this.isSensitivePath(objectKey)) {
        await this.checkSensitiveDataAccess(userId, objectKey);
      }
      
      console.log(`View permission granted for user ${userId}, object: ${objectKey}`);
      
    } catch (error) {
      console.error('View permission check failed:', error);
      throw error;
    }
  }
  
  /**
   * 检查删除权限
   */
  async checkDeletePermission(objectKey: string, userId: string): Promise<void> {
    try {
      // 1. 检查基础删除权限
      const hasDeletePermission = await this.validatePermission(userId, objectKey, 'delete');
      if (!hasDeletePermission) {
        throw new Error('用户没有删除权限');
      }
      
      // 2. 检查文件所有权
      if (!this.isUserOwnedPath(userId, objectKey)) {
        const userRole = await this.getUserRole(userId);
        if (userRole !== UserRole.ADMIN && userRole !== UserRole.SUPER_ADMIN) {
          throw new Error('用户只能删除自己的文件');
        }
      }
      
      // 3. 敏感数据不允许删除
      if (this.isSensitivePath(objectKey)) {
        const userRole = await this.getUserRole(userId);
        if (userRole !== UserRole.SUPER_ADMIN) {
          throw new Error('敏感数据不允许删除');
        }
      }
      
      console.log(`Delete permission granted for user ${userId}, object: ${objectKey}`);
      
    } catch (error) {
      console.error('Delete permission check failed:', error);
      throw error;
    }
  }
  
  /**
   * 生成权限策略
   */
  async generatePolicy(userId: string, businessType: MediaBusinessType): Promise<Permission[]> {
    try {
      const userRole = await this.getUserRole(userId);
      const policy = this.getPolicy(businessType, userRole);
      
      if (!policy) {
        // 返回最小权限
        return ['read'];
      }
      
      return policy.permissions;
      
    } catch (error) {
      console.error('Failed to generate policy:', error);
      return ['read']; // 默认只读权限
    }
  }
  
  /**
   * 验证权限
   */
  async validatePermission(userId: string, objectKey: string, permission: Permission): Promise<boolean> {
    try {
      // 1. 获取用户角色
      const userRole = await this.getUserRole(userId);
      
      // 2. 超级管理员拥有所有权限
      if (userRole === UserRole.SUPER_ADMIN) {
        return true;
      }
      
      // 3. 根据对象键判断业务类型
      const businessType = this.inferBusinessTypeFromPath(objectKey);
      
      // 4. 获取权限策略
      const policy = this.getPolicy(businessType, userRole);
      if (!policy) {
        return false;
      }
      
      // 5. 检查权限
      return policy.permissions.includes(permission);
      
    } catch (error) {
      console.error('Permission validation failed:', error);
      return false;
    }
  }
  
  /**
   * 获取用户角色
   */
  private async getUserRole(userId: string): Promise<UserRole> {
    // 先从缓存获取
    const cachedRole = this.userRoleCache.get(userId);
    if (cachedRole) {
      return cachedRole;
    }
    
    // TODO: 实际实现中应该从数据库或API获取用户角色
    // 这里提供一个模拟实现
    const mockRole = this.getMockUserRole(userId);
    
    // 缓存用户角色（5分钟）
    this.userRoleCache.set(userId, mockRole);
    setTimeout(() => {
      this.userRoleCache.delete(userId);
    }, 5 * 60 * 1000);
    
    return mockRole;
  }
  
  /**
   * 模拟获取用户角色
   */
  private getMockUserRole(userId: string): UserRole {
    // 简单的模拟逻辑
    if (userId.includes('admin')) {
      return UserRole.ADMIN;
    } else if (userId.includes('agent')) {
      return UserRole.AGENT;
    } else if (userId.includes('vip')) {
      return UserRole.VIP;
    } else {
      return UserRole.USER;
    }
  }
  
  /**
   * 获取权限策略
   */
  private getPolicy(businessType: MediaBusinessType, userRole: UserRole): PermissionPolicy | undefined {
    const key = this.getPolicyKey(businessType, userRole);
    return this.policies.get(key);
  }
  
  /**
   * 生成策略键
   */
  private getPolicyKey(businessType: MediaBusinessType, userRole: UserRole): string {
    return `${businessType}:${userRole}`;
  }
  
  /**
   * 检查是否为用户拥有的路径
   */
  private isUserOwnedPath(userId: string, path: string): boolean {
    // 用户文件路径格式：users/{userId}/...
    return path.startsWith(`users/${userId}/`) || path.includes(`/${userId}/`);
  }
  
  /**
   * 检查是否为公开路径
   */
  private isPublicPath(objectKey: string): boolean {
    const publicPrefixes = ['public/', 'system/', 'static/'];
    return publicPrefixes.some(prefix => objectKey.startsWith(prefix));
  }
  
  /**
   * 检查是否为敏感路径
   */
  private isSensitivePath(objectKey: string): boolean {
    const sensitivePrefixes = ['sensitive/', 'identity/', 'business/', 'financial/'];
    return sensitivePrefixes.some(prefix => objectKey.startsWith(prefix));
  }
  
  /**
   * 检查是否为敏感业务类型
   */
  private isSensitiveBusinessType(businessType: MediaBusinessType): boolean {
    const sensitiveTypes = [
      MediaBusinessType.IDENTITY_CARD,
      MediaBusinessType.BUSINESS_LICENSE,
      MediaBusinessType.PROPERTY_CERT,
      MediaBusinessType.FINANCIAL_DOC
    ];
    return sensitiveTypes.includes(businessType);
  }
  
  /**
   * 检查敏感数据权限
   */
  private async checkSensitiveDataPermission(userId: string, businessType: MediaBusinessType): Promise<void> {
    const userRole = await this.getUserRole(userId);
    
    // 游客不能访问敏感数据
    if (userRole === UserRole.GUEST) {
      throw new Error('游客不能访问敏感数据');
    }
    
    // TODO: 实际实现中可以添加更多敏感数据访问控制
    // 例如：实名认证检查、二次验证等
  }
  
  /**
   * 检查敏感数据访问权限
   */
  private async checkSensitiveDataAccess(userId: string, objectKey: string): Promise<void> {
    // TODO: 实际实现中可以添加敏感数据访问日志记录
    console.log(`Sensitive data access: user ${userId}, object ${objectKey}`);
  }
  
  /**
   * 从路径推断业务类型
   */
  private inferBusinessTypeFromPath(objectKey: string): MediaBusinessType {
    if (objectKey.includes('/identity/')) return MediaBusinessType.IDENTITY_CARD;
    if (objectKey.includes('/business/')) return MediaBusinessType.BUSINESS_LICENSE;
    if (objectKey.includes('/property-cert/')) return MediaBusinessType.PROPERTY_CERT;
    if (objectKey.includes('/financial/')) return MediaBusinessType.FINANCIAL_DOC;
    if (objectKey.includes('/avatar/')) return MediaBusinessType.USER_AVATAR;
    if (objectKey.includes('/video/')) return MediaBusinessType.PROPERTY_VIDEO;
    
    // 默认为房源图片
    return MediaBusinessType.PROPERTY_IMAGE;
  }
}
