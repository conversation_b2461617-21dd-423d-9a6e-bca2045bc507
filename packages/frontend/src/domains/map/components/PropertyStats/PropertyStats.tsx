/**
 * 房源统计组件
 * 职责：底部统计信息、需求匹配面板和功能网格
 * 企业级架构：修复定时器循环问题
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ScrollingTextTicker } from '../../../property/components/ScrollingTextTicker';

interface PropertyStatsProps {
  // 简化接口，只保留必要的属性
}

export const PropertyStats: React.FC<PropertyStatsProps> = () => {
  // 企业级架构：使用useCallback优化性能，避免不必要的重渲染
  const formatTime = useCallback((timestamp: Date): string => {
    const now = new Date();
    const minutes = Math.floor(
      (now.getTime() - timestamp.getTime()) / (1000 * 60)
    );

    if (minutes <= 5) {
      const hours = timestamp.getHours().toString().padStart(2, '0');
      const mins = timestamp.getMinutes().toString().padStart(2, '0');
      return `${hours}:${mins}`;
    }
    if (minutes < 60) {
      return `${minutes}分钟前`;
    }
    return `${Math.floor(minutes / 60)}小时前`;
  }, []);

  // 企业级架构：使用useCallback缓存消息生成函数
  const generateTickerMessage = useCallback(
    (event: any): string => {
      const time = formatTime(event.timestamp);
      return `${time} ${event.user} ${event.action} ${event.property}`;
    },
    [formatTime]
  );

  // 企业级架构：使用useRef避免闭包问题，防止循环引用
  const [tickerText, setTickerText] = useState('');
  const currentIndexRef = useRef(0);
  const isMountedRef = useRef(true);

  // 企业级架构：优化的定时器逻辑，防止内存泄漏和循环更新
  useEffect(() => {
    const events = [
      {
        user: '王女士',
        action: '发布了',
        property: '黄浦区办公楼',
        timestamp: new Date(Date.now() - 2 * 60 * 1000),
      },
      {
        user: '刘先生',
        action: '匹配了',
        property: '徐汇区商铺',
        timestamp: new Date(Date.now() - 10 * 60 * 1000),
      },
      {
        user: '赵总',
        action: '刚刚租下了',
        property: '长宁区厂房',
        timestamp: new Date(Date.now() - 35 * 60 * 1000),
      },
    ];

    const updateMessage = () => {
      if (!isMountedRef.current) return;

      const event = events[currentIndexRef.current];
      setTickerText(generateTickerMessage(event));
      currentIndexRef.current = (currentIndexRef.current + 1) % events.length;
    };

    // 立即显示第一条
    updateMessage();

    // 企业级架构：延长定时器间隔，减少重渲染频率
    const intervalId = setInterval(updateMessage, 8000);

    // 企业级架构：完整的清理机制
    return () => {
      isMountedRef.current = false;
      clearInterval(intervalId);
    };
  }, [generateTickerMessage]);

  return (
    <>
      {/* 底部信息面板 - 只保留轮动文字和解锁按钮 */}
      <View style={styles.bottomInfoContainer}>
        <View style={styles.infoTopRow}>
          <View style={styles.infoTabs}>
            <Text style={styles.infoTitle}>需求</Text>
            <Ionicons
              name="swap-horizontal-outline"
              size={22}
              color="#ccc"
              style={{ marginHorizontal: 12 }}
            />
            <Text style={styles.infoTitle}>房源</Text>
          </View>
          <TouchableOpacity style={styles.unlockButton}>
            <Text style={styles.unlockButtonText}>解锁秒速匹配</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.infoContent}>
          <ScrollingTextTicker text={tickerText} />
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  bottomInfoContainer: {
    position: 'absolute',
    bottom: 20, // 固定在底部，不再需要动态调整
    left: 16,
    right: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  infoTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoTabs: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  infoContent: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 50,
  },
  unlockButton: {
    backgroundColor: '#FF6B35',
    borderRadius: 18,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  unlockButtonText: {
    color: '#fff',
    fontSize: 13,
    fontWeight: 'bold',
  },
});
