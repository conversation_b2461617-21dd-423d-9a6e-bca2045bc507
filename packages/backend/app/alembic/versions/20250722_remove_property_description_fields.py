"""remove property description fields

Revision ID: 20250722_remove_description
Revises: 675010574b07
Create Date: 2025-07-22 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20250722_remove_description'
down_revision = '675010574b07'
branch_labels = None
depends_on = None


def upgrade():
    """Remove property description fields from properties table"""
    # Drop the description fields
    op.drop_column('properties', 'shop_description')
    op.drop_column('properties', 'office_description')
    op.drop_column('properties', 'factory_land_description')
    op.drop_column('properties', 'meeting_activity_description')


def downgrade():
    """Add back property description fields to properties table"""
    # Add back the description fields
    op.add_column('properties', sa.Column('shop_description', sa.String(length=500), nullable=True))
    op.add_column('properties', sa.Column('office_description', sa.String(length=500), nullable=True))
    op.add_column('properties', sa.Column('factory_land_description', sa.String(length=500), nullable=True))
    op.add_column('properties', sa.Column('meeting_activity_description', sa.String(length=500), nullable=True))