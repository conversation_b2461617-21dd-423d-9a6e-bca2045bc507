/**
 * 导航类型定义 - 2025企业级标准
 * 完整的类型安全导航系统
 */

import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import type { CompositeScreenProps } from '@react-navigation/native';
import type { StackScreenProps } from '@react-navigation/stack';

// ==================== 根导航参数类型 ====================
export type RootStackParamList = {
  // 主要导航流程
  Splash: undefined; // ✅ 添加Splash类型
  Onboarding: undefined;
  Auth: undefined;
  MainTabs: undefined;
  AuthenticatedMainTabs: undefined;
  UnauthenticatedMainTabs: undefined;

  // 临时页面（用于测试）
  TempHome: undefined;
  Test: undefined;

  // 房源相关页面
  PropertyDetail: {
    propertyId: string;
    source?: 'home' | 'search' | 'favorites' | 'recommendations' | 'published' | string;
    publishedData?: {
      id?: string;
      propertyId?: string;
      title: string;
      propertyType: string;
      tags: string[];
      area: string;
      images?: string[];
      [key: string]: any;
    };
  };
  PropertyList: {
    type?: 'shop' | 'office' | 'warehouse' | 'venue' | 'stall';
    filters?: PropertyFilters;
  };
  PropertyTypePage: {
    propertyType: '商铺' | '写字楼' | '厂房' | '仓库' | '土地';
  };
  CommuteAnalysis: {
    propertyId: string;
    propertyAddress: string;
    propertyLocation: {
      latitude: number;
      longitude: number;
    };
  };
  PropertyEdit: { propertyId: string };
  PropertyAdd: { type: 'shop' | 'office' | 'warehouse' | 'venue' | 'stall' };

  // 认证相关页面
  Login: undefined;
  QuickLogin: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  SmsLogin: undefined;
  VerifyCode: {
    phone: string;
    type: 'register' | 'login' | 'reset_password';
  };

  // 用户相关页面
  Profile: undefined;
  Settings: undefined;
  AccountManagement: undefined;
  NotificationSettings: undefined; // 新增：通知设置页面
  PrivacySettings: undefined;
  DeviceManagement: undefined;
  SecuritySettings: undefined;
  RoleVerification: undefined;
  Feedback: undefined;
  PasswordSetup: {
    isFirstTime?: boolean;
  };

  // 业务功能页面
  TenantRequirements: undefined;
  TenantDemandList: undefined;
  TenantDemandDetail: { demandId: string };
  PublishDemand: undefined;
  
  // 求租求购页面
  DemandForm: {
    demandType: 'RENTAL' | 'PURCHASE';
    editMode?: boolean;
    demandId?: string;
    demandData?: any;
  };
  DemandInfo: {
    formData: any;
    demandType: 'RENTAL' | 'PURCHASE';
  };
  DemandDetail: {
    demandId: string;
  };

  // 搜索相关页面
  Search: {
    keyword?: string;
    type?: 'property' | 'demand';
  };
  VoiceSearch: { mode: 'record' | 'history' };
  SearchResults: {
    query: string;
    filters?: PropertyFilters;
  };

  // 收藏和历史
  Favorites: undefined;
  SearchHistory: undefined;
  ViewHistory: undefined;

  // 预约和订单
  Appointments: undefined;
  AppointmentDetail: { appointmentId: string };
  Orders: undefined;
  OrderDetail: { orderId: string };

  // 积分和支付
  Points: undefined;
  PointsHistory: undefined;
  Payment: {
    orderId: string;
    amount: number;
    type: 'package' | 'service';
  };

  // 认证流程
  LandlordVerification: undefined;
  ManagerVerification: undefined;

  // 地图相关
  MapView: {
    properties?: Array<{ id: string; lat: number; lng: number }>;
    center?: { lat: number; lng: number };
  };

  // 帮助和支持
  Help: undefined;
  About: undefined;
  PrivacyPolicy: undefined;
  UserAgreement: undefined;

  // 消息系统
  MessageCenter: undefined;
  ChatDetail: {
    message: any; // BusinessMessage type
    userRole: 'landlord' | 'tenant';
    inquiryId: string; // 咨询记录ID，用于WebSocket连接
  };
  LandlordSubscription: undefined;

  // 用户中心详细页面
  MyDemands: undefined;
  MyProperties: { initialTab?: 'published' | 'draft' | 'inactive' } | undefined;
  TenantResource: undefined;
  Orders: undefined; // 我的订单页面
  Favorites: undefined; // 我的收藏页面
  Appointments: undefined; // 预约看房页面

  // 发布相关页面
  PublishOptions: undefined;
  Verification: {
    propertyType: string;
    returnTo: string;
  };
  PropertyDetailForm: {
    propertyType?: string; // 在编辑模式下，这个也可以是可选的
    isEdit?: boolean; // 添加isEdit可选参数
    draftId?: string; // 添加草稿ID参数
  };
  PublishSuccess: {
    publishedData: {
      title: string;
      propertyType: string;
      tags: string[];
      area: string;
      [key: string]: any;
    };
  };

  // 开发和调试（仅开发环境）
  DevTools?: undefined;
  AvatarTest?: undefined;
};

// ==================== Tab导航参数类型 ====================
export type MainTabParamList = {
  Home: undefined;
  Map: undefined;        // 🗺️ 替换Search为Map (地图找房)
  Publish: undefined;
  Messages: undefined;
  Profile: undefined;
};

// ==================== 认证导航参数类型 ====================
export type AuthStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  SmsLogin: undefined;
  VerifyCode: {
    phone: string;
    type: 'register' | 'login' | 'reset_password';
  };
};

// ==================== 房源导航参数类型 ====================
export type PropertyStackParamList = {
  PropertyList: {
    type?: 'shop' | 'office' | 'warehouse' | 'venue' | 'stall';
    filters?: PropertyFilters;
  };
  PropertyDetail: {
    propertyId: string;
    source?: 'list' | 'search' | 'favorites';
  };
  PropertyEdit: { propertyId: string };
  PropertyAdd: { type: 'shop' | 'office' | 'warehouse' | 'venue' | 'stall' };
  PropertyImages: { propertyId: string };
  PropertyMap: { propertyId: string };
};

// ==================== 辅助类型定义 ====================
export interface PropertyFilters {
  type?: 'shop' | 'office' | 'warehouse' | 'venue' | 'stall';
  priceRange?: { min: number; max: number };
  areaRange?: { min: number; max: number };
  location?: {
    city: string;
    district?: string;
    address?: string;
    radius?: number; // 搜索半径（公里）
  };
  features?: string[]; // 特色功能
  sortBy?: 'price' | 'area' | 'distance' | 'popularity' | 'newest';
  sortOrder?: 'asc' | 'desc';
}

// ==================== 屏幕Props类型 ====================
export type RootStackScreenProps<Screen extends keyof RootStackParamList> =
  StackScreenProps<RootStackParamList, Screen>;

export type MainTabScreenProps<Screen extends keyof MainTabParamList> =
  CompositeScreenProps<
    BottomTabScreenProps<MainTabParamList, Screen>,
    StackScreenProps<RootStackParamList>
  >;

export type AuthStackScreenProps<Screen extends keyof AuthStackParamList> =
  StackScreenProps<AuthStackParamList, Screen>;

export type PropertyStackScreenProps<
  Screen extends keyof PropertyStackParamList
> = StackScreenProps<PropertyStackParamList, Screen>;

// ==================== 导航Hook类型 ====================
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}

// ==================== 导航配置类型 ====================
export interface NavigationConfig {
  initialRouteName: keyof RootStackParamList;
  screenOptions: {
    headerShown: boolean;
    gestureEnabled: boolean;
    animation: 'slide_from_right' | 'slide_from_bottom' | 'fade' | 'none';
  };
}

// ==================== 深度链接类型 ====================
export interface DeepLinkConfig {
  screens: {
    [K in keyof RootStackParamList]:
      | string
      | {
          path: string;
          parse?: Record<string, (value: string) => any>;
        };
  };
}

// ==================== 导航状态类型 ====================
export interface NavigationState {
  currentRoute: keyof RootStackParamList;
  previousRoute?: keyof RootStackParamList;
  params?: any;
  canGoBack: boolean;
}

// ==================== 导航动作类型 ====================
export type NavigationAction =
  | {
      type: 'NAVIGATE';
      payload: { screen: keyof RootStackParamList; params?: any };
    }
  | { type: 'GO_BACK' }
  | {
      type: 'RESET';
      payload: { screen: keyof RootStackParamList; params?: any };
    }
  | {
      type: 'REPLACE';
      payload: { screen: keyof RootStackParamList; params?: any };
    };

// ==================== 导航中间件类型 ====================
export interface NavigationMiddleware {
  beforeNavigate?: (action: NavigationAction) => boolean | Promise<boolean>;
  afterNavigate?: (action: NavigationAction) => void | Promise<void>;
}

// ==================== 导航分析类型 ====================
export interface NavigationAnalytics {
  trackScreenView: (screenName: string, params?: any) => void;
  trackNavigation: (from: string, to: string, params?: any) => void;
  trackUserFlow: (flow: string, step: string) => void;
}
