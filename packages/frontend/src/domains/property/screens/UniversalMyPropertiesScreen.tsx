/**
 * 🏗️ 企业级架构：通用我的房源页面
 * 基于UniversalMyDemandsScreen的设计模式，适配房源管理
 * 遵循前端企业级架构规范 - 完整五层架构
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  RefreshControl,
} from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { useNavigation } from '@react-navigation/native';

// 🏗️ 企业级架构：组件导入
import { PropertyListItem } from '../components/PropertyListItem';
import { EmptyState } from '../../../shared/components/EmptyState';
import { LoadingSpinner } from '../../../shared/components/LoadingSpinner';
import { FilterBar } from '../../../shared/components/FilterBar';

// 🏗️ 企业级架构：类型定义
import type { PropertyStatus, APIPropertyListItem } from '../types/property.types';
import type { NavigationProp } from '@react-navigation/native';

// 🔧 API层：房源服务
import { PropertyAPI } from '../services/propertyAPI';

// 🎨 UI层：样式工具
import { spacing } from '../../../shared/utils/responsiveUtils';

interface UniversalMyPropertiesScreenProps {
  // 可选的初始筛选条件
  initialStatus?: PropertyStatus;
  // 页面标题（用于导航）
  screenTitle?: string;
  // 是否显示筛选栏
  showFilter?: boolean;
  // 自定义空状态
  emptyStateConfig?: {
    title: string;
    description: string;
    actionText?: string;
    onAction?: () => void;
  };
}

export const UniversalMyPropertiesScreen: React.FC<UniversalMyPropertiesScreenProps> = ({
  initialStatus,
  screenTitle = '我的房源',
  showFilter = true,
  emptyStateConfig,
}) => {
  const navigation = useNavigation<NavigationProp<any>>();

  // 🔧 状态管理
  const [properties, setProperties] = useState<APIPropertyListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<PropertyStatus | 'all'>(initialStatus || 'all');

  // 🔧 数据加载
  const loadProperties = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      
      console.log('[UniversalMyPropertiesScreen] 🔄 加载房源列表:', { currentStatus });
      
      const params = {
        status: currentStatus === 'all' ? undefined : currentStatus,
        page: 1,
        size: 50,
      };
      
      const result = await PropertyAPI.getMyProperties(params);
      
      if (result.success && result.data) {
        setProperties(result.data.items || []);
        console.log('[UniversalMyPropertiesScreen] ✅ 房源列表加载成功:', result.data.items?.length);
      } else {
        console.error('[UniversalMyPropertiesScreen] ❌ 房源列表加载失败:', result.message);
        Alert.alert('加载失败', result.message || '获取房源列表失败');
      }
    } catch (error: any) {
      console.error('[UniversalMyPropertiesScreen] 💥 房源列表加载异常:', error);
      Alert.alert('网络错误', '请检查网络连接后重试');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [currentStatus]);

  // 🔧 初始化加载
  useEffect(() => {
    loadProperties();
  }, [loadProperties]);

  // 🔧 下拉刷新
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    loadProperties(false);
  }, [loadProperties]);

  // 🔧 筛选状态变更
  const handleStatusFilter = useCallback((status: PropertyStatus | 'all') => {
    console.log('[UniversalMyPropertiesScreen] 🔄 筛选状态变更:', status);
    setCurrentStatus(status);
  }, []);

  // 🔧 房源操作处理
  const handleItemPress = useCallback((item: APIPropertyListItem) => {
    console.log('[UniversalMyPropertiesScreen] 🔍 查看房源详情:', item.id);
    navigation.navigate('PropertyDetail', { propertyId: item.id });
  }, [navigation]);

  const handleEditProperty = useCallback((item: APIPropertyListItem) => {
    console.log('[UniversalMyPropertiesScreen] ✏️ 编辑房源:', item.id);
    navigation.navigate('PropertyForm', { 
      propertyId: item.id,
      mode: 'edit'
    });
  }, [navigation]);

  const handleDeleteProperty = useCallback((item: APIPropertyListItem) => {
    console.log('[UniversalMyPropertiesScreen] 🗑️ 删除房源:', item.id);
    // 从列表中移除已删除的房源
    setProperties(prev => prev.filter(p => p.id !== item.id));
  }, []);

  const handleStatusChange = useCallback((item: APIPropertyListItem, newStatus: PropertyStatus) => {
    console.log('[UniversalMyPropertiesScreen] 🔄 房源状态变更:', item.id, newStatus);
    // 更新列表中的房源状态
    setProperties(prev => prev.map(p => 
      p.id === item.id ? { ...p, status: newStatus } : p
    ));
  }, []);

  // 🎨 UI层：筛选选项
  const filterOptions = [
    { label: '全部', value: 'all' },
    { label: '草稿', value: 'draft' },
    { label: '已发布', value: 'published' },
    { label: '已下架', value: 'inactive' },
    { label: '已售出', value: 'sold' },
    { label: '已出租', value: 'rented' },
    { label: '已过期', value: 'expired' },
  ];

  // 🎨 UI层：渲染房源列表项
  const renderPropertyItem = useCallback(({ item }: { item: APIPropertyListItem }) => {
    return (
      <PropertyListItem
        item={item}
        onPress={handleItemPress}
        onEdit={handleEditProperty}
        onDelete={handleDeleteProperty}
        onStatusChange={handleStatusChange}
        onRefresh={handleRefresh}
      />
    );
  }, [handleItemPress, handleEditProperty, handleDeleteProperty, handleStatusChange, handleRefresh]);

  // 🎨 UI层：空状态配置
  const defaultEmptyConfig = {
    title: '暂无房源',
    description: currentStatus === 'all' 
      ? '您还没有发布任何房源\n点击右上角"+"开始发布' 
      : `暂无${filterOptions.find(opt => opt.value === currentStatus)?.label}状态的房源`,
    actionText: '发布房源',
    onAction: () => navigation.navigate('PropertyForm', { mode: 'create' }),
  };

  const finalEmptyConfig = emptyStateConfig || defaultEmptyConfig;

  // 🎨 UI层：加载状态
  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <View style={styles.container}>
      {/* 筛选栏 */}
      {showFilter && (
        <FilterBar
          options={filterOptions}
          selectedValue={currentStatus}
          onValueChange={handleStatusFilter}
          style={styles.filterBar}
        />
      )}

      {/* 房源列表 */}
      <FlashList
        data={properties}
        renderItem={renderPropertyItem}
        keyExtractor={(item) => item.id}
        estimatedItemSize={120}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#4A90E2']}
            tintColor="#4A90E2"
          />
        }
        ListEmptyComponent={
          <EmptyState
            title={finalEmptyConfig.title}
            description={finalEmptyConfig.description}
            actionText={finalEmptyConfig.actionText}
            onAction={finalEmptyConfig.onAction}
          />
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  filterBar: {
    backgroundColor: '#FFFFFF',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  listContainer: {
    paddingVertical: spacing.sm,
  },
});

export default UniversalMyPropertiesScreen;
