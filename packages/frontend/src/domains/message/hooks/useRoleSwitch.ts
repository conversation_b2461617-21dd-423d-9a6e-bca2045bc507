/**
 * 角色切换Hook
 * 专门处理角色切换逻辑和状态管理，符合企业级架构规范
 */
import { useState, useCallback, useEffect } from 'react';

/**
 * 角色切换Hook接口
 */
interface UseRoleSwitchProps {
  initialRole?: 'tenant_buyer' | 'landlord';
  onRoleChange?: (role: 'tenant_buyer' | 'landlord') => void;
}

/**
 * 角色切换Hook返回值
 */
interface UseRoleSwitchReturn {
  // 状态
  activeRole: 'tenant_buyer' | 'landlord';
  
  // 方法
  switchToTenantBuyer: () => void;
  switchToLandlord: () => void;
  switchRole: (role: 'tenant_buyer' | 'landlord') => void;
  
  // 计算属性
  isTenantBuyerActive: boolean;
  isLandlordActive: boolean;
  roleDisplayName: string;
}

/**
 * 角色切换Hook
 * 
 * @param props - Hook配置参数
 * @returns 角色切换状态和方法
 */
export const useRoleSwitch = ({
  initialRole = 'tenant_buyer',
  onRoleChange,
}: UseRoleSwitchProps = {}): UseRoleSwitchReturn => {
  
  // 角色状态
  const [activeRole, setActiveRole] = useState<'tenant_buyer' | 'landlord'>(initialRole);

  // 切换到租买角色
  const switchToTenantBuyer = useCallback(() => {
    console.log('切换到租买消息');
    setActiveRole('tenant_buyer');
  }, []);

  // 切换到业主角色
  const switchToLandlord = useCallback(() => {
    console.log('切换到业主消息');
    setActiveRole('landlord');
  }, []);

  // 通用角色切换方法
  const switchRole = useCallback((role: 'tenant_buyer' | 'landlord') => {
    console.log(`切换角色: ${role}`);
    setActiveRole(role);
  }, []);

  // 角色变化时的副作用
  useEffect(() => {
    if (onRoleChange) {
      onRoleChange(activeRole);
    }
  }, [activeRole, onRoleChange]);

  // 计算属性
  const isTenantBuyerActive = activeRole === 'tenant_buyer';
  const isLandlordActive = activeRole === 'landlord';
  const roleDisplayName = activeRole === 'tenant_buyer' ? '租买消息' : '业主消息';

  return {
    // 状态
    activeRole,
    
    // 方法
    switchToTenantBuyer,
    switchToLandlord,
    switchRole,
    
    // 计算属性
    isTenantBuyerActive,
    isLandlordActive,
    roleDisplayName,
  };
};
