/**
 * 地址搜索Store - Zustand状态管理
 * 遵循企业级架构规范：devtools + persist + subscribeWithSelector
 */

import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  AddressSearchState,
  AddressSearchActions,
  AddressResult,
  SearchHistoryItem,
  LocationResult,
  POIResult,
} from '../types/addressSearch.types';
import { AddressSearchAPI } from '../services/addressSearchAPI';

// 防抖工具函数
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// 完整Store类型定义
type AddressSearchStore = AddressSearchState & AddressSearchActions;

// 初始状态
const initialState: AddressSearchState = {
  // 搜索状态
  searchQuery: '',
  searchResults: [],
  isSearching: false,
  searchError: null,
  hasMore: false,
  currentPage: 1,

  // 历史记录
  searchHistory: [],

  // UI状态
  showHistory: true,
  selectedIndex: -1,
  isHistoryExpanded: false,

  // 位置状态
  currentLocation: null,
  nearbyPOIs: [],
  isLoadingLocation: false,
  locationError: null,

  // 快捷位置
  quickLocations: [],
};

export const useAddressSearchStore = create<AddressSearchStore>()(
  devtools(
    persist(
      subscribeWithSelector((set, get) => ({
        // === 初始状态 ===
        ...initialState,

        // === 搜索操作 ===
        // 🛠️ 修复1：合并状态更新，避免多次set调用导致的连锁反应
        setSearchQuery: (query: string) => {
          if (!query.trim()) {
            // 清空查询时合并所有状态更新
            set(
              {
                searchQuery: query,
                showHistory: true,
                searchResults: [],
                searchError: null,
              },
              false,
              'clearQueryAndShowHistory'
            );
          } else {
            // 设置查询时合并状态更新
            set(
              {
                searchQuery: query,
                showHistory: false,
              },
              false,
              'setQueryAndHideHistory'
            );
          }
        },

        performSearch: async (query: string, page: number = 1) => {
          if (!query.trim()) return;

          const isLoadMore = page > 1;

          set(
            {
              isSearching: true,
              searchError: null,
              currentPage: page,
              ...(isLoadMore ? {} : { searchResults: [] }), // 新搜索清空结果
            },
            false,
            'startSearch'
          );

          try {
            // 获取当前位置用于附近优先排序
            const currentLocation = get().currentLocation;
            const location = currentLocation
              ? `${currentLocation.longitude},${currentLocation.latitude}`
              : undefined;

            // 🚀 实时搜索建议：优先使用输入提示API获得更快响应
            const response =
              query.trim().length <= 3
                ? await AddressSearchAPI.getInputTips({
                    keywords: query,
                    city: '南宁',
                    location,
                  })
                : await AddressSearchAPI.searchAddress({
                    keywords: query,
                    city: '南宁',
                    location,
                    radius: location ? 10000 : undefined, // 10km范围
                    page,
                    offset: 20,
                  });

            if (response.success && response.data) {
              // 统一处理两种API的返回格式
              const newResults = Array.isArray(response.data)
                ? response.data // 输入提示API返回数组
                : response.data.results; // POI搜索API返回对象

              set(
                state => ({
                  searchResults: isLoadMore
                    ? [...state.searchResults, ...newResults]
                    : newResults,
                  hasMore: Array.isArray(response.data)
                    ? false
                    : response.data?.hasMore || false, // 输入提示没有分页
                  isSearching: false,
                  searchError: null,
                }),
                false,
                'searchSuccess'
              );

              // 如果是新搜索且有结果，记录到历史
              if (!isLoadMore && newResults.length > 0) {
                get().addToHistory(newResults[0]); // 添加第一个结果到历史
              }
            } else {
              throw new Error(response.message || '搜索失败');
            }
          } catch (error) {
            console.error('[AddressSearchStore] 搜索失败:', error);
            set(
              {
                isSearching: false,
                searchError:
                  error instanceof Error ? error.message : '搜索失败',
              },
              false,
              'searchError'
            );
          }
        },

        loadMoreResults: async () => {
          const { searchQuery, currentPage, hasMore, isSearching } = get();

          if (!hasMore || isSearching || !searchQuery.trim()) return;

          await get().performSearch(searchQuery, currentPage + 1);
        },

        clearSearch: () => {
          set(
            {
              searchQuery: '',
              searchResults: [],
              searchError: null,
              showHistory: true,
              hasMore: false,
              currentPage: 1,
            },
            false,
            'clearSearch'
          );
        },

        setSearchError: (error: string | null) => {
          set({ searchError: error }, false, 'setSearchError');
        },

        // === 历史记录操作 ===
        // 🛠️ 修复2：优化历史记录更新，减少数组操作复杂度
        addToHistory: (address: AddressResult) => {
          set(
            state => {
              // 使用Map提高查找效率，避免每次findIndex遍历
              const historyMap = new Map<string, number>();
              state.searchHistory.forEach((item, index) => {
                const key = `${item.id}_${item.name}_${item.address}`;
                historyMap.set(key, index);
              });

              const addressKey = `${address.id}_${address.name}_${address.address}`;
              const existingIndex = historyMap.get(addressKey) ?? -1;

              const now = new Date().toISOString();
              let newHistory: SearchHistoryItem[];

              if (existingIndex >= 0) {
                // 更新现有记录
                const existing = state.searchHistory[existingIndex];
                const updated: SearchHistoryItem = {
                  ...existing,
                  searchCount: existing.searchCount + 1,
                  lastUsed: now,
                };

                // 使用更高效的数组操作
                newHistory = state.searchHistory.slice();
                newHistory.splice(existingIndex, 1);
                newHistory.unshift(updated);
              } else {
                // 添加新记录
                const newItem: SearchHistoryItem = {
                  ...address,
                  searchTime: now,
                  searchCount: 1,
                  lastUsed: now,
                };

                newHistory = [newItem, ...state.searchHistory.slice(0, 49)]; // 预留空间给新项
              }

              return {
                searchHistory: newHistory.slice(0, 50), // 最多保留50条
              };
            },
            false,
            'addToHistory'
          );
        },

        removeFromHistory: (addressId: string) => {
          set(
            state => ({
              searchHistory: state.searchHistory.filter(
                item => item.id !== addressId
              ),
            }),
            false,
            'removeFromHistory'
          );
        },

        clearHistory: () => {
          set({ searchHistory: [] }, false, 'clearHistory');
        },

        toggleHistoryExpanded: () => {
          set(
            state => ({
              isHistoryExpanded: !state.isHistoryExpanded,
            }),
            false,
            'toggleHistoryExpanded'
          );
        },

        // === 选择操作 ===
        selectAddress: (address: AddressResult) => {
          // 记录到历史
          get().addToHistory(address);

          set(
            {
              selectedIndex: -1,
            },
            false,
            'selectAddress'
          );

          // 这里不处理导航，由组件处理
        },

        setSelectedIndex: (index: number) => {
          set({ selectedIndex: index }, false, 'setSelectedIndex');
        },

        // === 位置操作 ===
        getCurrentLocation: async () => {
          set(
            {
              isLoadingLocation: true,
              locationError: null,
            },
            false,
            'startGetLocation'
          );

          try {
            // 使用react-native-amap3d的定位功能
            // 这里简化处理，实际应该集成高德定位SDK

            // 模拟获取当前位置（实际项目中应该使用真实定位）
            const mockLocation: LocationResult = {
              latitude: 22.816,
              longitude: 108.376,
              address: '广西壮族自治区南宁市青秀区',
              accuracy: 20,
              timestamp: new Date().toISOString(),
            };

            set(
              {
                currentLocation: mockLocation,
                isLoadingLocation: false,
                locationError: null,
              },
              false,
              'getCurrentLocationSuccess'
            );

            // 🔧 移除自动获取POI：地址搜索页面不需要POI，POI应该在地图找房页面使用
            // await get().getNearbyPOIs();
          } catch (error) {
            console.error('[AddressSearchStore] 获取当前位置失败:', error);
            set(
              {
                isLoadingLocation: false,
                locationError:
                  error instanceof Error ? error.message : '定位失败',
              },
              false,
              'getCurrentLocationError'
            );
          }
        },

        getNearbyPOIs: async (location?: string) => {
          const currentLocation = location || get().currentLocation;
          if (!currentLocation) return;

          try {
            const locationStr =
              typeof currentLocation === 'string'
                ? currentLocation
                : `${currentLocation.longitude},${currentLocation.latitude}`;

            const response = await AddressSearchAPI.getNearbyPOIs(
              locationStr,
              1000
            );

            if (response.success && response.data) {
              set(
                {
                  nearbyPOIs: response.data,
                },
                false,
                'getNearbyPOIsSuccess'
              );
            }
          } catch (error) {
            console.error('[AddressSearchStore] 获取附近POI失败:', error);
          }
        },

        setCurrentLocation: (location: LocationResult | null) => {
          set({ currentLocation: location }, false, 'setCurrentLocation');
        },

        // === 快捷位置操作 ===
        addQuickLocation: (address: AddressResult) => {
          set(
            state => {
              const exists = state.quickLocations.some(
                item => item.id === address.id
              );
              if (exists) return state;

              return {
                quickLocations: [...state.quickLocations, address].slice(0, 10), // 最多10个
              };
            },
            false,
            'addQuickLocation'
          );
        },

        removeQuickLocation: (addressId: string) => {
          set(
            state => ({
              quickLocations: state.quickLocations.filter(
                item => item.id !== addressId
              ),
            }),
            false,
            'removeQuickLocation'
          );
        },

        // === UI操作 ===
        setShowHistory: (show: boolean) => {
          set({ showHistory: show }, false, 'setShowHistory');
        },

        // 🛠️ 修复3：优化reset方法，避免get()调用可能导致的循环
        reset: () => {
          const currentState = get();
          set(
            {
              ...initialState,
              // 保留历史记录和快捷位置
              searchHistory: currentState.searchHistory,
              quickLocations: currentState.quickLocations,
              currentLocation: currentState.currentLocation,
            },
            false,
            'reset'
          );
        },

        // === 防抖搜索功能移除，改为手动搜索 ===
      })),
      {
        name: 'address-search-store',
        storage: {
          getItem: async name => {
            const value = await AsyncStorage.getItem(name);
            return value ? JSON.parse(value) : null;
          },
          setItem: async (name, value) => {
            await AsyncStorage.setItem(name, JSON.stringify(value));
          },
          removeItem: async name => {
            await AsyncStorage.removeItem(name);
          },
        },
        // 选择性持久化 - 不持久化临时UI状态
        partialize: state =>
          ({
            searchHistory: state.searchHistory,
            quickLocations: state.quickLocations,
            currentLocation: state.currentLocation,
          }) as Pick<
            AddressSearchStore,
            'searchHistory' | 'quickLocations' | 'currentLocation'
          >,
      }
    ),
    {
      name: 'AddressSearchStore',
    }
  )
);

// === 选择器Hook（性能优化） ===
export const useSearchQuery = () =>
  useAddressSearchStore(state => state.searchQuery);
export const useSearchResults = () =>
  useAddressSearchStore(state => state.searchResults);
export const useSearchLoading = () =>
  useAddressSearchStore(state => state.isSearching);
export const useSearchError = () =>
  useAddressSearchStore(state => state.searchError);
export const useSearchHistory = () =>
  useAddressSearchStore(state => state.searchHistory);
export const useCurrentLocation = () =>
  useAddressSearchStore(state => state.currentLocation);
export const useQuickLocations = () =>
  useAddressSearchStore(state => state.quickLocations);

// 操作方法选择器
export const useAddressSearchActions = () =>
  useAddressSearchStore(state => ({
    setSearchQuery: state.setSearchQuery,
    performSearch: state.performSearch,
    clearSearch: state.clearSearch,
    selectAddress: state.selectAddress,
    addToHistory: state.addToHistory,
    removeFromHistory: state.removeFromHistory,
    clearHistory: state.clearHistory,
    getCurrentLocation: state.getCurrentLocation,
    addQuickLocation: state.addQuickLocation,
    removeQuickLocation: state.removeQuickLocation,
    loadMoreResults: state.loadMoreResults,
    reset: state.reset,
  }));
