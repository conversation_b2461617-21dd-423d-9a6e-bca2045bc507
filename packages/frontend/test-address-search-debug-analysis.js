/**
 * 地址搜索调试分析脚本
 * 测试精确调试实现，分析Text渲染错误的根本原因
 * 基于用户提供的详细错误场景："青秀区 汇东国际" -> "青秀区"
 */

console.log('🔍 地址搜索调试分析开始');

// 模拟用户操作场景
const simulateUserScenario = () => {
  console.log('\n📱 模拟用户操作场景:');
  console.log('1. 输入框初始值: "青秀区 汇东国际"');
  console.log('2. 用户删除"汇东国际"和空格');
  console.log('3. 剩余文本: "青秀区"');
  console.log('4. 此时触发Text渲染错误');

  return {
    initial: '青秀区 汇东国际',
    afterDelete: '青秀区',
    triggerPoint: '删除操作后的搜索结果渲染',
  };
};

// 分析可能的数据格式问题
const analyzeDataStructure = () => {
  console.log('\n🔍 分析可能的数据结构问题:');

  // 模拟API返回的可能有问题的数据
  const problematicData = [
    {
      id: 'poi_1',
      name: null, // ❌ 可能为null
      address: undefined, // ❌ 可能为undefined
      formattedAddress: '', // ❌ 可能为空字符串
      distance: '500m', // ❌ 可能是字符串而非数字
    },
    {
      id: 'poi_2',
      name: 123, // ❌ 可能是数字
      address: ['青秀区', '南宁市'], // ❌ 可能是数组
      formattedAddress: { district: '青秀区' }, // ❌ 可能是对象
      distance: null,
    },
    {
      id: 'poi_3',
      name: '',
      address: '   ', // ❌ 只有空格
      formattedAddress: '\n\t', // ❌ 只有换行符和制表符
      distance: NaN, // ❌ 可能是NaN
    },
  ];

  console.log('❌ 问题数据示例:', JSON.stringify(problematicData, null, 2));

  return problematicData;
};

// 测试类型检查逻辑
const testTypeChecking = data => {
  console.log('\n🧪 测试类型检查逻辑:');

  data.forEach((item, index) => {
    console.log(`\n测试项 ${index}:`);
    console.log(`原始数据:`, item);

    // 模拟AddressSearchScreen中的类型检查
    const typeCheck = {
      name: typeof item.name,
      nameValue: item.name,
      address: typeof item.address,
      addressValue: item.address,
      formattedAddress: typeof item.formattedAddress,
      formattedAddressValue: item.formattedAddress,
      distance: typeof item.distance,
      distanceValue: item.distance,
    };

    console.log(`类型检查结果:`, typeCheck);

    // 检查是否会导致Text组件错误
    const nameResult =
      (item.name && typeof item.name === 'string' ? item.name : '') ||
      '未知地址';
    const addressResult =
      (item.formattedAddress && typeof item.formattedAddress === 'string'
        ? item.formattedAddress
        : '') ||
      (item.address && typeof item.address === 'string' ? item.address : '') ||
      '地址信息不完整';

    console.log(`Text组件将接收:`, { nameResult, addressResult });

    // 检查是否为有效字符串
    if (typeof nameResult !== 'string' || typeof addressResult !== 'string') {
      console.error(`❌ 发现问题! 非字符串值将传递给Text组件`);
    }
  });
};

// 检查SearchResultItem组件的具体实现
const checkSearchResultItemImplementation = () => {
  console.log('\n🔍 检查SearchResultItem组件实现:');

  console.log('当前代码中的关键Text组件位置:');
  console.log(
    "- 第72行: {(address.name && typeof address.name === 'string' ? address.name : '') || '未知地址'}"
  );
  console.log(
    "- 第75行: {(address.formattedAddress && typeof address.formattedAddress === 'string' ? address.formattedAddress : '') || (address.address && typeof address.address === 'string' ? address.address : '') || '地址信息不完整'}"
  );
  console.log('- 第82-85行: 距离显示逻辑');

  console.log('\n可能的问题点:');
  console.log('1. 复杂的三元操作符可能产生非字符串值');
  console.log('2. API返回的数据结构可能不符合预期');
  console.log('3. 异步数据更新时可能存在时序问题');
};

// 分析FlatList渲染流程
const analyzeFlatListRenderFlow = () => {
  console.log('\n🔍 分析FlatList渲染流程:');

  console.log('渲染流程:');
  console.log('1. FlatList data={searchResults}');
  console.log('2. renderItem 函数被调用');
  console.log('3. 每个item传递给SearchResultItem');
  console.log('4. SearchResultItem渲染Text组件');
  console.log('5. Text组件检查props是否为字符串');

  console.log('\n错误触发点分析:');
  console.log('- 用户删除文本触发新的搜索');
  console.log('- API返回的数据可能包含非字符串字段');
  console.log('- FlatList尝试渲染时发现Text组件接收到非字符串');
  console.log(
    '- React Native抛出"Text strings must be rendered within a <Text> component"'
  );
};

// 提供解决方案建议
const provideSolutionRecommendations = () => {
  console.log('\n💡 解决方案建议:');

  console.log('1. 数据验证增强:');
  console.log('   - 在API层添加更严格的数据验证');
  console.log('   - 使用zod或类似库进行运行时类型检查');

  console.log('2. 安全渲染模式:');
  console.log('   - 确保所有Text组件都接收字符串');
  console.log('   - 添加更多的null/undefined检查');

  console.log('3. 错误边界:');
  console.log('   - 在FlatList项目周围添加错误边界');
  console.log('   - 优雅处理渲染异常');

  console.log('4. 调试日志:');
  console.log('   - 实际运行时查看console.log输出');
  console.log('   - 确认数据结构和类型');
};

// 执行分析
const runAnalysis = () => {
  const scenario = simulateUserScenario();
  const problematicData = analyzeDataStructure();

  testTypeChecking(problematicData);
  checkSearchResultItemImplementation();
  analyzeFlatListRenderFlow();
  provideSolutionRecommendations();

  console.log('\n✅ 调试分析完成');
  console.log(
    '📋 下一步: 需要在实际设备上运行应用，查看调试日志输出，确认具体的数据问题'
  );
};

// 运行分析
runAnalysis();
