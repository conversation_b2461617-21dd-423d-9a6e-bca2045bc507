/**
 * 房源表单进度条组件
 * 
 * 从PropertyDetailFormScreen中拆分出来的进度显示功能
 * 包含4步骤进度条、百分比显示、温馨提示等功能
 */

import React from 'react';
import { View, Text } from 'react-native';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';

// 导入样式
import { allStyles as styles } from '../styles';

// 进度步骤类型定义
export interface ProgressStep {
  name: string;
  completed: boolean;
  hint: string;
}

// 进度数据类型定义
export interface FormProgress {
  steps: ProgressStep[];
  completed: number;
  total: number;
  percentage: number;
  nextStep?: ProgressStep;
  allCompleted: boolean;
}

interface PropertyFormProgressProps {
  progress: FormProgress;
}

export const PropertyFormProgress: React.FC<PropertyFormProgressProps> = ({
  progress,
}) => {
  return (
    <View style={styles.stepProgressContainer}>
      {/* 百分比显示在最左边 */}
      <Text style={styles.percentageText}>{progress.percentage}%</Text>

      {/* 4段颜色条 */}
      <View style={styles.stepProgressBar}>
        {progress.steps.map((step, index) => (
          <View
            key={index}
            style={[
              styles.stepSegment,
              {
                backgroundColor: step.completed
                  ? '#FF6B35' // 完成的步骤用橙色
                  : 'rgba(255, 107, 53, 0.2)' // 未完成用浅橙色
              }
            ]}
          />
        ))}
      </View>

      {/* 温馨提示 */}
      {!progress.allCompleted && progress.nextStep && (
        <Text style={styles.warmTip}>
          {progress.nextStep.hint}
        </Text>
      )}

      {progress.allCompleted && (
        <Text style={styles.completeTip}>
          信息已完整，可以发布啦！
        </Text>
      )}
    </View>
  );
};

export default PropertyFormProgress;
