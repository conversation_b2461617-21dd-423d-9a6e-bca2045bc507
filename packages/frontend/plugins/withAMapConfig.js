/**
 * Expo Config Plugin for react-native-amap3d
 * 自动配置高德地图API Key到Android和iOS原生代码中
 */

const { withAndroidManifest, withAppDelegate } = require('@expo/config-plugins');

function withAMapAndroid(config, { apiKey }) {
  return withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;
    
    // 确保application节点存在
    if (!androidManifest.manifest.application) {
      androidManifest.manifest.application = [{}];
    }
    
    const application = androidManifest.manifest.application[0];
    
    // 确保meta-data数组存在
    if (!application['meta-data']) {
      application['meta-data'] = [];
    }
    
    // 添加高德地图API Key
    const amapMetaData = {
      $: {
        'android:name': 'com.amap.api.v2.apikey',
        'android:value': apiKey,
      },
    };
    
    // 检查是否已存在，避免重复添加
    const existingIndex = application['meta-data'].findIndex(
      (item) => item.$?.['android:name'] === 'com.amap.api.v2.apikey'
    );
    
    if (existingIndex >= 0) {
      application['meta-data'][existingIndex] = amapMetaData;
    } else {
      application['meta-data'].push(amapMetaData);
    }
    
    return config;
  });
}

function withAMapIOS(config, { apiKey }) {
  return withAppDelegate(config, (config) => {
    const appDelegate = config.modResults;
    
    // 在didFinishLaunchingWithOptions方法中添加高德地图初始化
    const initCode = `  [AMapServices sharedServices].apiKey = @"${apiKey}";`;
    
    // 检查是否已经添加过
    if (!appDelegate.contents.includes(initCode)) {
      // 在#import语句后添加AMapFoundationKit导入
      if (!appDelegate.contents.includes('#import <AMapFoundationKit/AMapFoundationKit.h>')) {
        appDelegate.contents = appDelegate.contents.replace(
          /#import "AppDelegate.h"/,
          '#import "AppDelegate.h"\n#import <AMapFoundationKit/AMapFoundationKit.h>'
        );
      }
      
      // 在didFinishLaunchingWithOptions方法开始处添加初始化代码
      appDelegate.contents = appDelegate.contents.replace(
        /- \(BOOL\)application:\(UIApplication \*\)application didFinishLaunchingWithOptions:\(NSDictionary \*\)launchOptions\s*\{/,
        `- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions\n{\n${initCode}`
      );
    }
    
    return config;
  });
}

function withAMapConfig(config, { apiKey }) {
  if (!apiKey) {
    throw new Error('高德地图API Key是必需的');
  }
  
  // 移除console.log避免与Gradle构建冲突
  // console.log('[AMapConfig] 配置高德地图API Key:', apiKey);
  
  // 配置Android
  config = withAMapAndroid(config, { apiKey });
  
  // 配置iOS
  config = withAMapIOS(config, { apiKey });
  
  return config;
}

module.exports = withAMapConfig;
