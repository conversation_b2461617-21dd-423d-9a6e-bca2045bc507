/**
 * 🚀 PropertyNavigationMap - 简化版本（基于git历史恢复）
 * 功能：
 * 1. 实时获取用户GPS位置作为起点
 * 2. 房源地址作为终点
 * 3. 美化的Marker样式（起点蓝色，终点红色）
 * 4. 显示真实的导航路线
 * 5. 简洁的交通方式选择
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';
import { MapView, Marker, Polyline } from 'react-native-amap3d';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  getRouteByMode,
  type RouteRequest,
} from '../../services/amapRouteService';

// 🚀 路线规划基础类型定义
type RouteMode = 'driving' | 'taxi' | 'transit' | 'walking' | 'cycling';

interface RouteInfo {
  mode: RouteMode;
  distance: string; // 距离，如 "3.2公里"
  duration: string; // 时间，如 "12分钟"
  cost?: string; // 费用，如 "15元" (仅打车)
  coordinates?: Array<{ latitude: number; longitude: number }>; // 真实路线坐标点
}

interface RouteResult {
  routes: RouteInfo[];
  isLoading: boolean;
  error: string | null;
}

interface NavigationMapProps {
  route: {
    params: {
      propertyLocation: {
        latitude: number | null;
        longitude: number | null;
        address: string;
      };
    };
  };
}

const PropertyNavigationMapSimple: React.FC<NavigationMapProps> = ({
  route,
}) => {
  const { propertyLocation } = route.params;

  // 🚀 高德原生定位状态
  const [nativeLocation, setNativeLocation] = useState<{
    latitude: number;
    longitude: number;
    accuracy: number;
  } | null>(null);

  // 地图状态
  const [mapReady, setMapReady] = useState(false);

  // 🚀 路线规划状态管理
  const [routeResult, setRouteResult] = useState<RouteResult>({
    routes: [],
    isLoading: false,
    error: null,
  });
  const [selectedRouteMode, setSelectedRouteMode] =
    useState<RouteMode>('driving');

  // 🔧 初始化逻辑
  useEffect(() => {
    let isMounted = true;

    const initializeMap = async () => {
      if (!isMounted) return;

      console.log('🚀 [PropertyNavigationMap] 初始化开始');

      try {
        await AsyncStorage.setItem('amap_privacy_agreed_v1', 'true');

        if (!isMounted) return;

        console.log('⏳ 等待MapView加载...');
      } catch (error) {
        console.error('❌ [PropertyNavigationMap] 初始化失败:', error);
      }
    };

    initializeMap();

    return () => {
      isMounted = false;
    };
  }, []);

  // 🚀 获取当前起点位置
  const getCurrentStartLocation = useCallback(() => {
    if (nativeLocation) {
      return {
        latitude: nativeLocation.latitude,
        longitude: nativeLocation.longitude,
        address: '我的位置',
      };
    }
    // 备用：南宁测试位置
    return {
      latitude: 22.807413,
      longitude: 108.421136,
      address: '测试位置',
    };
  }, [nativeLocation]);

  // 🚀 获取当前终点位置
  const getCurrentEndLocation = useCallback(() => {
    if (propertyLocation?.latitude && propertyLocation?.longitude) {
      return {
        latitude: propertyLocation.latitude,
        longitude: propertyLocation.longitude,
        address: propertyLocation.address,
      };
    }
    return null;
  }, [propertyLocation]);

  // 🚀 路线规划API调用
  const calculateRoute = useCallback(
    async (mode?: RouteMode) => {
      const targetMode = mode || selectedRouteMode;
      const currentStart = getCurrentStartLocation();
      const currentEnd = getCurrentEndLocation();

      // 检查是否有必要的位置数据
      if (!currentStart || !currentEnd) {
        console.log('⚠️ [路线规划] 缺少位置数据，无法计算路线');
        return;
      }

      console.log('🚀 [路线规划] 开始计算路线:', {
        mode: targetMode,
        start: currentStart,
        end: currentEnd,
      });

      // 设置加载状态
      setRouteResult(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      try {
        const request: RouteRequest = {
          origin: {
            latitude: currentStart.latitude,
            longitude: currentStart.longitude,
          },
          destination: {
            latitude: currentEnd.latitude,
            longitude: currentEnd.longitude,
          },
        };

        const routeResponse = await getRouteByMode({
          ...request,
          mode: targetMode,
        });

        if (routeResponse.success && routeResponse.data) {
          setRouteResult({
            routes: [
              {
                mode: targetMode,
                distance: routeResponse.data.distance,
                duration: routeResponse.data.duration,
                cost: routeResponse.data.cost,
                coordinates: routeResponse.data.coordinates,
              },
            ],
            isLoading: false,
            error: null,
          });

          console.log('✅ [路线规划] 路线计算成功:', {
            mode: targetMode,
            distance: routeResponse.data.distance,
            duration: routeResponse.data.duration,
            cost: routeResponse.data.cost,
          });
        } else {
          throw new Error(routeResponse.error || '路线计算失败');
        }
      } catch (error) {
        console.error('❌ [路线规划] 路线计算失败:', error);
        setRouteResult({
          routes: [],
          isLoading: false,
          error: error instanceof Error ? error.message : '路线计算失败',
        });
      }
    },
    [selectedRouteMode, getCurrentStartLocation, getCurrentEndLocation]
  );

  // 🚀 外部导航功能
  const handleStartNavigation = useCallback(async () => {
    const currentStart = getCurrentStartLocation();
    const currentEnd = getCurrentEndLocation();

    if (!currentStart || !currentEnd) {
      Alert.alert('提示', '位置信息不完整，无法启动导航');
      return;
    }

    try {
      // 构建高德地图APP导航URL
      const startLat = currentStart.latitude;
      const startLng = currentStart.longitude;
      const endLat = currentEnd.latitude;
      const endLng = currentEnd.longitude;

      // 根据交通方式设置导航模式
      let navMode = '0'; // 默认驾车
      switch (selectedRouteMode) {
        case 'driving':
          navMode = '0';
          break; // 驾车
        case 'transit':
          navMode = '1';
          break; // 公交
        case 'walking':
          navMode = '2';
          break; // 步行
        case 'cycling':
          navMode = '3';
          break; // 骑行
        case 'taxi':
          navMode = '0';
          break; // 打车使用驾车模式
      }

      // 高德地图URL Schema
      const amapUrl = `amapuri://route/plan/?slat=${startLat}&slon=${startLng}&sname=${encodeURIComponent(currentStart.address)}&dlat=${endLat}&dlon=${endLng}&dname=${encodeURIComponent(currentEnd.address)}&dev=0&t=${navMode}`;

      console.log('🗺️ [外部导航] 尝试打开高德地图:', {
        mode: selectedRouteMode,
        navMode,
        url: amapUrl,
      });

      // 检查是否可以打开高德地图
      const canOpen = await Linking.canOpenURL(amapUrl);

      if (canOpen) {
        await Linking.openURL(amapUrl);
        console.log('✅ [外部导航] 成功打开高德地图APP');
      } else {
        // 如果没有安装高德地图，提供备选方案
        Alert.alert('未安装高德地图', '是否前往应用商店下载高德地图APP？', [
          { text: '取消', style: 'cancel' },
          {
            text: '下载',
            onPress: () => {
              const downloadUrl = 'https://mobile.amap.com/';
              Linking.openURL(downloadUrl);
            },
          },
        ]);
      }
    } catch (error) {
      console.error('❌ [外部导航] 启动导航失败:', error);
      Alert.alert('错误', '启动导航失败，请稍后重试');
    }
  }, [getCurrentStartLocation, getCurrentEndLocation, selectedRouteMode]);

  // 🚀 定位或地址更新后自动计算默认路线
  useEffect(() => {
    const currentStart = getCurrentStartLocation();
    const currentEnd = getCurrentEndLocation();

    if (
      currentStart &&
      currentEnd &&
      routeResult.routes.length === 0 &&
      !routeResult.isLoading
    ) {
      console.log('🚀 [自动路线] 检测到位置更新，自动计算驾车路线');
      calculateRoute('driving');
    }
  }, [
    getCurrentStartLocation,
    getCurrentEndLocation,
    routeResult.routes.length,
    routeResult.isLoading,
    calculateRoute,
  ]);

  // 🧪 测试位置备用逻辑
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!nativeLocation && mapReady) {
        console.log('🧪 [测试] 5秒后无定位，使用测试位置');
        setNativeLocation({
          latitude: 22.807413,
          longitude: 108.421136,
          accuracy: 20,
        });
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [nativeLocation, mapReady]);

  return (
    <View style={styles.container}>
      {/* 🎯 简化的交通方式选择栏 */}
      <View style={styles.transportModeContainer}>
        {[
          { mode: 'driving', icon: '🚗', label: '驾车' },
          { mode: 'taxi', icon: '🚕', label: '打车' },
          { mode: 'transit', icon: '🚌', label: '公交' },
          { mode: 'walking', icon: '🚶', label: '步行' },
        ].map(({ mode, icon, label }) => (
          <TouchableOpacity
            key={mode}
            style={[
              styles.transportModeButton,
              selectedRouteMode === mode && styles.transportModeButtonActive,
            ]}
            onPress={() => {
              setSelectedRouteMode(mode as RouteMode);
              calculateRoute(mode as RouteMode);
            }}
          >
            <Text style={styles.transportModeIcon}>{icon}</Text>
            <Text
              style={[
                styles.transportModeText,
                selectedRouteMode === mode && styles.transportModeTextActive,
              ]}
            >
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* 🗺️ 地图显示区域 */}
      <View style={styles.mapContainer}>
        <MapView
          style={styles.map}
          initialCameraPosition={{
            target: {
              latitude: getCurrentEndLocation()?.latitude || 22.816,
              longitude: getCurrentEndLocation()?.longitude || 108.376,
            },
            zoom: 14,
          }}
          myLocationEnabled={true}
          onLocation={event => {
            const coords = event.nativeEvent?.coords;
            if (coords?.latitude && coords?.longitude) {
              // 更新高德原生定位状态
              setNativeLocation(prev => {
                // 只有位置变化超过10米才打印日志，减少日志输出
                const shouldLog =
                  !prev ||
                  Math.abs(prev.latitude - coords.latitude) > 0.0001 ||
                  Math.abs(prev.longitude - coords.longitude) > 0.0001;

                if (shouldLog) {
                  console.log('📍 [高德原生定位] 位置更新:', {
                    latitude: coords.latitude.toFixed(6),
                    longitude: coords.longitude.toFixed(6),
                    accuracy: coords.accuracy?.toFixed(0) + 'm' || '未知',
                    timestamp: new Date().toLocaleTimeString(),
                  });
                }

                return {
                  latitude: coords.latitude,
                  longitude: coords.longitude,
                  accuracy: coords.accuracy || 0,
                };
              });
            }
          }}
          onLoad={() => {
            console.log('🎉 [SUCCESS] MapView加载完成！');
            setMapReady(true);
          }}
        >
          {/* 🏠 终点标记（红色房源标记） */}
          {(() => {
            const endLocation = getCurrentEndLocation();
            if (endLocation) {
              return (
                <Marker
                  position={{
                    latitude: endLocation.latitude,
                    longitude: endLocation.longitude,
                  }}
                  title="房源位置"
                  description={endLocation.address}
                />
              );
            }
            return null;
          })()}

          {/* 📍 起点标记（蓝色用户位置） */}
          {nativeLocation && (
            <Marker
              position={{
                latitude: nativeLocation.latitude,
                longitude: nativeLocation.longitude,
              }}
              title="我的位置"
              description="当前位置"
            />
          )}

          {/* 🔗 真实路线显示 */}
          {(() => {
            // 优先使用API计算的真实路线坐标
            if (
              routeResult.routes.length > 0 &&
              routeResult.routes[0].coordinates &&
              routeResult.routes[0].coordinates.length > 0
            ) {
              const realCoordinates = routeResult.routes[0].coordinates;
              const routeMode = routeResult.routes[0].mode;

              // 根据交通方式设置不同颜色
              const getRouteColor = (mode: string) => {
                switch (mode) {
                  case 'driving':
                    return '#007AFF'; // 蓝色 - 驾车
                  case 'taxi':
                    return '#FFD700'; // 金色 - 出租车
                  case 'transit':
                    return '#32CD32'; // 绿色 - 公交
                  case 'walking':
                    return '#FF6B35'; // 橙色 - 步行
                  case 'cycling':
                    return '#8A2BE2'; // 紫色 - 骑行
                  default:
                    return '#666666'; // 灰色 - 默认
                }
              };

              return (
                <Polyline
                  key={`api-route-${routeMode}-${realCoordinates.length}`}
                  points={realCoordinates}
                  color={getRouteColor(routeMode)}
                  width={6}
                />
              );
            }

            return null;
          })()}
        </MapView>
      </View>

      {/* 🎯 简化的路线信息显示 */}
      {routeResult.routes.length > 0 && !routeResult.isLoading && (
        <View style={styles.routeInfoContainer}>
          <View style={styles.routeInfoContent}>
            <Text style={styles.routeTime}>
              {routeResult.routes[0].duration}
            </Text>
            <Text style={styles.routeDistance}>
              {routeResult.routes[0].distance}
            </Text>
            {routeResult.routes[0].cost && (
              <Text style={styles.routeCost}>{routeResult.routes[0].cost}</Text>
            )}
          </View>

          {/* 🎯 开始导航按钮 */}
          <TouchableOpacity
            style={styles.navigateButton}
            onPress={handleStartNavigation}
          >
            <Text style={styles.navigateButtonText}>开始导航</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* 加载状态提示 */}
      {routeResult.isLoading && (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>正在计算路线...</Text>
        </View>
      )}

      {/* 错误状态提示 */}
      {routeResult.error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{routeResult.error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => calculateRoute(selectedRouteMode)}
          >
            <Text style={styles.retryButtonText}>重试</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },

  // 🎯 简化的交通方式选择栏
  transportModeContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  transportModeButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 8,
    backgroundColor: '#F8F8F8',
  },
  transportModeButtonActive: {
    backgroundColor: '#E8F4FD',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  transportModeIcon: {
    fontSize: 20,
    marginBottom: 2,
  },
  transportModeText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  transportModeTextActive: {
    color: '#007AFF',
    fontWeight: '600',
  },

  // 🗺️ 地图容器
  mapContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#E0E0E0',
  },
  map: {
    flex: 1,
    minHeight: 300,
  },

  // 🎯 路线信息显示
  routeInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    margin: 16,
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  routeInfoContent: {
    flex: 1,
  },
  routeTime: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  routeDistance: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  routeCost: {
    fontSize: 14,
    color: '#FF6B35',
    fontWeight: '500',
  },
  navigateButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  navigateButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },

  // 状态提示
  loadingContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  loadingText: {
    color: 'white',
    fontSize: 14,
  },
  errorContainer: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    right: 16,
    backgroundColor: '#FFE6E6',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FFB3B3',
  },
  errorText: {
    color: '#CC0000',
    fontSize: 14,
    marginBottom: 8,
  },
  retryButton: {
    backgroundColor: '#FF6B35',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  retryButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default PropertyNavigationMapSimple;
