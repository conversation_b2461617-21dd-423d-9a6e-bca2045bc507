/**
 * 文件组织管理器
 * 
 * 功能特性：
 * 1. 统一的文件路径规划
 * 2. 业务场景分类存储
 * 3. 支持文件版本管理
 * 4. 便于权限控制和清理
 * 
 * 文件组织结构设计：
 * 
 * 房源相关文件：
 * /properties/{propertyId}/{YYYYMMDD}/{uuid}_{filename}
 * 
 * 用户相关文件：
 * /users/{userId}/avatar/{uuid}_{filename}           # 头像
 * /users/{userId}/documents/{type}/{uuid}_{filename} # 个人文档
 * 
 * 敏感文档（需要特殊权限）：
 * /secure/{userId}/{type}/{YYYYMMDD}/{uuid}_{filename}
 * 
 * 临时文件（定期清理）：
 * /temp/{userId}/{YYYYMMDD}/{uuid}_{filename}
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月15日
 */

import { MediaBusinessType } from '../transformers/MediaTransformer';

// 文件路径配置
export interface FilePathConfig {
  basePath: string;           // 基础路径
  needsUserId: boolean;       // 是否需要用户ID
  needsPropertyId: boolean;   // 是否需要房源ID
  needsDateFolder: boolean;   // 是否需要日期文件夹
  isSecure: boolean;         // 是否为敏感文件
  isTemporary: boolean;      // 是否为临时文件
  maxRetentionDays?: number; // 最大保留天数
}

// 文件路径结果
export interface FilePathResult {
  fullPath: string;          // 完整路径
  directory: string;         // 目录路径
  filename: string;          // 文件名
  bucket: string;           // 存储桶
  isSecure: boolean;        // 是否敏感
}

export class FileOrganizationManager {
  private static instance: FileOrganizationManager;
  
  // 业务类型的路径配置
  private readonly pathConfigs: Record<MediaBusinessType, FilePathConfig> = {
    [MediaBusinessType.PROPERTY_IMAGE]: {
      basePath: 'properties',
      needsUserId: false,
      needsPropertyId: true,
      needsDateFolder: true,
      isSecure: false,
      isTemporary: false
    },
    [MediaBusinessType.PROPERTY_VIDEO]: {
      basePath: 'properties',
      needsUserId: false,
      needsPropertyId: true,
      needsDateFolder: true,
      isSecure: false,
      isTemporary: false
    },
    // 敏感文档不在此管理器中处理，使用专门的敏感文档管理器
    [MediaBusinessType.IDENTITY_CARD]: {
      basePath: 'NOT_SUPPORTED', // 敏感文档使用huixuanzhi-sensitive桶
      needsUserId: false,
      needsPropertyId: false,
      needsDateFolder: false,
      isSecure: true,
      isTemporary: false
    },
    [MediaBusinessType.BUSINESS_LICENSE]: {
      basePath: 'NOT_SUPPORTED', // 敏感文档使用huixuanzhi-sensitive桶
      needsUserId: false,
      needsPropertyId: false,
      needsDateFolder: false,
      isSecure: true,
      isTemporary: false
    },
    [MediaBusinessType.PROPERTY_CERT]: {
      basePath: 'NOT_SUPPORTED', // 敏感文档使用huixuanzhi-sensitive桶
      needsUserId: false,
      needsPropertyId: false,
      needsDateFolder: false,
      isSecure: true,
      isTemporary: false
    },
    [MediaBusinessType.USER_AVATAR]: {
      basePath: 'users',
      needsUserId: true,
      needsPropertyId: false,
      needsDateFolder: false,
      isSecure: false,
      isTemporary: false
    },
    [MediaBusinessType.PROPERTY_VR_PANORAMA]: {
      basePath: 'properties/vr',
      needsUserId: false,
      needsPropertyId: true,
      needsDateFolder: true,
      isSecure: false,
      isTemporary: false
    },
    [MediaBusinessType.PROPERTY_FLOOR_PLAN]: {
      basePath: 'properties/floorplans',
      needsUserId: false,
      needsPropertyId: true,
      needsDateFolder: true,
      isSecure: false,
      isTemporary: false
    },
    [MediaBusinessType.PROPERTY_THUMBNAIL]: {
      basePath: 'properties/thumbnails',
      needsUserId: false,
      needsPropertyId: true,
      needsDateFolder: false,
      isSecure: false,
      isTemporary: false
    },
    [MediaBusinessType.FINANCIAL_DOC]: {
      basePath: 'secure',
      needsUserId: true,
      needsPropertyId: false,
      needsDateFolder: true,
      isSecure: true,
      isTemporary: false,
      maxRetentionDays: 2555 // 7年保存期
    },
    [MediaBusinessType.OTHER_DOC]: {
      basePath: 'users',
      needsUserId: true,
      needsPropertyId: false,
      needsDateFolder: true,
      isSecure: false,
      isTemporary: false,
      maxRetentionDays: 365 // 1年保存期
    }
  };
  
  /**
   * 单例模式
   */
  public static getInstance(): FileOrganizationManager {
    if (!FileOrganizationManager.instance) {
      FileOrganizationManager.instance = new FileOrganizationManager();
    }
    return FileOrganizationManager.instance;
  }
  
  /**
   * 生成文件路径
   */
  generateFilePath(
    businessType: MediaBusinessType,
    filename: string,
    options: {
      userId?: string;
      propertyId?: string;
      customPath?: string;
    } = {}
  ): FilePathResult {
    const config = this.pathConfigs[businessType];
    const { userId, propertyId, customPath } = options;
    
    // 生成唯一文件名
    const uuid = this.generateUUID();
    const timestamp = Date.now();
    const uniqueFilename = `${uuid}_${timestamp}_${filename}`;
    
    // 构建路径组件
    const pathComponents: string[] = [config.basePath];
    
    // 添加用户ID
    if (config.needsUserId && userId) {
      pathComponents.push(userId);
    }
    
    // 添加房源ID
    if (config.needsPropertyId && propertyId) {
      pathComponents.push(propertyId);
    }
    
    // 添加业务类型子目录
    if (businessType === MediaBusinessType.USER_AVATAR) {
      pathComponents.push('avatar');
    } else if (config.isSecure) {
      pathComponents.push(this.getSecureSubPath(businessType));
    }
    
    // 添加日期文件夹
    if (config.needsDateFolder) {
      const dateFolder = this.getDateFolder();
      pathComponents.push(dateFolder);
    }
    
    // 使用自定义路径（如果提供）
    if (customPath) {
      pathComponents.push(customPath);
    }
    
    // 构建完整路径
    const directory = pathComponents.join('/');
    const fullPath = `${directory}/${uniqueFilename}`;
    
    // 确定存储桶
    const bucket = config.isSecure ? 'huixuanzhi-sensitive' : 'huixuanzhi-main';
    
    return {
      fullPath,
      directory,
      filename: uniqueFilename,
      bucket,
      isSecure: config.isSecure
    };
  }
  
  /**
   * 解析文件路径信息
   */
  parseFilePath(fullPath: string): {
    businessType?: MediaBusinessType;
    userId?: string;
    propertyId?: string;
    dateFolder?: string;
    originalFilename?: string;
    isSecure: boolean;
  } {
    const parts = fullPath.split('/');
    const result: any = { isSecure: false };
    
    if (parts.length === 0) return result;
    
    // 判断是否为敏感文件
    result.isSecure = parts[0] === 'secure';
    
    // 根据基础路径判断业务类型
    if (parts[0] === 'properties') {
      result.propertyId = parts[1];
      result.dateFolder = parts[2];
      if (parts[3]?.includes('video') || parts[3]?.includes('.mp4') || parts[3]?.includes('.mov')) {
        result.businessType = MediaBusinessType.PROPERTY_VIDEO;
      } else {
        result.businessType = MediaBusinessType.PROPERTY_IMAGE;
      }
    } else if (parts[0] === 'users') {
      result.userId = parts[1];
      if (parts[2] === 'avatar') {
        result.businessType = MediaBusinessType.USER_AVATAR;
      } else {
        result.businessType = MediaBusinessType.OTHER_DOC;
      }
    } else if (parts[0] === 'secure') {
      result.userId = parts[1];
      const subType = parts[2];
      result.dateFolder = parts[3];
      
      switch (subType) {
        case 'identity':
          result.businessType = MediaBusinessType.IDENTITY_CARD;
          break;
        case 'business':
          result.businessType = MediaBusinessType.BUSINESS_LICENSE;
          break;
        case 'property-cert':
          result.businessType = MediaBusinessType.PROPERTY_CERT;
          break;
        case 'financial':
          result.businessType = MediaBusinessType.FINANCIAL_DOC;
          break;
      }
    }
    
    // 提取原始文件名
    const filename = parts[parts.length - 1];
    if (filename) {
      const parts_filename = filename.split('_');
      if (parts_filename.length >= 3) {
        result.originalFilename = parts_filename.slice(2).join('_');
      }
    }
    
    return result;
  }
  
  /**
   * 检查文件是否过期
   */
  isFileExpired(fullPath: string): boolean {
    const pathInfo = this.parseFilePath(fullPath);
    if (!pathInfo.businessType || !pathInfo.dateFolder) return false;
    
    const config = this.pathConfigs[pathInfo.businessType];
    if (!config.maxRetentionDays) return false;
    
    try {
      const fileDate = new Date(
        parseInt(pathInfo.dateFolder.substring(0, 4)),
        parseInt(pathInfo.dateFolder.substring(4, 6)) - 1,
        parseInt(pathInfo.dateFolder.substring(6, 8))
      );
      
      const expiryDate = new Date(fileDate.getTime() + config.maxRetentionDays * 24 * 60 * 60 * 1000);
      return new Date() > expiryDate;
    } catch {
      return false;
    }
  }
  
  /**
   * 获取清理候选文件
   */
  getCleanupCandidates(businessType: MediaBusinessType): string[] {
    // 这里应该连接到实际的文件系统或数据库
    // 返回需要清理的文件路径列表
    console.log(`[FileOrganizationManager] 获取 ${businessType} 类型的清理候选文件`);
    return [];
  }
  
  /**
   * 生成UUID
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  /**
   * 获取日期文件夹名称
   */
  private getDateFolder(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }
  
  /**
   * 获取敏感文件子路径
   */
  private getSecureSubPath(businessType: MediaBusinessType): string {
    switch (businessType) {
      case MediaBusinessType.IDENTITY_CARD:
        return 'identity';
      case MediaBusinessType.BUSINESS_LICENSE:
        return 'business';
      case MediaBusinessType.PROPERTY_CERT:
        return 'property-cert';
      case MediaBusinessType.FINANCIAL_DOC:
        return 'financial';
      default:
        return 'other';
    }
  }
}

// 导出单例实例
export const fileOrganizationManager = FileOrganizationManager.getInstance();
