/**
 * 批量上传进度组件
 * 
 * 功能特性：
 * - WeChat风格的循环进度条
 * - 实时上传速度和剩余时间显示
 * - 文件列表和状态展示
 * - 错误处理和重试按钮
 * - 动画效果和用户反馈
 * 
 * 设计原则：
 * - 用户体验优先：清晰的进度反馈
 * - 移动端优化：适配小屏幕显示
 * - 交互友好：支持取消和重试操作
 * 
 * 作者: AI编码助手
 * 创建时间: 2025年7月16日
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  ScrollView,
  Dimensions
} from 'react-native';
import { BatchProgress, ProcessedFile, FileProcessingStatus } from '../../services/media/OptimizedBatchUploader';

interface BatchUploadProgressProps {
  progress: BatchProgress;
  files: ProcessedFile[];
  visible: boolean;
  onCancel?: () => void;
  onRetry?: (fileId: string) => void;
  onClose?: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

export const BatchUploadProgress: React.FC<BatchUploadProgressProps> = ({
  progress,
  files,
  visible,
  onCancel,
  onRetry,
  onClose
}) => {
  const [circleAnimation] = useState(new Animated.Value(0));
  const [fadeAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    if (visible) {
      // 淡入动画
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // 循环进度动画
      const animateCircle = () => {
        Animated.timing(circleAnimation, {
          toValue: progress.overallProgress / 100,
          duration: 500,
          useNativeDriver: false,
        }).start();
      };

      animateCircle();
    } else {
      Animated.timing(fadeAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, progress.overallProgress]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}秒`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}分${remainingSeconds}秒`;
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    return `${formatFileSize(bytesPerSecond)}/s`;
  };

  const getStatusColor = (status: FileProcessingStatus): string => {
    switch (status) {
      case FileProcessingStatus.COMPLETED:
        return '#4CAF50';
      case FileProcessingStatus.FAILED:
        return '#F44336';
      case FileProcessingStatus.UPLOADING:
        return '#2196F3';
      case FileProcessingStatus.RETRYING:
        return '#FF9800';
      default:
        return '#9E9E9E';
    }
  };

  const getStatusText = (status: FileProcessingStatus): string => {
    switch (status) {
      case FileProcessingStatus.PENDING:
        return '等待中';
      case FileProcessingStatus.PREPROCESSING:
        return '预处理';
      case FileProcessingStatus.UPLOADING:
        return '上传中';
      case FileProcessingStatus.COMPLETED:
        return '已完成';
      case FileProcessingStatus.FAILED:
        return '失败';
      case FileProcessingStatus.RETRYING:
        return '重试中';
      default:
        return '未知';
    }
  };

  if (!visible) return null;

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnimation }]}>
      <View style={styles.modal}>
        {/* 标题栏 */}
        <View style={styles.header}>
          <Text style={styles.title}>批量上传</Text>
          {onClose && (
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeText}>×</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* 总体进度 */}
        <View style={styles.overallProgress}>
          {/* WeChat风格的循环进度条 */}
          <View style={styles.circleContainer}>
            <View style={styles.circleBackground}>
              <Animated.View
                style={[
                  styles.circleProgress,
                  {
                    transform: [
                      {
                        rotate: circleAnimation.interpolate({
                          inputRange: [0, 1],
                          outputRange: ['0deg', '360deg'],
                        }),
                      },
                    ],
                  },
                ]}
              />
            </View>
            <Text style={styles.progressText}>
              {Math.round(progress.overallProgress)}%
            </Text>
          </View>

          {/* 进度信息 */}
          <View style={styles.progressInfo}>
            <Text style={styles.progressTitle}>
              {progress.completedFiles}/{progress.totalFiles} 个文件
            </Text>
            <Text style={styles.progressSubtitle}>
              {progress.currentFileName && `正在上传: ${progress.currentFileName}`}
            </Text>
            {progress.uploadSpeed && progress.uploadSpeed > 0 && (
              <Text style={styles.speedText}>
                速度: {formatSpeed(progress.uploadSpeed)}
              </Text>
            )}
            {progress.estimatedTimeRemaining && progress.estimatedTimeRemaining > 0 && (
              <Text style={styles.timeText}>
                剩余时间: {formatTime(progress.estimatedTimeRemaining)}
              </Text>
            )}
          </View>
        </View>

        {/* 文件列表 */}
        <ScrollView style={styles.fileList} showsVerticalScrollIndicator={false}>
          {files.map((file) => (
            <View key={file.id} style={styles.fileItem}>
              <View style={styles.fileInfo}>
                <Text style={styles.fileName} numberOfLines={1}>
                  {file.originalFile.name}
                </Text>
                <Text style={styles.fileSize}>
                  {formatFileSize(file.originalFile.size)}
                </Text>
              </View>

              <View style={styles.fileStatus}>
                <View
                  style={[
                    styles.statusIndicator,
                    { backgroundColor: getStatusColor(file.status) }
                  ]}
                />
                <Text style={[styles.statusText, { color: getStatusColor(file.status) }]}>
                  {getStatusText(file.status)}
                </Text>
                {file.status === FileProcessingStatus.UPLOADING && (
                  <Text style={styles.fileProgress}>
                    {Math.round(file.progress)}%
                  </Text>
                )}
              </View>

              {/* 重试按钮 */}
              {file.status === FileProcessingStatus.FAILED && onRetry && (
                <TouchableOpacity
                  style={styles.retryButton}
                  onPress={() => onRetry(file.id)}
                >
                  <Text style={styles.retryText}>重试</Text>
                </TouchableOpacity>
              )}

              {/* 错误信息 */}
              {file.error && (
                <Text style={styles.errorText} numberOfLines={2}>
                  {file.error}
                </Text>
              )}
            </View>
          ))}
        </ScrollView>

        {/* 操作按钮 */}
        <View style={styles.actions}>
          {onCancel && progress.overallProgress < 100 && (
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelText}>取消上传</Text>
            </TouchableOpacity>
          )}
          {progress.overallProgress === 100 && onClose && (
            <TouchableOpacity style={styles.completeButton} onPress={onClose}>
              <Text style={styles.completeText}>完成</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modal: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: screenWidth * 0.9,
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeText: {
    fontSize: 24,
    color: '#999',
  },
  overallProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  circleContainer: {
    marginRight: 20,
  },
  circleBackground: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  circleProgress: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    borderColor: '#4CAF50',
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
  },
  progressText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  progressInfo: {
    flex: 1,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  progressSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  speedText: {
    fontSize: 12,
    color: '#2196F3',
  },
  timeText: {
    fontSize: 12,
    color: '#FF9800',
  },
  fileList: {
    maxHeight: 200,
    marginBottom: 20,
  },
  fileItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  fileInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  fileName: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    marginRight: 10,
  },
  fileSize: {
    fontSize: 12,
    color: '#999',
  },
  fileStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 12,
    marginRight: 8,
  },
  fileProgress: {
    fontSize: 12,
    color: '#2196F3',
  },
  retryButton: {
    alignSelf: 'flex-start',
    marginTop: 8,
    paddingHorizontal: 12,
    paddingVertical: 4,
    backgroundColor: '#FF9800',
    borderRadius: 4,
  },
  retryText: {
    fontSize: 12,
    color: 'white',
  },
  errorText: {
    fontSize: 12,
    color: '#F44336',
    marginTop: 4,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  cancelButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#F44336',
    borderRadius: 6,
  },
  cancelText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  completeButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#4CAF50',
    borderRadius: 6,
  },
  completeText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
