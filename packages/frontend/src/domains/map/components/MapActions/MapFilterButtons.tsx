/**
 * 地图筛选按钮组件
 * 职责：租赁筛选、买房筛选、画圈筛选功能
 *
 * 设计理念：
 * 1. 参考贝壳找房、链家等主流应用的筛选设计
 * 2. 采用极简风格，清晰的视觉层次
 * 3. 选中状态有明显的视觉反馈
 * 4. 支持单选和切换操作
 */

import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { wp, hp, fp } from '@shared/utils/responsive';

interface MapFilterButtonsProps {
  selectedDealType: 'rent' | 'sale' | null;
  onDealTypeChange: (type: 'rent' | 'sale' | null) => void;
  onRentFilterPress: () => void;
  onSaleFilterPress: () => void; // 新增：打开筛选弹窗
}

export const MapFilterButtons: React.FC<MapFilterButtonsProps> = ({
  selectedDealType,
  onDealTypeChange,
  onRentFilterPress,
  onSaleFilterPress,
}) => {
  const handleRentPress = () => {
    if (selectedDealType === 'rent') {
      onDealTypeChange(null);
    } else {
      onDealTypeChange('rent');
      onRentFilterPress();
    }
  };

  const handleSalePress = () => {
    if (selectedDealType === 'sale') {
      onDealTypeChange(null);
    } else {
      onDealTypeChange('sale');
      onSaleFilterPress();
    }
  };

  return (
    <View style={styles.container}>
      {/* 租赁筛选按钮 */}
      <TouchableOpacity
        style={[
          styles.filterButton,
          selectedDealType === 'rent' && styles.filterButtonActive,
        ]}
        onPress={handleRentPress}
        activeOpacity={0.7}
      >
        <View style={styles.buttonContent}>
          <View
            style={[
              styles.indicator,
              selectedDealType === 'rent' && styles.indicatorActive,
            ]}
          />
          <Text
            style={[
              styles.buttonText,
              selectedDealType === 'rent' && styles.buttonTextActive,
            ]}
          >
            租赁
          </Text>
        </View>
      </TouchableOpacity>

      {/* 买房筛选按钮 */}
      <TouchableOpacity
        style={[
          styles.filterButton,
          selectedDealType === 'sale' && styles.filterButtonActive,
        ]}
        onPress={handleSalePress}
        activeOpacity={0.7}
      >
        <View style={styles.buttonContent}>
          <View
            style={[
              styles.indicator,
              selectedDealType === 'sale' && styles.indicatorActive,
            ]}
          />
          <Text
            style={[
              styles.buttonText,
              selectedDealType === 'sale' && styles.buttonTextActive,
            ]}
          >
            买房
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: hp(8),
  },
  filterButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: wp(20),
    paddingHorizontal: wp(12),
    paddingVertical: hp(8),
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    minWidth: wp(90),
  },
  filterButtonActive: {
    backgroundColor: '#fff',
    borderColor: '#FF4F19', // 主题红色
    borderWidth: 1.5,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: wp(6),
  },
  indicator: {
    width: wp(6),
    height: wp(6),
    borderRadius: wp(3),
    borderWidth: 1,
    borderColor: '#999',
    backgroundColor: 'transparent',
  },
  indicatorActive: {
    backgroundColor: '#FF4F19',
    borderColor: '#FF4F19',
  },
  buttonText: {
    fontSize: fp(12),
    color: '#666',
    fontWeight: '500',
  },
  buttonTextActive: {
    color: '#333',
    fontWeight: '600',
  },
});
