/**
 * 🚨 紧急修复验证脚本
 * 验证LocationData重复声明和关键语法错误是否已修复
 */

const fs = require('fs');
const path = require('path');

console.log('🚨 [EmergencyFix] 开始验证地图导航紧急修复...');

// 关键文件路径
const FILES_TO_CHECK = [
  '/data/my-real-estate-app/packages/frontend/src/domains/property/components/detail/hooks/usePropertyNavigation.ts',
  '/data/my-real-estate-app/packages/frontend/src/domains/property/components/detail/components/RouteSelectionPanel.tsx',
  '/data/my-real-estate-app/packages/frontend/src/screens/Property/PropertyNavigationScreen.tsx',
];

const criticalIssues = [];

// 1. 检查LocationData重复声明
console.log('\n📋 [Check 1] LocationData重复声明检查...');

const hookFile = FILES_TO_CHECK[0];
if (fs.existsSync(hookFile)) {
  const content = fs.readFileSync(hookFile, 'utf8');

  // 检查是否有多个LocationData导入
  const locationDataImports =
    content.match(/import[\s\S]*?LocationData/g) || [];
  console.log(`🔍 发现LocationData导入: ${locationDataImports.length}次`);

  if (locationDataImports.length > 1) {
    criticalIssues.push('LocationData重复导入');
    console.log('❌ LocationData重复导入问题仍存在');
  } else {
    console.log('✅ LocationData重复导入问题已修复');
  }

  // 检查是否有type LocationData导入
  const typeLocationDataImport = /type LocationData/.test(content);
  if (typeLocationDataImport) {
    criticalIssues.push('Store中type LocationData导入仍存在');
    console.log('❌ Store中type LocationData导入仍存在');
  } else {
    console.log('✅ Store中type LocationData导入已移除');
  }
} else {
  criticalIssues.push('usePropertyNavigation Hook文件不存在');
}

// 2. 检查RouteSelectionPanel类型冲突
console.log('\n📋 [Check 2] RouteSelectionPanel类型冲突检查...');

const panelFile = FILES_TO_CHECK[1];
if (fs.existsSync(panelFile)) {
  const content = fs.readFileSync(panelFile, 'utf8');

  // 检查是否还有重复的RouteMode定义
  const routeModeDefinitions =
    content.match(/export type RouteMode.*?=|export interface.*RouteMode/g) ||
    [];
  console.log(`🔍 发现本地RouteMode定义: ${routeModeDefinitions.length}次`);

  if (routeModeDefinitions.length > 0) {
    criticalIssues.push('RouteSelectionPanel存在重复类型定义');
    console.log('❌ RouteSelectionPanel仍有本地类型定义');
  } else {
    console.log('✅ RouteSelectionPanel已使用统一类型定义');
  }

  // 检查是否正确导入统一类型
  const unifiedImport =
    /import[\s\S]*?RouteMode[\s\S]*?from.*navigation\.types/.test(content);
  if (unifiedImport) {
    console.log('✅ RouteSelectionPanel正确导入统一类型');
  } else {
    criticalIssues.push('RouteSelectionPanel未正确导入统一类型');
    console.log('❌ RouteSelectionPanel未正确导入统一类型');
  }
} else {
  criticalIssues.push('RouteSelectionPanel文件不存在');
}

// 3. 检查PropertyNavigationScreen类型适配
console.log('\n📋 [Check 3] PropertyNavigationScreen类型适配检查...');

const screenFile = FILES_TO_CHECK[2];
if (fs.existsSync(screenFile)) {
  const content = fs.readFileSync(screenFile, 'utf8');

  // 检查是否有类型适配
  const hasTypeAdaptation = /as any/.test(content);
  if (hasTypeAdaptation) {
    console.log('✅ PropertyNavigationScreen已添加临时类型适配');
  } else {
    criticalIssues.push('PropertyNavigationScreen缺少类型适配');
    console.log('❌ PropertyNavigationScreen缺少类型适配');
  }

  // 检查route参数结构
  const hasRouteKey = /key:.*name:/.test(content);
  if (hasRouteKey) {
    console.log('✅ PropertyNavigationScreen route参数结构完整');
  } else {
    criticalIssues.push('PropertyNavigationScreen route参数结构不完整');
    console.log('❌ PropertyNavigationScreen route参数结构不完整');
  }
} else {
  criticalIssues.push('PropertyNavigationScreen文件不存在');
}

// 4. 语法检查
console.log('\n📋 [Check 4] 基础语法检查...');

FILES_TO_CHECK.forEach((filePath, index) => {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath);

    // 检查基础语法错误
    const syntaxChecks = [
      { name: '缺少分号', pattern: /import.*from.*[^;]$/m },
      { name: '重复声明', pattern: /Identifier.*already.*declared/i },
      { name: '未闭合括号', pattern: /\{[^}]*$/m },
      { name: '未闭合字符串', pattern: /['"][^'"]*$/m },
    ];

    syntaxChecks.forEach(({ name, pattern }) => {
      if (pattern.test(content)) {
        criticalIssues.push(`${fileName}: ${name}`);
        console.log(`❌ ${fileName}: 发现${name}问题`);
      }
    });

    console.log(`✅ ${fileName}: 基础语法检查通过`);
  }
});

// 5. 生成修复报告
console.log('\n📊 [Fix Report] 紧急修复结果汇总:');

if (criticalIssues.length === 0) {
  console.log('🎉 [SUCCESS] 所有关键问题已修复！');
  console.log('✅ LocationData重复声明: 已修复');
  console.log('✅ RouteSelectionPanel类型冲突: 已修复');
  console.log('✅ PropertyNavigationScreen类型适配: 已修复');
  console.log('✅ 基础语法错误: 无发现');

  console.log('\n🚀 [Next Steps] 后续建议:');
  console.log('1. 🔄 重启Metro bundler');
  console.log('2. 📱 测试地图导航页面打开');
  console.log('3. 🔍 验证地址搜索功能正常');
  console.log('4. 🎯 进行完整功能测试');
} else {
  console.log('❌ [FAILED] 仍有关键问题需要修复:');
  criticalIssues.forEach((issue, index) => {
    console.log(`   ${index + 1}. ${issue}`);
  });

  console.log('\n🔧 [Fix Required] 需要进一步修复');
}

console.log(
  `\n📈 [Summary] 修复成功率: ${Math.round(((FILES_TO_CHECK.length * 4 - criticalIssues.length) / (FILES_TO_CHECK.length * 4)) * 100)}%`
);

console.log('\n🔚 [EmergencyFix] 紧急修复验证完成！');
