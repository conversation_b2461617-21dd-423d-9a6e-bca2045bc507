"""
房源地理查询Repository层

企业级架构 - Repository层
负责所有与房源地理位置相关的数据访问操作，封装PostGIS空间查询逻辑。
严格遵循单一职责原则，只处理数据访问，不包含业务逻辑。
"""

from typing import List, Optional, Tuple
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, func
from sqlalchemy.orm import selectinload
from geoalchemy2.functions import ST_DWithin, ST_GeogFromText, ST_Distance, ST_AsText
from geoalchemy2 import Geography

from app.models.property.property import Property
import logging

logger = logging.getLogger(__name__)


class PropertyGeoRepository:
    """
    房源地理查询Repository
    
    企业级数据访问层，封装所有PostGIS空间查询操作。
    提供高性能的地理位置查询接口，支持距离查询、边界查询等。
    """
    
    def __init__(self, session: AsyncSession):
        """
        初始化Repository
        
        Args:
            session: 异步数据库会话
        """
        self.session = session
    
    async def find_properties_within_radius(
        self,
        center_lat: float,
        center_lng: float,
        radius_meters: int,
        limit: int = 50,
        offset: int = 0
    ) -> List[Property]:
        """
        查询指定半径内的房源
        
        使用PostGIS的ST_DWithin函数进行高效的空间查询。
        
        Args:
            center_lat: 中心点纬度
            center_lng: 中心点经度
            radius_meters: 查询半径（米）
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            房源列表
        """
        try:
            # 创建中心点的PostGIS地理对象
            center_point = f"POINT({center_lng} {center_lat})"
            
            # 构建空间查询
            query = (
                select(Property)
                .where(
                    Property.location.isnot(None),  # 确保有地理位置
                    Property.status == "ACTIVE",    # 只查询活跃房源
                    ST_DWithin(
                        Property.location,
                        ST_GeogFromText(center_point),
                        radius_meters
                    )
                )
                .options(
                    selectinload(Property.features),  # 预加载特性
                    selectinload(Property.media_files)  # 预加载媒体文件
                )
                .offset(offset)
                .limit(limit)
            )
            
            result = await self.session.execute(query)
            properties = result.scalars().all()
            
            logger.info(
                f"地理查询完成: 中心点({center_lat}, {center_lng}), "
                f"半径{radius_meters}m, 找到{len(properties)}个房源"
            )
            
            return list(properties)
            
        except Exception as e:
            logger.error(f"地理查询失败: {str(e)}")
            raise
    
    async def find_properties_within_bounds(
        self,
        north_lat: float,
        south_lat: float,
        east_lng: float,
        west_lng: float,
        limit: int = 100,
        offset: int = 0
    ) -> List[Property]:
        """
        查询指定边界内的房源
        
        使用边界框查询，适用于地图视窗查询。
        
        Args:
            north_lat: 北边界纬度
            south_lat: 南边界纬度
            east_lng: 东边界经度
            west_lng: 西边界经度
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            房源列表
        """
        try:
            # 构建边界框查询
            query = (
                select(Property)
                .where(
                    Property.location.isnot(None),
                    Property.status == "ACTIVE",
                    Property.latitude.between(south_lat, north_lat),
                    Property.longitude.between(west_lng, east_lng)
                )
                .options(
                    selectinload(Property.features),
                    selectinload(Property.media_files)
                )
                .offset(offset)
                .limit(limit)
            )
            
            result = await self.session.execute(query)
            properties = result.scalars().all()
            
            logger.info(
                f"边界查询完成: 边界({south_lat}-{north_lat}, {west_lng}-{east_lng}), "
                f"找到{len(properties)}个房源"
            )
            
            return list(properties)
            
        except Exception as e:
            logger.error(f"边界查询失败: {str(e)}")
            raise
    
    async def calculate_distance_to_property(
        self,
        property_id: UUID,
        target_lat: float,
        target_lng: float
    ) -> Optional[float]:
        """
        计算房源到指定点的距离
        
        Args:
            property_id: 房源ID
            target_lat: 目标点纬度
            target_lng: 目标点经度
            
        Returns:
            距离（米），如果房源不存在或无地理位置则返回None
        """
        try:
            target_point = f"POINT({target_lng} {target_lat})"
            
            query = (
                select(
                    ST_Distance(
                        Property.location,
                        ST_GeogFromText(target_point)
                    ).label("distance")
                )
                .where(
                    Property.id == property_id,
                    Property.location.isnot(None)
                )
            )
            
            result = await self.session.execute(query)
            distance = result.scalar()
            
            if distance is not None:
                logger.debug(f"距离计算完成: 房源{property_id}到目标点距离{distance:.2f}米")
            
            return distance
            
        except Exception as e:
            logger.error(f"距离计算失败: {str(e)}")
            raise
    
    async def update_property_location(
        self,
        property_id: UUID,
        latitude: float,
        longitude: float
    ) -> bool:
        """
        更新房源地理位置
        
        Args:
            property_id: 房源ID
            latitude: 纬度
            longitude: 经度
            
        Returns:
            更新是否成功
        """
        try:
            # 创建PostGIS点对象
            point_wkt = f"POINT({longitude} {latitude})"
            
            # 更新地理位置字段
            query = text("""
                UPDATE properties 
                SET 
                    latitude = :lat,
                    longitude = :lng,
                    location = ST_GeogFromText(:point_wkt),
                    updated_at = NOW()
                WHERE id = :property_id
            """)
            
            result = await self.session.execute(
                query,
                {
                    "lat": latitude,
                    "lng": longitude,
                    "point_wkt": point_wkt,
                    "property_id": str(property_id)
                }
            )
            
            success = result.rowcount > 0
            
            if success:
                logger.info(f"房源{property_id}地理位置更新成功: ({latitude}, {longitude})")
            else:
                logger.warning(f"房源{property_id}地理位置更新失败: 房源不存在")
            
            return success
            
        except Exception as e:
            logger.error(f"地理位置更新失败: {str(e)}")
            raise
    
    async def get_properties_with_distances(
        self,
        center_lat: float,
        center_lng: float,
        radius_meters: int,
        limit: int = 50
    ) -> List[Tuple[Property, float]]:
        """
        获取房源及其到中心点的距离
        
        Args:
            center_lat: 中心点纬度
            center_lng: 中心点经度
            radius_meters: 查询半径（米）
            limit: 返回数量限制
            
        Returns:
            (房源, 距离)元组列表，按距离排序
        """
        try:
            center_point = f"POINT({center_lng} {center_lat})"
            
            query = (
                select(
                    Property,
                    ST_Distance(
                        Property.location,
                        ST_GeogFromText(center_point)
                    ).label("distance")
                )
                .where(
                    Property.location.isnot(None),
                    Property.status == "ACTIVE",
                    ST_DWithin(
                        Property.location,
                        ST_GeogFromText(center_point),
                        radius_meters
                    )
                )
                .options(
                    selectinload(Property.features),
                    selectinload(Property.media_files)
                )
                .order_by("distance")
                .limit(limit)
            )
            
            result = await self.session.execute(query)
            rows = result.all()
            
            properties_with_distances = [(row.Property, row.distance) for row in rows]
            
            logger.info(
                f"距离查询完成: 中心点({center_lat}, {center_lng}), "
                f"半径{radius_meters}m, 找到{len(properties_with_distances)}个房源"
            )
            
            return properties_with_distances
            
        except Exception as e:
            logger.error(f"距离查询失败: {str(e)}")
            raise
