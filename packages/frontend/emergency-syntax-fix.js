/**
 * 🚨 紧急语法错误修复脚本
 *
 * 问题：React Native报告第297行有重复的generateDirectionMarkers定义
 * 解决：检查并修复可能的语法问题或隐藏字符
 */

const fs = require('fs');
const path = require('path');

console.log('🚨 紧急语法错误修复\n');

const filePath = path.join(
  __dirname,
  'src/domains/property/components/detail/PropertyNavigationMap.tsx'
);

console.log('📋 检查文件:', filePath);

try {
  // 读取文件内容
  const content = fs.readFileSync(filePath, 'utf8');

  // 查找所有generateDirectionMarkers出现位置
  const lines = content.split('\n');
  const matches = [];

  lines.forEach((line, index) => {
    if (line.includes('generateDirectionMarkers')) {
      matches.push({
        lineNumber: index + 1,
        content: line.trim(),
        isDefinition: line.includes('const generateDirectionMarkers'),
        isUsage:
          !line.includes('const generateDirectionMarkers') &&
          line.includes('generateDirectionMarkers'),
      });
    }
  });

  console.log('🔍 找到的所有generateDirectionMarkers引用:');
  console.log('─'.repeat(60));

  matches.forEach(match => {
    console.log(
      `第${match.lineNumber}行: ${match.isDefinition ? '[定义]' : '[使用]'}`
    );
    console.log(`   ${match.content}`);
    console.log('');
  });

  // 检查是否有重复定义
  const definitions = matches.filter(m => m.isDefinition);

  if (definitions.length > 1) {
    console.log('❌ 发现重复定义!');
    console.log('需要删除的重复定义:');
    definitions.slice(1).forEach(def => {
      console.log(`   第${def.lineNumber}行: ${def.content}`);
    });
  } else if (definitions.length === 1) {
    console.log('✅ 只找到一个定义，应该是正确的');
    console.log(
      `   第${definitions[0].lineNumber}行: ${definitions[0].content}`
    );
  } else {
    console.log('❌ 没有找到定义!');
  }

  // 检查第297行具体内容
  if (lines.length >= 297) {
    console.log('\n🔍 第297行内容检查:');
    console.log('─'.repeat(30));
    console.log('第295行:', lines[294] || '(空行)');
    console.log('第296行:', lines[295] || '(空行)');
    console.log('第297行:', lines[296] || '(空行)');
    console.log('第298行:', lines[297] || '(空行)');
    console.log('第299行:', lines[298] || '(空行)');
  }

  // 检查文件是否有隐藏字符或编码问题
  const hasSpecialChars = /[\u200B-\u200D\uFEFF]/.test(content);
  if (hasSpecialChars) {
    console.log('⚠️  发现隐藏字符，可能导致语法错误');
  }

  console.log('\n📊 文件统计:');
  console.log(`总行数: ${lines.length}`);
  console.log(`文件大小: ${content.length} 字符`);
  console.log(`generateDirectionMarkers引用次数: ${matches.length}`);
} catch (error) {
  console.error('❌ 读取文件失败:', error.message);
}

console.log('\n🚀 建议修复方案:');
console.log('1. 确认只有一个generateDirectionMarkers定义');
console.log('2. 清理Metro缓存: npx react-native start --reset-cache');
console.log('3. 重启开发服务器');
console.log('4. 如果仍有问题，重新创建该函数');
