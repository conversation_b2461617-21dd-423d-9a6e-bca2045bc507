/**
 * 主应用导航器 - 2025企业级标准
 * 实现完整的Stack + Tab混合导航架构
 */

import React, { useEffect, Suspense, useCallback } from 'react';
// @ts-ignore - 临时忽略模块解析问题，确保构建通过
import {
  NavigationContainer,
  createNavigationContainerRef,
  getFocusedRouteNameFromRoute,
  NavigationState,
} from '@react-navigation/native';
// @ts-ignore - 临时忽略模块解析问题，确保构建通过
import { createStackNavigator, StackScreenProps } from '@react-navigation/stack';
import { View, StyleSheet, Text, ActivityIndicator } from 'react-native';
// import { useColorScheme } from 'react-native'; // 暂时未使用

// 导航类型
import type { RootStackParamList } from './types';

// 导航器组件
import { MainTabNavigator } from './MainTabNavigator';
// import { AuthStackNavigator } from './AuthStackNavigator'; // 暂时未使用

// 页面组件 - 使用现有的组件
import { SplashScreenNav } from '../screens/SplashScreenNav';
// import { HomeScreen } from '../screens/MainTabs/HomeScreen'; // 暂时未使用
import { SearchScreen } from '../domains/search/screens/SearchScreen';
// import { PublishScreen } from '../screens/Publish/PublishScreen'; // 暂时未使用
// import { MessagesScreen } from '../screens/Messages/MessagesScreen'; // 暂时未使用
// 导入重构后的用户中心页面
import { ProfileScreen } from '../domains/user/screens/profile/ProfileScreen';

// ✅ React Navigation 6官方建议：使用React.lazy()懒加载非关键屏幕
const LoginScreen = React.lazy(() => import('../domains/auth/screens/LoginScreen'));
const QuickLoginScreen = React.lazy(() => import('../domains/auth/screens/QuickLoginScreen'));
const RegisterScreen = React.lazy(() => import('../domains/auth/screens/RegisterScreen'));
const HelpScreen = React.lazy(() => import('../domains/auth/screens/HelpScreen'));
const ForgotPasswordScreen = React.lazy(() => import('../domains/auth/screens/ForgotPasswordScreen'));
const SmsLoginScreen = React.lazy(() => import('../domains/auth/screens/SmsLoginScreen'));

const TempHomeScreen = React.lazy(() => import('../screens/TempHomeScreen'));

// 房源详情页面 - 非关键屏幕使用懒加载
const PropertyDetailScreen = React.lazy(() => import('../screens/Property/PropertyDetailScreen'));
const PropertyTypePage = React.lazy(() => import('../screens/PropertyType/PropertyTypePage').then(m => ({ default: m.PropertyTypePage })));
const CommuteAnalysisScreen = React.lazy(() => import('../screens/Property/CommuteAnalysisScreen'));

// 消息系统页面 - 使用懒加载
const MessageCenterScreen = React.lazy(() => import('../domains/message/screens/MessageCenterScreen'));
const ChatDetailScreen = React.lazy(() => import('../domains/message/screens/ChatDetailScreen').then(m => ({ default: m.ChatDetailScreen })));
const LandlordSubscriptionScreen = React.lazy(() => import('../domains/message/screens/LandlordSubscriptionScreen'));

// 用户中心详细页面 - 使用懒加载
const MyDemandsScreen = React.lazy(() => import('../domains/user/screens/MyDemandsScreen'));
const MyPropertiesScreen = React.lazy(() => import('../domains/user/screens/MyPropertiesScreen'));
const TenantResourceScreen = React.lazy(() => import('../domains/user/screens/TenantResourceScreen').then(m => ({ default: m.TenantResourceScreen })));
const OrdersScreen = React.lazy(() => import('../domains/user/screens/OrdersScreen').then(m => ({ default: m.OrdersScreen })));
const FavoritesScreen = React.lazy(() => import('../domains/user/screens/FavoritesScreen').then(m => ({ default: m.FavoritesScreen })));
const AppointmentsScreen = React.lazy(() => import('../domains/user/screens/AppointmentsScreen').then(m => ({ default: m.AppointmentsScreen })));

// 用户设置页面 - 使用懒加载
const SettingsScreen = React.lazy(() => import('../domains/user/screens/SettingsScreen').then(m => ({ default: m.SettingsScreen })));
const AccountManagementScreen = React.lazy(() => import('../domains/user/screens/AccountManagementScreen').then(m => ({ default: m.AccountManagementScreen })));
const DeviceManagementScreen = React.lazy(() => import('../domains/user/screens/DeviceManagementScreen').then(m => ({ default: m.DeviceManagementScreen })));
const SecuritySettingsScreen = React.lazy(() => import('../domains/user/screens/SecuritySettingsScreen').then(m => ({ default: m.SecuritySettingsScreen })));
const RoleVerificationScreen = React.lazy(() => import('../domains/user/screens/RoleVerificationScreen').then(m => ({ default: m.RoleVerificationScreen })));
const FeedbackScreen = React.lazy(() => import('../domains/user/screens/FeedbackScreen').then(m => ({ default: m.FeedbackScreen })));
// 使用我们新建的通知设置页面
const NotificationSettingsScreen = React.lazy(() => import('../domains/user/screens/NotificationSettingsScreen'));
const PasswordSetupScreen = React.lazy(() => import('../domains/user/screens/PasswordSetupScreen').then(m => ({ default: m.PasswordSetupScreen })));

// 发布相关页面 - 使用懒加载
const PublishOptionsScreen = React.lazy(() => import('../screens/Publish/PublishOptionsScreen'));
const VerificationScreen = React.lazy(() => import('../screens/Publish/VerificationScreen'));
const PropertyDetailFormScreen = React.lazy(() => import('../screens/Publish/PropertyDetailFormScreen'));
const PublishSuccessScreen = React.lazy(() => import('../screens/Publish/PublishSuccessScreen'));

// 求租求购页面 - 使用懒加载
const DemandFormScreen = React.lazy(() => import('../domains/demand/screens/DemandFormScreen'));
const DemandInfoScreen = React.lazy(() => import('../domains/demand/screens/DemandInfoScreen'));
const DemandDetailScreen = React.lazy(() => import('../domains/demand/screens/DemandDetailScreen'));

// 测试页面
const AvatarTestScreen = React.lazy(() => import('../screens/AvatarTestScreen').then(m => ({ default: m.AvatarTestScreen })));
// import { ShopPropertyDetailScreen } from '../screens/Properties/Detail/ShopPropertyDetailScreen';
// import { TenantRequirementsScreen } from '../screens/TenantRequirementsScreen';
// import { VoiceSearchScreen } from '../screens/Voice/VoiceSearchScreen';
// import { SearchResultsScreen } from '../screens/Search/SearchResultsScreen';
// import { FavoritesScreen } from '../screens/Favorites/FavoritesScreen';
// import { AppointmentsScreen } from '../screens/Appointments/AppointmentsScreen';
// import { OrdersScreen } from '../screens/Orders/OrdersScreen';
// import { PointsScreen } from '../screens/Points/PointsScreen';
// import { MapViewScreen } from '../screens/Map/MapViewScreen';
// import { PrivacyPolicyScreen } from '../screens/Auth/PrivacyPolicyScreen';
// import { UserAgreementScreen } from '../screens/Auth/UserAgreementScreen';

// 状态管理 - 使用新的AuthContext
import { useAuth } from '../contexts/AuthContext';
import { useUIStore } from '../stores/uiStore';
// import { useNavigationStore } from '../stores/navigationStore';

// 全局组件 - 直接导入避免barrel export问题
import { FloatingLoginBar } from '../domains/auth/components/FloatingLoginBar';
import UserOnboardingModal from '../domains/auth/components/UserOnboardingModal';

// 工具和配置
import { linking } from './linking';
// import { navigationTheme } from './theme';
// import { NavigationAnalytics } from '../services/analytics/navigationAnalytics';

// 创建导航引用
export const navigationRef = createNavigationContainerRef<RootStackParamList>();

// 全局导航函数
export function navigate(name: keyof RootStackParamList, params?: any) {
  if (navigationRef.isReady()) {
    (navigationRef as any).navigate(name, params);
  }
}

export function goBack() {
  if (navigationRef.isReady() && navigationRef.canGoBack()) {
    navigationRef.goBack();
  }
}

export function reset(name: keyof RootStackParamList, params?: any) {
  if (navigationRef.isReady()) {
    navigationRef.reset({
      index: 0,
      routes: [{ name: name as never, params: params as never }],
    });
  }
}

// 创建Stack导航器
const Stack = createStackNavigator<RootStackParamList>();

// ✅ React Navigation 6官方建议：使用Suspense处理异步组件加载
const LazyScreenWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Suspense fallback={
    <View style={styles.lazyLoadingContainer}>
      <ActivityIndicator size="large" color="#FF6B35" />
      <Text style={styles.lazyLoadingText}>加载中...</Text>
    </View>
  }>
    {children}
  </Suspense>
);

/**
 * 主应用导航器组件
 */
export const AppNavigator: React.FC = () => {
  // const colorScheme = useColorScheme(); // 暂时未使用
  const { state: authState } = useAuth(); // ✅ 使用新的AuthContext
  const { isLoading, userToken, user } = authState;
  const {
    shouldShowFloatingLoginBar,
    updateFloatingLoginBarState,
    floatingLoginBarState,
  } = useUIStore();

  // 原导航状态处理已合并到handleNavigationChange中

  // 根据认证状态和页面规则控制浮动登录栏显示
  useEffect(() => {
    if (userToken) {
      // 用户已认证，隐藏浮动登录栏
      updateFloatingLoginBarState({ isVisible: false });
      console.log('[AppNavigator] 用户已认证，隐藏浮动登录栏');
      
      // 🔧 关键修复：使用官方推荐的navigate而非reset，避免组件卸载
      if (navigationRef.isReady()) {
        try {
          // 检查当前是否在认证页面，如果是则导航到首页
          const currentState = navigationRef.getCurrentRoute();
          const authScreens = ['Login', 'QuickLogin', 'SmsLogin', 'Register', 'ForgotPassword'];
          
          if (currentState && authScreens.includes(currentState.name)) {
            console.log('[AppNavigator] 🏠 从认证页面自动导航到首页');
            // 🎯 官方最佳实践：使用navigate而非reset，保持现有组件挂载状态
            // 这样可以避免其他屏幕（如Map）被意外卸载
            (navigationRef as any).navigate('MainTabs', {
              screen: 'Home'
            });
          }
        } catch (error) {
          console.warn('[AppNavigator] 导航失败，但应用将继续运行:', error);
          // 不要重新抛出错误，避免崩溃
        }
      }
    } else if (!isLoading) {
      // ✅ 用户未认证且不在加载状态，立即显示浮动登录栏
      // 这里不需要等待页面切换，直接根据当前页面状态显示
      console.log('[AppNavigator] 用户未认证，立即显示浮动登录栏');

      // 获取当前页面名称，如果无法获取则默认为首页
      const currentRouteName = 'Home'; // 默认值，实际会在handleNavigationChange中更新
      const shouldShow = shouldShowFloatingLoginBar(currentRouteName);

      updateFloatingLoginBarState({
        isVisible: shouldShow,
        lastShownPage: currentRouteName
      });

      console.log('[AppNavigator] 浮动登录栏状态已更新:', {
        shouldShow,
        currentRouteName
      });
    }
  }, [userToken, isLoading, updateFloatingLoginBarState, shouldShowFloatingLoginBar]);

  // ✅ 官方推荐：使用useCallback优化函数，避免在导航器中进行复杂同步操作
  const getCurrentRouteName = useCallback((state: any): string => {
    if (!state?.routes?.length) {
      return 'Home'; // 简化：直接返回默认值
    }

    const currentRoute = state.routes[state.index];
    
    // 如果是MainTabs，使用官方辅助函数获取Tab内的实际页面名称
    if (currentRoute.name === 'MainTabs') {
      return getFocusedRouteNameFromRoute(currentRoute) || 'Home';
    }

    return currentRoute.name || 'Home';
  }, []);

  // ✅ 简化导航变化处理，减少复杂同步操作
  const handleNavigationChange = useCallback((state: any) => {
    if (!state || isLoading) return;

    const routeName = getCurrentRouteName(state);
    console.log('[AppNavigator] 页面导航变化:', { routeName, userToken: !!userToken });

    // 只有未认证时才处理浮动登录栏逻辑
    if (!userToken) {
      const shouldShow = shouldShowFloatingLoginBar(routeName);
      const shouldIncrement = shouldShow && !floatingLoginBarState.isVisible;

      console.log('[AppNavigator] 未认证状态，更新浮动登录栏:', {
        routeName,
        shouldShow,
        currentlyVisible: floatingLoginBarState.isVisible,
        shouldIncrement
      });

      updateFloatingLoginBarState({
        isVisible: shouldShow,
        lastShownPage: routeName,
        appearanceCount: shouldIncrement
          ? floatingLoginBarState.appearanceCount + 1
          : floatingLoginBarState.appearanceCount,
      });
    } else if (floatingLoginBarState.isVisible) {
      // 已认证状态，隐藏浮动登录栏
      console.log('[AppNavigator] 已认证状态，隐藏浮动登录栏');
      updateFloatingLoginBarState({ isVisible: false });
    }
  }, [
    getCurrentRouteName,
    isLoading,
    userToken,
    shouldShowFloatingLoginBar,
    floatingLoginBarState.isVisible,
    floatingLoginBarState.appearanceCount,
    updateFloatingLoginBarState,
  ]);

  // ✅ 使用useCallback优化浮动登录栏点击处理
  const handleFloatingLoginPress = useCallback(() => {
    if (navigationRef.isReady()) {
      (navigationRef as any).navigate('QuickLogin');
    }
  }, []);


  // 2025新架构：无loading启动，快速响应
  // isLoading现在仅用于表单提交等用户操作，不影响应用启动

  console.log('[AppNavigator] ✅ 渲染导航器 - 混合方案状态:', {
    isLoading,
    userToken: userToken ? '已有令牌' : '无令牌',
    user: user ? '已有用户' : '无用户',
    将显示: isLoading ? 'Splash加载屏幕' : 'MainTabs主应用',
    浮动登录栏可见性: floatingLoginBarState.isVisible,
  });

  return (
    <View style={styles.container}>
      <NavigationContainer
        ref={navigationRef}
        linking={linking}
        onReady={() => {
          console.log('[AppNavigator] 🎯 导航系统准备完成');
        }}
        onStateChange={(state: NavigationState | undefined) => {
          console.log(
            '[AppNavigator] 📱 导航状态变化:',
            state?.index,
            state?.routeNames?.[0]
          );
          handleNavigationChange(state);
        }}
      >
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            gestureEnabled: true,
          }}
        >
          {/* ✅ 混合方案：条件渲染loading + 主应用始终可用 + 透明Modal认证 */}
          {isLoading ? (
            // 正在检查认证状态，显示加载屏幕
            <Stack.Screen
              name="Splash"
              component={SplashScreenNav as any}
              options={{ gestureEnabled: false }}
            />
          ) : (
            // 主应用始终可用，通过浮动组件和透明Modal处理认证
            <>
              <Stack.Screen
                name="MainTabs"
                options={{
                  title: '首页',
                  gestureEnabled: false,
                }}
              >
                {() => (
                  <View style={{ flex: 1 }}>
                    <MainTabNavigator />
                    {/* 🔧 务实修复：基于React官方指导，在父组件控制条件渲染 */}
                    {!userToken && floatingLoginBarState.isVisible ? (
                      <FloatingLoginBar
                        isVisible={true}
                        onLoginPress={handleFloatingLoginPress}
                      />
                    ) : null}
                  </View>
                )}
              </Stack.Screen>

              {/* ✅ 认证页面作为透明Modal - 使用懒加载 + Suspense */}
              <Stack.Group 
                screenOptions={{
                  presentation: "transparentModal",
                  headerShown: false,
                  cardStyle: { backgroundColor: "transparent" }
                }}
              >
                <Stack.Screen name="Login">
                  {() => (
                    <LazyScreenWrapper>
                      <LoginScreen />
                    </LazyScreenWrapper>
                  )}
                </Stack.Screen>
                <Stack.Screen name="QuickLogin">
                  {() => (
                    <LazyScreenWrapper>
                      <QuickLoginScreen />
                    </LazyScreenWrapper>
                  )}
                </Stack.Screen>
                <Stack.Screen name="Register">
                  {() => (
                    <LazyScreenWrapper>
                      <RegisterScreen />
                    </LazyScreenWrapper>
                  )}
                </Stack.Screen>
                <Stack.Screen name="ForgotPassword">
                  {() => (
                    <LazyScreenWrapper>
                      <ForgotPasswordScreen />
                    </LazyScreenWrapper>
                  )}
                </Stack.Screen>
                <Stack.Screen name="SmsLogin">
                  {() => (
                    <LazyScreenWrapper>
                      <SmsLoginScreen />
                    </LazyScreenWrapper>
                  )}
                </Stack.Screen>
                <Stack.Screen name="Help">
                  {(props: StackScreenProps<RootStackParamList, 'Help'>) => (
                    <LazyScreenWrapper>
                      <HelpScreen
                        {...props}
                        onClose={() => {
                          if (props.navigation.canGoBack()) {
                            props.navigation.goBack();
                          } else {
                            props.navigation.navigate('MainTabs');
                          }
                        }}
                      />
                    </LazyScreenWrapper>
                  )}
                </Stack.Screen>
                <Stack.Screen
                  name="Onboarding"
                  component={UserOnboardingModal as any}
                />
              </Stack.Group>
            </>
          )}

          {/* ✅ 共享页面 - 使用懒加载优化 */}
          <Stack.Screen name="Search" component={SearchScreen as any} />
          <Stack.Screen name="Profile" component={ProfileScreen} />
          <Stack.Screen name="TempHome">
            {(props: StackScreenProps<RootStackParamList, 'TempHome'>) => (
              <LazyScreenWrapper>
                <TempHomeScreen {...props} />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>

          {/* ✅ 房源相关页面 - 使用懒加载优化 */}
          <Stack.Screen
            name="PropertyDetail"
            component={PropertyDetailScreen as any}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="PropertyTypePage"
            component={PropertyTypePage as any}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="CommuteAnalysis"
            component={CommuteAnalysisScreen as any}
            options={{ headerShown: false }}
          />

          {/* ✅ 消息系统页面 - 使用懒加载优化 */}
          <Stack.Screen name="MessageCenter">
            {() => (
              <LazyScreenWrapper>
                <MessageCenterScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen name="ChatDetail">
            {(props: StackScreenProps<RootStackParamList, 'ChatDetail'>) => (
              <LazyScreenWrapper>
                <ChatDetailScreen {...props} />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen name="LandlordSubscription">
            {() => (
              <LazyScreenWrapper>
                <LandlordSubscriptionScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>

          {/* ✅ 用户中心详细页面 - 使用懒加载优化 */}
          <Stack.Screen name="MyDemands">
            {() => (
              <LazyScreenWrapper>
                <MyDemandsScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen name="MyProperties">
            {() => (
              <LazyScreenWrapper>
                <MyPropertiesScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen name="TenantResource">
            {() => (
              <LazyScreenWrapper>
                <TenantResourceScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>

          {/* ✅ 我的订单页面 - 修复导航错误 */}
          <Stack.Screen name="Orders">
            {() => (
              <LazyScreenWrapper>
                <OrdersScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>

          {/* ✅ 我的收藏页面 */}
          <Stack.Screen name="Favorites">
            {() => (
              <LazyScreenWrapper>
                <FavoritesScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>

          {/* ✅ 预约看房页面 */}
          <Stack.Screen name="Appointments">
            {() => (
              <LazyScreenWrapper>
                <AppointmentsScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>

          {/* ✅ 用户设置相关页面 - 使用懒加载优化，启用手势返回 */}
          <Stack.Screen
            name="Settings"
            options={{
              headerShown: false,
              gestureEnabled: true,
              gestureDirection: 'horizontal',
            }}
          >
            {(props: any) => (
              <LazyScreenWrapper>
                <SettingsScreen
                  onBack={() => props.navigation.goBack()}
                  onAccountManagement={() => props.navigation.navigate('AccountManagement')}
                  onPersonalizationSettings={() => props.navigation.navigate('NotificationSettings')}
                  onFeedback={() => props.navigation.navigate('Feedback')}
                />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen
            name="AccountManagement"
            options={{
              headerShown: false,
              gestureEnabled: true,
              gestureDirection: 'horizontal',
            }}
          >
            {(props: any) => (
              <LazyScreenWrapper>
                <AccountManagementScreen
                  onBack={() => props.navigation.goBack()}
                  onLogout={() => {
                    // TODO: 处理登出逻辑
                    console.log('Logout from AccountManagement');
                  }}
                  onDeviceManagement={() => props.navigation.navigate('DeviceManagement')}
                  onSecuritySettings={() => props.navigation.navigate('SecuritySettings')}
                  onPasswordSetup={() => props.navigation.navigate('PasswordSetup', { isFirstTime: false })}
                />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen
            name="DeviceManagement"
            options={{
              headerShown: false,
              gestureEnabled: true,
              gestureDirection: 'horizontal',
            }}
          >
            {(props: any) => (
              <LazyScreenWrapper>
                <DeviceManagementScreen
                  onBack={() => props.navigation.goBack()}
                />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen
            name="SecuritySettings"
            options={{
              headerShown: false,
              gestureEnabled: true,
              gestureDirection: 'horizontal',
            }}
          >
            {(props: any) => (
              <LazyScreenWrapper>
                <SecuritySettingsScreen
                  onBack={() => props.navigation.goBack()}
                  onDeviceManagement={() => props.navigation.navigate('DeviceManagement')}
                />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen
            name="RoleVerification"
            options={{
              headerShown: false,
              gestureEnabled: true,
              gestureDirection: 'horizontal',
            }}
          >
            {(props: any) => (
              <LazyScreenWrapper>
                <RoleVerificationScreen
                  onBack={() => props.navigation.goBack()}
                />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen
            name="Feedback"
            options={{
              headerShown: false,
              gestureEnabled: true,
              gestureDirection: 'horizontal',
            }}
          >
            {(props: any) => (
              <LazyScreenWrapper>
                <FeedbackScreen
                  onBack={() => props.navigation.goBack()}
                />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen
            name="PasswordSetup"
            options={{
              headerShown: false,
              gestureEnabled: true,
              gestureDirection: 'horizontal',
            }}
          >
            {(props: any) => (
              <LazyScreenWrapper>
                <PasswordSetupScreen
                  onBack={() => props.navigation.goBack()}
                  isFirstTime={props.route?.params?.isFirstTime || false}
                />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen
            name="NotificationSettings"
            options={{
              headerShown: false,
              gestureEnabled: true,
              gestureDirection: 'horizontal',
            }}
          >
            {(props: any) => (
              <LazyScreenWrapper>
                <NotificationSettingsScreen
                  onBack={() => props.navigation.goBack()}
                />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>

          {/* ✅ 发布相关页面 - 使用懒加载优化 */}
          <Stack.Screen name="PublishOptions">
            {() => (
              <LazyScreenWrapper>
                <PublishOptionsScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen name="Verification">
            {() => (
              <LazyScreenWrapper>
                <VerificationScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen name="PropertyDetailForm">
            {() => (
              <Suspense fallback={
                <View style={styles.lazyLoadingContainer}>
                  <ActivityIndicator size="large" color="#FF6B35" />
                  <Text style={styles.lazyLoadingText}>加载中...</Text>
                </View>
              }>
                <PropertyDetailFormScreen />
              </Suspense>
            )}
          </Stack.Screen>
          <Stack.Screen name="PublishSuccess">
            {() => (
              <LazyScreenWrapper>
                <PublishSuccessScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>

          {/* ✅ 求租求购页面 - 使用懒加载优化 */}
          <Stack.Screen name="DemandForm">
            {() => (
              <LazyScreenWrapper>
                <DemandFormScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen name="DemandInfo">
            {(props: StackScreenProps<RootStackParamList, 'DemandInfo'>) => (
              <LazyScreenWrapper>
                <DemandInfoScreen {...props} />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>
          <Stack.Screen name="DemandDetail">
            {(props: StackScreenProps<RootStackParamList, 'DemandDetail'>) => (
              <LazyScreenWrapper>
                <DemandDetailScreen {...props} />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>

          {/* ✅ 头像测试页面 - 开发测试用 */}
          <Stack.Screen name="AvatarTest">
            {() => (
              <LazyScreenWrapper>
                <AvatarTestScreen />
              </LazyScreenWrapper>
            )}
          </Stack.Screen>

          {/* 暂时注释的页面，后续添加Tab导航时启用 */}
          {/*
          <Stack.Screen
            name="Home"
            component={HomeScreen}
          />
          <Stack.Screen
            name="Publish"
            component={PublishScreen}
          />
          <Stack.Screen
            name="Messages"
            component={MessagesScreen}
          />
          */}
        </Stack.Navigator>
      </NavigationContainer>

      {/* 方案A：浮动登录栏已移至条件渲染内部，避免全局渲染冲突 */}
    </View>
  );
};

// 样式定义
const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative', // 确保浮动组件能正确定位
  },
  // ✅ 懒加载屏幕加载状态样式
  lazyLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  lazyLoadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
});

export default AppNavigator;
