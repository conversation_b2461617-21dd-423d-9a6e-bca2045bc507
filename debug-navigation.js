#!/usr/bin/env node

/**
 * 导航崩溃调试脚本
 * 通过模拟点击和监控日志来定位崩溃原因
 */

const { exec } = require('child_process');
const fs = require('fs');

console.log('🔍 开始导航崩溃调试...');

// 1. 检查ErrorBoundary是否正确配置
console.log('📋 检查ErrorBoundary配置...');

const checkErrorBoundary = () => {
  const mainTabFile = 'packages/frontend/src/navigation/MainTabNavigator.tsx';
  
  if (fs.existsSync(mainTabFile)) {
    const content = fs.readFileSync(mainTabFile, 'utf8');
    
    // 检查ErrorBoundary导入
    const hasErrorBoundaryImport = content.includes('import { ErrorBoundary }');
    console.log(`✅ ErrorBoundary导入: ${hasErrorBoundaryImport ? '已配置' : '❌ 缺失'}`);
    
    // 检查每个Tab是否有ErrorBoundary包装
    const hasHomeErrorBoundary = content.includes('<ErrorBoundary') && content.includes('HomeScreen');
    const hasMapErrorBoundary = content.includes('<ErrorBoundary') && content.includes('MapSearchScreen');
    const hasProfileErrorBoundary = content.includes('<ErrorBoundary') && content.includes('ProfileScreen');
    
    console.log(`✅ Home ErrorBoundary: ${hasHomeErrorBoundary ? '已配置' : '❌ 缺失'}`);
    console.log(`✅ Map ErrorBoundary: ${hasMapErrorBoundary ? '已配置' : '❌ 缺失'}`);
    console.log(`✅ Profile ErrorBoundary: ${hasProfileErrorBoundary ? '已配置' : '❌ 缺失'}`);
    
    return {
      hasErrorBoundaryImport,
      hasHomeErrorBoundary,
      hasMapErrorBoundary,
      hasProfileErrorBoundary
    };
  }
  
  return null;
};

// 2. 检查ProfileScreen的Hook使用
console.log('📋 检查ProfileScreen Hook配置...');

const checkProfileScreen = () => {
  const profileFile = 'packages/frontend/src/domains/user/screens/profile/ProfileScreen.tsx';
  
  if (fs.existsSync(profileFile)) {
    const content = fs.readFileSync(profileFile, 'utf8');
    
    // 检查useLayoutPerformance是否被禁用
    const hasLayoutPerformanceDisabled = content.includes('// useLayoutPerformance');
    console.log(`✅ useLayoutPerformance已禁用: ${hasLayoutPerformanceDisabled ? '是' : '❌ 否'}`);
    
    // 检查try-catch包装
    const hasTryCatch = content.includes('try {') && content.includes('} catch (error)');
    console.log(`✅ try-catch错误处理: ${hasTryCatch ? '已配置' : '❌ 缺失'}`);
    
    return {
      hasLayoutPerformanceDisabled,
      hasTryCatch
    };
  }
  
  return null;
};

// 3. 生成调试报告
const generateReport = () => {
  const errorBoundaryCheck = checkErrorBoundary();
  const profileScreenCheck = checkProfileScreen();
  
  console.log('\n📊 调试报告:');
  console.log('================');
  
  if (errorBoundaryCheck) {
    console.log('🔧 ErrorBoundary配置:');
    console.log(`  - 导入: ${errorBoundaryCheck.hasErrorBoundaryImport ? '✅' : '❌'}`);
    console.log(`  - Home保护: ${errorBoundaryCheck.hasHomeErrorBoundary ? '✅' : '❌'}`);
    console.log(`  - Map保护: ${errorBoundaryCheck.hasMapErrorBoundary ? '✅' : '❌'}`);
    console.log(`  - Profile保护: ${errorBoundaryCheck.hasProfileErrorBoundary ? '✅' : '❌'}`);
  }
  
  if (profileScreenCheck) {
    console.log('🔧 ProfileScreen配置:');
    console.log(`  - useLayoutPerformance禁用: ${profileScreenCheck.hasLayoutPerformanceDisabled ? '✅' : '❌'}`);
    console.log(`  - 错误处理: ${profileScreenCheck.hasTryCatch ? '✅' : '❌'}`);
  }
  
  // 生成修复建议
  console.log('\n🛠️ 修复建议:');
  
  if (!errorBoundaryCheck?.hasErrorBoundaryImport) {
    console.log('❌ 需要在MainTabNavigator中导入ErrorBoundary');
  }
  
  if (!profileScreenCheck?.hasLayoutPerformanceDisabled) {
    console.log('❌ 需要禁用ProfileScreen中的useLayoutPerformance Hook');
  }
  
  console.log('\n✅ 调试完成！');
};

// 执行调试
generateReport();
