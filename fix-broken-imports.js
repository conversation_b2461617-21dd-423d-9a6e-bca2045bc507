#!/usr/bin/env node

/**
 * 修复被破坏的导入语句
 * 将错误插入的FeedbackService导入移动到正确位置
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 开始修复被破坏的导入语句...\n');

// 获取所有有TypeScript错误的文件
let errorFiles = [];
try {
  const tscOutput = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { 
    cwd: '/data/my-real-estate-app/packages/frontend',
    encoding: 'utf8' 
  });
} catch (error) {
  const output = error.stdout || error.message;
  const lines = output.split('\n');
  
  lines.forEach(line => {
    const match = line.match(/^([^(]+)\(\d+,\d+\):/);
    if (match) {
      const filePath = match[1];
      if (filePath.startsWith('src/') && !errorFiles.includes(filePath)) {
        errorFiles.push(filePath);
      }
    }
  });
}

console.log(`发现 ${errorFiles.length} 个有错误的文件`);

let fixedCount = 0;

errorFiles.forEach(filePath => {
  try {
    if (!fs.existsSync(filePath)) {
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;

    // 检查是否有错误插入的FeedbackService导入
    const brokenImportPattern = /import\s+{\s*\nimport\s+FeedbackService\s+from\s+['"'][^'"]+['"];?\s*\n/g;
    const brokenImportPattern2 = /import\s+FeedbackService\s+from\s+['"'][^'"]+['"];?\s*\n\s*([A-Za-z_$][A-Za-z0-9_$]*,?\s*)/g;
    
    if (brokenImportPattern.test(content) || brokenImportPattern2.test(content)) {
      console.log(`🔧 修复文件: ${filePath}`);
      
      // 移除所有错误的FeedbackService导入
      content = content.replace(/import\s+FeedbackService\s+from\s+['"'][^'"]+['"];?\s*\n/g, '');
      
      // 修复被破坏的import语句
      content = content.replace(/import\s+{\s*\n\s*([A-Za-z_$][A-Za-z0-9_$,\s\n]*)\s*}\s*from/g, 'import {\n  $1\n} from');
      
      // 计算正确的FeedbackService导入路径
      const pathParts = filePath.split('/');
      const depth = pathParts.length - 2; // 减去src和文件名
      const relativePath = '../'.repeat(depth) + 'shared/services/FeedbackService';
      
      // 在所有import语句后添加FeedbackService导入
      const lines = content.split('\n');
      let lastImportIndex = -1;
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line.startsWith('import ') && !line.includes('type ')) {
          lastImportIndex = i;
        }
        // 如果遇到非import语句，停止搜索
        if (line && !line.startsWith('import ') && !line.startsWith('//') && !line.startsWith('/*') && !line.startsWith('*') && !line.startsWith('*/')) {
          break;
        }
      }
      
      if (lastImportIndex !== -1) {
        const importStatement = `import FeedbackService from '${relativePath}';`;
        lines.splice(lastImportIndex + 1, 0, importStatement);
        content = lines.join('\n');
      }
      
      // 写回文件
      fs.writeFileSync(filePath, content, 'utf8');
      fixedCount++;
      console.log(`✅ ${filePath}: 已修复`);
    }

  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
  }
});

console.log(`\n🎉 修复完成!`);
console.log(`📊 统计信息:`);
console.log(`   - 修复文件: ${fixedCount} 个`);
