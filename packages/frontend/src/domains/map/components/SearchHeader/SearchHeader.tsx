/**
 * 搜索头部组件
 * 职责：顶部搜索区域容器，包含房源类型筛选和搜索框
 */

import React from 'react';
import { View, StyleSheet, SafeAreaView, StatusBar } from 'react-native';
import { PropertyTypeFilter } from './PropertyTypeFilter';
import { SearchBar } from './SearchBar';

interface SearchHeaderProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onSearchSubmit: () => void;
  selectedPropertyType: string;
  onPropertyTypeChange: (type: string) => void;
  propertyTypes: string[];
  loading: boolean;
}

export const SearchHeader: React.FC<SearchHeaderProps> = ({
  searchQuery,
  onSearchChange,
  onSearchSubmit,
  selectedPropertyType,
  onPropertyTypeChange,
  propertyTypes,
  loading,
}) => {
  return (
    <SafeAreaView style={styles.overlayContainer}>
      {/* 房源类型标签 */}
      <PropertyTypeFilter
        propertyTypes={propertyTypes}
        selectedType={selectedPropertyType}
        onTypeChange={onPropertyTypeChange}
      />

      {/* 搜索框 */}
      <SearchBar
        searchQuery={searchQuery}
        onSearchChange={onSearchChange}
        onSearchSubmit={onSearchSubmit}
        loading={loading}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
    paddingTop: StatusBar.currentHeight || 44,
    zIndex: 1000,
    backgroundColor: 'transparent',
  },
});
