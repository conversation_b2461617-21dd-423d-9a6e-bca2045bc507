# SwipeableTabView 通用可滑动标签页组件

## 📋 功能概述

SwipeableTabView 是一个通用的可滑动标签页组件，提供了点击切换和左右滑动的功能，适用于多种场景的分类展示。

### 核心特性
- ✅ **双重交互**: 支持点击切换和左右滑动
- ✅ **自定义样式**: 完全可定制的标签栏和指示器样式
- ✅ **懒加载**: 支持内容页面的懒加载，提升性能
- ✅ **计数显示**: 支持标签计数和徽章显示
- ✅ **响应式设计**: 适配各种屏幕尺寸
- ✅ **TypeScript**: 完整的类型安全支持

## 🎯 适用场景

- 我的房源页面（已发布/草稿/已下架）
- 消息页面（业主消息/租买消息）
- 用户中心页面（个人信息/设置/历史记录）
- 搜索结果页面（房源/需求/用户）
- 任何需要分类展示的页面

## 🔧 基础使用

### 1. 导入组件

```typescript
import { SwipeableTabView, type SwipeableTab, type SceneRenderer } from '../../../shared/components/SwipeableTabView';
```

### 2. 定义标签页

```typescript
const tabs: SwipeableTab[] = [
  { key: 'published', title: '已发布', count: 3 },
  { key: 'draft', title: '草稿', count: 1 },
  { key: 'inactive', title: '已下架', count: 2 },
];
```

### 3. 定义场景渲染器

```typescript
const scenes: SceneRenderer = {
  published: () => <PublishedContent />,
  draft: () => <DraftContent />,
  inactive: () => <InactiveContent />,
};
```

### 4. 使用组件

```typescript
<SwipeableTabView
  tabs={tabs}
  scenes={scenes}
  initialIndex={0}
  onIndexChange={(index) => console.log('切换到:', index)}
/>
```

## 📚 完整示例

### 我的房源页面示例

```typescript
import React, { useState } from 'react';
import { View, Text, FlatList, ActivityIndicator } from 'react-native';
import { SwipeableTabView, type SwipeableTab, type SceneRenderer } from '../../../shared/components/SwipeableTabView';

interface PropertyTab extends SwipeableTab {
  count: number;
}

const MyPropertiesScreen: React.FC = () => {
  const [index, setIndex] = useState(0);
  
  // 定义标签页
  const tabs: PropertyTab[] = [
    { key: 'published', title: '已发布', count: 3 },
    { key: 'draft', title: '草稿', count: 1 },
    { key: 'inactive', title: '已下架', count: 2 },
  ];

  // 定义场景渲染器
  const scenes: SceneRenderer = {
    published: () => <PropertyList status="published" />,
    draft: () => <PropertyList status="draft" />,
    inactive: () => <PropertyList status="inactive" />,
  };

  return (
    <View style={{ flex: 1 }}>
      <SwipeableTabView
        tabs={tabs}
        scenes={scenes}
        initialIndex={index}
        onIndexChange={setIndex}
        selectedTabStyle={{
          textColor: '#000000',
          fontWeight: '700',
        }}
        unselectedTabStyle={{
          textColor: '#999999',
          fontWeight: 'normal',
        }}
        indicatorStyle={{
          backgroundColor: '#FF6B35',
          height: 3,
        }}
        showTabCounts={true}
        lazy={true}
      />
    </View>
  );
};
```

### 消息页面示例

```typescript
import React, { useState, useMemo } from 'react';
import { SwipeableTabView, type SwipeableTab, type SceneRenderer } from '../../../shared/components/SwipeableTabView';

const MessageCenterScreen: React.FC = () => {
  const [activeRole, setActiveRole] = useState<'tenant_buyer' | 'landlord'>('tenant_buyer');
  
  // 动态生成角色标签页
  const roleTabs: SwipeableTab[] = useMemo(() => {
    const tabs: SwipeableTab[] = [];
    
    if (userRoles?.isTenantBuyer) {
      tabs.push({
        key: 'tenant_buyer',
        title: '租买消息',
        count: userRoles.tenantBuyerUnreadCount,
        badge: userRoles.tenantBuyerUnreadCount > 0,
      });
    }
    
    if (userRoles?.isLandlord) {
      tabs.push({
        key: 'landlord',
        title: '业主消息',
        count: userRoles.landlordUnreadCount,
        badge: userRoles.landlordUnreadCount > 0,
      });
    }
    
    return tabs;
  }, [userRoles]);

  const scenes: SceneRenderer = {
    tenant_buyer: () => <TenantBuyerMessages />,
    landlord: () => <LandlordMessages />,
  };

  return (
    <SwipeableTabView
      tabs={roleTabs}
      scenes={scenes}
      onIndexChange={(index) => {
        const role = roleTabs[index]?.key as 'tenant_buyer' | 'landlord';
        setActiveRole(role);
      }}
      selectedTabStyle={{
        textColor: '#FF6B35',
        fontWeight: '600',
      }}
      unselectedTabStyle={{
        textColor: '#666666',
        fontWeight: 'normal',
      }}
    />
  );
};
```

## 🎨 样式自定义

### 基础样式配置

```typescript
<SwipeableTabView
  // 标签栏样式
  tabBarStyle={{
    backgroundColor: '#FFFFFF',
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E0E0E0',
  }}
  
  // 指示器样式
  indicatorStyle={{
    backgroundColor: '#FF6B35',
    height: 3,
    borderRadius: 1.5,
  }}
  
  // 选中状态样式
  selectedTabStyle={{
    textColor: '#000000',
    fontWeight: '700',
    backgroundColor: '#F5F5F5', // 可选背景色
  }}
  
  // 未选中状态样式
  unselectedTabStyle={{
    textColor: '#999999',
    fontWeight: 'normal',
  }}
/>
```

### 高级样式定制

```typescript
// 自定义主题色
const primaryColor = '#FF6B35';
const textColor = '#333333';
const backgroundColor = '#FFFFFF';

<SwipeableTabView
  selectedTabStyle={{
    textColor: textColor,
    fontWeight: '700',
  }}
  unselectedTabStyle={{
    textColor: '#999999',
    fontWeight: 'normal',
  }}
  indicatorStyle={{
    backgroundColor: primaryColor,
    height: 3,
  }}
  tabBarStyle={{
    backgroundColor: backgroundColor,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  }}
/>
```

## ⚙️ 配置选项

### Props 详解

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `tabs` | `SwipeableTab[]` | 必填 | 标签页配置数组 |
| `scenes` | `SceneRenderer` | 必填 | 场景渲染器对象 |
| `initialIndex` | `number` | `0` | 初始选中的标签页索引 |
| `onIndexChange` | `(index: number) => void` | 可选 | 标签页切换回调 |
| `selectedTabStyle` | `object` | 见下方 | 选中状态样式 |
| `unselectedTabStyle` | `object` | 见下方 | 未选中状态样式 |
| `tabBarStyle` | `object` | 见下方 | 标签栏样式 |
| `indicatorStyle` | `object` | 见下方 | 指示器样式 |
| `lazy` | `boolean` | `true` | 是否启用懒加载 |
| `showTabCounts` | `boolean` | `true` | 是否显示标签计数 |
| `scrollEnabled` | `boolean` | `true` | 是否启用标签栏滚动 |
| `renderLazyPlaceholder` | `() => ReactNode` | 默认加载器 | 懒加载占位符 |

### SwipeableTab 接口

```typescript
interface SwipeableTab {
  key: string;        // 唯一标识符
  title: string;      // 标签标题
  count?: number;     // 标签计数（可选）
  icon?: string;      // 图标（可选）
  badge?: boolean;    // 是否显示徽章（可选）
}
```

### SceneRenderer 接口

```typescript
interface SceneRenderer {
  [key: string]: () => ReactNode;
}
```

## 🚀 最佳实践

### 1. 性能优化

```typescript
// 使用 useMemo 缓存标签页配置
const tabs = useMemo(() => [
  { key: 'tab1', title: '标签1', count: data1.length },
  { key: 'tab2', title: '标签2', count: data2.length },
], [data1.length, data2.length]);

// 使用 useCallback 缓存场景渲染器
const scenes = useMemo(() => ({
  tab1: () => <Tab1Content data={data1} />,
  tab2: () => <Tab2Content data={data2} />,
}), [data1, data2]);
```

### 2. 懒加载内容

```typescript
// 为复杂内容启用懒加载
<SwipeableTabView
  lazy={true}
  renderLazyPlaceholder={() => (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ActivityIndicator size="large" color="#FF6B35" />
      <Text style={{ marginTop: 10 }}>加载中...</Text>
    </View>
  )}
/>
```

### 3. 动态标签页

```typescript
// 基于数据动态生成标签页
const tabs = useMemo(() => {
  const dynamicTabs = [];
  
  if (showTab1) {
    dynamicTabs.push({ key: 'tab1', title: '标签1', count: count1 });
  }
  
  if (showTab2) {
    dynamicTabs.push({ key: 'tab2', title: '标签2', count: count2 });
  }
  
  return dynamicTabs;
}, [showTab1, showTab2, count1, count2]);
```

### 4. 状态同步

```typescript
// 与外部状态同步
const handleIndexChange = useCallback((index: number) => {
  const selectedTab = tabs[index];
  setActiveTab(selectedTab.key);
  
  // 触发其他副作用
  onTabChange?.(selectedTab.key);
}, [tabs, onTabChange]);
```

## 🔍 常见问题

### Q: 如何实现标签页的条件显示？

A: 使用 `useMemo` 动态生成标签页配置：

```typescript
const tabs = useMemo(() => {
  const conditionalTabs = [];
  
  if (userRole === 'admin') {
    conditionalTabs.push({ key: 'admin', title: '管理员' });
  }
  
  conditionalTabs.push({ key: 'user', title: '用户' });
  
  return conditionalTabs;
}, [userRole]);
```

### Q: 如何处理标签页的权限控制？

A: 在标签页配置中加入权限检查：

```typescript
const tabs = useMemo(() => {
  const allTabs = [
    { key: 'public', title: '公开', permission: 'public' },
    { key: 'private', title: '私有', permission: 'private' },
    { key: 'admin', title: '管理员', permission: 'admin' },
  ];
  
  return allTabs.filter(tab => hasPermission(tab.permission));
}, [hasPermission]);
```

### Q: 如何实现标签页的异步加载？

A: 结合 React Query 或其他数据获取方案：

```typescript
const { data: tabData, isLoading } = useQuery({
  queryKey: ['tabs'],
  queryFn: fetchTabData,
});

const tabs = useMemo(() => {
  if (!tabData) return [];
  
  return tabData.map(item => ({
    key: item.id,
    title: item.name,
    count: item.count,
  }));
}, [tabData]);

if (isLoading) {
  return <LoadingScreen />;
}
```

## 🛠️ 扩展功能

### 1. 添加图标支持

```typescript
// 在 SwipeableTab 中添加图标
const tabs = [
  { key: 'home', title: '首页', icon: 'home' },
  { key: 'search', title: '搜索', icon: 'search' },
];

// 在组件中渲染图标
<View style={styles.tabLabelContainer}>
  {route.icon && <Icon name={route.icon} size={16} />}
  <Text style={styles.tabLabel}>{route.title}</Text>
</View>
```

### 2. 添加徽章支持

```typescript
// 标签页配置中添加徽章
const tabs = [
  { key: 'messages', title: '消息', badge: hasUnreadMessages },
  { key: 'notifications', title: '通知', count: notificationCount },
];
```

### 3. 自定义动画

```typescript
// 可以通过 tabBarStyle 和 indicatorStyle 自定义动画
<SwipeableTabView
  indicatorStyle={{
    backgroundColor: '#FF6B35',
    height: 3,
    borderRadius: 1.5,
    // 可以添加自定义动画效果
  }}
/>
```

## 🎯 总结

SwipeableTabView 组件提供了一个强大而灵活的标签页解决方案，适用于各种场景。通过合理使用配置选项和最佳实践，可以创建出用户体验优秀的分类界面。

主要优势：
- 🎨 **高度可定制**: 支持丰富的样式配置
- 🚀 **性能优化**: 内置懒加载和缓存机制
- 📱 **响应式设计**: 适配各种屏幕尺寸
- 🔧 **易于使用**: 简洁的API设计
- 🛡️ **类型安全**: 完整的TypeScript支持

开始使用 SwipeableTabView，为您的应用创建更好的用户体验！