/**
 * 🏗️ 企业级架构：通用Tab列表逻辑Hook
 * 遵循前端企业级架构规范 - Hook层
 */

import { useState, useCallback, useRef } from 'react';
import PagerView from 'react-native-pager-view';
import { UseTabListLogicProps, TabStatus, TabListState } from '../types';

export const useTabListLogic = ({
  type,
  tabs,
  initialTab,
  onTabChange,
}: UseTabListLogicProps) => {
  // 🏗️ 企业级架构：状态管理（遵循Hook层规范）
  const [activeTab, setActiveTab] = useState<TabStatus>(initialTab || tabs[0]?.key || 'active');
  const [currentPage, setCurrentPage] = useState(() => {
    const initialIndex = tabs.findIndex(tab => tab.key === (initialTab || tabs[0]?.key));
    return Math.max(0, initialIndex);
  });

  // PagerView引用
  const pagerRef = useRef<PagerView>(null);

  // Tab索引映射
  const tabIndexMap = tabs.reduce((acc, tab, index) => {
    acc[tab.key] = index;
    return acc;
  }, {} as Record<TabStatus, number>);

  const indexTabMap = tabs.map(tab => tab.key);

  // 🔧 企业级事件处理：Tab点击（遵循Hook层规范）
  const handleTabPress = useCallback((tab: TabStatus) => {
    const targetIndex = tabIndexMap[tab];
    if (targetIndex === undefined) return;

    setActiveTab(tab);
    setCurrentPage(targetIndex);
    pagerRef.current?.setPage(targetIndex);
    
    // 触发外部回调
    onTabChange?.(tab);
    
    console.log(`[useTabListLogic] ${type} Tab切换: ${tab}`);
  }, [tabIndexMap, onTabChange, type]);

  // 🔧 企业级事件处理：页面滑动
  const handlePageSelected = useCallback((event: any) => {
    const { position } = event.nativeEvent;
    const newTab = indexTabMap[position];
    
    if (newTab && newTab !== activeTab) {
      setCurrentPage(position);
      setActiveTab(newTab);
      onTabChange?.(newTab);
      
      console.log(`[useTabListLogic] ${type} 滑动切换: ${newTab}`);
    }
  }, [indexTabMap, activeTab, onTabChange, type]);

  // 🔧 企业级方法：程序化切换Tab
  const switchToTab = useCallback((tab: TabStatus) => {
    handleTabPress(tab);
  }, [handleTabPress]);

  // 🔧 企业级方法：获取当前Tab信息
  const getCurrentTabInfo = useCallback(() => {
    const currentTab = tabs.find(tab => tab.key === activeTab);
    return {
      tab: activeTab,
      index: currentPage,
      title: currentTab?.title || '',
      count: currentTab?.count || 0,
    };
  }, [activeTab, currentPage, tabs]);

  return {
    // 状态
    activeTab,
    currentPage,
    pagerRef,
    
    // 映射
    tabIndexMap,
    indexTabMap,
    
    // 事件处理
    handleTabPress,
    handlePageSelected,
    
    // 方法
    switchToTab,
    getCurrentTabInfo,
    
    // 计算属性
    tabsWithIndex: tabs.map((tab, index) => ({
      ...tab,
      index,
      isActive: tab.key === activeTab,
    })),
  };
};
