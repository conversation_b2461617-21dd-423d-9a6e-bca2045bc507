# 🗺️ 地图导航和地址搜索功能重构TODO清单

## 📋 项目信息
- **创建日期**: 2025-08-03
- **负责人**: AI Assistant
- **优先级**: 🔥 高优先级（架构合规性问题）
- **预期完成**: 3天
- **当前状态**: 开始执行

## 🎯 重构目标
将地图导航和地址搜索功能从当前B级（75%）提升到A级（93%）架构合规度，严格遵循企业级五层架构规范。

---

## 📍 Phase 1: 紧急修复（高优先级）

### ✅ Task 1.1: 创建地图导航专用Hook
- [ ] **创建 usePropertyNavigation.ts**
  - 路径：`/packages/frontend/src/domains/property/components/detail/hooks/usePropertyNavigation.ts`
  - 功能：封装路线计算、坐标转换、状态管理逻辑
  - 要求：使用 Transformers.map 进行数据转换
  - 预期：< 200行，职责单一

- [ ] **集成Zustand Store管理状态**
  - 路径：`/packages/frontend/src/domains/property/components/detail/stores/MapNavigationStore.ts`
  - 功能：路线状态、选中路线、计算状态管理
  - 要求：使用三中间件（devtools, persist, subscribeWithSelector）

- [ ] **验证Hook功能**
  - 测试路线计算功能
  - 测试坐标转换准确性
  - 测试状态管理稳定性

### ✅ Task 1.2: 集成统一转换层
- [ ] **修复地址搜索数据转换**
  - 文件：`/packages/frontend/src/domains/property/components/detail/hooks/useAddressSearch.ts`
  - 修改：使用 `Transformers.map.toAPI()` 替换自定义转换
  - 移除：`convertToSerializableAddress` 函数

- [ ] **创建地图数据转换器（如需要）**
  - 文件：`/packages/frontend/src/shared/services/dataTransform/transformers/MapTransformer.ts`
  - 功能：地址数据、坐标数据、路线数据转换
  - 验证：Schema验证、数据清理、类型安全

- [ ] **验证数据转换**
  - 测试地址选择数据转换
  - 测试导航参数序列化
  - 测试坐标数据转换

### ✅ Task 1.3: 组件拆分优化
- [ ] **拆分 PropertyNavigationMap.tsx**
  - 原文件：600+行 → 主控制器 < 150行
  - 子组件1：`RouteSelectionPanel.tsx` < 100行
  - 子组件2：`NavigationControls.tsx` < 100行
  - 子组件3：`MapDisplay.tsx` < 100行
  - 子组件4：`RouteInfoDisplay.tsx` < 100行

- [ ] **拆分 AddressSearchScreen.tsx**
  - 原文件：700+行 → 主控制器 < 200行
  - 子组件1：`SearchInput.tsx` < 80行
  - 子组件2：`SearchResults.tsx` < 100行
  - 子组件3：`SearchHistory.tsx` < 100行
  - 子组件4：`QuickLocations.tsx` < 80行

- [ ] **验证组件功能**
  - 确保拆分后功能完整
  - 测试组件间通信正常
  - 验证性能无下降

---

## 📍 Phase 2: 架构标准化（中优先级）

### ✅ Task 2.1: 完善Store层管理
- [ ] **创建 MapNavigationStore.ts**
  - 功能：路线管理、位置状态、计算状态
  - 规范：Zustand + 三中间件
  - 接口：标准化的actions和selectors

- [ ] **创建 AddressSearchStore.ts（验证现有）**
  - 验证：是否符合企业级标准
  - 优化：性能和状态管理
  - 完善：错误处理和边界情况

- [ ] **Store集成测试**
  - 测试状态更新准确性
  - 测试持久化功能
  - 测试开发工具集成

### ✅ Task 2.2: 完善类型定义
- [ ] **增强 PropertyNavigationProps**
  - 文件：`/packages/frontend/src/screens/Property/PropertyNavigationScreen.tsx`
  - 添加：完整的错误处理类型
  - 移除：所有 `as any` 类型断言

- [ ] **创建统一的地图类型定义**
  - 文件：`/packages/frontend/src/domains/property/components/detail/types/navigation.types.ts`
  - 内容：RouteInfo, NavigationParams, MapState等
  - 要求：类型安全、可扩展

- [ ] **验证类型安全**
  - TypeScript编译无错误
  - 消除所有any类型使用
  - 完善Props接口定义

---

## 📍 Phase 3: 性能和稳定性优化（低优先级）

### ✅ Task 3.1: 性能优化
- [ ] **添加 React.memo 优化**
  - 目标组件：所有新创建的子组件
  - 比较函数：智能props比较
  - 验证：渲染次数减少

- [ ] **实现地图数据缓存**
  - 路线计算结果缓存
  - 地址搜索结果缓存
  - 坐标转换结果缓存

- [ ] **优化坐标计算性能**
  - 算法优化
  - 防抖处理
  - 异步计算

### ✅ Task 3.2: 错误边界和容错
- [ ] **添加错误边界组件**
  - 地图组件错误边界
  - 搜索组件错误边界
  - 导航错误边界

- [ ] **完善容错机制**
  - 网络失败重试
  - 数据异常处理
  - 用户体验降级

---

## 🔍 验证检查清单

### ✅ 功能完整性验证
- [ ] **地址搜索功能**
  - [ ] 实时搜索建议正常显示
  - [ ] 搜索历史正确保存和显示
  - [ ] 快捷位置功能正常
  - [ ] 搜索结果选择后正确跳转

- [ ] **地图导航功能**
  - [ ] 路线计算和显示正确
  - [ ] 距离和时间计算准确
  - [ ] 起点终点标记正确
  - [ ] 导航控制功能完整

- [ ] **数据流验证**
  - [ ] 地址选择 → 参数传递 → 地图显示
  - [ ] 坐标转换准确无误
  - [ ] 状态同步正常
  - [ ] 错误处理完善

### ✅ 架构合规性验证
- [ ] **五层架构遵循**
  - [ ] UI层：只负责渲染，< 300行
  - [ ] Hook层：业务逻辑封装完整
  - [ ] Store层：使用Zustand管理状态
  - [ ] DTO层：使用统一转换层
  - [ ] API层：网络请求规范

- [ ] **类型安全**
  - [ ] 消除所有any类型使用
  - [ ] Props接口定义完整
  - [ ] TypeScript编译无错误
  - [ ] 运行时类型验证

- [ ] **性能要求**
  - [ ] 组件渲染 < 16ms
  - [ ] 状态更新无不必要re-render
  - [ ] 内存使用稳定
  - [ ] 网络请求优化

### ✅ 用户体验验证
- [ ] **交互流畅性**
  - [ ] 地址搜索响应快速
  - [ ] 地图加载无卡顿
  - [ ] 导航切换自然
  - [ ] 错误提示友好

- [ ] **功能可靠性**
  - [ ] 网络异常处理
  - [ ] 数据异常恢复
  - [ ] 边界情况处理
  - [ ] 错误状态友好

---

## 📊 进度跟踪

### 当前进度：43% (Phase 1 完成)

| Phase | 任务数 | 已完成 | 进度 | 状态 |
|-------|--------|--------|------|------|
| Phase 1 | 9 | 9 | 100% | ✅ 已完成 |
| Phase 2 | 6 | 0 | 0% | ⏳ 待开始 |  
| Phase 3 | 6 | 0 | 0% | ⏳ 待开始 |
| **总计** | **21** | **9** | **43%** | 🔄 **进行中** |

### 里程碑
- [x] **M1: Phase 1 完成** - 紧急修复，解决架构违规 ✅ (2025-08-03)
- [ ] **M2: Phase 2 完成** - 架构标准化
- [ ] **M3: Phase 3 完成** - 性能和稳定性优化
- [ ] **M4: 最终验收** - 达到A级架构合规度

---

## ⚠️ 风险和注意事项

### 🚨 高风险项
1. **组件拆分风险**: 可能影响现有功能，需要充分测试
2. **状态管理迁移**: Store迁移可能导致状态丢失
3. **数据转换改动**: 可能影响地址选择和导航功能

### 🔧 缓解措施
1. **渐进式重构**: 一次只修改一个模块，确保功能稳定
2. **充分测试**: 每次修改后立即验证核心功能
3. **备份机制**: 保留原有文件备份，便于快速回滚

### 📋 回滚计划
- **Phase 1 回滚**: 恢复原有Hook和组件文件
- **Phase 2 回滚**: 恢复原有Store和类型定义
- **Phase 3 回滚**: 移除优化代码，恢复基础功能

---

## 📝 备注
- 所有修改必须保持功能完整性
- 严格遵循企业级架构规范
- 重构过程中持续进行功能验证
- 每个Phase完成后进行全面测试

**重构原则**: 功能第一，架构第二，性能第三 🎯