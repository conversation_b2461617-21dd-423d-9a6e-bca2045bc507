/**
 * 房源草稿迁移Hook - 企业级版本
 *
 * 功能：
 * 1. 自动检测并触发迁移
 * 2. 提供手动迁移接口
 * 3. 迁移状态管理
 * 4. 用户体验优化
 */

import { useState, useEffect, useCallback } from 'react';
import {
  propertyDraftMigration,
  MigrationResult,
  MigrationStatus,
} from '../../services/migration/PropertyDraftMigration';

interface MigrationState {
  needsMigration: boolean;
  isChecking: boolean;
  isMigrating: boolean;
  localDraftCount: number;
  lastMigrationResult: MigrationResult | null;
  error: string | null;
}

interface MigrationActions {
  checkMigrationNeeded: () => Promise<void>;
  startMigration: (showProgress?: boolean) => Promise<MigrationResult>;
  resetMigrationStatus: () => Promise<void>;
}

/**
 * 房源草稿迁移Hook
 */
export const usePropertyDraftMigration = (): MigrationState &
  MigrationActions => {
  const [state, setState] = useState<MigrationState>({
    needsMigration: false,
    isChecking: false,
    isMigrating: false,
    localDraftCount: 0,
    lastMigrationResult: null,
    error: null,
  });

  /**
   * 检查是否需要迁移
   */
  const checkMigrationNeeded = useCallback(async () => {
    setState(prev => ({ ...prev, isChecking: true, error: null }));

    try {
      console.log('[usePropertyDraftMigration] 检查迁移需求...');

      const [needsMigration, localDraftCount] = await Promise.all([
        propertyDraftMigration.needsMigration(),
        propertyDraftMigration.getLocalDraftCount(),
      ]);

      setState(prev => ({
        ...prev,
        needsMigration,
        localDraftCount,
        isChecking: false,
      }));

      console.log('[usePropertyDraftMigration] 检查完成:', {
        needsMigration,
        localDraftCount,
      });
    } catch (error) {
      console.error('[usePropertyDraftMigration] 检查迁移需求失败:', error);
      setState(prev => ({
        ...prev,
        isChecking: false,
        error: error instanceof Error ? error.message : '检查失败',
      }));
    }
  }, []);

  /**
   * 开始迁移
   */
  const startMigration = useCallback(
    async (showProgress = true): Promise<MigrationResult> => {
      setState(prev => ({ ...prev, isMigrating: true, error: null }));

      try {
        console.log('[usePropertyDraftMigration] 开始迁移...');

        const result = await propertyDraftMigration.migrate(showProgress);

        setState(prev => ({
          ...prev,
          isMigrating: false,
          lastMigrationResult: result,
          needsMigration: result.status !== MigrationStatus.COMPLETED,
          localDraftCount:
            result.status === MigrationStatus.COMPLETED
              ? 0
              : prev.localDraftCount,
        }));

        console.log('[usePropertyDraftMigration] 迁移完成:', result);
        return result;
      } catch (error) {
        console.error('[usePropertyDraftMigration] 迁移失败:', error);

        const errorMessage =
          error instanceof Error ? error.message : '迁移失败';
        setState(prev => ({
          ...prev,
          isMigrating: false,
          error: errorMessage,
        }));

        throw error;
      }
    },
    []
  );

  /**
   * 重置迁移状态
   */
  const resetMigrationStatus = useCallback(async () => {
    try {
      await propertyDraftMigration.resetMigrationStatus();
      await checkMigrationNeeded();
      console.log('[usePropertyDraftMigration] 迁移状态已重置');
    } catch (error) {
      console.error('[usePropertyDraftMigration] 重置迁移状态失败:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '重置失败',
      }));
    }
  }, [checkMigrationNeeded]);

  /**
   * 组件挂载时自动检查
   */
  useEffect(() => {
    checkMigrationNeeded();
  }, [checkMigrationNeeded]);

  return {
    ...state,
    checkMigrationNeeded,
    startMigration,
    resetMigrationStatus,
  };
};

/**
 * 自动迁移Hook - 用于应用启动时
 */
export const useAutoPropertyDraftMigration = (enabled = true) => {
  const migration = usePropertyDraftMigration();
  const [autoMigrationCompleted, setAutoMigrationCompleted] = useState(false);

  useEffect(() => {
    if (
      !enabled ||
      autoMigrationCompleted ||
      migration.isMigrating ||
      migration.isChecking
    ) {
      return;
    }

    if (migration.needsMigration && migration.localDraftCount > 0) {
      console.log(
        '[useAutoPropertyDraftMigration] 检测到本地草稿，开始自动迁移...'
      );

      migration
        .startMigration(true)
        .then(result => {
          console.log('[useAutoPropertyDraftMigration] 自动迁移完成:', result);
          setAutoMigrationCompleted(true);
        })
        .catch(error => {
          console.error('[useAutoPropertyDraftMigration] 自动迁移失败:', error);
          // 自动迁移失败不阻塞应用，用户可以手动迁移
        });
    }
  }, [
    enabled,
    migration.needsMigration,
    migration.localDraftCount,
    migration.isMigrating,
    migration.isChecking,
    autoMigrationCompleted,
    migration,
  ]);

  return {
    ...migration,
    autoMigrationCompleted,
  };
};
