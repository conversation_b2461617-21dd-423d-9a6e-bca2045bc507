#!/usr/bin/env node

/**
 * 地图功能完整验证脚本
 * 验证所有恢复的地图功能是否正常工作
 */

const fs = require('fs');

console.log('🗺️ 地图功能完整验证');
console.log('==================');

// 1. 验证租买切换功能
const checkDealTypeSwitch = () => {
  console.log('\n🏠 验证租买切换功能...');
  
  const propertyStatsFile = 'packages/frontend/src/domains/map/components/PropertyStats/PropertyStats.tsx';
  const hookFile = 'packages/frontend/src/domains/map/hooks/useMapScreenState.ts';
  
  let uiImplemented = false;
  let stateManagement = false;
  
  // 检查UI实现
  if (fs.existsSync(propertyStatsFile)) {
    const content = fs.readFileSync(propertyStatsFile, 'utf8');
    uiImplemented = content.includes('selectedDealType') && 
                   content.includes('onDealTypeChange') &&
                   content.includes('dealTypeContainer') &&
                   content.includes('租赁') && 
                   content.includes('出售');
    console.log(`  PropertyStats UI实现: ${uiImplemented ? '✅' : '❌'}`);
  }
  
  // 检查状态管理
  if (fs.existsSync(hookFile)) {
    const content = fs.readFileSync(hookFile, 'utf8');
    stateManagement = content.includes('selectedDealType') && 
                     content.includes('handleDealTypeChange') &&
                     content.includes('setSelectedDealType');
    console.log(`  状态管理实现: ${stateManagement ? '✅' : '❌'}`);
  }
  
  return uiImplemented && stateManagement;
};

// 2. 验证房源列表显示
const checkPropertyDisplay = () => {
  console.log('\n📍 验证房源列表显示...');
  
  const hookFile = 'packages/frontend/src/domains/map/hooks/useMapScreenState.ts';
  const mapContainerFile = 'packages/frontend/src/shared/components/MapContainer.tsx';
  
  let dataFlow = false;
  let markerRendering = false;
  let autoSearch = false;
  
  // 检查数据流
  if (fs.existsSync(hookFile)) {
    const content = fs.readFileSync(hookFile, 'utf8');
    dataFlow = content.includes('markers: properties') && 
              content.includes('searchResults') &&
              content.includes('convertedProperties');
    autoSearch = content.includes('用户位置已获取，开始自动搜索房源') &&
                content.includes('handleSearch');
    console.log(`  数据流配置: ${dataFlow ? '✅' : '❌'}`);
    console.log(`  自动搜索房源: ${autoSearch ? '✅' : '❌'}`);
  }
  
  // 检查标记渲染
  if (fs.existsSync(mapContainerFile)) {
    const content = fs.readFileSync(mapContainerFile, 'utf8');
    markerRendering = content.includes('markers?.map') && 
                     content.includes('<Marker') &&
                     content.includes('coordinate');
    console.log(`  地图标记渲染: ${markerRendering ? '✅' : '❌'}`);
  }
  
  return dataFlow && markerRendering && autoSearch;
};

// 3. 验证筛选按钮功能
const checkFilterButton = () => {
  console.log('\n🔍 验证筛选按钮功能...');
  
  const mapActionsFile = 'packages/frontend/src/domains/map/components/MapActions/MapActions.tsx';
  const hookFile = 'packages/frontend/src/domains/map/hooks/useMapScreenState.ts';
  
  let buttonImplemented = false;
  let clickHandler = false;
  let modalIntegration = false;
  
  // 检查按钮实现
  if (fs.existsSync(mapActionsFile)) {
    const content = fs.readFileSync(mapActionsFile, 'utf8');
    buttonImplemented = content.includes('onFilterPress') && 
                       content.includes('filterButton') &&
                       content.includes('options-outline');
    console.log(`  筛选按钮UI: ${buttonImplemented ? '✅' : '❌'}`);
  }
  
  // 检查点击处理
  if (fs.existsSync(hookFile)) {
    const content = fs.readFileSync(hookFile, 'utf8');
    clickHandler = content.includes('handleFilterPress') && 
                  content.includes('筛选按钮点击');
    modalIntegration = content.includes('setRentModalVisible') && 
                      content.includes('setSaleModalVisible');
    console.log(`  点击处理逻辑: ${clickHandler ? '✅' : '❌'}`);
    console.log(`  弹窗集成: ${modalIntegration ? '✅' : '❌'}`);
  }
  
  return buttonImplemented && clickHandler && modalIntegration;
};

// 4. 验证房源搜索功能
const checkPropertySearch = () => {
  console.log('\n🔎 验证房源搜索功能...');
  
  const hookFile = 'packages/frontend/src/domains/map/hooks/useMapScreenState.ts';
  const mapSearchFile = 'packages/frontend/src/domains/map/hooks/useMapSearch.ts';
  
  let searchIntegration = false;
  let searchHook = false;
  
  // 检查搜索集成
  if (fs.existsSync(hookFile)) {
    const content = fs.readFileSync(hookFile, 'utf8');
    searchIntegration = content.includes('useMapSearch') && 
                       content.includes('handleSearch') &&
                       content.includes('searchResults');
    console.log(`  搜索功能集成: ${searchIntegration ? '✅' : '❌'}`);
  }
  
  // 检查搜索Hook
  if (fs.existsSync(mapSearchFile)) {
    searchHook = true;
    console.log(`  useMapSearch Hook: ${searchHook ? '✅' : '❌'}`);
  } else {
    console.log(`  useMapSearch Hook: ❌ 文件不存在`);
  }
  
  return searchIntegration && searchHook;
};

// 5. 验证组件生命周期
const checkComponentLifecycle = () => {
  console.log('\n🔄 验证组件生命周期...');
  
  const mapScreenFile = 'packages/frontend/src/domains/map/screens/MapSearchScreen.tsx';
  const mapContainerFile = 'packages/frontend/src/shared/components/MapContainer.tsx';
  
  let mapScreenLifecycle = false;
  let mapContainerLifecycle = false;
  
  // 检查MapSearchScreen生命周期
  if (fs.existsSync(mapScreenFile)) {
    const content = fs.readFileSync(mapScreenFile, 'utf8');
    mapScreenLifecycle = content.includes('组件挂载 - 开始初始化') && 
                        content.includes('组件卸载 - 开始清理');
    console.log(`  MapSearchScreen生命周期: ${mapScreenLifecycle ? '✅' : '❌'}`);
  }
  
  // 检查MapContainer生命周期
  if (fs.existsSync(mapContainerFile)) {
    const content = fs.readFileSync(mapContainerFile, 'utf8');
    mapContainerLifecycle = content.includes('组件挂载，开始初始化') && 
                           content.includes('组件卸载，清理所有异步操作');
    console.log(`  MapContainer生命周期: ${mapContainerLifecycle ? '✅' : '❌'}`);
  }
  
  return mapScreenLifecycle && mapContainerLifecycle;
};

// 6. 生成完整验证报告
const generateCompleteReport = () => {
  console.log('\n📊 地图功能完整验证报告:');
  console.log('============================');
  
  const dealTypeSwitchOk = checkDealTypeSwitch();
  const propertyDisplayOk = checkPropertyDisplay();
  const filterButtonOk = checkFilterButton();
  const propertySearchOk = checkPropertySearch();
  const lifecycleOk = checkComponentLifecycle();
  
  console.log('\n🎯 功能验证汇总:');
  console.log(`✅ 租买切换功能: ${dealTypeSwitchOk ? '通过' : '失败'}`);
  console.log(`✅ 房源列表显示: ${propertyDisplayOk ? '通过' : '失败'}`);
  console.log(`✅ 筛选按钮功能: ${filterButtonOk ? '通过' : '失败'}`);
  console.log(`✅ 房源搜索功能: ${propertySearchOk ? '通过' : '失败'}`);
  console.log(`✅ 组件生命周期: ${lifecycleOk ? '通过' : '失败'}`);
  
  const allFunctionsWork = dealTypeSwitchOk && propertyDisplayOk && 
                          filterButtonOk && propertySearchOk && lifecycleOk;
  
  if (allFunctionsWork) {
    console.log('\n🎉 所有地图功能恢复成功！');
    console.log('💡 现在可以进行完整的地图功能测试');
    
    console.log('\n📋 完整测试流程:');
    console.log('1. 🗺️ 地图基础功能:');
    console.log('   - 地图正常显示');
    console.log('   - 定位功能工作');
    console.log('   - 组件生命周期正常');
    
    console.log('\n2. 🏠 租买切换功能:');
    console.log('   - 底部显示租赁/出售切换按钮');
    console.log('   - 点击切换应该触发房源重新搜索');
    console.log('   - 观察日志: [DealType] 切换交易类型');
    
    console.log('\n3. 📍 房源显示功能:');
    console.log('   - 获取位置后自动搜索房源');
    console.log('   - 地图上显示房源标记点');
    console.log('   - 观察日志: [MapScreen] 显示X个房源');
    
    console.log('\n4. 🔍 筛选功能:');
    console.log('   - 左侧显示筛选按钮');
    console.log('   - 点击筛选按钮打开对应弹窗');
    console.log('   - 租赁模式打开租赁筛选，出售模式打开出售筛选');
    
    console.log('\n5. 🏷️ 房源类型选择:');
    console.log('   - 顶部显示房源类型选择器');
    console.log('   - 可以切换商铺、写字楼、厂房等类型');
    
    console.log('\n6. 🔄 导航切换:');
    console.log('   - 切换到其他Tab观察组件卸载日志');
    console.log('   - 切换回地图Tab观察组件挂载日志');
    console.log('   - 确认没有崩溃和内存泄漏');
  } else {
    console.log('\n⚠️  部分地图功能验证失败');
    console.log('需要检查失败的功能并进行修复');
  }
  
  console.log('\n✅ 验证完成！');
};

// 执行完整验证
generateCompleteReport();
