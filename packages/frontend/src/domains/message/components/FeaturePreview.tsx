/**
 * 功能预览组件
 * 展示不同角色将收到的消息类型
 */
import React from 'react';
import {
  View,
  Text,
  Dimensions,
} from 'react-native';

// 响应式设计 - 获取屏幕尺寸
const { width: screenWidth } = Dimensions.get('window');

interface FeaturePreviewProps {
  activeRole: 'tenant_buyer' | 'landlord';
}

/**
 * 功能预览项组件 - 紧凑布局
 */
const FeaturePreviewItem: React.FC<{
  icon: string;
  title: string;
  description: string;
  color: string;
}> = ({ icon, title, description, color }) => (
  <View style={styles.featureItemCompact}>
    <View style={[styles.featureIconCompact, { backgroundColor: color + '20' }]}>
      <Text style={styles.featureIconText}>{icon}</Text>
    </View>
    <View style={styles.featureContentCompact}>
      <Text style={styles.featureTitleCompact}>{title}</Text>
      <Text style={styles.featureDescriptionCompact}>{description}</Text>
    </View>
  </View>
);

export const FeaturePreview: React.FC<FeaturePreviewProps> = ({
  activeRole,
}) => {
  return (
    <View style={styles.featurePreviewSection}>
      <Text style={styles.sectionTitle}>您将收到的消息类型</Text>
      
      {activeRole === 'tenant_buyer' ? (
        <View style={styles.featureListCompact}>
          <FeaturePreviewItem
            icon="🏠"
            title="房源推荐"
            description="业主主动推荐合适房源"
            color="#FF6B35"
          />
          <FeaturePreviewItem
            icon="💬"
            title="咨询回复"
            description="业主回复您的咨询消息"
            color="#4CAF50"
          />
          <FeaturePreviewItem
            icon="📞"
            title="联系邀请"
            description="业主邀请电话或实地看房"
            color="#2196F3"
          />
          <FeaturePreviewItem
            icon="💰"
            title="价格变动"
            description="关注房源的价格调整通知"
            color="#FF9800"
          />
        </View>
      ) : (
        <View style={styles.featureListCompact}>
          <FeaturePreviewItem
            icon="🔍"
            title="租买咨询"
            description="租客买家咨询您的房源"
            color="#FF6B35"
          />
          <FeaturePreviewItem
            icon="📅"
            title="看房预约"
            description="租客买家预约看房时间"
            color="#4CAF50"
          />
          <FeaturePreviewItem
            icon="💼"
            title="合作意向"
            description="中介或其他业主合作邀请"
            color="#2196F3"
          />
          <FeaturePreviewItem
            icon="⭐"
            title="房源关注"
            description="有人收藏或关注您的房源"
            color="#9C27B0"
          />
        </View>
      )}
    </View>
  );
};

const styles = {
  featurePreviewSection: {
    backgroundColor: '#ffffff',
    paddingVertical: screenWidth * 0.03,      // 响应式垂直内边距，更紧凑
    paddingHorizontal: screenWidth * 0.04,   // 响应式水平内边距
    marginBottom: screenWidth * 0.015,       // 响应式下边距，更紧凑
  },
  sectionTitle: {
    fontSize: screenWidth * 0.038,          // 响应式标题字体
    fontWeight: '600' as const,
    color: '#333333',
    marginBottom: screenWidth * 0.025,     // 响应式下边距，更紧凑
  },
  featureListCompact: {
    gap: screenWidth * 0.015,              // 响应式间距，更紧凑
  },
  featureItemCompact: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingVertical: 6,
  },
  featureIconCompact: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginRight: 12,
  },
  featureIconText: {
    fontSize: 16,
  },
  featureContentCompact: {
    flex: 1,
  },
  featureTitleCompact: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#333333',
    marginBottom: 2,
  },
  featureDescriptionCompact: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
  },
};
