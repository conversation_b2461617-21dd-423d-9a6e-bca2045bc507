import { StyleSheet } from 'react-native';
import { wp, hp, fp } from '../../../../shared/utils/responsiveUtils';

export const progressStyles = StyleSheet.create({
  // 4步骤进度条样式
  stepProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(16),
    paddingVertical: hp(12),
  },
  percentageText: {
    fontSize: fp(16),
    fontWeight: 'bold',
    color: '#FF6B35',
    marginRight: wp(12),
    minWidth: wp(45), // 固定宽度，避免跳动
  },
  stepProgressBar: {
    flex: 1,
    flexDirection: 'row',
    height: hp(6),
    marginRight: wp(12),
  },
  stepSegment: {
    flex: 1,
    height: '100%',
    marginRight: wp(2),
    borderRadius: wp(3),
  },
  warmTip: {
    fontSize: fp(12),
    color: '#FF6B35',
    fontWeight: '500',
    flex: 2,
  },
  completeTip: {
    fontSize: fp(12),
    color: '#34C759',
    fontWeight: '600',
    flex: 2,
  },
  
  // 上传进度条样式（保留原有功能）
  uploadProgressContainer: {
    marginTop: hp(16),
    paddingHorizontal: wp(16),
    backgroundColor: '#F8F9FA',
    borderRadius: wp(8),
    padding: wp(16),
  },
  uploadProgressBar: {
    height: hp(4),
    backgroundColor: '#E5E5E5',
    borderRadius: wp(2),
    overflow: 'hidden',
  },
  uploadProgressFill: {
    height: '100%',
    backgroundColor: '#007AFF',
    borderRadius: wp(2),
  },
  uploadProgressText: {
    fontSize: fp(12),
    color: '#666666',
    textAlign: 'center',
    marginTop: hp(4),
  },

  // 补充缺失的进度条样式
  progressBar: {
    height: hp(4),
    backgroundColor: '#E5E5EA',
    borderRadius: wp(2),
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#007AFF',
    borderRadius: wp(2),
  },

  // 移除重复的上传进度容器样式定义
  
  // 步骤指示器样式
  stepIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp(16),
  },
  stepDot: {
    width: wp(8),
    height: wp(8),
    borderRadius: wp(4),
    marginHorizontal: wp(4),
  },
  stepDotActive: {
    backgroundColor: '#007AFF',
  },
  stepDotInactive: {
    backgroundColor: '#E5E5E5',
  },
  stepDotCompleted: {
    backgroundColor: '#34C759',
  },
  
  // 进度文本样式
  progressLabel: {
    fontSize: fp(14),
    fontWeight: '500',
    color: '#333333',
    marginBottom: hp(8),
  },
  progressValue: {
    fontSize: fp(12),
    color: '#666666',
  },
  progressPercentage: {
    fontSize: fp(14),
    fontWeight: 'bold',
    color: '#007AFF',
  },
});
