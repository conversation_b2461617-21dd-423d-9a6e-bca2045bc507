#!/usr/bin/env python3
"""
修复SQLModel Field参数冲突问题
SQLModel不支持在Field中同时传递某些参数和sa_column
"""

import os
import re
import glob

def fix_field_conflicts(file_path):
    """修复单个文件中的Field参数冲突"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 修复nullable + sa_column冲突
    content = re.sub(
        r'(\w+:\s*\w+\s*=\s*Field\([^)]*?)nullable\s*=\s*(True|False)\s*,?\s*([^)]*sa_column\s*=[^)]*)\)',
        r'\1\3)',
        content,
        flags=re.DOTALL
    )
    
    # 修复index + sa_column冲突
    content = re.sub(
        r'(\w+:\s*\w+\s*=\s*Field\([^)]*?)index\s*=\s*(True|False)\s*,?\s*([^)]*sa_column\s*=\s*Column\([^)]*?)\)',
        lambda m: f"{m.group(1)}{m.group(3).replace(')', f', index={m.group(2)})'))}",
        content,
        flags=re.DOTALL
    )
    
    # 修复primary_key + sa_column冲突
    content = re.sub(
        r'(\w+:\s*\w+\s*=\s*Field\([^)]*?)primary_key\s*=\s*(True|False)\s*,?\s*([^)]*sa_column\s*=\s*Column\([^)]*?)\)',
        lambda m: f"{m.group(1)}{m.group(3).replace(')', f', primary_key={m.group(2)})'))}",
        content,
        flags=re.DOTALL
    )
    
    # 修复unique + sa_column冲突
    content = re.sub(
        r'(\w+:\s*\w+\s*=\s*Field\([^)]*?)unique\s*=\s*(True|False)\s*,?\s*([^)]*sa_column\s*=\s*Column\([^)]*?)\)',
        lambda m: f"{m.group(1)}{m.group(3).replace(')', f', unique={m.group(2)})'))}",
        content,
        flags=re.DOTALL
    )
    
    # 清理多余的逗号
    content = re.sub(r',\s*,', ',', content)
    content = re.sub(r'Field\(\s*,', 'Field(', content)
    content = re.sub(r',\s*\)', ')', content)
    
    # 如果内容有变化，写回文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Fixed: {file_path}")
        return True
    return False

def main():
    """主函数"""
    model_files = glob.glob('packages/backend/app/models/**/*.py', recursive=True)
    
    fixed_count = 0
    for file_path in model_files:
        if fix_field_conflicts(file_path):
            fixed_count += 1
    
    print(f"Total files fixed: {fixed_count}")

if __name__ == "__main__":
    main()
