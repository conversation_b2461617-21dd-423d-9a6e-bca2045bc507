#!/usr/bin/env node

/**
 * 批量替换所有Alert.alert调用为FeedbackService
 * 企业级代码规范修复脚本
 */

const fs = require('fs');
const path = require('path');

// 需要处理的文件列表
const filesToProcess = [
  'packages/frontend/src/components/dev/DevAuthController.tsx',
  'packages/frontend/src/domains/auth/components/UserInfoModal.tsx',
  'packages/frontend/src/domains/auth/components/UserProfileCompletionModal.tsx',
  'packages/frontend/src/domains/auth/screens/EnhancedRegisterScreen.tsx',
  'packages/frontend/src/domains/auth/screens/HelpScreen.tsx',
  'packages/frontend/src/domains/auth/screens/LoginScreen.tsx',
  'packages/frontend/src/domains/auth/screens/QuickLoginScreen.tsx',
  'packages/frontend/src/domains/auth/screens/RegisterScreen.tsx',
  'packages/frontend/src/domains/auth/screens/SwitchAccountScreen.tsx',
  'packages/frontend/src/domains/demand/screens/DemandFormScreen.tsx',
  'packages/frontend/src/domains/demand/screens/MyDemandsScreen.tsx',
  'packages/frontend/src/domains/message/screens/ChatDetailScreen.tsx',
  'packages/frontend/src/domains/message/screens/LandlordSubscriptionScreen.tsx',
  'packages/frontend/src/domains/property/components/detail/CommuteMapSectionAdvanced.tsx',
  'packages/frontend/src/domains/property/components/detail/CommuteMapSection.tsx',
  'packages/frontend/src/domains/property/components/detail/HotQuestionsSection.tsx',
  'packages/frontend/src/domains/property/components/detail/VideoPlayer.tsx',
  'packages/frontend/src/domains/publish/components/AIContent/AIContentGenerator.tsx',
  'packages/frontend/src/domains/publish/components/IntelligentTags/IntelligentTagSelector.tsx',
  'packages/frontend/src/domains/user/components/ExpoDateTimePicker.tsx',
  'packages/frontend/src/domains/user/components/PasswordConfirmModal.tsx',
  'packages/frontend/src/domains/user/components/VerificationCodeConfirmModal.tsx',
  'packages/frontend/src/domains/user/screens/AccountManagementScreen.tsx',
  'packages/frontend/src/domains/user/screens/DeviceManagementScreen.tsx',
  'packages/frontend/src/domains/user/screens/FeedbackScreen.tsx',
  'packages/frontend/src/domains/user/screens/MyPropertiesScreen.tsx',
  'packages/frontend/src/domains/user/screens/ProfileScreen.tsx',
  'packages/frontend/src/domains/user/screens/RoleVerificationScreen.tsx',
  'packages/frontend/src/domains/user/screens/SecuritySettingsScreen.tsx',
  'packages/frontend/src/domains/verification/components/DocumentUpload.tsx',
  'packages/frontend/src/domains/verification/components/VerificationForm.tsx',
  'packages/frontend/src/hooks/useMediaUpload.ts',
  'packages/frontend/src/hooks/useOptimizedBatchUpload.ts',
  'packages/frontend/src/screens/Property/PropertyDetailScreen.tsx',
  'packages/frontend/src/screens/Publish/PropertyDetailFormScreen/components/MediaUploadSection.tsx',
  'packages/frontend/src/screens/Publish/PropertyDetailFormScreen.tsx',
  'packages/frontend/src/screens/Publish/PropertyPublishScreen.tsx',
  'packages/frontend/src/screens/Publish/PublishOptionsScreen.tsx',
  'packages/frontend/src/screens/Publish/VerificationScreen.tsx',
  'packages/frontend/src/screens/TenantRequirementsScreen.tsx',
  'packages/frontend/src/screens/TestRollback.tsx',
  'packages/frontend/src/services/media/MediaPickerService.ts',
  'packages/frontend/src/shared/hooks/useFormValidationWithScroll.ts',
  'packages/frontend/src/shared/hooks/useVerificationCode.ts',
  'packages/frontend/src/shared/services/verificationCodeService.examples.tsx'
];

// Alert.alert替换规则
const replacementRules = [
  // 成功提示
  {
    pattern: /Alert\.alert\(\s*['"`]成功['"`]\s*,\s*([^,)]+)\s*(?:,\s*\[[^\]]*\])?\s*\)/g,
    replacement: 'FeedbackService.showSuccess($1)'
  },
  // 错误提示
  {
    pattern: /Alert\.alert\(\s*['"`]错误['"`]\s*,\s*([^,)]+)\s*(?:,\s*\[[^\]]*\])?\s*\)/g,
    replacement: 'FeedbackService.showError($1)'
  },
  {
    pattern: /Alert\.alert\(\s*['"`]失败['"`]\s*,\s*([^,)]+)\s*(?:,\s*\[[^\]]*\])?\s*\)/g,
    replacement: 'FeedbackService.showError($1)'
  },
  // 登录相关
  {
    pattern: /Alert\.alert\(\s*['"`]登录失败['"`]\s*,\s*([^,)]+)\s*(?:,\s*\[[^\]]*\])?\s*\)/g,
    replacement: 'FeedbackService.showLoginFailed($1)'
  },
  {
    pattern: /Alert\.alert\(\s*['"`]注册成功['"`]\s*,\s*([^,)]+)\s*(?:,\s*\[[^\]]*\])?\s*\)/g,
    replacement: 'FeedbackService.showRegisterSuccess()'
  },
  {
    pattern: /Alert\.alert\(\s*['"`]注册失败['"`]\s*,\s*([^,)]+)\s*(?:,\s*\[[^\]]*\])?\s*\)/g,
    replacement: 'FeedbackService.showRegisterFailed($1)'
  },
  // 验证码相关
  {
    pattern: /Alert\.alert\(\s*['"`]发送成功['"`]\s*,\s*([^,)]+)\s*(?:,\s*\[[^\]]*\])?\s*\)/g,
    replacement: 'FeedbackService.showCodeSent()'
  },
  {
    pattern: /Alert\.alert\(\s*['"`]发送失败['"`]\s*,\s*([^,)]+)\s*(?:,\s*\[[^\]]*\])?\s*\)/g,
    replacement: 'FeedbackService.showCodeSendFailed($1)'
  },
  // 提示信息
  {
    pattern: /Alert\.alert\(\s*['"`]提示['"`]\s*,\s*([^,)]+)\s*(?:,\s*\[[^\]]*\])?\s*\)/g,
    replacement: 'FeedbackService.showInfo($1)'
  },
  // 警告信息
  {
    pattern: /Alert\.alert\(\s*['"`]警告['"`]\s*,\s*([^,)]+)\s*(?:,\s*\[[^\]]*\])?\s*\)/g,
    replacement: 'FeedbackService.showWarning($1)'
  },
  // 确认对话框 - 复杂的需要手动处理
  {
    pattern: /Alert\.alert\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*\[\s*\{[^}]*text:\s*['"`]取消['"`][^}]*\}\s*,\s*\{[^}]*text:\s*['"`]确定['"`][^}]*onPress:\s*([^}]+)\}[^\]]*\]\s*\)/g,
    replacement: 'FeedbackService.showConfirm($1, $2, $3)'
  },
  // 通用Alert.alert替换为showInfo
  {
    pattern: /Alert\.alert\(\s*([^,)]+)\s*,\s*([^,)]+)\s*(?:,\s*\[[^\]]*\])?\s*\)/g,
    replacement: 'FeedbackService.showInfo($2)'
  }
];

console.log('🚀 开始批量替换Alert.alert调用...\n');

let totalFiles = 0;
let totalReplacements = 0;

filesToProcess.forEach(filePath => {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let fileReplacements = 0;

    // 应用所有替换规则
    replacementRules.forEach(rule => {
      const matches = content.match(rule.pattern);
      if (matches) {
        content = content.replace(rule.pattern, rule.replacement);
        fileReplacements += matches.length;
      }
    });

    // 添加FeedbackService导入（如果需要且不存在）
    if (fileReplacements > 0) {
      if (!content.includes('FeedbackService')) {
        // 找到合适的位置插入导入
        const importMatch = content.match(/import.*from\s+['"`][^'"`]*react-native['"`];?\s*\n/);
        if (importMatch) {
          const insertPos = content.indexOf(importMatch[0]) + importMatch[0].length;
          content = content.slice(0, insertPos) + 
                   "import FeedbackService from '../../../shared/services/FeedbackService';\n" +
                   content.slice(insertPos);
        }
      }

      // 移除Alert导入（如果不再需要）
      if (!content.includes('Alert.')) {
        content = content.replace(/,\s*Alert\s*,/g, ',');
        content = content.replace(/Alert\s*,\s*/g, '');
        content = content.replace(/,\s*Alert\s*}/g, '}');
        content = content.replace(/{\s*Alert\s*}/g, '{}');
      }

      // 写入文件
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ ${filePath}: ${fileReplacements} 个替换`);
      totalFiles++;
      totalReplacements += fileReplacements;
    } else {
      console.log(`ℹ️  ${filePath}: 无需替换`);
    }

  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
  }
});

console.log(`\n🎉 批量替换完成!`);
console.log(`📊 统计信息:`);
console.log(`   - 处理文件: ${totalFiles} 个`);
console.log(`   - 总替换数: ${totalReplacements} 个`);
console.log(`\n⚠️  注意: 复杂的确认对话框可能需要手动调整`);
