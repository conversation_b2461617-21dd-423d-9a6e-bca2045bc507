#!/usr/bin/env node

/**
 * 导航修复验证脚本
 * 对比Git历史版本，验证导航配置修复
 */

const fs = require('fs');
const { exec } = require('child_process');

console.log('🔍 验证导航修复效果...');

// 1. 验证当前MainTabNavigator配置
const testCurrentNavigation = () => {
  console.log('\n📱 检查当前MainTabNavigator配置...');
  
  const navFile = 'packages/frontend/src/navigation/MainTabNavigator.tsx';
  
  if (fs.existsSync(navFile)) {
    const content = fs.readFileSync(navFile, 'utf8');
    
    // 检查Map Tab是否使用component
    const hasMapComponent = content.includes('name="Map"') && 
                           content.includes('component={MapSearchScreen}');
    console.log(`  Map Tab使用component: ${hasMapComponent ? '✅' : '❌'}`);
    
    // 检查是否移除了ErrorBoundary render函数
    const hasMapRenderFunction = content.includes('name="Map"') && 
                                 content.includes('{() => (') &&
                                 content.includes('<ErrorBoundary');
    console.log(`  Map Tab移除render函数: ${!hasMapRenderFunction ? '✅' : '❌'}`);
    
    // 检查其他Tab是否都使用component
    const allTabsUseComponent = content.includes('component={HomeScreen}') &&
                               content.includes('component={MapSearchScreen}') &&
                               content.includes('component={PublishOptionsScreen}') &&
                               content.includes('component={MessageCenterScreen}') &&
                               content.includes('component={ProfileScreen}');
    console.log(`  所有Tab使用component: ${allTabsUseComponent ? '✅' : '❌'}`);
    
    return {
      hasMapComponent,
      hasMapRenderFunction: !hasMapRenderFunction,
      allTabsUseComponent
    };
  }
  
  return null;
};

// 2. 对比Git历史版本
const compareWithGitHistory = () => {
  console.log('\n📚 对比Git历史版本...');
  
  return new Promise((resolve) => {
    exec('git show 3f4052b00:packages/frontend/src/navigation/MainTabNavigator.tsx', 
         { cwd: '/data/my-real-estate-app' }, 
         (error, stdout, stderr) => {
      if (error) {
        console.log('  Git历史对比: ❌ 无法获取历史版本');
        resolve(false);
        return;
      }
      
      // 检查历史版本的配置
      const hasHistoryMapComponent = stdout.includes('name="Map"') && 
                                    stdout.includes('component={MapSearchScreen}');
      console.log(`  历史版本Map Tab使用component: ${hasHistoryMapComponent ? '✅' : '❌'}`);
      
      const hasHistoryRenderFunction = stdout.includes('name="Map"') && 
                                      stdout.includes('{() => (');
      console.log(`  历史版本无render函数: ${!hasHistoryRenderFunction ? '✅' : '❌'}`);
      
      resolve(hasHistoryMapComponent && !hasHistoryRenderFunction);
    });
  });
};

// 3. 验证MapSearchScreen组件监控
const testMapScreenMonitoring = () => {
  console.log('\n🗺️ 检查MapSearchScreen组件监控...');
  
  const mapScreenFile = 'packages/frontend/src/domains/map/screens/MapSearchScreen.tsx';
  
  if (fs.existsSync(mapScreenFile)) {
    const content = fs.readFileSync(mapScreenFile, 'utf8');
    
    const hasUnmountLog = content.includes('组件挂载') && content.includes('组件卸载');
    console.log(`  MapSearchScreen卸载监控: ${hasUnmountLog ? '✅' : '❌'}`);
    
    const hasUseEffect = content.includes('useEffect') && content.includes('return () =>');
    console.log(`  useEffect清理函数: ${hasUseEffect ? '✅' : '❌'}`);
    
    return { hasUnmountLog, hasUseEffect };
  }
  
  return null;
};

// 4. 生成修复报告
const generateFixReport = async () => {
  console.log('\n📊 导航修复验证报告:');
  console.log('========================');
  
  const currentNav = testCurrentNavigation();
  const historyComparison = await compareWithGitHistory();
  const mapMonitoring = testMapScreenMonitoring();
  
  console.log('\n🎯 关键修复验证:');
  
  if (currentNav) {
    console.log(`✅ Map Tab配置修复: ${currentNav.hasMapComponent ? '通过' : '失败'}`);
    console.log(`✅ render函数移除: ${currentNav.hasMapRenderFunction ? '通过' : '失败'}`);
    console.log(`✅ 配置一致性: ${currentNav.allTabsUseComponent ? '通过' : '失败'}`);
  }
  
  console.log(`✅ 与历史版本一致: ${historyComparison ? '通过' : '失败'}`);
  
  if (mapMonitoring) {
    console.log(`✅ 组件监控: ${mapMonitoring.hasUnmountLog && mapMonitoring.hasUseEffect ? '通过' : '失败'}`);
  }
  
  console.log('\n🔍 问题根源分析:');
  console.log('❌ 问题配置: 使用render函数 {() => (<ErrorBoundary><MapSearchScreen /></ErrorBoundary>)}');
  console.log('✅ 正确配置: 使用component属性 component={MapSearchScreen}');
  
  console.log('\n📱 React Navigation原理:');
  console.log('- component属性: React Navigation直接管理组件生命周期');
  console.log('- render函数: 每次导航都创建新的组件实例，导致卸载问题');
  
  console.log('\n🧪 测试建议:');
  console.log('1. 进入地图找房页面，观察: [MapSearchScreen] 🚀 组件挂载');
  console.log('2. 切换到其他Tab，观察: [MapSearchScreen] 🧹 组件卸载');
  console.log('3. 确认MapContainer日志不再出现');
  console.log('4. 验证导航切换不再崩溃');
  
  console.log('\n✅ 验证完成！');
};

// 执行验证
generateFixReport();
