/**
 * 企业级认证状态管理 - 统一解决方案
 * 
 * 参考Uber、Airbnb等主流APP的最佳实践：
 * 1. 单一数据源 (Single Source of Truth)
 * 2. 不可变状态更新 (Immutable State Updates)  
 * 3. 类型安全 (Type Safety)
 * 4. 可预测的状态变更 (Predictable State Changes)
 * 5. 优秀的开发者体验 (Developer Experience)
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { devtools } from 'zustand/middleware';
import { User, AuthStatus, LoginCredentials, AuthTokens } from '../types/auth.types';

// 🏢 企业级类型定义 - User类型已移至独立文件

export interface AuthState {
  // ✅ 认证状态 - 核心数据
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // ✅ UI状态 - 分离关注点
  showFloatingLoginBar: boolean;
  floatingLoginBarAppearanceCount: number;
  lastShownPage: string | null;
  
  // ✅ 会话状态 - 安全管理
  accessToken: string | null;
  tokenExpiry: number | null;
  
  // ✅ 错误状态 - 统一错误处理
  error: string | null;
  lastError: {
    type: 'LOGIN' | 'REFRESH' | 'LOGOUT' | 'NETWORK';
    message: string;
    timestamp: number;
  } | null;
}

export interface AuthActions {
  // 🔐 认证操作
  login: (credentials: { phone_number: string; password?: string; verification_code?: string }) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  checkAuthStatus: () => Promise<void>;
  
  // 👤 用户操作
  updateUser: (userData: Partial<User>) => void;
  
  // 🎨 UI操作
  updateFloatingLoginBar: (config: { 
    isVisible: boolean; 
    shouldIncrement?: boolean;
    currentPage?: string;
  }) => void;
  
  // 🚨 错误处理
  setError: (error: string, type?: AuthState['lastError']['type']) => void;
  clearError: () => void;
  
  // 🔄 重置操作
  reset: () => void;
}

// 🏭 初始状态 - 企业级默认值
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  showFloatingLoginBar: false,
  floatingLoginBarAppearanceCount: 0,
  lastShownPage: null,
  accessToken: null,
  tokenExpiry: null,
  error: null,
  lastError: null,
};

/**
 * 🏢 企业级认证状态管理
 * 
 * 使用Zustand的多个中间件实现企业级功能：
 * - immer: 不可变状态更新
 * - subscribeWithSelector: 细粒度订阅
 * - devtools: 开发者工具集成
 */
export const useAuthState = create<AuthState & AuthActions>()(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        // 初始状态
        ...initialState,
        
        // 🔐 登录操作 - 企业级实现
        login: async (credentials) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
            state.lastError = null;
          });
          
          try {
            console.log('[AuthState] 🚀 开始登录流程');
            
            // 动态导入认证服务，避免循环依赖
            const { authService } = await import('../services/authService');
            
            let result;
            if (credentials.verification_code) {
              // 验证码登录
              result = await authService.loginWithSMS({
                phone_number: credentials.phone_number,
                verification_code: credentials.verification_code,
              });
            } else if (credentials.password) {
              // 密码登录
              result = await authService.loginWithPassword({
                phone_number: credentials.phone_number,
                password: credentials.password,
              });
            } else {
              throw new Error('请提供密码或验证码');
            }
            
            // 更新状态
            set((state) => {
              state.user = result.user;
              state.isAuthenticated = true;
              state.isLoading = false;
              state.accessToken = result.accessToken;
              state.tokenExpiry = result.tokenExpiry;
              state.showFloatingLoginBar = false;
              state.error = null;
            });
            
            console.log('[AuthState] ✅ 登录成功');
            
            // 触发登录成功事件（用于分析和其他副作用）
            get()._emitEvent('LOGIN_SUCCESS', { user: result.user });
            
          } catch (error: any) {
            console.error('[AuthState] ❌ 登录失败:', error);
            
            set((state) => {
              state.isLoading = false;
              state.error = error.message || '登录失败';
              state.lastError = {
                type: 'LOGIN',
                message: error.message || '登录失败',
                timestamp: Date.now(),
              };
            });
            
            throw error; // 重新抛出错误供UI处理
          }
        },
        
        // 🚪 登出操作 - 清理所有状态
        logout: async () => {
          try {
            console.log('[AuthState] 🚪 开始登出流程');
            
            // 可选：调用服务端登出API
            try {
              const { authService } = await import('../services/authService');
              await authService.logout();
            } catch (apiError) {
              console.warn('[AuthState] 服务端登出失败，继续本地清理:', apiError);
            }
            
            // 清理本地状态
            set(() => ({
              ...initialState, // 重置到初始状态
            }));
            
            // 清理本地存储
            const { clearAuthData } = await import('../services/storage');
            await clearAuthData();
            
            console.log('[AuthState] ✅ 登出完成');
            
            // 触发登出事件
            get()._emitEvent('LOGOUT_SUCCESS', {});
            
          } catch (error: any) {
            console.error('[AuthState] ❌ 登出失败:', error);
            
            set((state) => {
              state.error = error.message || '登出失败';
              state.lastError = {
                type: 'LOGOUT',
                message: error.message || '登出失败', 
                timestamp: Date.now(),
              };
            });
          }
        },
        
        // 🔄 刷新Token - 自动续期
        refreshToken: async () => {
          const currentToken = get().accessToken;
          if (!currentToken) {
            throw new Error('没有有效的访问令牌');
          }
          
          try {
            console.log('[AuthState] 🔄 刷新访问令牌');
            
            const { authService } = await import('../services/authService');
            const result = await authService.refreshToken(currentToken);
            
            set((state) => {
              state.accessToken = result.accessToken;
              state.tokenExpiry = result.tokenExpiry;
              state.user = result.user; // 可能包含更新的用户信息
            });
            
            console.log('[AuthState] ✅ Token刷新成功');
            
          } catch (error: any) {
            console.error('[AuthState] ❌ Token刷新失败:', error);
            
            // 如果刷新失败，可能token已过期，需要重新登录
            set((state) => {
              state.isAuthenticated = false;
              state.accessToken = null;
              state.tokenExpiry = null;
              state.user = null;
              state.showFloatingLoginBar = true;
              state.lastError = {
                type: 'REFRESH',
                message: 'Session expired, please login again',
                timestamp: Date.now(),
              };
            });
            
            throw error;
          }
        },
        
        // 🔍 检查认证状态 - 应用启动时调用
        checkAuthStatus: async () => {
          set((state) => {
            state.isLoading = true;
          });
          
          try {
            console.log('[AuthState] 🔍 检查认证状态');
            
            // 从本地存储恢复状态
            const { getStoredAuthData } = await import('../services/storage');
            const storedAuth = await getStoredAuthData();
            
            if (!storedAuth?.accessToken) {
              console.log('[AuthState] 没有找到存储的认证信息');
              set((state) => {
                state.isLoading = false;
                state.isAuthenticated = false;
              });
              return;
            }
            
            // 检查token是否过期
            if (storedAuth.tokenExpiry && Date.now() > storedAuth.tokenExpiry) {
              console.log('[AuthState] Token已过期，尝试刷新');
              try {
                await get().refreshToken();
              } catch (refreshError) {
                console.log('[AuthState] Token刷新失败，需要重新登录');
                set((state) => {
                  state.isLoading = false;
                  state.isAuthenticated = false;
                });
                return;
              }
            } else {
              // Token有效，恢复认证状态
              set((state) => {
                state.user = storedAuth.user;
                state.isAuthenticated = true;
                state.accessToken = storedAuth.accessToken;
                state.tokenExpiry = storedAuth.tokenExpiry;
                state.isLoading = false;
              });
            }
            
            console.log('[AuthState] ✅ 认证状态检查完成');
            
          } catch (error: any) {
            console.error('[AuthState] ❌ 认证状态检查失败:', error);
            
            set((state) => {
              state.isLoading = false;
              state.isAuthenticated = false;
              state.error = error.message || '认证检查失败';
              state.lastError = {
                type: 'NETWORK',
                message: error.message || '认证检查失败',
                timestamp: Date.now(),
              };
            });
          }
        },
        
        // 👤 更新用户信息
        updateUser: (userData) => {
          set((state) => {
            if (state.user) {
              Object.assign(state.user, userData);
            }
          });
          
          console.log('[AuthState] 👤 用户信息已更新');
        },
        
        // 🎨 更新浮动登录栏状态 - 统一UI管理
        updateFloatingLoginBar: ({ isVisible, shouldIncrement = false, currentPage }) => {
          set((state) => {
            state.showFloatingLoginBar = isVisible;
            
            if (shouldIncrement) {
              state.floatingLoginBarAppearanceCount++;
            }
            
            if (currentPage) {
              state.lastShownPage = currentPage;
            }
          });
          
          console.log('[AuthState] 🎨 浮动登录栏状态已更新:', { isVisible, currentPage });
        },
        
        // 🚨 设置错误
        setError: (error, type = 'NETWORK') => {
          set((state) => {
            state.error = error;
            state.lastError = {
              type,
              message: error,
              timestamp: Date.now(),
            };
          });
        },
        
        // ✅ 清除错误
        clearError: () => {
          set((state) => {
            state.error = null;
            state.lastError = null;
          });
        },
        
        // 🔄 重置状态
        reset: () => {
          set(() => ({ ...initialState }));
          console.log('[AuthState] 🔄 状态已重置');
        },
        
        // 📡 内部事件系统（用于解耦组件间通信）
        _emitEvent: (eventType: string, data: any) => {
          // 可以在这里添加全局事件处理，如分析统计
          console.log(`[AuthState] 📡 Event: ${eventType}`, data);
        },
      })),
      {
        name: 'auth-state', // DevTools中显示的名称
        version: 1, // 用于状态迁移
      }
    )
  )
);

/**
 * 🎯 选择器 - 提供细粒度的状态订阅
 * 参考Redux的最佳实践，避免不必要的重渲染
 */
export const authSelectors = {
  // 认证状态
  isAuthenticated: (state: AuthState) => state.isAuthenticated,
  isLoading: (state: AuthState) => state.isLoading,
  user: (state: AuthState) => state.user,
  
  // UI状态  
  shouldShowFloatingLoginBar: (state: AuthState) => 
    !state.isAuthenticated && state.showFloatingLoginBar,
  
  // 错误状态
  hasError: (state: AuthState) => !!state.error,
  error: (state: AuthState) => state.error,
  
  // 复合状态
  canRefreshToken: (state: AuthState) => 
    !!state.accessToken && !!state.tokenExpiry && Date.now() < state.tokenExpiry,
};

/**
 * 🔄 自动Token刷新 - 后台定时任务
 * 企业级应用需要自动处理token续期
 */
let refreshInterval: NodeJS.Timeout | null = null;

export const setupAutoTokenRefresh = () => {
  // 清理之前的定时器
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
  
  // 每5分钟检查一次token是否需要刷新
  refreshInterval = setInterval(async () => {
    const state = useAuthState.getState();
    
    if (!state.isAuthenticated || !state.tokenExpiry) {
      return;
    }
    
    // 如果token在10分钟内过期，提前刷新
    const timeUntilExpiry = state.tokenExpiry - Date.now();
    const shouldRefresh = timeUntilExpiry < 10 * 60 * 1000; // 10分钟
    
    if (shouldRefresh) {
      try {
        await state.refreshToken();
        console.log('[AuthState] 🔄 Token自动刷新成功');
      } catch (error) {
        console.error('[AuthState] ❌ Token自动刷新失败:', error);
      }
    }
  }, 5 * 60 * 1000); // 5分钟
};

/**
 * 🧹 清理资源
 */
export const cleanupAuthState = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
    refreshInterval = null;
  }
};

export default useAuthState;