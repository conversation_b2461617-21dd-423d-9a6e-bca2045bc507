/**
 * 消息UI状态管理Hook
 * 专门处理UI相关的状态和交互逻辑，符合企业级架构规范
 * 重构版本：使用Zustand Store管理状态
 */
import { useCallback } from 'react';
import { useMessageStore } from '../stores/MessageStore';

/**
 * 消息UI状态Hook接口
 */
interface UseMessageUIProps {
  initialRefreshing?: boolean;
  initialShowPaymentModal?: boolean;
}

/**
 * 消息UI状态Hook返回值
 */
interface UseMessageUIReturn {
  // UI状态
  refreshing: boolean;
  showPaymentModal: boolean;
  
  // UI操作方法
  setRefreshing: (refreshing: boolean) => void;
  setShowPaymentModal: (show: boolean) => void;
  startRefreshing: () => void;
  stopRefreshing: () => void;
  openPaymentModal: () => void;
  closePaymentModal: () => void;
  
  // 事件处理方法
  handleRefresh: () => Promise<void>;
  handleSettingsPress: () => void;
  handlePaymentPress: () => void;
}

/**
 * 消息UI状态管理Hook
 * 
 * @param props - Hook配置参数
 * @returns UI状态和操作方法
 */
export const useMessageUI = ({
  initialRefreshing = false,
  initialShowPaymentModal = false,
}: UseMessageUIProps = {}): UseMessageUIReturn => {

  // 企业级架构师修复：直接使用Store，避免选择器循环问题
  const refreshing = useMessageStore((state) => state.refreshing);
  const showPaymentModal = useMessageStore((state) => state.showPaymentModal);

  // 企业级架构师修复：直接使用Store方法，确保引用稳定
  const startRefreshing = useCallback(() => {
    console.log('[useMessageUI] 开始刷新');
    useMessageStore.getState().setRefreshing(true);
  }, []);

  const stopRefreshing = useCallback(() => {
    console.log('[useMessageUI] 停止刷新');
    useMessageStore.getState().setRefreshing(false);
  }, []);

  const openPaymentModal = useCallback(() => {
    console.log('[useMessageUI] 打开付费弹窗');
    useMessageStore.getState().setShowPaymentModal(true);
  }, []);

  const closePaymentModal = useCallback(() => {
    console.log('[useMessageUI] 关闭付费弹窗');
    useMessageStore.getState().setShowPaymentModal(false);
  }, []);

  // 下拉刷新处理
  const handleRefresh = useCallback(async () => {
    console.log('[useMessageUI] 处理下拉刷新');
    useMessageStore.getState().setRefreshing(true);

    try {
      // 调用Store中的刷新方法
      await useMessageStore.getState().refreshAllData();
    } catch (error) {
      console.error('[useMessageUI] 刷新失败:', error);
    } finally {
      useMessageStore.getState().setRefreshing(false);
    }
  }, []);

  // 设置按钮点击处理
  const handleSettingsPress = useCallback(() => {
    console.log('[useMessageUI] 设置按钮点击');
    // TODO: 导航到设置页面
  }, []);

  // 付费按钮点击处理
  const handlePaymentPress = useCallback(() => {
    console.log('[useMessageUI] 付费按钮点击');
    openPaymentModal();
  }, []); // 移除openPaymentModal依赖，避免循环

  return {
    // UI状态（直接从Store获取）
    refreshing,
    showPaymentModal,

    // UI操作方法（稳定的useCallback）
    setRefreshing: useCallback((value: boolean) => {
      useMessageStore.getState().setRefreshing(value);
    }, []),
    setShowPaymentModal: useCallback((value: boolean) => {
      useMessageStore.getState().setShowPaymentModal(value);
    }, []),
    startRefreshing,
    stopRefreshing,
    openPaymentModal,
    closePaymentModal,

    // 事件处理方法
    handleRefresh,
    handleSettingsPress,
    handlePaymentPress,
  };
};
