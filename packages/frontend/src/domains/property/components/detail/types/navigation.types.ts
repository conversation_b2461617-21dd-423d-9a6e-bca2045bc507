/**
 * 🎯 地图导航模块类型定义
 * Phase 2.2: 完善类型定义，移除any类型
 *
 * 此文件包含地图导航功能的所有类型定义，确保100%类型安全
 */

import { NavigationProp } from '@react-navigation/native';

// ==================== 基础枚举类型 ====================

/**
 * 路线计算模式枚举
 * 使用枚举替代字符串字面量，提供更好的类型安全
 */
export enum RouteMode {
  DRIVING = 'driving',
  TAXI = 'taxi',
  TRANSIT = 'transit',
  WALKING = 'walking',
  CYCLING = 'cycling',
}

/**
 * 地址搜索返回键枚举
 * 限制returnKey的可选值
 */
export enum AddressReturnKey {
  START_LOCATION = 'startLocation',
  END_LOCATION = 'endLocation',
}

// ==================== 地址和位置类型 ====================

/**
 * 完整的地址数据接口
 * 支持各种地址搜索API的返回格式
 */
export interface AddressData {
  latitude: number;
  longitude: number;
  name?: string;
  formattedAddress?: string;
  address?: string;
  district?: string;
  city?: string;
  province?: string;
  country?: string;
  adcode?: string;
  building?: string;
  neighborhood?: string;
}

/**
 * GPS位置信息接口
 * 包含精度信息
 */
export interface GPSLocation {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  timestamp?: number;
}

/**
 * 基础位置接口
 * 用于简单的坐标表示
 */
export interface BasicLocation {
  latitude: number;
  longitude: number;
}

/**
 * 带地址的位置接口
 * 继承基础位置，添加地址信息
 */
export interface LocationData extends BasicLocation {
  address: string;
}

// ==================== 导航相关类型 ====================

/**
 * React Navigation类型定义
 * 扩展原生NavigationProp，提供类型安全的导航方法
 */
export interface AppNavigationProp extends NavigationProp<any> {
  navigate: (name: string, params?: NavigationParams) => void;
  goBack: () => void;
  replace: (name: string, params?: any) => void;
  reset: (state: any) => void;
}

/**
 * 导航参数接口
 * 定义传递给导航组件的参数类型
 */
export interface NavigationParams {
  propertyLocation: {
    latitude: number | null;
    longitude: number | null;
    address: string;
  };
  selectedAddress?: AddressData;
  returnKey?: AddressReturnKey;
}

/**
 * 属性导航路由参数
 * 专门用于PropertyNavigation路由的参数类型
 */
export interface PropertyNavigationParams {
  propertyLocation: {
    latitude: number | null;
    longitude: number | null;
    address: string;
  };
  selectedAddress?: AddressData;
  returnKey?: AddressReturnKey;
}

// ==================== 路线相关类型 ====================

/**
 * 路线信息接口
 * 包含路线的所有基本信息
 */
export interface RouteInfo {
  mode: RouteMode;
  distance: string;
  duration: string;
  cost?: string;
  coordinates?: BasicLocation[];
  description?: string;
  trafficLights?: number;
  tolls?: number;
}

/**
 * 驾车路线备选方案
 * 专门用于驾车模式的多路线支持
 */
export interface DrivingAlternative {
  distance: string;
  duration: string;
  description: string;
  cost?: string;
  coordinates: BasicLocation[];
  trafficLights?: number;
  tolls?: number;
  roadType?: 'highway' | 'urban' | 'mixed';
  congestionLevel?: 'low' | 'medium' | 'high';
}

/**
 * 路线计算结果
 * 包含所有路线和状态信息
 */
export interface RouteResult {
  routes: RouteInfo[];
  isLoading: boolean;
  error: string | null;
  drivingAlternatives?: DrivingAlternative[];
  totalRoutes?: number;
  calculationTime?: number;
}

/**
 * 选中的路线
 * 记录用户当前选择的路线
 */
export interface SelectedRoute {
  mode: RouteMode;
  alternativeIndex?: number;
  routeId?: string;
}

// ==================== 错误处理类型 ====================

/**
 * 地图错误接口
 * 统一的错误处理类型
 */
export interface MapError {
  code: string | number;
  message: string;
  domain?: string;
  userInfo?: Record<string, any>;
  timestamp?: Date;
}

/**
 * API错误接口
 * 网络请求相关错误
 */
export interface APIError extends MapError {
  statusCode?: number;
  url?: string;
  method?: string;
}

/**
 * 定位错误接口
 * GPS定位相关错误
 */
export interface LocationError extends MapError {
  accuracy?: number;
  timeout?: boolean;
  permissionDenied?: boolean;
}

// ==================== 地图事件类型 ====================

/**
 * 地图点击事件
 * 用户点击地图时的事件数据
 */
export interface MapPressEvent {
  coordinate: BasicLocation;
  position: {
    x: number;
    y: number;
  };
  timestamp?: number;
}

/**
 * 地图加载事件
 * 地图加载完成时的事件数据
 */
export interface MapLoadEvent {
  target: any;
  region?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  success: boolean;
}

/**
 * 地图区域变化事件
 * 地图视图区域变化时的事件数据
 */
export interface MapRegionChangeEvent {
  region: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  isGesture: boolean;
}

// ==================== 组件Props类型 ====================

/**
 * 基础组件Props
 * 所有地图导航组件的基础Props
 */
export interface BaseMapComponentProps {
  testID?: string;
  accessible?: boolean;
  accessibilityLabel?: string;
}

/**
 * 地图显示组件Props
 * MapDisplay组件的Props类型
 */
export interface MapDisplayProps extends BaseMapComponentProps {
  currentStartLocation: LocationData | null;
  currentEndLocation: LocationData | null;
  routeResult: RouteResult;
  selectedRoute: SelectedRoute;
  mapReady: boolean;
  onMapReady: () => void;
  onMapError?: (error: MapError) => void;
  onMapPress?: (event: MapPressEvent) => void;
}

/**
 * 导航控制组件Props
 * NavigationControls组件的Props类型
 */
export interface NavigationControlsProps extends BaseMapComponentProps {
  startLocationText: string;
  endLocationText: string;
  onStartLocationPress: () => void;
  onEndLocationPress: () => void;
  onLocationSwap: () => void;
  onStartNavigation: () => void;
  canNavigate: boolean;
  isLocationSwapped: boolean;
}

/**
 * 路线选择面板Props
 * RouteSelectionPanel组件的Props类型
 */
export interface RouteSelectionPanelProps extends BaseMapComponentProps {
  routeResult: RouteResult;
  selectedRoute: SelectedRoute;
  onSelectRoute: (mode: RouteMode, alternativeIndex?: number) => void;
  onCalculateRoute: (
    mode: RouteMode,
    requestMultiRoute?: boolean
  ) => Promise<void>;
}

/**
 * 路线信息显示Props
 * RouteInfoDisplay组件的Props类型
 */
export interface RouteInfoDisplayProps extends BaseMapComponentProps {
  routeResult: RouteResult;
  selectedRoute: SelectedRoute;
  hasValidRoute: boolean;
}

// ==================== API相关类型 ====================

/**
 * 路线请求接口
 * 发送给API的路线计算请求
 */
export interface RouteRequest {
  origin: string | BasicLocation;
  destination: string | BasicLocation;
  mode: RouteMode;
  requestMultiRoute?: boolean;
  avoidTolls?: boolean;
  avoidHighways?: boolean;
  waypoints?: BasicLocation[];
}

/**
 * API响应基础接口
 * 所有API响应的基础结构
 */
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  code?: string | number;
  timestamp?: string;
}

// ==================== 类型守卫函数 ====================

/**
 * 检查是否为有效坐标
 */
export const isValidCoordinate = (coord: any): coord is BasicLocation => {
  return (
    coord &&
    typeof coord.latitude === 'number' &&
    typeof coord.longitude === 'number' &&
    !isNaN(coord.latitude) &&
    !isNaN(coord.longitude) &&
    coord.latitude >= -90 &&
    coord.latitude <= 90 &&
    coord.longitude >= -180 &&
    coord.longitude <= 180
  );
};

/**
 * 检查是否为有效地址数据
 */
export const isValidAddressData = (data: any): data is AddressData => {
  return (
    data &&
    isValidCoordinate(data) &&
    (typeof data.name === 'string' ||
      typeof data.formattedAddress === 'string' ||
      typeof data.address === 'string')
  );
};

/**
 * 检查是否为有效路线模式
 */
export const isValidRouteMode = (mode: any): mode is RouteMode => {
  return Object.values(RouteMode).includes(mode);
};

/**
 * 检查是否为有效GPS位置
 */
export const isValidGPSLocation = (location: any): location is GPSLocation => {
  return (
    location &&
    isValidCoordinate(location) &&
    typeof location.accuracy === 'number' &&
    location.accuracy >= 0
  );
};

// ==================== 默认值和常量 ====================

/**
 * 默认路线配置
 */
export const DEFAULT_ROUTE_CONFIG = {
  mode: RouteMode.DRIVING,
  alternativeIndex: 0,
} as const;

/**
 * 默认位置精度要求
 */
export const DEFAULT_LOCATION_ACCURACY = 100; // 米

/**
 * 支持的路线模式配置
 */
export const ROUTE_MODE_CONFIG = {
  [RouteMode.DRIVING]: {
    name: '驾车',
    icon: 'car',
    color: '#1890ff',
    description: '推荐路线，考虑实时路况',
  },
  [RouteMode.TAXI]: {
    name: '打车',
    icon: 'taxi',
    color: '#52c41a',
    description: '预估打车费用和时间',
  },
  [RouteMode.TRANSIT]: {
    name: '公交',
    icon: 'bus',
    color: '#722ed1',
    description: '公共交通出行方案',
  },
  [RouteMode.WALKING]: {
    name: '步行',
    icon: 'walk',
    color: '#fa541c',
    description: '步行导航路线',
  },
  [RouteMode.CYCLING]: {
    name: '骑行',
    icon: 'bike',
    color: '#13c2c2',
    description: '自行车骑行路线',
  },
} as const;
